//-----------------------------------------------------------------------------
//                                                                            |
//                   Softing Industrial Automation GmbH                       |
//                        Richard<PERSON>-<PERSON>ee 6                            |
//                           85540 Haar, Germany                              |
//                                                                            |
//                 This is a part of the Softing OPC Toolkit                  |
//        Copyright (c) 2005-2018 Softing Industrial Automation GmbH          |
//                           All Rights Reserved                              |
//                                                                            |
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
//                             OPC Toolkit C++                                |
//                                                                            |
//  Filename    : OpcServer.cpp                                               |
//  Version     : 4.46                                                        |
//  Date        : 15-March-2018                                               |
//                                                                            |
//  Description : OPC Server template class implementation                    |
//                                                                            |
//-----------------------------------------------------------------------------

#include "stdafx.h"
#include "OpcServer.h"
#include "ServerCommon.h"

#include <iostream>
#include <sstream>
#include "../../GTWWebLib/SDGversion.h"
#include "../../GTWWebLib/svn_rev.h"

OpcServer* instance = NULL;

extern HANDLE g_opcServerEndEvent;

OpcServer* getOpcServer(void)
{
	return instance;
} // end getOpcServer


void createOpcServer(void)
{
	if (instance == NULL)
	{
		instance = new OpcServer();
	} // end if
} // end createOpcServer


void destroyOpcServer(void)
{
	if (instance != NULL)
	{
		delete instance;
		instance = NULL;
	} // end if
} // end destroyOpcServer


long API_CALL handleShutdownRequest(void)
{
	SetEvent(g_opcServerEndEvent);
	return S_OK;
} // end handleShutdownRequest


OpcServer::OpcServer(void)
{
	m_pDaSimulationElement = NULL;
} // end constructor


OpcServer::~OpcServer(void)
{
} // end destructor


long OpcServer::initialize(void)
{
	getApp()->setVersionOtb(445);
	getApp()->setAppType(EnumApplicationType_EXECUTABLE);
	//getApp()->setClsidDa(_T("{C0709A86-9876-43D7-8D91-9DD3A1C2025A}"));
  getApp()->setClsidDa(_T("{EC8D890D-3B9F-4635-AF73-4447728A782E}"));
  getApp()->setProgIdDa(_T("TMW.OpcTestServer.DA.1"));
	getApp()->setVerIndProgIdDa(_T("TMW.OpcTestServer.DA"));
	getApp()->setDescription(_T("TMW OpcTestServer OPC Server"));

  // setup xml here if configured
  getApp()->setIpPortHTTP(8083);
  getApp()->setUrlDa("/OPCTEST");

  getApp()->setMajorVersion(SDG_TMWVERSION_MAJOR);
  getApp()->setMinorVersion(SDG_TMWVERSION_MINOR);
  getApp()->setBuildNumber(SVN_BUILD_NUM);
  getApp()->setPatchVersion(SDG_TMWVERSION_PATCH);
  getApp()->setVendorInfo(_T("Triangle MicroWorks, Inc."));

	getApp()->setMinUpdateRateDa(1000);
	getApp()->setClientCheckPeriod(30000);
	getApp()->setAddressSpaceDelimiter(_T('.'));
	getApp()->setPropertyDelimiter(_T('/'));
	getApp()->ShutdownRequest = handleShutdownRequest;
	// proceed with initializing of the Toolkit tracing mechanism
	getApp()->enableTracing(
		EnumTraceGroup_ALL,
		EnumTraceGroup_ALL,
		EnumTraceGroup_SERVER,
		EnumTraceGroup_SERVER,
		_T("Trace.txt"),
		1000000,
		0);
	return S_OK;
} // end initialize

void OpcServer::setServiceName(tstring serviceName)
{
	getApp()->setServiceName(serviceName);
} // end setServiceName

long OpcServer::prepare(MyCreator* creator)
{
	long result = S_OK;
	// TODO - binary license activation
	// Fill in your binary license activation keys here
	//
	// NOTE: you can activate one or all of the features at the same time

	// activate the COM-DA Server feature
	result = getApp()->activate(EnumFeature_DA_SERVER, _T("1100-042c-1d84-3cea-da23"));
	if (!SUCCEEDED(result))
	{
		return result;
	}

	// END TODO - binary license activation
	// a valid creator must be provided to the initialize
	result = getApp()->initialize(creator);

	if (!SUCCEEDED(result))
	{
		return result;
	}

	return result;
} // end prepare

long OpcServer::start(void)
{
	return getApp()->start();
} // end start

long OpcServer::ready(void)
{
	return getApp()->ready();
} // end ready

long OpcServer::stop(void)
{
	return getApp()->stop();
} // end stop

long OpcServer::terminate(void)
{
	long result = getApp()->terminate();
	releaseApp();
	return result;
} // end terminate

long OpcServer::processCommandLine(tstring command)
{
	return getApp()->processCommandLine(command);
} // end processCommandLine


long OpcServer::buildAddressSpace(int ptCount, std::string tagsfile)
{
	MyCreator* creator = (MyCreator*)getApp()->getCreator();
	tstring aName;
	// DA
	DaAddressSpaceRoot* daRoot = getApp()->getDaAddressSpaceRoot();

  std::string tagFilePath = ""; // "c:\\TMWopcTestServerTags.txt";
  if (tagsfile != "")
  {
    tagFilePath = tagsfile;
  }
  FILE* fp = NULL;
  if (tagFilePath != "")
  {
    fp = fopen(tagFilePath.c_str(), "r");
  }
  if (fp != NULL)
  {
    std::cout << "Found tags file " << tagFilePath << "..Loading tags..." << std::endl;
    int nCount = LoadTagsFromFile(fp);
    fclose(fp);
    std::cout << "Finished loading " << nCount << " tags" << std::endl;
  }
  else
  {
    std::cout << "Loading default tags." << std::endl;
    LoadDefaultTags(ptCount);
  }

	m_pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
	aName = tstring(_T("Simulation"));
	m_pDaSimulationElement->setName(aName);
	m_pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
	m_pDaSimulationElement->setDatatype(VT_I4);
	m_pDaSimulationElement->setIoMode(EnumIoMode_POLL);
	daRoot->addChild(m_pDaSimulationElement);
	DateTime now;
	Variant aVariant(rand());
	ValueQT value(aVariant, EnumQuality_GOOD, now);
	m_pDaSimulationElement->valueChanged(value);
	DaProperty* newProperty = new DaProperty();
	newProperty->setId(EnumPropertyId_ITEM_DESCRIPTION);
	tstring propName(_T("Description"));
	newProperty->setName(propName);
	tstring propDescription(_T("Element Description"));
	newProperty->setDescription(propDescription);
	newProperty->setItemId(propName);
	newProperty->setDatatype(VT_BSTR);
	newProperty->setAccessRights(EnumAccessRights_READABLE);
	m_pDaSimulationElement->addProperty(newProperty);
  
	return S_OK;
} // end buildAddressSpace

void OpcServer::LoadDefaultTags(int ptCount)
{
  MyCreator* creator = (MyCreator*)getApp()->getCreator();
  DaAddressSpaceRoot* daRoot = getApp()->getDaAddressSpaceRoot();
  MyDaAddressSpaceElement *pCurElement = (MyDaAddressSpaceElement *)daRoot;

  DateTime now;
  Variant aVariant;

  int cnt = ptCount;

  int nBoolCount = cnt;
  int nI2Count = cnt;
  int nI4Count = cnt;
  int nUI4Count = cnt;
  int nR4Count = cnt;
  int nR8Count = cnt;
  int nStringCount = cnt;

  std::cout << "Loading " << nBoolCount << " boolean data types." << std::endl;
  std::cout << "Loading " << nI2Count << " I2 data types." << std::endl;
  std::cout << "Loading " << nI4Count << " I4 data types." << std::endl;
  std::cout << "Loading " << nUI4Count << " UI4 data types." << std::endl;
  std::cout << "Loading " << nR4Count << " R4 data types." << std::endl;
  std::cout << "Loading " << nR8Count << " R8 data types." << std::endl << std::endl;
  std::cout << "Loading " << nStringCount << " String data types." << std::endl << std::endl;

  for (int i = 0; i < nBoolCount; i++)
  {
    MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
    std::stringstream sName;
    sName << "B_" << (i + 1);
    tstring aName = tstring(sName.str().c_str());
    pDaSimulationElement->setName(aName);
    pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
    pDaSimulationElement->setDatatype(VT_BOOL);
    pDaSimulationElement->setIoMode(EnumIoMode_POLL);
    pCurElement->addChild(pDaSimulationElement);
    m_pDaAddressSpaceElements.push_back(pDaSimulationElement);
  }
  std::cout << "Loaded " << nBoolCount << " boolean data types." << std::endl;

  for (int i = 0; i < nI2Count; i++)
  {
    MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
    std::stringstream sName;
    sName << "I2_" << (i + 1);
    tstring aName = tstring(sName.str().c_str());
    pDaSimulationElement->setName(aName);
    pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
    pDaSimulationElement->setDatatype(VT_I2);
    pDaSimulationElement->setIoMode(EnumIoMode_POLL);
    pCurElement->addChild(pDaSimulationElement);
    m_pDaAddressSpaceElements.push_back(pDaSimulationElement);
  }
  std::cout << "Loaded " << nI2Count << " I2 data types." << std::endl;

  for (int i = 0; i < nI4Count; i++)
  {
    MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
    std::stringstream sName;
    sName << "I4_" << (i + 1);
    tstring aName = tstring(sName.str().c_str());
    pDaSimulationElement->setName(aName);
    pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
    pDaSimulationElement->setDatatype(VT_I4);
    pDaSimulationElement->setIoMode(EnumIoMode_POLL);
    pCurElement->addChild(pDaSimulationElement);
    m_pDaAddressSpaceElements.push_back(pDaSimulationElement);
  }
  std::cout << "Loaded " << nI4Count << " I4 data types." << std::endl;

  for (int i = 0; i < nUI4Count; i++)
  {
    MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
    std::stringstream sName;
    sName << "UI4_" << (i + 1);
    tstring aName = tstring(sName.str().c_str());
    pDaSimulationElement->setName(aName);
    pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
    pDaSimulationElement->setDatatype(VT_UI4);
    pDaSimulationElement->setIoMode(EnumIoMode_POLL);
    pCurElement->addChild(pDaSimulationElement);
    m_pDaAddressSpaceElements.push_back(pDaSimulationElement);
  }

  std::cout << "Loaded " << nUI4Count << " UI4 data types." << std::endl;

  for (int i = 0; i < nR4Count; i++)
  {
    MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
    std::stringstream sName;
    sName << "R4_" << (i + 1);
    tstring aName = tstring(sName.str().c_str());
    pDaSimulationElement->setName(aName);
    pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
    pDaSimulationElement->setDatatype(VT_R4);
    pDaSimulationElement->setIoMode(EnumIoMode_POLL);
    pCurElement->addChild(pDaSimulationElement);
    m_pDaAddressSpaceElements.push_back(pDaSimulationElement);
  }

  std::cout << "Loaded " << nR4Count << " R4 data types." << std::endl;


  for (int i = 0; i < nR8Count; i++)
  {
    MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
    std::stringstream sName;
    sName << "R8 with space" << (i + 1);
    tstring aName = tstring(sName.str().c_str());
    pDaSimulationElement->setName(aName);
    pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
    pDaSimulationElement->setDatatype(VT_R8);
    pDaSimulationElement->setIoMode(EnumIoMode_POLL);
    pCurElement->addChild(pDaSimulationElement);
    m_pDaAddressSpaceElements.push_back(pDaSimulationElement);
  }

  std::cout << "Loaded " << nR8Count << " R8 data types." << std::endl;

  for (int i = 0; i < nStringCount; i++)
  {
    MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
    std::stringstream sName;
    sName << "STR with space" << (i + 1);
    tstring aName = tstring(sName.str().c_str());
    pDaSimulationElement->setName(aName);
    pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
    pDaSimulationElement->setDatatype(VT_BSTR);
    pDaSimulationElement->setIoMode(EnumIoMode_POLL);
    pCurElement->addChild(pDaSimulationElement);
    m_pDaAddressSpaceElements.push_back(pDaSimulationElement);
  }

  std::cout << "Loaded " << nStringCount << " String data types." << std::endl;

}

int OpcServer::LoadTagsFromFile(FILE *fp)
{
  char line[256];
  char tagName[256];
  int tagType;
  MyCreator* creator = (MyCreator*)getApp()->getCreator();
  DaAddressSpaceRoot* daRoot = getApp()->getDaAddressSpaceRoot();
  DateTime now;
  Variant aVariant;

  int nCount = 0;
  do 
  {
    if (fgets(line, 256, fp) == NULL)
    {
      break;
    }

    std::string sLine = line;
    size_t index = sLine.find('\t');

    sprintf(tagName, sLine.substr(0, index).c_str());
    tagType = atoi(sLine.substr(index + 1).c_str());

    //sscanf(line, "%s %d",&tagName,&tagType);
    int j=0;
    bool bReachedType = false;
    char pointName[256];
    bool bDone = false;
    MyDaAddressSpaceElement *pCurElement = (MyDaAddressSpaceElement *)daRoot;

    // loop through tagName stop at '.' or ' '
    int ii = 0;
    while (bDone == false)
    {
      int currentJ = j;
      for (unsigned int i=0;i<strlen(&tagName[currentJ])+1;i++)
      {
        if (tagName[j] == '.')
        {
          break;
        }
        if (tagName[j] == 0)
        {
          bReachedType = true;
          break;
        }
        pointName[i] = tagName[j++];
        ii = i;
      }
      pointName[ii+1] = '\0';
      j++;

      if (bReachedType == true)
      {
        //TRACE("add point %s\n",pointName);
        MyDaAddressSpaceElement* pDaSimulationElement = (MyDaAddressSpaceElement*)creator->createMyDaAddressSpaceElement();
	      tstring aName = tstring(_T(pointName));
	      pDaSimulationElement->setName(aName);
	      pDaSimulationElement->setAccessRights(EnumAccessRights_READWRITEABLE);
	      pDaSimulationElement->setDatatype((VARENUM)tagType);
	      pDaSimulationElement->setIoMode(EnumIoMode_POLL);
	      pCurElement->addChild(pDaSimulationElement);
         m_pDaAddressSpaceElements.push_back(pDaSimulationElement);

        switch(tagType) 
        {
        case VT_R8:
          aVariant.setR8(0);
          break;
        case VT_R4:
          aVariant.setR4(0);
          break;
        case VT_I4:
          aVariant.setI4(0);
          break;
        case VT_UI4:
          aVariant.setUI4(0);
          break;
        case VT_I2:
          aVariant.setI2(0);
          break;
        case VT_UI2:
          aVariant.setUI2(0);
          break;
        case VT_I1:
          aVariant.setI1(0);
          break;
        case VT_UI1:
          aVariant.setUI1(0);
          break;
        case VT_BOOL:
          aVariant.setBOOL(VARIANT_FALSE);
          break;
        case VT_BSTR:
          aVariant.setBSTR("");
          break;
        case VT_EMPTY:
        case VT_NULL:
          aVariant.setBOOL(VARIANT_FALSE);
          aVariant.setR8(0);
          break;
        default:
          std::cout << "Failed to parse file line: " << tagName << ", skipping" << std::endl;
          //AfxMessageBox(msg);
          //ExitProcess(1);
          continue;
        }
        nCount++;

        ValueQT initValue(aVariant, EnumQuality_GOOD, now);
        pDaSimulationElement->valueChanged(initValue);
        bDone = true;
      }
      else
      {
        bool found = false;
        std::vector<AddressSpaceElement*> children = pCurElement->getChildren();
        for (size_t childCnt = 0; childCnt < children.size(); childCnt++)
        {
          if (children[childCnt]->getName() == pointName)
          {
            found = true;
            pCurElement = (MyDaAddressSpaceElement*)children[childCnt];
            break;
          }
        }

        if (!found)
        {
          //TRACE("add branch %s\n",pointName);
          MyDaAddressSpaceElement* pMyDaAddressSpaceElement = new MyDaAddressSpaceElement();
	        tstring aName = tstring(_T(pointName));
	        pMyDaAddressSpaceElement->setName(aName);
          pMyDaAddressSpaceElement->setIoMode(EnumIoMode_POLL);
          pMyDaAddressSpaceElement->setHasChildren(true);
          pCurElement->addChild(pMyDaAddressSpaceElement);
          pCurElement = pMyDaAddressSpaceElement;
         

        }
      }
    }

  } while (!feof(fp));
  
  return nCount;
}

void OpcServer::printSimulationValues()
{
  if (m_pDaAddressSpaceElements.size() > 500)
  {
    std::cout << "address space larger than 500, not showing changes" << std::endl << std::endl;
    return;
  }

  for (int ctr = 0; ctr < m_pDaAddressSpaceElements.size(); ctr++)
  {
    ValueQT value;// (aVariant, EnumQuality_GOOD, now);
    m_pDaAddressSpaceElements[ctr]->getCacheValue(value);
    std::cout << m_pDaAddressSpaceElements[ctr]->getName().c_str() << " : " << value.toString().c_str() << std::endl;
  }
  std::cout << std::endl << std::endl;
}

void OpcServer::changeSimulationValues(void)
{
  static int64_t nSimCount = 1;

	//if (m_pDaSimulationElement != NULL)
	//{
  int v = ::rand();
	Variant aVariant(v);
  std::cout << nSimCount << " Changing " << m_pDaAddressSpaceElements.size() << " points..." << std::endl;
    
  size_t ctr = 0;
  for (; ctr < m_pDaAddressSpaceElements.size(); ctr++)
  {
    int rNum = rand();
    int randomNumber = (rNum * 1000) / RAND_MAX;
    VARENUM dataType = m_pDaAddressSpaceElements[ctr]->getDatatype();

    //std::cout << ctr << ". changing value of " << m_pDaAddressSpaceElements[ctr]->getName().c_str() << " to " << v << std::endl;

    switch (dataType)
    {
    case VT_R8:
      aVariant.setR8((double)randomNumber);
      break;
    case VT_R4:
      aVariant.setR4((float)randomNumber);
      break;
    case VT_I4:
      aVariant.setI4((long)randomNumber);
      break;
    case VT_UI4:
      aVariant.setUI4((unsigned long)randomNumber);
      break;
    case VT_I2:
      aVariant.setI2((short)randomNumber);
      break;
    case VT_UI2:
      aVariant.setUI2((unsigned short)randomNumber);
      break;
    case VT_I1:
      aVariant.setI1((char)randomNumber);
      break;
    case VT_UI1:
      aVariant.setUI1((unsigned char)randomNumber);
      break;
    case VT_BOOL:
      //std::cout << ctr << ". changing value of " << m_pDaAddressSpaceElements[ctr]->getName().c_str() << " randomnumber= " << randomNumber << std::endl;
      if (randomNumber > 500)
      {
        aVariant.setBOOL((bool)VARIANT_TRUE);
      }
      else
      {
        aVariant.setBOOL((bool)VARIANT_FALSE);
      }
      break;





    case VT_BSTR:
    {
      std::string s = "string_";
      char buf[32];
      s += _itoa(randomNumber, buf, 10);
      aVariant.setBSTR(s.c_str());
    }
    break;

    default:
      break;
    }

    DateTime now;
    ValueQT value(aVariant, EnumQuality_GOOD, now);
    m_pDaAddressSpaceElements[ctr]->valueChanged(value);
  }
	//}

  std::cout << nSimCount++ << " finished changing " << ctr << " points." << std::endl << std::endl;

} // end changeSimulationValues

void OpcServer::trace(
	EnumTraceLevel aLevel,
	EnumTraceGroup aMask,
	const TCHAR* anObjectID,
	const TCHAR* aMessage,
	...)
{
	const unsigned long LENGTH = 400;
	TCHAR buffer[LENGTH + 1] = { 0 };
	va_list arglist;
	va_start(arglist, aMessage);
#ifdef TBC_OS_WINDOWS
	_vsntprintf(buffer, LENGTH, aMessage, arglist);
#endif
#ifdef TBC_OS_LINUX
	vsnprintf(buffer, LENGTH, aMessage, arglist);
#endif
	getApp()->trace(aLevel, aMask, anObjectID, buffer);
} // end trace
