/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2010 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTW61400AlarmMDO.cpp                                */
/* DESCRIPTION:  Implementation of IEC 61850-MDO                             */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 6 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#include "stdafx.h"

#include "WinTimer.h"
#include "GTW61850Client.h"
#include "GTW61850ControlBlock.h"
#include "GTW61400AlarmMDO.h"
#include "GTW61400Alarms.h"
#include "GTW61400AlarmMDOEditor.h"
#include "GTW61850DataAttributeMDO.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

using namespace tmw;

ImplementClassBaseInfo (GTW61400AlarmMDO,GTWBaseDataObject,pClassInfo,new GTW61400AlarmMDO());

class GTW61400ALM_CNVTR_READ_TMWDTIME : public GTWReadConverterTemplate<TMWDTIME>
{
private:
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  GTW61400ALM_CNVTR_READ_TMWDTIME(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags()
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWDTIME* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};


class GTW61400ALM_CNVTR_READ_STRING : public GTWReadConverterTemplate<CStdString>
{
private:
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  GTW61400ALM_CNVTR_READ_STRING(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, CStdString* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

/* class: GTW61400ALM_CNVTR_READ_DOUBLE
*/
class GTW61400ALM_CNVTR_READ_DOUBLE : public GTWReadConverterTemplate<TMWTYPES_DOUBLE>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  /* Constructor
  */
  GTW61400ALM_CNVTR_READ_DOUBLE(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags()
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_DOUBLE* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

/* class: GTW61400ALM_CNVTR_READ_SFLOAT
*/
class GTW61400ALM_CNVTR_READ_SFLOAT : public GTWReadConverterTemplate<TMWTYPES_SFLOAT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  /* Constructor
  */
  GTW61400ALM_CNVTR_READ_SFLOAT(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags()
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_SFLOAT* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

/* class: GTW61400ALM_CNVTR_READ_LONG
*/
class GTW61400ALM_CNVTR_READ_LONG : public GTWReadConverterTemplate<TMWTYPES_INT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  /* Constructor
  */
  GTW61400ALM_CNVTR_READ_LONG(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_INT* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};
/* class: GTW61400ALM_CNVTR_READ_ULONG
*/
class GTW61400ALM_CNVTR_READ_ULONG : public GTWReadConverterTemplate<TMWTYPES_UINT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  /* Constructor
  */
  GTW61400ALM_CNVTR_READ_ULONG(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_UINT* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

/* class: GTW61400ALM_CNVTR_READ_CHAR
*/
class GTW61400ALM_CNVTR_READ_CHAR : public GTWReadConverterTemplate<TMWTYPES_CHAR>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  /* Constructor
  */
  GTW61400ALM_CNVTR_READ_CHAR(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags()
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_CHAR* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

class GTW61400ALM_CNVTR_READ_SHORT : public GTWReadConverterTemplate<TMWTYPES_SHORT>
{
private:
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  GTW61400ALM_CNVTR_READ_SHORT(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags()
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_SHORT* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

class GTW61400ALM_CNVTR_READ_USHORT : public GTWReadConverterTemplate<TMWTYPES_USHORT>
{
private:
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  GTW61400ALM_CNVTR_READ_USHORT(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_USHORT* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

class GTW61400ALM_CNVTR_READ_BOOL : public GTWReadConverterTemplate<bool>
{
private:
  GTW61400AlarmMDO* m_p61400AlarmMdo;

public:
  GTW61400ALM_CNVTR_READ_BOOL(GTW61400AlarmMDO* pMdo) :
    m_p61400AlarmMdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61400AlarmMdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61400AlarmMdo->get61850Quality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_BOOL* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};

void GTW61400AlarmMDO::SetDefaultOptions(void)
{
  CStdString empty = "";
  setMdoUserTagName(empty);
  return GTWMasterDataObjectTemplate<GTWBaseDataObject>::SetDefaultOptions();
}

GTWDEFS_STAT GTW61400AlarmMDO::ParseOptionsField(const char* connectionToken, const char** ppOptionString)
{
  return GTWMasterDataObjectTemplate<GTWBaseDataObject>::ParseOptionsField(connectionToken, ppOptionString);
}

void GTW61400AlarmMDO::GetAllowedOptions(GTWDEFS_PARSED_OPTION_ARRAY& optionsArray)
{
  GTWMasterDataObjectTemplate<GTWBaseDataObject>::GetAllowedOptions(optionsArray);
}

bool GTW61400AlarmMDO::checkMdoToMdoConverter(GTWCNVTR_TYPE cnvtrType)
{
  if (
    cnvtrType == GTWCNVTR_TYPE_BOOL
    || cnvtrType == GTWCNVTR_TYPE_SHORT
    || cnvtrType == GTWCNVTR_TYPE_USHORT
    || cnvtrType == GTWCNVTR_TYPE_LONG
    || cnvtrType == GTWCNVTR_TYPE_ULONG
    || cnvtrType == GTWCNVTR_TYPE_SFLOAT
    || cnvtrType == GTWCNVTR_TYPE_DOUBLE
    )
  {
    return TMWDEFS_TRUE;
  }
  return TMWDEFS_FALSE;
}

GTWDEFS_STD_QLTY GTW61400AlarmMDO::getMdoStdQuality(void)
{
  return GTW61850DataAttributeMDO::getStdQuality(m_i61850Quality);
}

void GTW61400AlarmMDO::GetDescriptionColText(CStdString &itemStr)
{
  itemStr.Format("Status Code = %d", this->m_iStatusCode);
}

GTWBaseEditor *GTW61400AlarmMDO::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
    m_pEditor = new GTW61400AlarmMDOEditor(dto, this->m_pAlarmsNode, this,  false);
  else
    m_pEditor->SetDTO(dto);

  return m_pEditor;
}

void GTW61400AlarmMDO::getMdoDbasDataId(GTWDEFS_DBAS_DATA_ID *pDbasDataId)
{
  memset(pDbasDataId,0,sizeof(GTWDEFS_DBAS_DATA_ID));  // 61850 MDOs don't have a Dbase ID
}

void GTW61400AlarmMDO::getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg)
{
  msg.Format(pFormat.stringFormat, m_Value ? "On" : "Off");
}

void GTW61400AlarmMDO::getMdoValueAsVariant(GtwVariant &variant)
{
  variant = m_Value;
}

CStdString GTW61400AlarmMDO::getValueAsString()
{
  CStdString result;
  result.Format("%d",m_Value != 0 ? 1 : 0);
  return(result);
}

TMWTYPES_DOUBLE GTW61400AlarmMDO::getValue()
{
  TMWTYPES_DOUBLE result = m_Value;
  return(result);
}

void *GTW61400AlarmMDO::bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo, GTWCNVTR_TYPE cnvtrType)
{
  void *gtwcnvtr = TMWDEFS_NULL;

  switch(cnvtrType)
  {
  case GTWCNVTR_TYPE_FLAGS:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_BOOL(this);
    break;
  case GTWCNVTR_TYPE_BOOL:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_BOOL(this);
    break;
  case GTWCNVTR_TYPE_CHAR:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_CHAR(this);
    break;
  case GTWCNVTR_TYPE_SHORT:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_SHORT(this);
    break;
  case GTWCNVTR_TYPE_LONG:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_LONG(this);
    break;
  case GTWCNVTR_TYPE_USHORT:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_USHORT(this);
    break;
  case GTWCNVTR_TYPE_ULONG:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_ULONG(this);
    break;
  case GTWCNVTR_TYPE_SFLOAT:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_SFLOAT(this);
    break;
  case GTWCNVTR_TYPE_DOUBLE:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_DOUBLE(this);
    break;
  case GTWCNVTR_TYPE_STRING:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_STRING(this);
    break;
  case GTWCNVTR_TYPE_TMWDTIME:
    gtwcnvtr = new GTW61400ALM_CNVTR_READ_TMWDTIME(this);
    break;
  }

  if (gtwcnvtr != TMWDEFS_NULL)
    addSdoToReadBoundList(pUpdateSdo);

  return(gtwcnvtr);
}


/**********************************************************************************\
Function :			GTW61400AlarmMDO::CompareTagName
Description : [none]
Return :			GTWDEFS_STAT	-
Parameters :
const char  **ppTagName	-
Note : [none]
\**********************************************************************************/
GTWDEFS_STAT GTW61400AlarmMDO::CompareTagName(  CStdString &tagName)
{
  GTWDEFS_STAT stat = GTWBaseDataObject::CompareTagField(tagName,m_sTagName);
  return(stat);
}

/**********************************************************************************\
Function :			GTW61400AlarmMDO::updateMDO
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_UPDTRSN updateReason	-
Note : [none]
\**********************************************************************************/
void GTW61400AlarmMDO::updateMDO(GTWDEFS_UPDTRSN updateReason)
{
  CStdString msgStr;
  CStdString valueStr;
  CStdString clientName = "unknown";

  GTW61850Client* pClientNode = GetClientNode();

  if (pClientNode)
  {
    clientName = pClientNode->GetFullName();
  }

  GTWBaseDataObject const *pBdo = getBdo();
  if (pBdo == nullptr)
  {
    return;
  }
  LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850,
    "61400 Alarm Updating MDO: %s reason %u value=%s, quality=%u, time=%s", GetFullName().c_str(), updateReason,
    getValueAsString().c_str(), m_i61850Quality, GetReportedTimeString().c_str());

  GTWMasterDataObject::updateMDO(updateReason);
}

/**********************************************************************************\
Function :			GTW61400AlarmMDO::updateMDO
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_UPDTRSN updateReason	-
GTWMasterDataObject *pMdo	-
Note : [none]
\**********************************************************************************/
void GTW61400AlarmMDO::updateMDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pSourceMdo)
{
  GTW61850Client* pClientNode = GetClientNode();


  if (pSourceMdo != nullptr && pSourceMdo->getBdo() != nullptr)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61400AlarmMDO::updateMDO:: %s from %s", GetFullName().c_str(), pSourceMdo->getBdo()->GetFullName().c_str());
  }

  GtwVariant variantValue;
  if (variantValue.ChangeType(getMdoType()) != true)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "GTW61400AlarmMDO: Failed to write %s (type conversion error)\n",(const char *)GetFullName());
    return;
  }

  if ((updateReason & GTWDEFS_UPDTRSN_BY_OPERATOR) == GTWDEFS_UPDTRSN_BY_OPERATOR)
  {
    getMdoValueAsVariant(variantValue);
  }
  else if (m_pVariantReadCnvtr)
  {
    m_pVariantReadCnvtr->getValue(variantValue);
  }
  else
  {
    if (pSourceMdo != nullptr)
    {
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "GTW61400AlarmMDO::updateMDO:: failed to find converter");
    }
    return;
  }
}

/**********************************************************************************\
Function :			GTW61400AlarmMDO::RemoveMe
Description : [none]
Return :			void	-
Parameters :
void	-
Note : [none]
\**********************************************************************************/
void GTW61400AlarmMDO::DeleteMe(void)
{
  m_pClientNode->DeleteItem(this);
}

void GTW61400AlarmMDO::InitializeValue(void)
{

    m_Value = false;
}

CStdString GTW61400AlarmMDO::GetAlarmsNodeName()
  {
    if (m_pAlarmsNode)
      return m_pAlarmsNode->GetFullName();
    return "";
  }

CStdString GTW61400AlarmMDO::GetStatusAlarmsArrayName()
{
  if (m_pAlarmsNode)
    return m_pAlarmsNode->GetStatusAlarmsArrayName();
  return "";
}
CStdString GTW61400AlarmMDO::GetEventAlarmsArrayName()
{
  if (m_pAlarmsNode)
    return m_pAlarmsNode->GetEventAlarmsArrayName();
  return "";
}
CStdString GTW61400AlarmMDO::GetFullName(void)
{
  if (m_pClientNode != nullptr && m_pAlarmsNode != nullptr)
  {
    return m_pClientNode->GetMemberName() + "." + m_pAlarmsNode->GetMemberName() + "." + m_sTagName;
  }
  return "";
}

bool GTW61400AlarmMDO::UponRemove(GTWCollectionBase *pParent)
{
  GTWMasterDataObjectTemplate<GTWBaseDataObject>::UponRemove(pParent);
  m_pAlarmsNode->OnRemoveAlarmMDO(this);
  return true;
}

void GTW61400AlarmMDO::UponInsert(GTWCollectionBase *pParent)
{
  GTWMasterDataObjectTemplate<GTWBaseDataObject>::UponInsert(pParent);
  m_pAlarmsNode->OnAddAlarmMDO(this);
}


GTW61400AlarmMDO::GTW61400AlarmMDO()
{
    m_i61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;
    m_sTagName = "";
    m_sTagName = "";
    
    m_pClientNode = NULL;
    m_Value = false;
    m_LastValue = -1;
    //tmwtarg_getDateTime( &m_dsReportedTime);
    //m_eReportedTimeQuality = GTWDEFS_TIME_QLTY_ASUM;
}

void GTW61400AlarmMDO::SetAlarmsNode( GTW61400Alarms * p61400Alarms )
{
  m_pAlarmsNode = p61400Alarms;
}

void GTW61400AlarmMDO::GetTime( TMWDTIME *pValue )
{
  GTWDEFS_TIME_QLTY timeQuality;
  // Here we dont use GtwTimeZone::GetCurrentTimeZone() - keep logic used in previous release
  getMdoReportedTime(pValue, &timeQuality, GetGTWApp()->gtwUtcTimeZone);
  //*pValue = m_dsReportedTime;
}

void GTW61400ALM_CNVTR_READ_BOOL::getValue(GTWMasterDataObject *pMdo, TMWTYPES_BOOL *pValue, GTWDEFS_STD_QLTY *pStdQuality)
{
  *pValue = m_p61400AlarmMdo->getValue() != 0 ? TMWDEFS_TRUE : TMWDEFS_FALSE;
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}


void GTW61400ALM_CNVTR_READ_TMWDTIME::getValue( GTWMasterDataObject *pMdo, TMWDTIME *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  GTWDEFS_TIME_QLTY timeQuality;
  // Here we dont use GtwTimeZone::GetCurrentTimeZone() - keep logic used in previous release
  m_p61400AlarmMdo->getMdoReportedTime(pValue, &timeQuality, GetGTWApp()->gtwUtcTimeZone);
  //*pValue = m_p61400AlarmMdo->m_dsReportedTime;
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_STRING::getValue( GTWMasterDataObject *pMdo, CStdString *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = m_p61400AlarmMdo->getValueAsString();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_DOUBLE::getValue( GTWMasterDataObject *pMdo, TMWTYPES_DOUBLE *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = m_p61400AlarmMdo->getValue();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_SFLOAT::getValue( GTWMasterDataObject *pMdo, TMWTYPES_SFLOAT *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = (TMWTYPES_SFLOAT)m_p61400AlarmMdo->getValue();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_LONG::getValue( GTWMasterDataObject *pMdo, TMWTYPES_INT *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = (TMWTYPES_INT)m_p61400AlarmMdo->getValue();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_ULONG::getValue( GTWMasterDataObject *pMdo, TMWTYPES_UINT *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = (TMWTYPES_UINT)m_p61400AlarmMdo->getValue();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_CHAR::getValue( GTWMasterDataObject *pMdo, TMWTYPES_CHAR *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = (TMWTYPES_CHAR)m_p61400AlarmMdo->getValue();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_SHORT::getValue( GTWMasterDataObject *pMdo, TMWTYPES_SHORT *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = (TMWTYPES_SHORT)m_p61400AlarmMdo->getValue();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}

void GTW61400ALM_CNVTR_READ_USHORT::getValue( GTWMasterDataObject *pMdo, TMWTYPES_USHORT *pValue, GTWDEFS_STD_QLTY *pStdQuality )
{
  *pValue = (TMWTYPES_USHORT)m_p61400AlarmMdo->getValue();
  *pStdQuality = m_p61400AlarmMdo->getMdoStdQuality();
}
