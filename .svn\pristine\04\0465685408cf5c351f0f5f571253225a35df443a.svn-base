/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1994-2014 */
/*****************************************************************************/
/*                                                                           */
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                      <www.TriangleMicroWorks.com>                         */
/*                    <<EMAIL>>                       */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/

/*
* XMLHelperBDA.cpp
*
*/

#include "haspinfoxmltags.h"
#include "XMLHelperLicenseType.h"

XMLHelperLicenseType::XMLHelperLicenseType()
{
}

XMLHelperLicenseType::~XMLHelperLicenseType()
{
}

HaspInfoXMLEventHandler *XMLHelperLicenseType::startElement(HaspInfoXMLContext *pCtxt,
												   const char *pName,
												   const tmw::Array<tmw::String>& attrnames,
												   const tmw::Array<tmw::String>& attrvals)
{
	HaspInfoXMLEventHandler *h = 0;
	if (!m_stack.isEmpty())
	{
		if ((h = m_stack.top()))
			h = h->startElement(pCtxt, pName, attrnames, attrvals);
	}
  else
    h = HaspInfoXMLEventHandler::startElement(pCtxt, pName, attrnames, attrvals);

  if (h)
	  m_stack.push(h);
	return this;
}

HaspInfoXMLEventHandler *XMLHelperLicenseType::start(HaspInfoXMLContext *pCtxt,
											const char *pName,
											const tmw::Array<tmw::String>& attrnames,
											const tmw::Array<tmw::String>& attrvals)
{                   
  m_val.resize(0);
  return this;
}

bool XMLHelperLicenseType::characters(HaspInfoXMLContext *pCtxt,
                                const char *pName,
							                  const char *chars)
{
  if (HaspInfoXMLEventHandler::characters(pCtxt, pName, chars) && tmw::util::compare(pName, HASP_TAG_LICENSETYPE))
  {
    m_val.append(chars);
    return true;
  }
  return false;
}


bool XMLHelperLicenseType::endElement(HaspInfoXMLContext *pCtxt,
							                  const char *pName)
{
  if (HaspInfoXMLEventHandler::endElement(pCtxt, pName) && tmw::util::compare(pName, HASP_TAG_LICENSETYPE))
  {
    m_val.trim();
    pCtxt->CurrentFeature()->SetLicenseType(m_val);
    return true;
  }
  return false;
}
