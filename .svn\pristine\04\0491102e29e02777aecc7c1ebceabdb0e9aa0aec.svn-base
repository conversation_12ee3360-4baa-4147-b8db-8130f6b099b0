#include "stdafx.h"
#include "GtwOsUtils.h"

#include <asio.hpp>
#include <istream>
#include <iostream>
#include <ostream>
#include <string>
#include <cstdlib>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

#include "icmp_header.hpp"
#include "ipv4_header.hpp"

using asio::ip::icmp;

// Helper function to get the current hostname
std::string get_hostname() {
  std::string hostname;
  TMWGetHostName(hostname);

  // Convert hostname to lowercase for case-insensitive comparison
  std::transform(hostname.begin(), hostname.end(), hostname.begin(),
    [](unsigned char c) { return std::tolower(c); });

  return hostname;
}

class pinger
{
public:
  pinger(asio::io_context& io_context, const char* destination, int count)
    : resolver_(io_context), socket_(io_context, icmp::v4()),
    timer_(io_context), sequence_number_(0), num_replies_(0),
    io_context_(io_context)
  {
    // Check if destination is the current hostname 
    std::string curr_hostname = get_hostname();
    std::string dest_str(destination);

    // Convert destination to lowercase for case-insensitive comparison
    std::transform(dest_str.begin(), dest_str.end(), dest_str.begin(),
      [](unsigned char c) { return std::tolower(c); });

    // Default to localhost if:
    // 1. Hostname matches current machine's hostname
    // 2. Destination is "localhost"
    // 3. Unable to resolve current hostname
    if (curr_hostname.empty() ||
      (dest_str == curr_hostname ||
        dest_str == "localhost" ||
        dest_str == "127.0.0.1")) {
      destination = "127.0.0.1";
    }

    // Resolve destination
    icmp::resolver::results_type results = resolver_.resolve(destination, "");

    if (results.empty()) {
      // If resolution fails, fall back to localhost
      destination = "127.0.0.1";
      results = resolver_.resolve(destination, "");
    }

    if (results.empty()) {
      throw std::runtime_error("Failed to resolve destination");
    }
    destination_ = *results.begin();

#ifdef _DEBUG
    std::cout << "Resolved destination: "
      << destination_.address().to_string()
      << ":" << destination_.port()
      << std::endl;
#endif

    _count = count;
    _counter = 0;
    _numSuccess = 0;

    start_send();
    start_receive();
  }

  int _numSuccess;
  int _counter;
  std::size_t num_replies_;

private:
  void start_send()
  {
    std::string body("\"Hello!\" from Asio ping.");

    // Create the ICMP header
    icmp_header echo_request;
    echo_request.type(icmp_header::echo_request);
    echo_request.code(0);
    echo_request.identifier(GetPid());
    echo_request.sequence_number(++sequence_number_);

    // Compute the checksum before creating the buffer
    compute_checksum(echo_request, body.begin(), body.end());

    // Create the complete ICMP message (header + body)
    asio::streambuf request_buffer;
    std::ostream os(&request_buffer);
    os << echo_request << body;

    try {
#ifdef _DEBUG
      std::cout << "Sending to: "
        << destination_.address().to_string()
        << ":" << destination_.port()
        << std::endl;
#endif

      time_sent_ = std::chrono::steady_clock::now();
      socket_.send_to(request_buffer.data(), destination_);
    }
    catch (const std::exception& e) {
#ifdef _DEBUG
      std::cerr << "Error in send_to: " << e.what() << std::endl;
#endif
      return;
    }

    num_replies_ = 0;
    timer_.expires_at(time_sent_ + std::chrono::seconds(5));
    timer_.async_wait(std::bind(&pinger::handle_timeout, this));
  }

  void handle_timeout()
  {
    if (num_replies_ == 0)
    {
      ++_counter;
    }

    timer_.expires_at(time_sent_ + std::chrono::seconds(1));
    timer_.async_wait(std::bind(&pinger::start_send, this));

    if (_counter >= _count)
    {
      io_context_.stop();  // Directly stop the io_context
      return;
    }
  }

  void start_receive()
  {
    reply_buffer_.consume(reply_buffer_.size());

    socket_.async_receive(reply_buffer_.prepare(65536), std::bind(&pinger::handle_receive, this, std::placeholders::_2));
  }

  void handle_receive(std::size_t length)
  {
    ++_counter;
    reply_buffer_.commit(length);

    std::istream is(&reply_buffer_);
    ipv4_header ipv4_hdr;
    icmp_header icmp_hdr;
    is >> ipv4_hdr >> icmp_hdr;

    if (is && icmp_hdr.type() == icmp_header::echo_reply
      && icmp_hdr.identifier() == GetPid()
      && icmp_hdr.sequence_number() == sequence_number_)
    {
      if (num_replies_++ == 0)
        timer_.cancel();
      ++_numSuccess;
    }

    if (_counter >= _count)
    {
      io_context_.stop();  // Directly stop the io_context
      return;
    }
    start_receive();
  }

  icmp::resolver resolver_;
  icmp::endpoint destination_;
  icmp::socket socket_;
  asio::steady_timer timer_;
  unsigned short sequence_number_;
  std::chrono::steady_clock::time_point time_sent_;
  asio::streambuf reply_buffer_;
  int _count;
  asio::io_context& io_context_;  // Store reference to io_context
};

////////////////////////////////////////////////////////////////////////////////////////////////////
/// <summary> Pings an ip address. </summary>
///
/// <param name="ip_addr">  The IP address. </param>
/// <param name="count">    Number of attempts. </param>
///
/// <returns> An int, the number of successful pings. </returns>
////////////////////////////////////////////////////////////////////////////////////////////////////
GTWOSUTILS_API int ping(const char* ip_addr, int count)
{
  try
  {
    asio::io_context io_context;
    pinger p(io_context, ip_addr, count);
    io_context.run();
    return p._numSuccess;
  }
  catch (std::exception& e)
  {
#ifdef _DEBUG
    std::cerr << "Exception during ping: " << e.what() << std::endl;
#endif
    return 0;
  }
}

// The port_in_use function remains unchanged from the previous implementation

/// <summary> Port in use. </summary>
///
/// <param name="port">     The port. </param>
/// <param name="timeOut">  The time out in seconds. </param>
///
/// <returns> True if port in use, false if not in use. </returns>

GTWOSUTILS_API bool port_in_use(unsigned short port, uint32_t timeOut)
{
  using namespace asio;
  using ip::tcp;

  io_context svc;
  tcp::acceptor a(svc);

  std::error_code ec;
  uint32_t elapsedTime = 0;

  while (elapsedTime <= timeOut)
  {
    if (timeOut > 0)
    {
      GtwOsSleep(1000);
    }
    elapsedTime += 1;

    a.open(tcp::v4(), ec) || a.bind({ tcp::v4(), port }, ec);
    int ecc = ec.value();
    a.close();

    if (ecc == 0)
    {
      return false;
    }

    if (ecc != 0 && elapsedTime > timeOut)
    {
      return true;
    }
  }
  return false;
}