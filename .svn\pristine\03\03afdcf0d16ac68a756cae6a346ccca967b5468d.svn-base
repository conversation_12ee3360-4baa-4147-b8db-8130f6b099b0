<map id="ListRemove" name="ListRemove">
<area shape="rect" id="node1" title="Removes and frees an item in a list by comparing the pointer to the content." alt="" coords="5,5,97,32"/>
<area shape="rect" id="node2" href="$LinkedList_8c.html#ab82181e0c1d96954f82dee67cee41296" title="Removes and optionally frees an element in a list by comparing the content." alt="" coords="145,5,227,32"/>
<area shape="rect" id="node3" href="$LinkedList_8c.html#acad319e9ce896ff1fff30ddbeb06d22d" title="Finds an element in a list by comparing the content or pointer to the content." alt="" coords="275,5,369,32"/>
<area shape="rect" id="node4" href="$LinkedList_8c.html#a7c6fba7f4b569c69829676bed2852ee9" title="Forward iteration through a list." alt="" coords="417,5,536,32"/>
</map>
