#!/bin/bash

# Create an archive of the necessary files to perform a post mortem analysis of a core file.
ARCH_DIR="../../../archive"
tar -cv --exclude=web -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../ARM64_Release
tar -rv --exclude=TestINIandCSVfiles --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../gateway/GTWEngine
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../gateway/GTWLib
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../gateway/GTWMonitor
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../gateway/GTWOsUtils
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../gateway/GTWSNLicUtils
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../gateway/GTWWebLib
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../gateway/GTWWebMonitor
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj --exclude=.svn -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../thirdPartyCode/asio
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj --exclude=.svn --exclude=*.dll -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../thirdPartyCode/OPCUACppToolkit/Windows/Source
#tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj --exclude=.svn --exclude=*.dll -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../thirdPartyCode/openssl
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj --exclude=.svn -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../thirdPartyCode/TimeZone
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj --exclude=.svn -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../thirdPartyCode/unixODBC
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj --exclude=.svn -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../thirdPartyCode/ZipLib
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj --exclude=.svn --exclude=*.dll --exclude=Setups --exclude=xampleSCLFiles --exclude=openSSL -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../TMW61850
tar -rv --exclude=arm64 --exclude=VisualGDBCache -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../tmwscl/config
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../tmwscl/dnp
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../tmwscl/i870
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../tmwscl/modbus
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../tmwscl/tmwtarg/LinIoTarg
tar -rv --exclude=arm64 --exclude=VisualGDBCache --exclude=*.obj -f $ARCH_DIR/tmwsdg-$1-$2.tar ../../tmwscl/utils

cd $ARCH_DIR
bzip2 tmwsdg-$1-$2.tar
