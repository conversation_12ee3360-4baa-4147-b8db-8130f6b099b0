2025-08-13T19:39:31.195 UTC: License Manager Startup.
2025-08-13T19:39:31.195 UTC: ProductIDSeat is 6 or 12
2025-08-13T19:39:31.430 UTC: Primary Search Result:
<?xml version="1.0" encoding="UTF-8" ?>
<hasp_info>
  <feature id="19" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="22" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="23" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="24" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="25" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="26" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="27" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="28" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="29" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="30" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="31" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="32" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="33" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="34" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="35" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="36" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="37" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="38" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="39" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="40" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="41" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="42" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="43" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="44" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="45" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="46" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="86" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="87" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="95" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="96" expired="false" disabled="false" locked="true" usable="true">
    <license>
      <license_type>perpetual</license_type>
    </license>
    <hasp id="520587390365712135">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="19" expired="true" disabled="false" locked="false" usable="false">
    <license>
      <license_type>trial</license_type>
      <time_start>**********</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="22" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="24" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="25" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="26" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="29" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="30" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="31" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="34" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="35" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="36" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="37" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="38" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="39" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="40" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="41" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="42" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="43" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="44" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="45" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="47" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>7776000</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="95" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
  <feature id="96" expired="false" disabled="false" locked="false" usable="true">
    <license>
      <license_type>trial</license_type>
      <time_start>uninitialized</time_start>
      <total_time>1814400</total_time>
    </license>
    <hasp id="1108727542486942339">
      <disabled>false</disabled>
    </hasp>
    <product>
      <id>6</id>
    </product>
  </feature>
</hasp_info>

2025-08-13T19:39:31.432 UTC: License found=  1
2025-08-13T19:39:31.432 UTC: Key found= 520587390365712135
2025-08-13T19:39:31.432 UTC: Is Trial= 0
2025-08-13T19:39:31.432 UTC: Is Timed= 0
2025-08-13T19:39:31.441 UTC: Login attempt for Feature ID 19; Result= 0
2025-08-13T19:39:31.441 UTC: Key acquired= 1
2025-08-13T19:39:31.483 UTC: Read MPX attempt; Result= 0
2025-08-13T19:39:31.483 UTC: MPX Value= 2066-10-04 (YYYY-MM-DD)
2025-08-13T19:39:31.484 UTC: Read invoice attempt; Result= 0
2025-08-13T19:39:31.484 UTC: Invoice Value= 12345
2025-08-13T19:44:42.315 UTC: Attempting logout from license
2025-08-13T19:44:42.317 UTC: Logout Result= 0
