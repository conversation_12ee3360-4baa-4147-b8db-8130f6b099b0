/* 
 * SDG Runtime
 *
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.IO;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reflection;
using RestSharp;
using NUnit.Framework;

using IO.Swagger.Client;
using IO.Swagger.Api;
using IO.Swagger.Model;

namespace IO.Swagger.Test
{
    /// <summary>
    ///  Class for testing EditorsApi
    /// </summary>
    /// <remarks>
    /// This file is automatically generated by Swagger Codegen.
    /// Please update the test case below to test the API endpoint.
    /// </remarks>
    [TestFixture]
    public class EditorsApiTests
    {
        private EditorsApi instance;

        /// <summary>
        /// Setup before each unit test
        /// </summary>
        [SetUp]
        public void Init()
        {
            instance = new EditorsApi();
        }

        /// <summary>
        /// Clean up after each unit test
        /// </summary>
        [TearDown]
        public void Cleanup()
        {

        }

        /// <summary>
        /// Test an instance of EditorsApi
        /// </summary>
        [Test]
        public void InstanceTest()
        {
            // TODO uncomment below to test 'IsInstanceOfType' EditorsApi
            //Assert.IsInstanceOfType(typeof(EditorsApi), instance, "instance is a EditorsApi");
        }

        
        /// <summary>
        /// Test CreateOrUpdateEditorObject
        /// </summary>
        [Test]
        public void CreateOrUpdateEditorObjectTest()
        {
            // TODO uncomment below to test the method and replace null with proper value
            //string command = null;
            //EditorCommandDTO body = null;
            //instance.CreateOrUpdateEditorObject(command, body);
            
        }
        
        /// <summary>
        /// Test EditorAction
        /// </summary>
        [Test]
        public void EditorActionTest()
        {
            // TODO uncomment below to test the method and replace null with proper value
            //string objectName = null;
            //string action = null;
            //string objectCollectionKind = null;
            //string parameter1 = null;
            //string parameter2 = null;
            //string parameter3 = null;
            //string parameter4 = null;
            //string parameter5 = null;
            //string parameter6 = null;
            //instance.EditorAction(objectName, action, objectCollectionKind, parameter1, parameter2, parameter3, parameter4, parameter5, parameter6);
            
        }
        
        /// <summary>
        /// Test GetEditorData
        /// </summary>
        [Test]
        public void GetEditorDataTest()
        {
            // TODO uncomment below to test the method and replace null with proper value
            //string command = null;
            //string objectName = null;
            //string parentObjectName = null;
            //bool? editAtRuntime = null;
            //string objectClassName = null;
            //string objectCollectionKind = null;
            //var response = instance.GetEditorData(command, objectName, parentObjectName, editAtRuntime, objectClassName, objectCollectionKind);
            //Assert.IsInstanceOf<EditorSpecificationObjectDTO> (response, "response is EditorSpecificationObjectDTO");
        }
        
        /// <summary>
        /// Test PostEditorCommand
        /// </summary>
        [Test]
        public void PostEditorCommandTest()
        {
            // TODO uncomment below to test the method and replace null with proper value
            //string command = null;
            //EditorCommandDTO body = null;
            //instance.PostEditorCommand(command, body);
            
        }
        
        /// <summary>
        /// Test RemoveObject
        /// </summary>
        [Test]
        public void RemoveObjectTest()
        {
            // TODO uncomment below to test the method and replace null with proper value
            //string command = null;
            //string objectName = null;
            //string parentObjectName = null;
            //string objectCollectionKind = null;
            //instance.RemoveObject(command, objectName, parentObjectName, objectCollectionKind);
            
        }
        
    }

}
