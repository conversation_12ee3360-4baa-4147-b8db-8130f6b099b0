version: 1.1.{build}
image: Visual Studio 2015
configuration: Debug
install:
- cmd: >-
    git clone https://github.com/eclipse/paho.mqtt.c.git

    cd paho.mqtt.c

    mkdir build_cmake

    cd build_cmake

    "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" x64

    cmake -G "NMake Makefiles" -DCMAKE_INSTALL_PREFIX="C:\Temp\paho-c" -DPAHO_WITH_SSL=TRUE -DPAHO_BUILD_DOCUMENTATION=FALSE -DPAHO_BUILD_SAMPLES=FALSE -DCMAKE_BUILD_TYPE=Release -DCMAKE_VERBOSE_MAKEFILE=FALSE ..

    nmake install

    cd ..\..

build_script:
- cmd: >-
    mkdir build_cmake

    cd build_cmake

    "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" x64

    cmake -G "NMake Makefiles" -DCMAKE_INSTALL_PREFIX="C:\Temp\paho-cpp" -DCMAKE_PREFIX_PATH="C:\Temp\paho-c" -DPAHO_WITH_SSL=TRUE -DPAHO_BUILD_DOCUMENTATION=FALSE -DCMAKE_BUILD_TYPE=Release -DCMAKE_VERBOSE_MAKEFILE=TRUE ..

    nmake

    nmake install

    cd ..

