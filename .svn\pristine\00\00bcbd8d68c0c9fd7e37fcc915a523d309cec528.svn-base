<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>MQTT C Client Libraries Internals: MQTTPacket.c File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MQTT C Client Libraries Internals
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">MQTTPacket.c File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>functions to deal with reading and writing of MQTT packets from and to sockets  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &quot;MQTTPacket.h&quot;</code><br />
<code>#include &quot;Log.h&quot;</code><br />
<code>#include &quot;MQTTPersistence.h&quot;</code><br />
<code>#include &quot;Messages.h&quot;</code><br />
<code>#include &quot;StackTrace.h&quot;</code><br />
<code>#include &quot;WebSocket.h&quot;</code><br />
<code>#include &quot;MQTTTime.h&quot;</code><br />
<code>#include &lt;stdlib.h&gt;</code><br />
<code>#include &lt;string.h&gt;</code><br />
<code>#include &quot;Heap.h&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for MQTTPacket.c:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c__incl.png" border="0" usemap="#MQTTPacket_8c" alt=""/></div>
<map name="MQTTPacket_8c" id="MQTTPacket_8c">
<area shape="rect" title="functions to deal with reading and writing of MQTT packets from and to sockets" alt="" coords="25,5,136,32"/>
<area shape="rect" title=" " alt="" coords="5,80,68,107"/>
<area shape="rect" title=" " alt="" coords="93,80,157,107"/>
</map>
</div>
</div><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a2d017cd3beb218080a7988e2deed2a11"><td class="memItemLeft" align="right" valign="top"><a id="a2d017cd3beb218080a7988e2deed2a11"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>min</b>(A,  B)&#160;&#160;&#160;( (A) &lt; (B) ? (A):(B))</td></tr>
<tr class="separator:a2d017cd3beb218080a7988e2deed2a11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a166c9117b1ef3db3292d475f300ca560"><td class="memItemLeft" align="right" valign="top"><a id="a166c9117b1ef3db3292d475f300ca560"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>MAX_NO_OF_REMAINING_LENGTH_BYTES</b>&#160;&#160;&#160;4</td></tr>
<tr class="separator:a166c9117b1ef3db3292d475f300ca560"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a166c9117b1ef3db3292d475f300ca560"><td class="memItemLeft" align="right" valign="top"><a id="a166c9117b1ef3db3292d475f300ca560"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>MAX_NO_OF_REMAINING_LENGTH_BYTES</b>&#160;&#160;&#160;4</td></tr>
<tr class="separator:a166c9117b1ef3db3292d475f300ca560"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a9f9e58aba3ed5d513862d76bc5e29743"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a9f9e58aba3ed5d513862d76bc5e29743">MQTTPacket_name</a> (int ptype)</td></tr>
<tr class="memdesc:a9f9e58aba3ed5d513862d76bc5e29743"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts an MQTT packet code into its name.  <a href="MQTTPacket_8c.html#a9f9e58aba3ed5d513862d76bc5e29743">More...</a><br /></td></tr>
<tr class="separator:a9f9e58aba3ed5d513862d76bc5e29743"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae1ec2d8714335c6ec88c93e957b644d2"><td class="memItemLeft" align="right" valign="top">static char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#ae1ec2d8714335c6ec88c93e957b644d2">readUTFlen</a> (char **pptr, char *enddata, int *<a class="el" href="utf-8_8c.html#afed088663f8704004425cdae2120b9b3">len</a>)</td></tr>
<tr class="memdesc:ae1ec2d8714335c6ec88c93e957b644d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reads a "UTF" string from the input buffer.  <a href="MQTTPacket_8c.html#ae1ec2d8714335c6ec88c93e957b644d2">More...</a><br /></td></tr>
<tr class="separator:ae1ec2d8714335c6ec88c93e957b644d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad89e627a37f7f7eb4355b076cd46e0b0"><td class="memItemLeft" align="right" valign="top">static int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#ad89e627a37f7f7eb4355b076cd46e0b0">MQTTPacket_send_ack</a> (int MQTTVersion, int type, int msgid, int dup, <a class="el" href="structnetworkHandles.html">networkHandles</a> *net)</td></tr>
<tr class="memdesc:ad89e627a37f7f7eb4355b076cd46e0b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send an MQTT acknowledgement packet down a socket.  <a href="MQTTPacket_8c.html#ad89e627a37f7f7eb4355b076cd46e0b0">More...</a><br /></td></tr>
<tr class="separator:ad89e627a37f7f7eb4355b076cd46e0b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af922033c495b05fe4242afc54211f20d"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#af922033c495b05fe4242afc54211f20d">MQTTPacket_Factory</a> (int MQTTVersion, <a class="el" href="structnetworkHandles.html">networkHandles</a> *net, int *error)</td></tr>
<tr class="memdesc:af922033c495b05fe4242afc54211f20d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reads one MQTT packet from a socket.  <a href="MQTTPacket_8c.html#af922033c495b05fe4242afc54211f20d">More...</a><br /></td></tr>
<tr class="separator:af922033c495b05fe4242afc54211f20d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51f58f1c7864f9fe87b55cc6ccb10129"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129">MQTTPacket_send</a> (<a class="el" href="structnetworkHandles.html">networkHandles</a> *net, <a class="el" href="unionHeader.html">Header</a> header, char *buffer, size_t buflen, int freeData, int MQTTVersion)</td></tr>
<tr class="memdesc:a51f58f1c7864f9fe87b55cc6ccb10129"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sends an MQTT packet in one system call write.  <a href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129">More...</a><br /></td></tr>
<tr class="separator:a51f58f1c7864f9fe87b55cc6ccb10129"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3635fd8035177b20c478daea6bad9328"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a3635fd8035177b20c478daea6bad9328">MQTTPacket_sends</a> (<a class="el" href="structnetworkHandles.html">networkHandles</a> *net, <a class="el" href="unionHeader.html">Header</a> header, <a class="el" href="structPacketBuffers.html">PacketBuffers</a> *bufs, int MQTTVersion)</td></tr>
<tr class="memdesc:a3635fd8035177b20c478daea6bad9328"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sends an MQTT packet from multiple buffers in one system call write.  <a href="MQTTPacket_8c.html#a3635fd8035177b20c478daea6bad9328">More...</a><br /></td></tr>
<tr class="separator:a3635fd8035177b20c478daea6bad9328"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadd77a4fa1b2d5c7791e3542c56af856"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856">MQTTPacket_encode</a> (char *buf, size_t length)</td></tr>
<tr class="memdesc:aadd77a4fa1b2d5c7791e3542c56af856"><td class="mdescLeft">&#160;</td><td class="mdescRight">Encodes the message length according to the MQTT algorithm.  <a href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856">More...</a><br /></td></tr>
<tr class="separator:aadd77a4fa1b2d5c7791e3542c56af856"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1f61e571640513a9785b3164141dfbc"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#ac1f61e571640513a9785b3164141dfbc">MQTTPacket_decode</a> (<a class="el" href="structnetworkHandles.html">networkHandles</a> *net, size_t *value)</td></tr>
<tr class="memdesc:ac1f61e571640513a9785b3164141dfbc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decodes the message length according to the MQTT algorithm.  <a href="MQTTPacket_8c.html#ac1f61e571640513a9785b3164141dfbc">More...</a><br /></td></tr>
<tr class="separator:ac1f61e571640513a9785b3164141dfbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a132d2d5b304d37cd2348a973f7b315de"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de">readInt</a> (char **pptr)</td></tr>
<tr class="memdesc:a132d2d5b304d37cd2348a973f7b315de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculates an integer from two bytes read from the input buffer.  <a href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de">More...</a><br /></td></tr>
<tr class="separator:a132d2d5b304d37cd2348a973f7b315de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adca3afbe588ae7e6f342c5a697e4ee45"><td class="memItemLeft" align="right" valign="top">char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#adca3afbe588ae7e6f342c5a697e4ee45">readUTF</a> (char **pptr, char *enddata)</td></tr>
<tr class="memdesc:adca3afbe588ae7e6f342c5a697e4ee45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reads a "UTF" string from the input buffer.  <a href="MQTTPacket_8c.html#adca3afbe588ae7e6f342c5a697e4ee45">More...</a><br /></td></tr>
<tr class="separator:adca3afbe588ae7e6f342c5a697e4ee45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff1d10b221f5b4ce421b4c2588cbe511"><td class="memItemLeft" align="right" valign="top">unsigned char&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#aff1d10b221f5b4ce421b4c2588cbe511">readChar</a> (char **pptr)</td></tr>
<tr class="memdesc:aff1d10b221f5b4ce421b4c2588cbe511"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reads one character from the input buffer.  <a href="MQTTPacket_8c.html#aff1d10b221f5b4ce421b4c2588cbe511">More...</a><br /></td></tr>
<tr class="separator:aff1d10b221f5b4ce421b4c2588cbe511"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad29ec8b2fbf0ec0195621b44f8945923"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#ad29ec8b2fbf0ec0195621b44f8945923">writeChar</a> (char **pptr, char c)</td></tr>
<tr class="memdesc:ad29ec8b2fbf0ec0195621b44f8945923"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes one character to an output buffer.  <a href="MQTTPacket_8c.html#ad29ec8b2fbf0ec0195621b44f8945923">More...</a><br /></td></tr>
<tr class="separator:ad29ec8b2fbf0ec0195621b44f8945923"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07aa0146eda3d32979142e7df8ad5fc3"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3">writeInt</a> (char **pptr, int anInt)</td></tr>
<tr class="memdesc:a07aa0146eda3d32979142e7df8ad5fc3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes an integer as 2 bytes to an output buffer.  <a href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3">More...</a><br /></td></tr>
<tr class="separator:a07aa0146eda3d32979142e7df8ad5fc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0fcaa11ac05ce448a433a53f9cae420"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#af0fcaa11ac05ce448a433a53f9cae420">writeUTF</a> (char **pptr, const char *string)</td></tr>
<tr class="memdesc:af0fcaa11ac05ce448a433a53f9cae420"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes a "UTF" string to an output buffer.  <a href="MQTTPacket_8c.html#af0fcaa11ac05ce448a433a53f9cae420">More...</a><br /></td></tr>
<tr class="separator:af0fcaa11ac05ce448a433a53f9cae420"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8886398fbf89872f8e593444d351a5aa"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a8886398fbf89872f8e593444d351a5aa">writeData</a> (char **pptr, const void *data, int datalen)</td></tr>
<tr class="memdesc:a8886398fbf89872f8e593444d351a5aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes length delimited data to an output buffer.  <a href="MQTTPacket_8c.html#a8886398fbf89872f8e593444d351a5aa">More...</a><br /></td></tr>
<tr class="separator:a8886398fbf89872f8e593444d351a5aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3ddd9c1a35d51bf70f44a1aa6fa0bba"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#af3ddd9c1a35d51bf70f44a1aa6fa0bba">MQTTPacket_header_only</a> (int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</td></tr>
<tr class="memdesc:af3ddd9c1a35d51bf70f44a1aa6fa0bba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Function used in the new packets table to create packets which have only a header.  <a href="MQTTPacket_8c.html#af3ddd9c1a35d51bf70f44a1aa6fa0bba">More...</a><br /></td></tr>
<tr class="separator:af3ddd9c1a35d51bf70f44a1aa6fa0bba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9e17261b4d09d802dba6a65bcdc44fa"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#aa9e17261b4d09d802dba6a65bcdc44fa">MQTTPacket_send_disconnect</a> (<a class="el" href="structClients.html">Clients</a> *client, enum MQTTReasonCodes reason, <a class="el" href="structMQTTProperties.html">MQTTProperties</a> *props)</td></tr>
<tr class="memdesc:aa9e17261b4d09d802dba6a65bcdc44fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send an MQTT disconnect packet down a socket.  <a href="MQTTPacket_8c.html#aa9e17261b4d09d802dba6a65bcdc44fa">More...</a><br /></td></tr>
<tr class="separator:aa9e17261b4d09d802dba6a65bcdc44fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58feb89ee5616f2ea6d222bbbef927bb"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a58feb89ee5616f2ea6d222bbbef927bb">MQTTPacket_publish</a> (int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</td></tr>
<tr class="memdesc:a58feb89ee5616f2ea6d222bbbef927bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Function used in the new packets table to create publish packets.  <a href="MQTTPacket_8c.html#a58feb89ee5616f2ea6d222bbbef927bb">More...</a><br /></td></tr>
<tr class="separator:a58feb89ee5616f2ea6d222bbbef927bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a642e5f62d21f14e81f8fc70b491d89c8"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a642e5f62d21f14e81f8fc70b491d89c8">MQTTPacket_freePublish</a> (<a class="el" href="structPublish.html">Publish</a> *pack)</td></tr>
<tr class="memdesc:a642e5f62d21f14e81f8fc70b491d89c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free allocated storage for a publish packet.  <a href="MQTTPacket_8c.html#a642e5f62d21f14e81f8fc70b491d89c8">More...</a><br /></td></tr>
<tr class="separator:a642e5f62d21f14e81f8fc70b491d89c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ef92fb0207ed6272de9ebc749745d0c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a0ef92fb0207ed6272de9ebc749745d0c">MQTTPacket_freeAck</a> (<a class="el" href="structAck.html">Ack</a> *pack)</td></tr>
<tr class="memdesc:a0ef92fb0207ed6272de9ebc749745d0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free allocated storage for an ack packet.  <a href="MQTTPacket_8c.html#a0ef92fb0207ed6272de9ebc749745d0c">More...</a><br /></td></tr>
<tr class="separator:a0ef92fb0207ed6272de9ebc749745d0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b93325a6a336406497b20b489d6c46a"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a8b93325a6a336406497b20b489d6c46a">MQTTPacket_send_puback</a> (int MQTTVersion, int msgid, <a class="el" href="structnetworkHandles.html">networkHandles</a> *net, const char *clientID)</td></tr>
<tr class="memdesc:a8b93325a6a336406497b20b489d6c46a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send an MQTT PUBACK packet down a socket.  <a href="MQTTPacket_8c.html#a8b93325a6a336406497b20b489d6c46a">More...</a><br /></td></tr>
<tr class="separator:a8b93325a6a336406497b20b489d6c46a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4262b8f35885dc123f59268fb74cec3d"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a4262b8f35885dc123f59268fb74cec3d">MQTTPacket_freeSuback</a> (<a class="el" href="structSuback.html">Suback</a> *pack)</td></tr>
<tr class="memdesc:a4262b8f35885dc123f59268fb74cec3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free allocated storage for a suback packet.  <a href="MQTTPacket_8c.html#a4262b8f35885dc123f59268fb74cec3d">More...</a><br /></td></tr>
<tr class="separator:a4262b8f35885dc123f59268fb74cec3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9dd0446112e7bc982f8e3bb8bbb6b409"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a9dd0446112e7bc982f8e3bb8bbb6b409">MQTTPacket_freeUnsuback</a> (<a class="el" href="structUnsuback.html">Unsuback</a> *pack)</td></tr>
<tr class="memdesc:a9dd0446112e7bc982f8e3bb8bbb6b409"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free allocated storage for a suback packet.  <a href="MQTTPacket_8c.html#a9dd0446112e7bc982f8e3bb8bbb6b409">More...</a><br /></td></tr>
<tr class="separator:a9dd0446112e7bc982f8e3bb8bbb6b409"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8167e3188a4bdcfb30c10e0d76c82afe"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a8167e3188a4bdcfb30c10e0d76c82afe">MQTTPacket_send_pubrec</a> (int MQTTVersion, int msgid, <a class="el" href="structnetworkHandles.html">networkHandles</a> *net, const char *clientID)</td></tr>
<tr class="memdesc:a8167e3188a4bdcfb30c10e0d76c82afe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send an MQTT PUBREC packet down a socket.  <a href="MQTTPacket_8c.html#a8167e3188a4bdcfb30c10e0d76c82afe">More...</a><br /></td></tr>
<tr class="separator:a8167e3188a4bdcfb30c10e0d76c82afe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adec2c812b12255dab78f75163a4f1960"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#adec2c812b12255dab78f75163a4f1960">MQTTPacket_send_pubrel</a> (int MQTTVersion, int msgid, int dup, <a class="el" href="structnetworkHandles.html">networkHandles</a> *net, const char *clientID)</td></tr>
<tr class="memdesc:adec2c812b12255dab78f75163a4f1960"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send an MQTT PUBREL packet down a socket.  <a href="MQTTPacket_8c.html#adec2c812b12255dab78f75163a4f1960">More...</a><br /></td></tr>
<tr class="separator:adec2c812b12255dab78f75163a4f1960"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a908f81381b1720e4a53d15c1c4690f5c"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a908f81381b1720e4a53d15c1c4690f5c">MQTTPacket_send_pubcomp</a> (int MQTTVersion, int msgid, <a class="el" href="structnetworkHandles.html">networkHandles</a> *net, const char *clientID)</td></tr>
<tr class="memdesc:a908f81381b1720e4a53d15c1c4690f5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send an MQTT PUBCOMP packet down a socket.  <a href="MQTTPacket_8c.html#a908f81381b1720e4a53d15c1c4690f5c">More...</a><br /></td></tr>
<tr class="separator:a908f81381b1720e4a53d15c1c4690f5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93c155059c80bd01b4a1561d9bec1d13"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a> (int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</td></tr>
<tr class="memdesc:a93c155059c80bd01b4a1561d9bec1d13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Function used in the new packets table to create acknowledgement packets.  <a href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">More...</a><br /></td></tr>
<tr class="separator:a93c155059c80bd01b4a1561d9bec1d13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ebcf93ff2ba3bd103f016c975e9c9c4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a9ebcf93ff2ba3bd103f016c975e9c9c4">MQTTPacket_send_publish</a> (<a class="el" href="structPublish.html">Publish</a> *pack, int dup, int qos, int retained, <a class="el" href="structnetworkHandles.html">networkHandles</a> *net, const char *clientID)</td></tr>
<tr class="memdesc:a9ebcf93ff2ba3bd103f016c975e9c9c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Send an MQTT PUBLISH packet down a socket.  <a href="MQTTPacket_8c.html#a9ebcf93ff2ba3bd103f016c975e9c9c4">More...</a><br /></td></tr>
<tr class="separator:a9ebcf93ff2ba3bd103f016c975e9c9c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28f1754fe1f090d54bc3b5687fb2dd8c"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a28f1754fe1f090d54bc3b5687fb2dd8c">MQTTPacket_free_packet</a> (<a class="el" href="structMQTTPacket.html">MQTTPacket</a> *pack)</td></tr>
<tr class="memdesc:a28f1754fe1f090d54bc3b5687fb2dd8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Free allocated storage for a various packet tyoes.  <a href="MQTTPacket_8c.html#a28f1754fe1f090d54bc3b5687fb2dd8c">More...</a><br /></td></tr>
<tr class="separator:a28f1754fe1f090d54bc3b5687fb2dd8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefc0aa52c1cb13fa7bfcd77810d6a617"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#aefc0aa52c1cb13fa7bfcd77810d6a617">writeInt4</a> (char **pptr, int anInt)</td></tr>
<tr class="memdesc:aefc0aa52c1cb13fa7bfcd77810d6a617"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes an integer as 4 bytes to an output buffer.  <a href="MQTTPacket_8c.html#aefc0aa52c1cb13fa7bfcd77810d6a617">More...</a><br /></td></tr>
<tr class="separator:aefc0aa52c1cb13fa7bfcd77810d6a617"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa8fc559d3a1e58ab50e69146666f2f63"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#aa8fc559d3a1e58ab50e69146666f2f63">readInt4</a> (char **pptr)</td></tr>
<tr class="memdesc:aa8fc559d3a1e58ab50e69146666f2f63"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calculates an integer from two bytes read from the input buffer.  <a href="MQTTPacket_8c.html#aa8fc559d3a1e58ab50e69146666f2f63">More...</a><br /></td></tr>
<tr class="separator:aa8fc559d3a1e58ab50e69146666f2f63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1721836572050b02dda48d9ec0509cf0"><td class="memItemLeft" align="right" valign="top"><a id="a1721836572050b02dda48d9ec0509cf0"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>writeMQTTLenString</b> (char **pptr, <a class="el" href="structMQTTLenString.html">MQTTLenString</a> lenstring)</td></tr>
<tr class="separator:a1721836572050b02dda48d9ec0509cf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2d27b49d88f5c8c59e5bd03918e45f9"><td class="memItemLeft" align="right" valign="top"><a id="ae2d27b49d88f5c8c59e5bd03918e45f9"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>MQTTLenStringRead</b> (<a class="el" href="structMQTTLenString.html">MQTTLenString</a> *lenstring, char **pptr, char *enddata)</td></tr>
<tr class="separator:ae2d27b49d88f5c8c59e5bd03918e45f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1774533e9d0beb549eb9136c05775a5a"><td class="memItemLeft" align="right" valign="top"><a id="a1774533e9d0beb549eb9136c05775a5a"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>MQTTPacket_VBIlen</b> (int rem_len)</td></tr>
<tr class="separator:a1774533e9d0beb549eb9136c05775a5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4fc1ee4d2cf8fd9bd59d89aadab222df"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a4fc1ee4d2cf8fd9bd59d89aadab222df">MQTTPacket_VBIdecode</a> (int(*getcharfn)(char *, int), unsigned int *value)</td></tr>
<tr class="memdesc:a4fc1ee4d2cf8fd9bd59d89aadab222df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Decodes the message length according to the MQTT algorithm.  <a href="MQTTPacket_8c.html#a4fc1ee4d2cf8fd9bd59d89aadab222df">More...</a><br /></td></tr>
<tr class="separator:a4fc1ee4d2cf8fd9bd59d89aadab222df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ac216a4deeff513193ce9d515d1e3b4"><td class="memItemLeft" align="right" valign="top"><a id="a4ac216a4deeff513193ce9d515d1e3b4"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>bufchar</b> (char *c, int count)</td></tr>
<tr class="separator:a4ac216a4deeff513193ce9d515d1e3b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ce47d3140f32c7d38b4ab1468d4dbe4"><td class="memItemLeft" align="right" valign="top"><a id="a3ce47d3140f32c7d38b4ab1468d4dbe4"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>MQTTPacket_decodeBuf</b> (char *buf, unsigned int *value)</td></tr>
<tr class="separator:a3ce47d3140f32c7d38b4ab1468d4dbe4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a4536b1c36ed06171f20baeb69c01aa02"><td class="memItemLeft" align="right" valign="top">static const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a4536b1c36ed06171f20baeb69c01aa02">packet_names</a> []</td></tr>
<tr class="memdesc:a4536b1c36ed06171f20baeb69c01aa02"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="structList.html" title="Structure to hold all data for one list.">List</a> of the predefined MQTT v3/v5 packet names.  <a href="MQTTPacket_8c.html#a4536b1c36ed06171f20baeb69c01aa02">More...</a><br /></td></tr>
<tr class="separator:a4536b1c36ed06171f20baeb69c01aa02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36755eeac28fc14f18486b5a2c6f3852"><td class="memItemLeft" align="right" valign="top"><a id="a36755eeac28fc14f18486b5a2c6f3852"></a>
const char **&#160;</td><td class="memItemRight" valign="bottom"><b>MQTTClient_packet_names</b> = <a class="el" href="MQTTPacket_8c.html#a4536b1c36ed06171f20baeb69c01aa02">packet_names</a></td></tr>
<tr class="separator:a36755eeac28fc14f18486b5a2c6f3852"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a210a7b616c27aa7247824022285da784"><td class="memItemLeft" align="right" valign="top">pf&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="MQTTPacket_8c.html#a210a7b616c27aa7247824022285da784">new_packets</a> []</td></tr>
<tr class="memdesc:a210a7b616c27aa7247824022285da784"><td class="mdescLeft">&#160;</td><td class="mdescRight">Array of functions to build packets, indexed according to packet code.  <a href="MQTTPacket_8c.html#a210a7b616c27aa7247824022285da784">More...</a><br /></td></tr>
<tr class="separator:a210a7b616c27aa7247824022285da784"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b2413569ef95c163d3e4b8efed6891b"><td class="memItemLeft" align="right" valign="top"><a id="a6b2413569ef95c163d3e4b8efed6891b"></a>
static char *&#160;</td><td class="memItemRight" valign="bottom"><b>bufptr</b></td></tr>
<tr class="separator:a6b2413569ef95c163d3e4b8efed6891b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>functions to deal with reading and writing of MQTT packets from and to sockets </p>
<p>Some other related functions are in the MQTTPacketOut module </p>
</div><h2 class="groupheader">Function Documentation</h2>
<a id="a93c155059c80bd01b4a1561d9bec1d13"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a93c155059c80bd01b4a1561d9bec1d13">&#9670;&nbsp;</a></span>MQTTPacket_ack()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* MQTTPacket_ack </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned char&#160;</td>
          <td class="paramname"><em>aHeader</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>datalen</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Function used in the new packets table to create acknowledgement packets. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT being used </td></tr>
    <tr><td class="paramname">aHeader</td><td>the MQTT header byte </td></tr>
    <tr><td class="paramname">data</td><td>the rest of the packet </td></tr>
    <tr><td class="paramname">datalen</td><td>the length of the rest of the packet </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>pointer to the packet structure </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a93c155059c80bd01b4a1561d9bec1d13_cgraph.png" border="0" usemap="#MQTTPacket_8c_a93c155059c80bd01b4a1561d9bec1d13_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a93c155059c80bd01b4a1561d9bec1d13_cgraph" id="MQTTPacket_8c_a93c155059c80bd01b4a1561d9bec1d13_cgraph">
<area shape="rect" title="Function used in the new packets table to create acknowledgement packets." alt="" coords="5,31,133,57"/>
<area shape="rect" href="MQTTPacket_8c.html#aff1d10b221f5b4ce421b4c2588cbe511" title="Reads one character from the input buffer." alt="" coords="181,5,256,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="187,56,250,83"/>
</map>
</div>

</div>
</div>
<a id="ac1f61e571640513a9785b3164141dfbc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac1f61e571640513a9785b3164141dfbc">&#9670;&nbsp;</a></span>MQTTPacket_decode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_decode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t *&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Decodes the message length according to the MQTT algorithm. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">socket</td><td>the socket from which to read the bytes </td></tr>
    <tr><td class="paramname">value</td><td>the decoded length returned </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the number of bytes read from the socket </dd></dl>

</div>
</div>
<a id="aadd77a4fa1b2d5c7791e3542c56af856"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aadd77a4fa1b2d5c7791e3542c56af856">&#9670;&nbsp;</a></span>MQTTPacket_encode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_encode </td>
          <td>(</td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>buf</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>length</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Encodes the message length according to the MQTT algorithm. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">buf</td><td>the buffer into which the encoded data is written </td></tr>
    <tr><td class="paramname">length</td><td>the length to be encoded </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the number of bytes written to buffer </dd></dl>

</div>
</div>
<a id="af922033c495b05fe4242afc54211f20d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af922033c495b05fe4242afc54211f20d">&#9670;&nbsp;</a></span>MQTTPacket_Factory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* MQTTPacket_Factory </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>error</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Reads one MQTT packet from a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">socket</td><td>a socket from which to read an MQTT packet </td></tr>
    <tr><td class="paramname">error</td><td>pointer to the error code which is completed if no packet is returned </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the packet structure or NULL if there was an error </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_af922033c495b05fe4242afc54211f20d_cgraph.png" border="0" usemap="#MQTTPacket_8c_af922033c495b05fe4242afc54211f20d_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_af922033c495b05fe4242afc54211f20d_cgraph" id="MQTTPacket_8c_af922033c495b05fe4242afc54211f20d_cgraph">
<area shape="rect" title="Reads one MQTT packet from a socket." alt="" coords="5,81,156,108"/>
<area shape="rect" href="Log_8c.html#a669722e3f57811871f97c12392aba85d" title="Log a message." alt="" coords="279,5,323,32"/>
<area shape="rect" href="MQTTPacket_8c.html#ac1f61e571640513a9785b3164141dfbc" title="Decodes the message length according to the MQTT algorithm." alt="" coords="227,56,376,83"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="227,107,376,133"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="204,157,399,184"/>
</map>
</div>

</div>
</div>
<a id="a28f1754fe1f090d54bc3b5687fb2dd8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28f1754fe1f090d54bc3b5687fb2dd8c">&#9670;&nbsp;</a></span>MQTTPacket_free_packet()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTPacket_free_packet </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structMQTTPacket.html">MQTTPacket</a> *&#160;</td>
          <td class="paramname"><em>pack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free allocated storage for a various packet tyoes. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pack</td><td>pointer to the suback packet structure </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a28f1754fe1f090d54bc3b5687fb2dd8c_cgraph.png" border="0" usemap="#MQTTPacket_8c_a28f1754fe1f090d54bc3b5687fb2dd8c_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a28f1754fe1f090d54bc3b5687fb2dd8c_cgraph" id="MQTTPacket_8c_a28f1754fe1f090d54bc3b5687fb2dd8c_cgraph">
<area shape="rect" title="Free allocated storage for a various packet tyoes." alt="" coords="5,5,180,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a642e5f62d21f14e81f8fc70b491d89c8" title="Free allocated storage for a publish packet." alt="" coords="228,5,400,32"/>
</map>
</div>

</div>
</div>
<a id="a0ef92fb0207ed6272de9ebc749745d0c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0ef92fb0207ed6272de9ebc749745d0c">&#9670;&nbsp;</a></span>MQTTPacket_freeAck()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTPacket_freeAck </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structAck.html">Ack</a> *&#160;</td>
          <td class="paramname"><em>pack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free allocated storage for an ack packet. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pack</td><td>pointer to the publish packet structure </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a642e5f62d21f14e81f8fc70b491d89c8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a642e5f62d21f14e81f8fc70b491d89c8">&#9670;&nbsp;</a></span>MQTTPacket_freePublish()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTPacket_freePublish </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structPublish.html">Publish</a> *&#160;</td>
          <td class="paramname"><em>pack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free allocated storage for a publish packet. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pack</td><td>pointer to the publish packet structure </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a4262b8f35885dc123f59268fb74cec3d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4262b8f35885dc123f59268fb74cec3d">&#9670;&nbsp;</a></span>MQTTPacket_freeSuback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTPacket_freeSuback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structSuback.html">Suback</a> *&#160;</td>
          <td class="paramname"><em>pack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free allocated storage for a suback packet. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pack</td><td>pointer to the suback packet structure </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a4262b8f35885dc123f59268fb74cec3d_cgraph.png" border="0" usemap="#MQTTPacket_8c_a4262b8f35885dc123f59268fb74cec3d_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a4262b8f35885dc123f59268fb74cec3d_cgraph" id="MQTTPacket_8c_a4262b8f35885dc123f59268fb74cec3d_cgraph">
<area shape="rect" title="Free allocated storage for a suback packet." alt="" coords="5,5,179,32"/>
<area shape="rect" href="LinkedList_8c.html#a276950531b3f54e0dda27d597572c192" title="Removes and frees all items in a list, and frees the list itself." alt="" coords="227,5,297,32"/>
<area shape="rect" href="LinkedList_8c.html#a3cf8578dba23f97dd0411e85fe3b7ff2" title="Removes and frees all items in a list, leaving the list ready for new items." alt="" coords="345,5,425,32"/>
</map>
</div>

</div>
</div>
<a id="a9dd0446112e7bc982f8e3bb8bbb6b409"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9dd0446112e7bc982f8e3bb8bbb6b409">&#9670;&nbsp;</a></span>MQTTPacket_freeUnsuback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void MQTTPacket_freeUnsuback </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structUnsuback.html">Unsuback</a> *&#160;</td>
          <td class="paramname"><em>pack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Free allocated storage for a suback packet. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pack</td><td>pointer to the suback packet structure </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a9dd0446112e7bc982f8e3bb8bbb6b409_cgraph.png" border="0" usemap="#MQTTPacket_8c_a9dd0446112e7bc982f8e3bb8bbb6b409_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a9dd0446112e7bc982f8e3bb8bbb6b409_cgraph" id="MQTTPacket_8c_a9dd0446112e7bc982f8e3bb8bbb6b409_cgraph">
<area shape="rect" title="Free allocated storage for a suback packet." alt="" coords="5,5,193,32"/>
<area shape="rect" href="LinkedList_8c.html#a276950531b3f54e0dda27d597572c192" title="Removes and frees all items in a list, and frees the list itself." alt="" coords="241,5,312,32"/>
<area shape="rect" href="LinkedList_8c.html#a3cf8578dba23f97dd0411e85fe3b7ff2" title="Removes and frees all items in a list, leaving the list ready for new items." alt="" coords="360,5,440,32"/>
</map>
</div>

</div>
</div>
<a id="af3ddd9c1a35d51bf70f44a1aa6fa0bba"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af3ddd9c1a35d51bf70f44a1aa6fa0bba">&#9670;&nbsp;</a></span>MQTTPacket_header_only()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* MQTTPacket_header_only </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned char&#160;</td>
          <td class="paramname"><em>aHeader</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>datalen</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Function used in the new packets table to create packets which have only a header. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT </td></tr>
    <tr><td class="paramname">aHeader</td><td>the MQTT header byte </td></tr>
    <tr><td class="paramname">data</td><td>the rest of the packet </td></tr>
    <tr><td class="paramname">datalen</td><td>the length of the rest of the packet </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>pointer to the packet structure </dd></dl>

</div>
</div>
<a id="a9f9e58aba3ed5d513862d76bc5e29743"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f9e58aba3ed5d513862d76bc5e29743">&#9670;&nbsp;</a></span>MQTTPacket_name()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* MQTTPacket_name </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ptype</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts an MQTT packet code into its name. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">ptype</td><td>packet code </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the corresponding string, or "UNKNOWN" </dd></dl>

</div>
</div>
<a id="a58feb89ee5616f2ea6d222bbbef927bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a58feb89ee5616f2ea6d222bbbef927bb">&#9670;&nbsp;</a></span>MQTTPacket_publish()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* MQTTPacket_publish </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned char&#160;</td>
          <td class="paramname"><em>aHeader</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>datalen</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Function used in the new packets table to create publish packets. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td></td></tr>
    <tr><td class="paramname">aHeader</td><td>the MQTT header byte </td></tr>
    <tr><td class="paramname">data</td><td>the rest of the packet </td></tr>
    <tr><td class="paramname">datalen</td><td>the length of the rest of the packet </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>pointer to the packet structure </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a58feb89ee5616f2ea6d222bbbef927bb_cgraph.png" border="0" usemap="#MQTTPacket_8c_a58feb89ee5616f2ea6d222bbbef927bb_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a58feb89ee5616f2ea6d222bbbef927bb_cgraph" id="MQTTPacket_8c_a58feb89ee5616f2ea6d222bbbef927bb_cgraph">
<area shape="rect" title="Function used in the new packets table to create publish packets." alt="" coords="5,5,153,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="340,5,403,32"/>
<area shape="rect" href="MQTTPacket_8c.html#ae1ec2d8714335c6ec88c93e957b644d2" title="Reads a &quot;UTF&quot; string from the input buffer." alt="" coords="201,31,292,57"/>
</map>
</div>

</div>
</div>
<a id="a51f58f1c7864f9fe87b55cc6ccb10129"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51f58f1c7864f9fe87b55cc6ccb10129">&#9670;&nbsp;</a></span>MQTTPacket_send()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_send </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionHeader.html">Header</a>&#160;</td>
          <td class="paramname"><em>header</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>buffer</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>buflen</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>freeData</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Sends an MQTT packet in one system call write. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">socket</td><td>the socket to which to write the data </td></tr>
    <tr><td class="paramname">header</td><td>the one-byte MQTT header </td></tr>
    <tr><td class="paramname">buffer</td><td>the rest of the buffer to write (not including remaining length) </td></tr>
    <tr><td class="paramname">buflen</td><td>the length of the data in buffer to be written </td></tr>
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT being used </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (TCPSOCKET_COMPLETE etc) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a51f58f1c7864f9fe87b55cc6ccb10129_cgraph.png" border="0" usemap="#MQTTPacket_8c_a51f58f1c7864f9fe87b55cc6ccb10129_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a51f58f1c7864f9fe87b55cc6ccb10129_cgraph" id="MQTTPacket_8c_a51f58f1c7864f9fe87b55cc6ccb10129_cgraph">
<area shape="rect" title="Sends an MQTT packet in one system call write." alt="" coords="5,56,140,83"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="211,5,360,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="188,56,383,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="254,107,317,133"/>
</map>
</div>

</div>
</div>
<a id="ad89e627a37f7f7eb4355b076cd46e0b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad89e627a37f7f7eb4355b076cd46e0b0">&#9670;&nbsp;</a></span>MQTTPacket_send_ack()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static int MQTTPacket_send_ack </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>type</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>msgid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Send an MQTT acknowledgement packet down a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT being used </td></tr>
    <tr><td class="paramname">type</td><td>the MQTT packet type e.g. SUBACK </td></tr>
    <tr><td class="paramname">msgid</td><td>the MQTT message id to use </td></tr>
    <tr><td class="paramname">dup</td><td>boolean - whether to set the MQTT DUP flag </td></tr>
    <tr><td class="paramname">net</td><td>the network handle to send the data to </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (e.g. TCPSOCKET_COMPLETE) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_ad89e627a37f7f7eb4355b076cd46e0b0_cgraph.png" border="0" usemap="#MQTTPacket_8c_ad89e627a37f7f7eb4355b076cd46e0b0_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_ad89e627a37f7f7eb4355b076cd46e0b0_cgraph" id="MQTTPacket_8c_ad89e627a37f7f7eb4355b076cd46e0b0_cgraph">
<area shape="rect" title="Send an MQTT acknowledgement packet down a socket." alt="" coords="5,81,168,108"/>
<area shape="rect" href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129" title="Sends an MQTT packet in one system call write." alt="" coords="216,56,351,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="251,107,316,133"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="421,5,571,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="399,56,593,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="465,107,527,133"/>
</map>
</div>

</div>
</div>
<a id="aa9e17261b4d09d802dba6a65bcdc44fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa9e17261b4d09d802dba6a65bcdc44fa">&#9670;&nbsp;</a></span>MQTTPacket_send_disconnect()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_send_disconnect </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structClients.html">Clients</a> *&#160;</td>
          <td class="paramname"><em>client</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">enum MQTTReasonCodes&#160;</td>
          <td class="paramname"><em>reason</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structMQTTProperties.html">MQTTProperties</a> *&#160;</td>
          <td class="paramname"><em>props</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send an MQTT disconnect packet down a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">socket</td><td>the open socket to send the data to </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (e.g. TCPSOCKET_COMPLETE) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_aa9e17261b4d09d802dba6a65bcdc44fa_cgraph.png" border="0" usemap="#MQTTPacket_8c_aa9e17261b4d09d802dba6a65bcdc44fa_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_aa9e17261b4d09d802dba6a65bcdc44fa_cgraph" id="MQTTPacket_8c_aa9e17261b4d09d802dba6a65bcdc44fa_cgraph">
<area shape="rect" title="Send an MQTT disconnect packet down a socket." alt="" coords="5,56,211,83"/>
<area shape="rect" href="Log_8c.html#a669722e3f57811871f97c12392aba85d" title="Log a message." alt="" coords="304,5,348,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129" title="Sends an MQTT packet in one system call write." alt="" coords="259,56,393,83"/>
<area shape="rect" href="MQTTPacket_8c.html#ad29ec8b2fbf0ec0195621b44f8945923" title="Writes one character to an output buffer." alt="" coords="287,107,365,133"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="464,5,613,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="441,56,636,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="507,107,570,133"/>
</map>
</div>

</div>
</div>
<a id="a8b93325a6a336406497b20b489d6c46a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b93325a6a336406497b20b489d6c46a">&#9670;&nbsp;</a></span>MQTTPacket_send_puback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_send_puback </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>msgid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>clientID</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send an MQTT PUBACK packet down a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT being used </td></tr>
    <tr><td class="paramname">msgid</td><td>the MQTT message id to use </td></tr>
    <tr><td class="paramname">socket</td><td>the open socket to send the data to </td></tr>
    <tr><td class="paramname">clientID</td><td>the string client identifier, only used for tracing </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (e.g. TCPSOCKET_COMPLETE) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a8b93325a6a336406497b20b489d6c46a_cgraph.png" border="0" usemap="#MQTTPacket_8c_a8b93325a6a336406497b20b489d6c46a_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a8b93325a6a336406497b20b489d6c46a_cgraph" id="MQTTPacket_8c_a8b93325a6a336406497b20b489d6c46a_cgraph">
<area shape="rect" title="Send an MQTT PUBACK packet down a socket." alt="" coords="5,56,189,83"/>
<area shape="rect" href="Log_8c.html#a669722e3f57811871f97c12392aba85d" title="Log a message." alt="" coords="297,31,341,57"/>
<area shape="rect" href="MQTTPacket_8c.html#ad89e627a37f7f7eb4355b076cd46e0b0" title="Send an MQTT acknowledgement packet down a socket." alt="" coords="237,81,400,108"/>
<area shape="rect" href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129" title="Sends an MQTT packet in one system call write." alt="" coords="448,56,583,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="483,107,548,133"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="653,5,803,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="631,56,825,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="697,107,759,133"/>
</map>
</div>

</div>
</div>
<a id="a908f81381b1720e4a53d15c1c4690f5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a908f81381b1720e4a53d15c1c4690f5c">&#9670;&nbsp;</a></span>MQTTPacket_send_pubcomp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_send_pubcomp </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>msgid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>clientID</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send an MQTT PUBCOMP packet down a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT being used </td></tr>
    <tr><td class="paramname">msgid</td><td>the MQTT message id to use </td></tr>
    <tr><td class="paramname">socket</td><td>the open socket to send the data to </td></tr>
    <tr><td class="paramname">clientID</td><td>the string client identifier, only used for tracing </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (e.g. TCPSOCKET_COMPLETE) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a908f81381b1720e4a53d15c1c4690f5c_cgraph.png" border="0" usemap="#MQTTPacket_8c_a908f81381b1720e4a53d15c1c4690f5c_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a908f81381b1720e4a53d15c1c4690f5c_cgraph" id="MQTTPacket_8c_a908f81381b1720e4a53d15c1c4690f5c_cgraph">
<area shape="rect" title="Send an MQTT PUBCOMP packet down a socket." alt="" coords="5,56,200,83"/>
<area shape="rect" href="Log_8c.html#a669722e3f57811871f97c12392aba85d" title="Log a message." alt="" coords="307,31,351,57"/>
<area shape="rect" href="MQTTPacket_8c.html#ad89e627a37f7f7eb4355b076cd46e0b0" title="Send an MQTT acknowledgement packet down a socket." alt="" coords="248,81,411,108"/>
<area shape="rect" href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129" title="Sends an MQTT packet in one system call write." alt="" coords="459,56,593,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="493,107,559,133"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="664,5,813,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="641,56,836,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="707,107,770,133"/>
</map>
</div>

</div>
</div>
<a id="a9ebcf93ff2ba3bd103f016c975e9c9c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9ebcf93ff2ba3bd103f016c975e9c9c4">&#9670;&nbsp;</a></span>MQTTPacket_send_publish()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_send_publish </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structPublish.html">Publish</a> *&#160;</td>
          <td class="paramname"><em>pack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>qos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>retained</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>clientID</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send an MQTT PUBLISH packet down a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pack</td><td>a structure from which to get some values to use, e.g topic, payload </td></tr>
    <tr><td class="paramname">dup</td><td>boolean - whether to set the MQTT DUP flag </td></tr>
    <tr><td class="paramname">qos</td><td>the value to use for the MQTT QoS setting </td></tr>
    <tr><td class="paramname">retained</td><td>boolean - whether to set the MQTT retained flag </td></tr>
    <tr><td class="paramname">socket</td><td>the open socket to send the data to </td></tr>
    <tr><td class="paramname">clientID</td><td>the string client identifier, only used for tracing </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (e.g. TCPSOCKET_COMPLETE) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a9ebcf93ff2ba3bd103f016c975e9c9c4_cgraph.png" border="0" usemap="#MQTTPacket_8c_a9ebcf93ff2ba3bd103f016c975e9c9c4_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a9ebcf93ff2ba3bd103f016c975e9c9c4_cgraph" id="MQTTPacket_8c_a9ebcf93ff2ba3bd103f016c975e9c9c4_cgraph">
<area shape="rect" title="Send an MQTT PUBLISH packet down a socket." alt="" coords="5,56,188,83"/>
<area shape="rect" href="Log_8c.html#a669722e3f57811871f97c12392aba85d" title="Log a message." alt="" coords="285,5,329,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a3635fd8035177b20c478daea6bad9328" title="Sends an MQTT packet from multiple buffers in one system call write." alt="" coords="236,56,379,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="275,107,340,133"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="449,5,599,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="427,56,621,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="493,107,555,133"/>
</map>
</div>

</div>
</div>
<a id="a8167e3188a4bdcfb30c10e0d76c82afe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8167e3188a4bdcfb30c10e0d76c82afe">&#9670;&nbsp;</a></span>MQTTPacket_send_pubrec()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_send_pubrec </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>msgid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>clientID</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send an MQTT PUBREC packet down a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT being used </td></tr>
    <tr><td class="paramname">msgid</td><td>the MQTT message id to use </td></tr>
    <tr><td class="paramname">socket</td><td>the open socket to send the data to </td></tr>
    <tr><td class="paramname">clientID</td><td>the string client identifier, only used for tracing </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (e.g. TCPSOCKET_COMPLETE) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a8167e3188a4bdcfb30c10e0d76c82afe_cgraph.png" border="0" usemap="#MQTTPacket_8c_a8167e3188a4bdcfb30c10e0d76c82afe_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a8167e3188a4bdcfb30c10e0d76c82afe_cgraph" id="MQTTPacket_8c_a8167e3188a4bdcfb30c10e0d76c82afe_cgraph">
<area shape="rect" title="Send an MQTT PUBREC packet down a socket." alt="" coords="5,56,187,83"/>
<area shape="rect" href="Log_8c.html#a669722e3f57811871f97c12392aba85d" title="Log a message." alt="" coords="294,31,338,57"/>
<area shape="rect" href="MQTTPacket_8c.html#ad89e627a37f7f7eb4355b076cd46e0b0" title="Send an MQTT acknowledgement packet down a socket." alt="" coords="235,81,397,108"/>
<area shape="rect" href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129" title="Sends an MQTT packet in one system call write." alt="" coords="445,56,580,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="480,107,545,133"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="651,5,800,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="628,56,823,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="694,107,757,133"/>
</map>
</div>

</div>
</div>
<a id="adec2c812b12255dab78f75163a4f1960"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adec2c812b12255dab78f75163a4f1960">&#9670;&nbsp;</a></span>MQTTPacket_send_pubrel()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_send_pubrel </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>msgid</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>dup</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>clientID</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Send an MQTT PUBREL packet down a socket. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">MQTTVersion</td><td>the version of MQTT being used </td></tr>
    <tr><td class="paramname">msgid</td><td>the MQTT message id to use </td></tr>
    <tr><td class="paramname">dup</td><td>boolean - whether to set the MQTT DUP flag </td></tr>
    <tr><td class="paramname">socket</td><td>the open socket to send the data to </td></tr>
    <tr><td class="paramname">clientID</td><td>the string client identifier, only used for tracing </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (e.g. TCPSOCKET_COMPLETE) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_adec2c812b12255dab78f75163a4f1960_cgraph.png" border="0" usemap="#MQTTPacket_8c_adec2c812b12255dab78f75163a4f1960_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_adec2c812b12255dab78f75163a4f1960_cgraph" id="MQTTPacket_8c_adec2c812b12255dab78f75163a4f1960_cgraph">
<area shape="rect" title="Send an MQTT PUBREL packet down a socket." alt="" coords="5,56,183,83"/>
<area shape="rect" href="Log_8c.html#a669722e3f57811871f97c12392aba85d" title="Log a message." alt="" coords="290,31,334,57"/>
<area shape="rect" href="MQTTPacket_8c.html#ad89e627a37f7f7eb4355b076cd46e0b0" title="Send an MQTT acknowledgement packet down a socket." alt="" coords="231,81,393,108"/>
<area shape="rect" href="MQTTPacket_8c.html#a51f58f1c7864f9fe87b55cc6ccb10129" title="Sends an MQTT packet in one system call write." alt="" coords="441,56,576,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="476,107,541,133"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="647,5,796,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="624,56,819,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="690,107,753,133"/>
</map>
</div>

</div>
</div>
<a id="a3635fd8035177b20c478daea6bad9328"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3635fd8035177b20c478daea6bad9328">&#9670;&nbsp;</a></span>MQTTPacket_sends()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_sends </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnetworkHandles.html">networkHandles</a> *&#160;</td>
          <td class="paramname"><em>net</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="unionHeader.html">Header</a>&#160;</td>
          <td class="paramname"><em>header</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structPacketBuffers.html">PacketBuffers</a> *&#160;</td>
          <td class="paramname"><em>bufs</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>MQTTVersion</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Sends an MQTT packet from multiple buffers in one system call write. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">socket</td><td>the socket to which to write the data </td></tr>
    <tr><td class="paramname">header</td><td>the one-byte MQTT header </td></tr>
    <tr><td class="paramname">count</td><td>the number of buffers </td></tr>
    <tr><td class="paramname">buffers</td><td>the rest of the buffers to write (not including remaining length) </td></tr>
    <tr><td class="paramname">buflens</td><td>the lengths of the data in the array of buffers to be written </td></tr>
    <tr><td class="paramname">the</td><td>MQTT version being used </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the completion code (TCPSOCKET_COMPLETE etc) </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a3635fd8035177b20c478daea6bad9328_cgraph.png" border="0" usemap="#MQTTPacket_8c_a3635fd8035177b20c478daea6bad9328_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a3635fd8035177b20c478daea6bad9328_cgraph" id="MQTTPacket_8c_a3635fd8035177b20c478daea6bad9328_cgraph">
<area shape="rect" title="Sends an MQTT packet from multiple buffers in one system call write." alt="" coords="5,56,148,83"/>
<area shape="rect" href="MQTTPacket_8c.html#aadd77a4fa1b2d5c7791e3542c56af856" title="Encodes the message length according to the MQTT algorithm." alt="" coords="219,5,368,32"/>
<area shape="rect" href="MQTTPersistence_8c.html#a18eab666c3e6aecd92a6c8f10df808e3" title="Adds a record to the persistent store." alt="" coords="196,56,391,83"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="262,107,325,133"/>
</map>
</div>

</div>
</div>
<a id="a4fc1ee4d2cf8fd9bd59d89aadab222df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4fc1ee4d2cf8fd9bd59d89aadab222df">&#9670;&nbsp;</a></span>MQTTPacket_VBIdecode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int MQTTPacket_VBIdecode </td>
          <td>(</td>
          <td class="paramtype">int(*)(char *, int)&#160;</td>
          <td class="paramname"><em>getcharfn</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned int *&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Decodes the message length according to the MQTT algorithm. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">getcharfn</td><td>pointer to function to read the next character from the data source </td></tr>
    <tr><td class="paramname">value</td><td>the decoded length returned </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the number of bytes read from the socket </dd></dl>

</div>
</div>
<a id="aff1d10b221f5b4ce421b4c2588cbe511"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff1d10b221f5b4ce421b4c2588cbe511">&#9670;&nbsp;</a></span>readChar()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned char readChar </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Reads one character from the input buffer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the input buffer - incremented by the number of bytes used &amp; returned </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the character read </dd></dl>

</div>
</div>
<a id="a132d2d5b304d37cd2348a973f7b315de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a132d2d5b304d37cd2348a973f7b315de">&#9670;&nbsp;</a></span>readInt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int readInt </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculates an integer from two bytes read from the input buffer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the input buffer - incremented by the number of bytes used &amp; returned </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the integer value calculated </dd></dl>

</div>
</div>
<a id="aa8fc559d3a1e58ab50e69146666f2f63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa8fc559d3a1e58ab50e69146666f2f63">&#9670;&nbsp;</a></span>readInt4()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int readInt4 </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Calculates an integer from two bytes read from the input buffer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the input buffer - incremented by the number of bytes used &amp; returned </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the integer value calculated </dd></dl>

</div>
</div>
<a id="adca3afbe588ae7e6f342c5a697e4ee45"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adca3afbe588ae7e6f342c5a697e4ee45">&#9670;&nbsp;</a></span>readUTF()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">char* readUTF </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>enddata</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Reads a "UTF" string from the input buffer. </p>
<p>UTF as in the MQTT v3 spec which really means a length delimited string. So it reads the two byte length then the data according to that length. The end of the buffer is provided too, so we can prevent buffer overruns caused by an incorrect length. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the input buffer - incremented by the number of bytes used &amp; returned </td></tr>
    <tr><td class="paramname">enddata</td><td>pointer to the end of the buffer not to be read beyond </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>an allocated C string holding the characters read, or NULL if the length read would have caused an overrun. </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_adca3afbe588ae7e6f342c5a697e4ee45_cgraph.png" border="0" usemap="#MQTTPacket_8c_adca3afbe588ae7e6f342c5a697e4ee45_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_adca3afbe588ae7e6f342c5a697e4ee45_cgraph" id="MQTTPacket_8c_adca3afbe588ae7e6f342c5a697e4ee45_cgraph">
<area shape="rect" title="Reads a &quot;UTF&quot; string from the input buffer." alt="" coords="5,5,79,32"/>
<area shape="rect" href="MQTTPacket_8c.html#ae1ec2d8714335c6ec88c93e957b644d2" title="Reads a &quot;UTF&quot; string from the input buffer." alt="" coords="127,5,217,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="265,5,328,32"/>
</map>
</div>

</div>
</div>
<a id="ae1ec2d8714335c6ec88c93e957b644d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae1ec2d8714335c6ec88c93e957b644d2">&#9670;&nbsp;</a></span>readUTFlen()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static char * readUTFlen </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char *&#160;</td>
          <td class="paramname"><em>enddata</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int *&#160;</td>
          <td class="paramname"><em>len</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Reads a "UTF" string from the input buffer. </p>
<p>UTF as in the MQTT v3 spec which really means a length delimited string. So it reads the two byte length then the data according to that length. The end of the buffer is provided too, so we can prevent buffer overruns caused by an incorrect length. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the input buffer - incremented by the number of bytes used &amp; returned </td></tr>
    <tr><td class="paramname">enddata</td><td>pointer to the end of the buffer not to be read beyond </td></tr>
    <tr><td class="paramname">len</td><td>returns the calculcated value of the length bytes read </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>an allocated C string holding the characters read, or NULL if the length read would have caused an overrun. </dd></dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_ae1ec2d8714335c6ec88c93e957b644d2_cgraph.png" border="0" usemap="#MQTTPacket_8c_ae1ec2d8714335c6ec88c93e957b644d2_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_ae1ec2d8714335c6ec88c93e957b644d2_cgraph" id="MQTTPacket_8c_ae1ec2d8714335c6ec88c93e957b644d2_cgraph">
<area shape="rect" title="Reads a &quot;UTF&quot; string from the input buffer." alt="" coords="5,5,96,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a132d2d5b304d37cd2348a973f7b315de" title="Calculates an integer from two bytes read from the input buffer." alt="" coords="144,5,207,32"/>
</map>
</div>

</div>
</div>
<a id="ad29ec8b2fbf0ec0195621b44f8945923"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad29ec8b2fbf0ec0195621b44f8945923">&#9670;&nbsp;</a></span>writeChar()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void writeChar </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">char&#160;</td>
          <td class="paramname"><em>c</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Writes one character to an output buffer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the output buffer - incremented by the number of bytes used &amp; returned </td></tr>
    <tr><td class="paramname">c</td><td>the character to write </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a8886398fbf89872f8e593444d351a5aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8886398fbf89872f8e593444d351a5aa">&#9670;&nbsp;</a></span>writeData()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void writeData </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const void *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>datalen</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Writes length delimited data to an output buffer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the output buffer - incremented by the number of bytes used &amp; returned </td></tr>
    <tr><td class="paramname">data</td><td>the data to write </td></tr>
    <tr><td class="paramname">datalen</td><td>the length of the data to write </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_a8886398fbf89872f8e593444d351a5aa_cgraph.png" border="0" usemap="#MQTTPacket_8c_a8886398fbf89872f8e593444d351a5aa_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_a8886398fbf89872f8e593444d351a5aa_cgraph" id="MQTTPacket_8c_a8886398fbf89872f8e593444d351a5aa_cgraph">
<area shape="rect" title="Writes length delimited data to an output buffer." alt="" coords="5,5,83,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="131,5,196,32"/>
</map>
</div>

</div>
</div>
<a id="a07aa0146eda3d32979142e7df8ad5fc3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a07aa0146eda3d32979142e7df8ad5fc3">&#9670;&nbsp;</a></span>writeInt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void writeInt </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>anInt</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Writes an integer as 2 bytes to an output buffer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the output buffer - incremented by the number of bytes used &amp; returned </td></tr>
    <tr><td class="paramname">anInt</td><td>the integer to write </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aefc0aa52c1cb13fa7bfcd77810d6a617"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefc0aa52c1cb13fa7bfcd77810d6a617">&#9670;&nbsp;</a></span>writeInt4()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void writeInt4 </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>anInt</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Writes an integer as 4 bytes to an output buffer. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the output buffer - incremented by the number of bytes used &amp; returned </td></tr>
    <tr><td class="paramname">anInt</td><td>the integer to write </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="af0fcaa11ac05ce448a433a53f9cae420"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af0fcaa11ac05ce448a433a53f9cae420">&#9670;&nbsp;</a></span>writeUTF()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void writeUTF </td>
          <td>(</td>
          <td class="paramtype">char **&#160;</td>
          <td class="paramname"><em>pptr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>string</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Writes a "UTF" string to an output buffer. </p>
<p>Converts C string to length-delimited. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">pptr</td><td>pointer to the output buffer - incremented by the number of bytes used &amp; returned </td></tr>
    <tr><td class="paramname">string</td><td>the C string to write </td></tr>
  </table>
  </dd>
</dl>
<div class="dynheader">
Here is the call graph for this function:</div>
<div class="dyncontent">
<div class="center"><img src="MQTTPacket_8c_af0fcaa11ac05ce448a433a53f9cae420_cgraph.png" border="0" usemap="#MQTTPacket_8c_af0fcaa11ac05ce448a433a53f9cae420_cgraph" alt=""/></div>
<map name="MQTTPacket_8c_af0fcaa11ac05ce448a433a53f9cae420_cgraph" id="MQTTPacket_8c_af0fcaa11ac05ce448a433a53f9cae420_cgraph">
<area shape="rect" title="Writes a &quot;UTF&quot; string to an output buffer." alt="" coords="5,5,81,32"/>
<area shape="rect" href="MQTTPacket_8c.html#a07aa0146eda3d32979142e7df8ad5fc3" title="Writes an integer as 2 bytes to an output buffer." alt="" coords="129,5,195,32"/>
</map>
</div>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a id="a210a7b616c27aa7247824022285da784"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a210a7b616c27aa7247824022285da784">&#9670;&nbsp;</a></span>new_packets</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">pf new_packets[]</td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">{</div>
<div class="line">        NULL,   </div>
<div class="line">        NULL,   </div>
<div class="line">        <a class="code" href="MQTTPacketOut_8c.html#ad05dd32a547e82e0741d9d8279568a65">MQTTPacket_connack</a>, </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#a58feb89ee5616f2ea6d222bbbef927bb">MQTTPacket_publish</a>,     </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a>, </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a>, </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a>, </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a>, </div>
<div class="line">        NULL, </div>
<div class="line">        <a class="code" href="MQTTPacketOut_8c.html#aee4b3e106128629671828ae7bfa70850">MQTTPacket_suback</a>, </div>
<div class="line">        NULL, </div>
<div class="line">        <a class="code" href="MQTTPacketOut_8c.html#a93f6aa4b23d30f6c8c5be87b0b58e37c">MQTTPacket_unsuback</a>, </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#af3ddd9c1a35d51bf70f44a1aa6fa0bba">MQTTPacket_header_only</a>, </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#af3ddd9c1a35d51bf70f44a1aa6fa0bba">MQTTPacket_header_only</a>, </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a>,  </div>
<div class="line">        <a class="code" href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a>   </div>
<div class="line">}</div>
</div><!-- fragment -->
<p>Array of functions to build packets, indexed according to packet code. </p>

</div>
</div>
<a id="a4536b1c36ed06171f20baeb69c01aa02"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4536b1c36ed06171f20baeb69c01aa02">&#9670;&nbsp;</a></span>packet_names</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* packet_names[]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">{</div>
<div class="line">        <span class="stringliteral">&quot;RESERVED&quot;</span>, <span class="stringliteral">&quot;CONNECT&quot;</span>, <span class="stringliteral">&quot;CONNACK&quot;</span>, <span class="stringliteral">&quot;PUBLISH&quot;</span>, <span class="stringliteral">&quot;PUBACK&quot;</span>, <span class="stringliteral">&quot;PUBREC&quot;</span>, <span class="stringliteral">&quot;PUBREL&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;PUBCOMP&quot;</span>, <span class="stringliteral">&quot;SUBSCRIBE&quot;</span>, <span class="stringliteral">&quot;SUBACK&quot;</span>, <span class="stringliteral">&quot;UNSUBSCRIBE&quot;</span>, <span class="stringliteral">&quot;UNSUBACK&quot;</span>,</div>
<div class="line">        <span class="stringliteral">&quot;PINGREQ&quot;</span>, <span class="stringliteral">&quot;PINGRESP&quot;</span>, <span class="stringliteral">&quot;DISCONNECT&quot;</span>, <span class="stringliteral">&quot;AUTH&quot;</span></div>
<div class="line">}</div>
</div><!-- fragment -->
<p><a class="el" href="structList.html" title="Structure to hold all data for one list.">List</a> of the predefined MQTT v3/v5 packet names. </p>

</div>
</div>
</div><!-- contents -->
<div class="ttc" id="aMQTTPacketOut_8c_html_aee4b3e106128629671828ae7bfa70850"><div class="ttname"><a href="MQTTPacketOut_8c.html#aee4b3e106128629671828ae7bfa70850">MQTTPacket_suback</a></div><div class="ttdeci">void * MQTTPacket_suback(int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</div><div class="ttdoc">Function used in the new packets table to create suback packets.</div><div class="ttdef"><b>Definition:</b> MQTTPacketOut.c:290</div></div>
<div class="ttc" id="aMQTTPacket_8c_html_a93c155059c80bd01b4a1561d9bec1d13"><div class="ttname"><a href="MQTTPacket_8c.html#a93c155059c80bd01b4a1561d9bec1d13">MQTTPacket_ack</a></div><div class="ttdeci">void * MQTTPacket_ack(int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</div><div class="ttdoc">Function used in the new packets table to create acknowledgement packets.</div><div class="ttdef"><b>Definition:</b> MQTTPacket.c:794</div></div>
<div class="ttc" id="aMQTTPacketOut_8c_html_ad05dd32a547e82e0741d9d8279568a65"><div class="ttname"><a href="MQTTPacketOut_8c.html#ad05dd32a547e82e0741d9d8279568a65">MQTTPacket_connack</a></div><div class="ttdeci">void * MQTTPacket_connack(int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</div><div class="ttdoc">Function used in the new packets table to create connack packets.</div><div class="ttdef"><b>Definition:</b> MQTTPacketOut.c:142</div></div>
<div class="ttc" id="aMQTTPacketOut_8c_html_a93f6aa4b23d30f6c8c5be87b0b58e37c"><div class="ttname"><a href="MQTTPacketOut_8c.html#a93f6aa4b23d30f6c8c5be87b0b58e37c">MQTTPacket_unsuback</a></div><div class="ttdeci">void * MQTTPacket_unsuback(int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</div><div class="ttdoc">Function used in the new packets table to create unsuback packets.</div><div class="ttdef"><b>Definition:</b> MQTTPacketOut.c:411</div></div>
<div class="ttc" id="aMQTTPacket_8c_html_af3ddd9c1a35d51bf70f44a1aa6fa0bba"><div class="ttname"><a href="MQTTPacket_8c.html#af3ddd9c1a35d51bf70f44a1aa6fa0bba">MQTTPacket_header_only</a></div><div class="ttdeci">void * MQTTPacket_header_only(int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</div><div class="ttdoc">Function used in the new packets table to create packets which have only a header.</div><div class="ttdef"><b>Definition:</b> MQTTPacket.c:498</div></div>
<div class="ttc" id="aMQTTPacket_8c_html_a58feb89ee5616f2ea6d222bbbef927bb"><div class="ttname"><a href="MQTTPacket_8c.html#a58feb89ee5616f2ea6d222bbbef927bb">MQTTPacket_publish</a></div><div class="ttdeci">void * MQTTPacket_publish(int MQTTVersion, unsigned char aHeader, char *data, size_t datalen)</div><div class="ttdoc">Function used in the new packets table to create publish packets.</div><div class="ttdef"><b>Definition:</b> MQTTPacket.c:556</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:46 for MQTT C Client Libraries Internals by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
