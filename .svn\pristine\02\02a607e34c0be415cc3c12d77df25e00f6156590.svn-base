/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1994-2014 */
/*****************************************************************************/
/*                                                                           */
/*                                                                           */
/* This file is the property of:                                             */
/*                                                                           */
/*                       Triangle MicroWorks, Inc.                           */
/*                      Raleigh, North Carolina USA                          */
/*                      <www.TriangleMicroWorks.com>                         */
/*                    <<EMAIL>>                       */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*                                                                           */
/*****************************************************************************/


#pragma once

#include "Common/TMWBaseCPP/TMWBaseCPP.h"
#include "Common/TMWBaseCPP/ExpatImpl.h"

#include "Feature.h"

class HaspInfoXMLContext
{
public:
  HaspInfoXMLContext();
	virtual ~HaspInfoXMLContext();

  void SetXMLParer(XMLPARSE::XML_Parser parser) { m_parser = parser; }

  XMLPARSE::XML_Size GetCurrentLineNumber();
  XMLPARSE::XML_Size GetCurrentColumnNumber();

  Feature *CurrentFeature() { return m_features[m_features.size() - 1]; }
  
  tmw::Array<Feature*> *GetFeatures() { return &m_features; }

private:
  
  XMLPARSE::XML_Parser m_parser;
  tmw::Array<Feature*> m_features;
};

