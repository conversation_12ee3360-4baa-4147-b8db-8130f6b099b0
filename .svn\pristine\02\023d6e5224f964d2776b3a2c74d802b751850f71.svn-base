﻿import { Component, OnInit, Input, Output, EventEmitter} from "@angular/core";

@Component({
  selector: "bitmaskComponent",
  styles: [`
        .panel {
          margin-bottom: 2px; !important;
        }
 			  .chevron {
				  font-size: 16px;
				  margin-left: 6px;
				  margin-right: 4px;
			  }
        .input-bitmask {
          display: block;
          width: 104px;
          padding: 2px 12px;
          font-size: inherit;
          color: #555;
          background-color: #fff;
          background-image: none;
          border: 1px solid #ccc;
          border-radius: 4px;
        }
        .list-bitmask{
          padding:8px; 
          position:absolute; 
          background-color:#f8f8f8;
          border: 1px solid #ccc;
          z-index:99;
        }
`],
	template: `
			  <div class="panel" style="width: 134px;background-color: #f8f8f8;border: 1px solid #ccc;" (mouseenter)="selectOpenToggle(true)"  (mouseleave)="selectOpenToggle(false)">
				  <div>
					  <div style="display: inline-block; width:100px;">
						  <input type="text" uppercase class="input-bitmask" [(ngModel)]="bitmaskString" (change)="bitmaskStringOnChange($event)" />
					  </div>
					  <div style="display: inline-block; vertical-align:middle;">
						  <div *ngIf="isSelectOpen" class="glyphicon glyphicon-chevron-down chevron"></div>
						  <div *ngIf="!isSelectOpen" class="glyphicon glyphicon-chevron-up chevron"></div>
					  </div>
				  </div>
				  <div *ngIf="isSelectOpen" class="panel list-bitmask" style="">
					  <div *ngFor="let bitmask of this.bitmaskList">
						  <label title="{{bitmask.description | translate}}">
								  <input type="checkbox"
										  class="form-check"
										  [checked]=bitmask.isChecked
										  (change)="bitmaskListOnChange(bitmask)"/> {{bitmask.name | translate}}
						  </label>
					  </div>
				  </div>
			  </div>
		    `
})

export class BitmaskComponent implements OnInit {
  @Input() bitmaskString: string = "0";
  @Input() bitmaskLength: number = 4;
  @Input() bitmaskList: Bitmask[] = [];
  @Output() bitmaskOnChange: EventEmitter<string> = new EventEmitter();
  @Output() bitmaskOnInit: EventEmitter<Bitmask[]> = new EventEmitter();

	private isSelectOpen: boolean;
  private bitmask: number = 0x0;

  constructor() {}

  public ngOnInit(): void {
    this.bitmaskOnInit.emit(this.bitmaskList);

    if (this.bitmaskString!=null)
      this.bitmask = parseInt(this.bitmaskString, 16);
    else
      this.bitmask = 0;

    this.bitmaskString = this.decimalToHexConverter(this.bitmask);

    if (this.bitmask === 0 && this.bitmaskList[0].value === 0x0)
      this.bitmaskList[0].isChecked = true;
    else
		for (let i in this.bitmaskList)
			if (this.bitmaskList[i].value & this.bitmask)
				this.bitmaskList[i].isChecked = true;
	}

  private selectOpenToggle(isOpen: boolean): void {
    this.isSelectOpen = isOpen;
	}

  private bitmaskStringOnChange(event: any): void {
    try {
      if (this.bitmaskString != null && this.isHex(this.bitmaskString.toUpperCase())) {
        this.bitmask = parseInt(this.bitmaskString, 16);
      }
      else {
        this.bitmask = 0;
      }
    } catch (e) {
      this.bitmaskString = "0000";
    }
    if (this.bitmask === 0 && this.bitmaskList[0].value === 0x0) {
      this.bitmaskList[0].isChecked = true;
      for (let i in this.bitmaskList)
        if (i > "0")
          this.bitmaskList[i].isChecked = false;
    }
    else {
      for (let i in this.bitmaskList)
        if (this.bitmaskList[i].value & this.bitmask)
          this.bitmaskList[i].isChecked = true;
      this.bitmaskList[0].isChecked = false;
    }
    this.bitmaskOnChange.emit(this.bitmaskString);
  }

  private bitmaskListOnChange(bitmask: Bitmask): void {
    bitmask.isChecked = !bitmask.isChecked;

    if (bitmask.isChecked && bitmask.value === 0x0) {
      for (let i in this.bitmaskList)
        if (i > "0")
          this.bitmaskList[i].isChecked = false;
      this.bitmask = 0x0;
    }
    else if (this.bitmaskList[0].value === 0x0) {
      this.bitmaskList[0].isChecked = false;
    }
      
    let bitmaskTemp = this.bitmask;
    if (bitmask.isChecked)
	    bitmaskTemp = bitmaskTemp | bitmask.value;
    else
	    bitmaskTemp = bitmaskTemp & (~(bitmask.value));

    this.bitmask = bitmaskTemp;
    this.bitmaskString = this.decimalToHexConverter(this.bitmask); 
    this.bitmaskOnChange.emit(this.bitmaskString);
	}

  private decimalToHexConverter(inputNumber: number): string{
    let str = inputNumber.toString(16);
    return ("0".repeat(this.bitmaskLength - str.length) + str).toUpperCase();
  }

  private isHex(hexString: string): boolean {
    let a = parseInt(hexString, 16);
    let b = this.decimalToHexConverter(a);
    return (b === hexString)
  }
}

export interface Bitmask {
	name?: string;
	value?: number;
	description?: string;
	isChecked?: boolean
}