#
# This script runs the Test Harness that is connected to the SDG
# during the nightly SDG regression tests
# The script assumes the standard examples have already been loaded
# However, it redefines some of the procedures in the example scripts
#

namespace eval s1012m104 {
  variable NumMSP   10
  variable NumMDP    5
  variable NumMST    7
  variable NumMBO   14
  variable NumMMENA  6
  variable NumMMENB  4
  variable NumMMENC  3
  variable NumMIT   13

  global MSPErrors MSPSesnErrors MDPErrors MDPSesnErrors MSTErrors MSTSesnErrors MBOErrors MBOSesnErrors
  global MMENAErrors MMENASesnErrors MMENBErrors MMENBSesnErrors MMENCErrors MMENCSesnErrors
  global MITErrors MITSesnErrors
  
  namespace export setAnlg
  
  # proc: open101slave
  # purpose: Open an IEC 60870-5-101 slave session
  proc open101slave {} {
    global s101channel s101sesn s101sctr
    set s101channel [s101openchannel type tcp mode server portnum 2401 name S101]
    set s101sesn [s101opensession channel $s101channel]
    set s101sctr [s101opensector session $s101sesn]
  }

  # proc: close101slave
  # purpose: Close the IEC 60870-5-101 slave session opened above
  proc close101slave {} {
    global s101channel s101sesn s101sctr
    s101closesector sector $s101sctr
    s101closesession session $s101sesn
    s101closechannel channel $s101channel
  }

  # proc: open104master
  # purpose: Open an IEC 60870-5-104 master session
  # comments: If talking to an external device modify this routine
  #  to specify the required channel, session, and sector parameters.
  proc open104master {} {
    global m104channel m104sesn m104sctr
    set m104channel [m104openchannel type tcp mode client portnum 2404 name M104]
    set m104sesn [m104opensession channel $m104channel]
    set m104sctr [m104opensector session $m104sesn]
  }

  # proc: close104master
  # purpose: Close the IEC 60870-5-104 master session opened above
  proc close104master {} {
    global m104channel m104sesn m104sctr
    m104closesector sector $m104sctr
    m104closesession session $m104sesn
    m104closechannel channel $m104channel
  }


  #
  # proc: initS101MSP
  # purpose: Set initial values for binary points
  # This procedure sets every other binary input point
  # to on. This gives us a 50% chance of finding mapping
  # errors
  #
  proc initS101MSP {} {
    global s101sctr MSPErrors MSPSesnErrors
    variable NumMSP
  
    set MSPErrors 0
    set MSPSesnErrors 0
    
    for {set i 0} {$i < $NumMSP} {incr i} {
      s101msp set sector $s101sctr ioa [expr 100 + $i] value [expr $i % 2]
    }
  }
  
  #
  # proc: initS101MDP
  # purpose: Set initial values for binary points
  # This procedure sets initial values.
  #
  proc initS101MDP {} {
    global s101sctr MDPErrors MDPSesnErrors
    variable NumMDP
    
    set MDPErrors 0
    set MDPSesnErrors 0
  
    for {set i 0} {$i < $NumMDP} {incr i} {
      s101mdp set sector $s101sctr ioa [expr 200 + $i] value [expr $i % 4]
    }
  }
  
  #
  # proc: initS101MST
  # purpose: Set initial values for Step Position points
  #
  proc initS101MST {} {
    global s101sctr  MSTErrors MSTSesnErrors
    variable NumMST
  
    set MSTErrors 0
    set MSTSesnErrors 0
    
    for {set i 0} {$i < $NumMST} {incr i} {
      s101mst set sector $s101sctr ioa [expr 300 + $i] value $i
    }
  }
  
  #
  # proc: initS101MBO
  # purpose: Set initial values for bit strings
  #
  proc initS101MBO {} {
    global s101sctr MBOErrors MBOSesnErrors
    variable NumMBO
    
    set MBOErrors 0
    set MBOSesnErrors 0
    
    for {set i 0} {$i < $NumMBO} {incr i} {
      s101mbo set sector $s101sctr ioa [expr 400 + $i] value $i
    }
  }
  
  #
  # proc: initS101MMENA
  # purpose: Set initial values for normalized measurands 
  #
  proc initS101MMENA {} {
    global s101sctr MMENAErrors MMENASesnErrors
    variable NumMMENA
  
    set MMENAErrors 0
    set MMENASesnErrors 0
    
    for {set i 0} {$i < $NumMMENA} {incr i} {
      s101mmena set sector $s101sctr ioa [expr 500 + $i] value $i
    }
  }

  #
  # proc: initS101MMENB
  # purpose: Set initial values for scaled measurands
  #
  proc initS101MMENB {} {
    global s101sctr MMENBErrors MMENBSesnErrors
    variable NumMMENB
  
    set MMENBErrors 0
    set MMENBSesnErrors 0
    
    for {set i 0} {$i < $NumMMENB} {incr i} {
      s101mmenb set sector $s101sctr ioa [expr 600 + $i] value $i
    }
  }  
  
  #
  # proc: initS101MMENC
  # purpose: Set initial values for floating points
  #
  proc initS101MMENC {} {
    global s101sctr MMENCErrors MMENCSesnErrors
    variable NumMMENC
  
    set MMENCErrors 0
    set MMENCSesnErrors 0
    
    for {set i 0} {$i < $NumMMENC} {incr i} {
      s101mmenc set sector $s101sctr ioa [expr 700 + $i] value $i
    }
  }
  
  #
  # proc: initS101MIT
  # purpose: Set initial values for integrated totals
  #
  proc initS101MIT {} {
    global s101sctr MITErrors MITSesnErrors
    variable NumMIT
  
    set MITErrors 0
    set MITSesnErrors 0
    
    for {set i 0} {$i < $NumMIT} {incr i} {
      s101mit set sector $s101sctr ioa [expr 800 + $i] value $i
    }
  }  

  #
  # proc: changeMSP
  # purpose: Change Single Point values
  # arguments: none
  #
  proc changeMSP {} {
    global s101sctr s101sctr
    variable NumMSP

    tmwlog insert "//// Changing MSP Points..."
    
    for {set i 100} {$i < [expr 100 + $NumMSP]} {incr i} {
      set oldvalue [s101msp get sector $s101sctr ioa $i value]
      set newvalue [expr !$oldvalue]
      s101msp set sector $s101sctr ioa $i value $newvalue
    }
  }    

  #
  # proc: changeMDP
  # purpose: Change Double Point values
  # arguments: none
  #
  proc changeMDP {} {
    global s101sesn s101sctr
    variable NumMDP

    tmwlog insert "//// Changing MDP Points..."
    
    for {set i 200} {$i < [expr 200 + $NumMDP]} {incr i} {
      set oldvalue [s101mdp get sector $s101sctr ioa $i value]
      set newvalue [expr ($oldvalue + 1) % 4]
      s101mdp set sector $s101sctr ioa $i value $newvalue
    }
  }    

  #
  # proc: changeMST
  # purpose: Change Step Position values
  # arguments: none
  #
  proc changeMST {} {
    global s101sesn s101sctr
    variable NumMST

    tmwlog insert "//// Changing MST Points..."
    
    for {set i 300} {$i < [expr 300 + $NumMST]} {incr i} {
      set oldvalue [s101mst get sector $s101sctr ioa $i value]
      set newvalue [expr ($oldvalue + 1) % 256]
      s101mst set sector $s101sctr ioa $i value $newvalue
    }
  } 
  
  #
  # proc: changeMBO
  # purpose: Change Bitstring values
  # arguments: none
  #
  proc changeMBO {} {
    global s101sesn s101sctr
    variable NumMBO

    tmwlog insert "//// Changing MBO Points..."
    
    for {set i 400} {$i < [expr 400 + $NumMBO]} {incr i} {
      set oldvalue [s101mbo get sector $s101sctr ioa $i value]
      set newvalue [expr {($oldvalue + 1) % 4294967295}]
      s101mbo set sector $s101sctr ioa $i value $newvalue
    }
  }    
  
  #
  # proc: changeMMENA
  # purpose: Change Normalized Measurand values
  # arguments: none
  #
  proc changeMMENA {} {
    global s101sesn s101sctr
    variable NumMMENA

    tmwlog insert "//// Changing MMENA Points..."
    
    for {set i 500} {$i < [expr 500 + $NumMMENA]} {incr i} {
      set oldvalue [s101mmena get sector $s101sctr ioa $i value]
      set newvalue [expr $oldvalue + 1]
      if {$newvalue > 32767} {
        $newvalue = -32768
      }
      s101mmena set sector $s101sctr ioa $i value $newvalue
    }
  }  

  #
  # proc: changeMMENB
  # purpose: Change Scaled Measurand values
  # arguments: none
  #
  proc changeMMENB {} {
    global s101sesn s101sctr
    variable NumMMENB

    tmwlog insert "//// Changing MMENB Points..."
    
    for {set i 600} {$i < [expr 600 + $NumMMENB]} {incr i} {
      set oldvalue [s101mmenb get sector $s101sctr ioa $i value]
      set newvalue [expr $oldvalue + 1]
      if {$newvalue > 32767} {
        $newvalue = -32768
      }
      s101mmenb set sector $s101sctr ioa $i value $newvalue
    }
  }  
  
  #
  # proc: changeMMENC
  # purpose: Change Float Measurand values
  # arguments: none
  #
  proc changeMMENC {} {
    global s101sesn s101sctr
    variable NumMMENC

    tmwlog insert "//// Changing MMENC Points..."
    
    for {set i 700} {$i < [expr 700 + $NumMMENC]} {incr i} {
      set oldvalue [s101mmenc get sector $s101sctr ioa $i value]
      set newvalue [expr $oldvalue + 1]
      if {$newvalue > 3.40282e+038} {
        $newvalue = -3.40282e+038
      }
      s101mmenc set sector $s101sctr ioa $i value $newvalue
    }
  }  
  
  #
  # proc: changeMIT
  # purpose: Change Float Measurand values
  # arguments: none
  #
  proc changeMIT {} {
    global s101sesn s101sctr
    variable NumMIT

    tmwlog insert "//// Changing MIT Points..."
    
    for {set i 800} {$i < [expr 800 + $NumMIT]} {incr i} {
      set oldvalue [s101mit get sector $s101sctr ioa $i value]
      set newvalue [expr {($oldvalue + 1) % 4294967296}]
      s101mit set sector $s101sctr ioa $i value $newvalue
    }
  }  
  
  #
  # proc: ckMSP
  # purpose: Check if Single Points in S104 Master have same value as Single Points in 101 Slave
  #
  proc ckMSP {} {
    global s101sctr m104sctr MSPErrors MSPSesnErrors
    variable NumMSP
    variable mspErr "false"

    tmwlog insert "//// Checking MSP Points..."

    for {set i 0; set j 100} {$i < $NumMSP} {incr i; incr j} {
      set m104mspVal [m104data sector $m104sctr ioa $j]
      set s101mspVal [s101msp  get sector $s101sctr ioa $j value]

      #
      # map 101 points from "1" and "0" to "on" and "off"
      #
      if {$s101mspVal == 0} {
         set s101mspVal "off"
      } elseif {$s101mspVal == 1} {
         set s101mspVal "on"
      }
      
      if {$s101mspVal != $m104mspVal} {
         logSDGTestMsg "#### Error: S101 MSP Point $j is not equal to M104 MSP Point $j"
         logSDGTestMsg "    S101 MSP Point $j: $s101mspVal;  M104 MSP Point $j: $m104mspVal"
         set mspErr "true"
         incr MSPErrors
      }
    }
  
    if {$mspErr == "true"} {
      incr MSPSesnErrors
    }
  }
  
  #
  # proc: ckMDP
  # purpose: Check if Double Points in S104 Master have same value as Single Points in 101 Slave
  #
  proc ckMDP {} {
    global s101sctr m104sctr MDPErrors MDPSesnErrors
    variable NumMDP
    variable mdpErr false

    tmwlog insert "//// Checking MDP Points..."
    
    for {set i 0; set j 200} {$i < $NumMDP} {incr i; incr j} {
      set m104mdpVal [m104data sector $m104sctr ioa $j]
      set s101mdpVal [s101mdp  get sector $s101sctr ioa $j value]
  
      #
      # Map from 0-3 to double point values
      #
      if {$s101mdpVal == 0} {
        set s101mdpVal "intermediate"
      } elseif {$s101mdpVal == 1} {
        set s101mdpVal "off"
      } elseif {$s101mdpVal == 2} {
        set s101mdpVal "on"
      } elseif {$s101mdpVal == 3} {
        set s101mdpVal "indeterminate"
      }
      
      if {$s101mdpVal != $m104mdpVal} {
         logSDGTestMsg "#### Error: S101 MDP Point $j is not equal to M104 MDP Point $j"
         logSDGTestMsg "    S101 MDP Point $j: $s101mdpVal;  M104 MDP Point $j: $m104mdpVal"
         set mdpErr "true"
         incr MDPErrors
      }
    }
    
    if {$mdpErr == "true"} {
      incr MDPSesnErrors
    }
  }

  #
  # proc: ckMST
  # purpose: Check if Step Position points in 101 Slave
  #          have the same value as Step position points in 104 Slave
  #
  proc ckMST {} {
    global s101sctr m104sctr MSTErrors MSTSesnErrors
    variable NumMST
    variable mstErr false
  
    tmwlog insert "//// Checking MST Points..."

    for {set i 0; set j 300} {$i < $NumMST} {incr i; incr j} {
      set m104mstVal [m104data sector $m104sctr ioa $j]
      set s101mstVal [s101mst  get sector $s101sctr ioa $j value]
                 
      if {$s101mstVal != $m104mstVal} {
         logSDGTestMsg "#### Error: S101 MST Point $j is not equal to M104 MST Point $j"
         logSDGTestMsg "    S101 MST Point $j: $s101mdpVal;  M104 MST Point $j: $m104mdpVal"
         set mstErr "true"
         incr MSTErrors
      }
    }
    
    if {$mstErr == "true"} {
      incr MSTSesnErrors
    }
  }

  #
  # proc: ckMBO
  # purpose: Check if Bitstring points in 104 Master
  #          have the same value as Step position points in 101 Slave
  #
  proc ckMBO {} {
    global s101sctr m104sctr MBOErrors MBOSesnErrors
    variable NumMBO
    variable mboErr false
  
    tmwlog insert "//// Checking MBO Points..."

    for {set i 0; set j 400} {$i < $NumMBO} {incr i; incr j} {
      set m104mboVal [m104data sector $m104sctr ioa $j]
      set s101mboVal [s101mbo  get sector $s101sctr ioa $j value]
                 
      if {$s101mboVal != $m104mboVal} {
         logSDGTestMsg "#### Error: S101 MBO Point $j is not equal to M104 MBO Point $j"
         logSDGTestMsg "    S101 MBO Point $j: $s101mdpVal;  M104 MBO Point $j: $m104mdpVal"
         set mboErr "true"
         incr MBOErrors
      }
    }
    
    if {$mboErr == "true"} {
      incr MBOSesnErrors
    }
  }
  
  #
  # proc: ckMMENA
  # purpose: Check if Normalized Measurands in S104 Master have same value as
  #          Normalized Measurands in 101 Slave
  #
  proc ckMMENA {} {
    global s101sctr m104sctr MMENAErrors MMENASesnErrors
    variable NumMMENA
    variable mmenaErr false

    tmwlog insert "//// Checking MMENA Points..."
    
    for {set i 0; set j 500} {$i < $NumMMENA} {incr i; incr j} {
      set m104mmenaVal [m104data sector $m104sctr ioa $j]
      set s101mmenaVal [s101mmena  get sector $s101sctr ioa $j value]
                 
      if {$s101mmenaVal != $m104mmenaVal} {
         logSDGTestMsg "#### Error: S101 MMENA Point $j is not equal to M104 MMENA Point $j"
         logSDGTestMsg "    S101 MMENA Point $j: $s101mmenaVal;  M104 MMENA Point $j: $m104mmenaVal"
         set mmenaErr "true"
         incr MMENAErrors
      }
    }
    
    if {$mmenaErr == "true"} {
      incr MMENASesnErrors
    }
  }
  
  #
  # proc: ckMMENB
  # purpose: Check if Scaled Measurands in S104 Master have same value as Scaled Measurands in 101 Slave
  #
  proc ckMMENB {} {
    global s101sctr m104sctr MMENBErrors MMENBSesnErrors
    variable NumMMENB
    variable mmenbErr false

    tmwlog insert "//// Checking MMENB Points..."
    
    for {set i 0; set j 600} {$i < $NumMMENB} {incr i; incr j} {
      set m104mmenbVal [m104data sector $m104sctr ioa $j]
      set s101mmenbVal [s101mmenb  get sector $s101sctr ioa $j value]
                 
      if {$s101mmenbVal != $m104mmenbVal} {
         logSDGTestMsg "#### Error: S101 MMENB Point $j is not equal to M104 MMENB Point $j"
         logSDGTestMsg "    S101 MMENB Point $j: $s101mmenbVal;  M104 MMENB Point $j: $m104mmenbVal"
         set mmenbErr "true"
         incr MMENBErrors
      }
    }
      
    if {$mmenbErr == "true"} {
      incr MMENBSesnErrors
    }
  }
  
  #
  # proc: ckMMENC
  # purpose: Check if Scaled Measurands in S104 Master have same value as Scaled Measurands in 101 Slave
  #
  proc ckMMENC {} {
    global s101sctr m104sctr MMENCErrors MMENCSesnErrors
    variable NumMMENC
    variable mmencErr false

    tmwlog insert "//// Checking MMENC Points..."
    
    for {set i 0; set j 700} {$i < $NumMMENC} {incr i; incr j} {
      set m104mmencVal [m104data sector $m104sctr ioa $j]
      set s101mmencVal [s101mmenc get sector $s101sctr ioa $j value]
                 
      if {$s101mmencVal != $m104mmencVal} {
         logSDGTestMsg "#### Error: S101 MMENC Point $j is not equal to M104 MMENC Point $j"
         logSDGTestMsg "    S101 MMENC Point $j: $s101mmencVal;  M104 MMENC Point $j: $m104mmencVal"
         set mmencErr "true"
         incr MMENCErrors
      }
    }
      
    if {$mmencErr == "true"} {
      incr MMENCSesnErrors
    }
  }
  
  #
  # proc: ckMIT
  # purpose: Check if Integrated Totals in S104 Master have same value as Integrated Totals in 101 Slave
  #
  proc ckMIT {} {
    global s101sctr m104sctr MITErrors MITSesnErrors
    variable NumMIT
    variable mitErr false

    tmwlog insert "//// Checking MIT Points..."
    
    for {set i 0; set j 800} {$i < $NumMIT} {incr i; incr j} {
      set m104mitVal [m104data sector $m104sctr ioa $j]
      set s101mitVal [s101mit  get sector $s101sctr ioa $j value]
                 
      if {$s101mitVal != $m104mitVal} {
         logSDGTestMsg "#### Error: S101 MIT Point $j is not equal to M104 MIT Point $j"
         logSDGTestMsg "    S101 MIT Point $j: $s101mitVal;  M104 MIT Point $j: $m104mitVal"
         set mitErr "true"
         incr MITErrors
      }
    }
      
    if {$mitErr == "true"} {
      incr MITSesnErrors
    }
  }
  
  #
  # proc: ckMappings
  # purpose: Check if all mappings succeeded
  #  
  proc ckMappings {} {
    global NumRuns m104sesn m104sctr

    #
    # Increment the counter of the number of times this
    # test has been run.
    #
    incr NumRuns
    
    #
    # Change the input points on S101
    #
    changeMSP
    changeMDP
    changeMST
    changeMBO
    changeMMENA
    changeMMENB
    changeMMENC
    changeMIT
    
    snooze 5000

    #
    # Do GI (cicna) and Counter Interrogation (ccina)
    #
    m104cicna session $m104sesn sector $m104sctr group 0
    
    m104ccina session $m104sesn sector $m104sctr group 5 qualifier freeze
    snooze 5000
    m104ccina session $m104sesn sector $m104sctr group 5 qualifier read
    
    #
    # Sleep 5 seconds. This allows the SDG 101 Master to poll
    # and get the new values. (Not needed?)
    #
    tmwlog insert "//// Sleeping..."
    snooze 5000

    #
    # All of the changes should be being passed through as events
    # so there's no need to do a GI. Just check the results
    #
    ckMSP
    ckMDP
    ckMST
    ckMBO
    ckMMENA
    ckMMENB
    ckMMENC
    ckMIT
  }

  # proc: InitTest
  # purpose: initialize test
  #
  proc InitTest {{service 0}} {
    global NumRuns
    logSDGTestMsg "*s1012m104 Init"
    
    #
    # Open the appropriate ports
    #
    open104master
    open101slave

    #
    # Start with known values
    # After setting the values, do a GI to get them into the SDG
    #
    set NumRuns 0
    set   MSPErrors 0; set   MSPSesnErrors 0
    set   MDPErrors 0; set   MDPSesnErrors 0
    set   MBOErrors 0; set   MBOSesnErrors 0
    set   MSTErrors 0; set   MSTSesnErrors 0
    set MMENAErrors 0; set MMENASesnErrors 0
    set MMENBErrors 0; set MMENBSesnErrors 0
    set MMENCErrors 0; set MMENCSesnErrors 0
    set   MITErrors 0; set   MITSesnErrors 0
    
    initS101MSP
    initS101MDP
    initS101MBO
    initS101MST
    initS101MMENA
    initS101MMENB
    initS101MMENC
    initS101MIT
    

    if {$service == 0} {
      startSDG "s1012m104.ini"
	} else {
      startSDGservice "s1012m104.ini"
	}

    snooze 30000; #move this to sdgmain.tcl -- sleep for 60 secs when starting
    
    # connectSDGopcServer
  }

  # proc: StopTest
  # purpose: stop the test
  #
  proc StopTest {{service 0}} {
    logSDGTestMsg "*s1012m104 Stop"
    close101slave
    close104master
    if {$service == 0} {
      stopSDG
    } else {
	  stopSDGservice
    }  
  }
  
  # proc: ValidateTest
  # purpose: validate the test results
  #
  proc ValidateTest {} {
    global NumRuns
    global MSPErrors MDPErrors MBOErrors MMENAErrors MMENBErrors MMENCErrors MITErrors
    global MSPSesnErrors MDPSesnErrors MBOSesnErrors MMENASesnErrors MMENBSesnErrors MMENCSesnErrors
    global MITSesnErrors
    
    logSDGTestMsg "*s1012m104 Validate"

    set result [expr $MSPErrors + $MDPErrors + $MBOErrors \
                   + $MMENAErrors + $MMENBErrors + $MMENCErrors \
                   + $MITErrors]
                   
    logSDGTestMsg "#### Total Number of Runs: $NumRuns"
    logSDGTestMsg "#### Sessions with MSP Errors:    $MSPSesnErrors  Total MSP Errors:   $MSPErrors"
    logSDGTestMsg "#### Sessions with MDP Errors:    $MDPSesnErrors  Total MDP Errors:   $MDPErrors"
    logSDGTestMsg "#### Sessions with MBO Errors:    $MBOSesnErrors  Total MBO Errors:   $MBOErrors"
    logSDGTestMsg "#### Sessions with MMENA Errors:  $MMENASesnErrors  Total MMENA Errors: $MMENAErrors"
    logSDGTestMsg "#### Sessions with MMENB Errors:  $MMENBSesnErrors  Total MMENB Errors: $MMENBErrors"
    logSDGTestMsg "#### Sessions with MMENC Errors:  $MMENCSesnErrors  Total MMENC Errors: $MMENCErrors"
    logSDGTestMsg "#### Sessions with MIT Errors:    $MITSesnErrors  Total MIT Errors:   $MITErrors"
    logSDGTestMsg "#### Total Number of Errors:      $result"
    
    if { $result == 0 } {
      logSDGTestMsg "<--*-*-*--> s1012m104 test passed"
      return "passed"
    } else {
      logSDGTestMsg "<--*-*-*--> s1012m104 test failed"
      return "failed"
    }
  }
  
  # proc: DoTest
  # purpose: do the test
  #
  proc DoTest {{service 0}} {
    global gTestPassed

    # Mark log at beginning of test
    tmwlog mark m1
    
    InitTest $service
    
    # The M104 points in the SDG are mapped to S104 points
    # Cause some S101 events that will be passed on through 104
    #
    for { set i 0} {$i<5} {incr i} {
      ckMappings 
      snooze 2000
    }
    
    # save the log file for this test
    tmwlog save s1012m104.log m1 end
    
    if { [ValidateTest] == "failed" } {
      set gTestPassed false
    }
    
    StopTest $service
  }

}