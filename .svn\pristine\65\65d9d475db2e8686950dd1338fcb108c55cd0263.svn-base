/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2008 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTW61850Client.cpp                                          */
/* DESCRIPTION:  Implementation of the 61850 Client                          */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 6 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"

#include "gateway/GTWOsUtils/filesystem.hpp"
#include "gateway/GTWOsUtils/GtwOsSync.h"
#include "GTW61850ClientNode.h"
#include "GTW61850GOOSEControlBlock.h"
#include "GTW61850ReportControlBlock.h"
#include "GTW61850PolledPointSet.h"
#include "GTW61850CommandPointSet.h"
#include "GTW61850WritablePointSet.h"
#include "GTW61850PolledDataSet.h"
#include "GTW61850DataAttributeMDO.h"
#include "WinEventManager.h"
#include "GTW61400Alarms.h"
#include "gateway/GTWSNLicUtils/Crypto.h"
#include "GTW61850ClientEditor.h"
#include "GTW61850Client.h"
#include "gateway/GTWSNLicUtils/TmwAsymCrypto.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

using namespace tmw;
using namespace tmw61850;
using std::string;
using std::list;
using std::unique_ptr;

#if defined(DEBUG) && defined(_WIN32)
static bool g_bTurnOnDataSimulation = false;
static unsigned int g_iSimulateThrottleTimeMS = 1000;
#endif

unsigned int GTW61850Client::m_nTotalClientCount = 0;
unsigned int GTW61850Client::m_nTotalClientConnected = 0;

tmw::AutoCriticalSection GTW61850Client::m_SequentialConnectCS;

ImplementClassBaseInfo (GTW61850Client,GTWCollectionListParent,pClassInfo,nullptr);

//CONST ULONG NEXT_COUNT = 100; // number of elements to request when calling Next

#define FULL_TEST 1 // define this to test more interfaces

int GTW61850Client::m_CurrentCloseCount = 0;
bool GTW61850Client::m_bIsLicensed = false;

// static initialization
std::list<GTW61850Client*> GTW61850Client::m_ClientList; // the one and only 61850 client list

#define IS_REPORT_CHANGE_REASON(r) ((r | tmw61850::ClientPointChangeInfo::PointChangeReason_DataChange) || \
                (r | tmw61850::ClientPointChangeInfo::PointChangeReason_QualityChange) || \
                (r | tmw61850::ClientPointChangeInfo::PointChangeReason_Unknown) || \
                (r | tmw61850::ClientPointChangeInfo::PointChangeReason_Integrity) || \
                (r | tmw61850::ClientPointChangeInfo::PointChangeReason_GeneralInterrogation))

#define IS_READ_REASON(r) ((r | tmw61850::ClientPointChangeInfo::PointChangeReason_Read) || \
                (r | tmw61850::ClientPointChangeInfo::PointChangeReason_Read_Array) || \
                (r | tmw61850::ClientPointChangeInfo::PointChangeReason_Read_DataSet))

#define ASSERT_REPORT_CHANGE_REASON(r) assert(IS_REPORT_CHANGE_REASON(r))


CStdString PointChangeReasonToString(unsigned int reason)
{
  string s;
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_Unknown)
  {
    s += "PointChangeReason_Unknown";
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_GeneralInterrogation)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_GeneralInterrogation");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_Integrity)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_Integrity");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_DataUpdate)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_DataUpdate");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_QualityChange)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_QualityChange");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_DataChange)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_DataChange");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_Goose)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_Goose");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_Read)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_Read");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_Read_Array)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_Read_Array");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_Read_DataSet)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_Read_DataSet");
  }
  if (reason & tmw61850::ClientPointChangeInfo::PointChangeReason_Log)
  {
    s += (s.length() > 0 ? "|" : "") + string("PointChangeReason_Log");
  }
  if (s.length() == 0)
  {
    s = "Unknown reason";
  }
  return CStdString(s.c_str());
}

#ifdef DEBUG
  //#define DBG_SE // Start End in general

  //#define DBG_RCB
  //#define DBG_RCB_TERSE
  //#define DBG_GOOSE
  //#define DBG_PDS
  //#define DBG_PPS

void TraceDataAtttributeChangeEvent(const char* sContext, tmw61850::DataAttribute *pDa, unsigned int reason)
{
  String s, sval, squal,sts;
  pDa->GetValueAsString(sval);

  tmw61850::DataAttribute *q, *ts;
  q = ts = nullptr;
  pDa->GetQualityAndTimeStamp(q,ts);

  squal = sts = "N/A";
  if (q)
  {
    q->GetValueAsString(squal);
  }
  if (ts)
  {
    ts->GetValueAsString(sts);
  }

  CStdString sReason = PointChangeReasonToString(reason);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "%s: %s, value = %s, quality = %s, timestamp = %s, reason = %s\n", sContext, (const char*)pDa->GetFullName(s), (const char*)sval, (const char*)squal, (const char*)sts, sReason);
}

#endif

class GTW61850ClientChannelActiveControl : public GTWChannelActiveControlBase
{
public:
  GTW61850ClientChannelActiveControl(GTW61850Client* p);
  virtual GTWDEFS_CTRL_STAT SetValue(bool newValue) override;

private:
  GTW61850Client* m_pClient;
};

class GTWNumGooseTimeOuts : public GTWInternalCounter
{
public:
  GTWNumGooseTimeOuts(GTW61850Client* pClient) : GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_HEALTH), m_pClient(pClient)
  {
    assert(m_pClient);
  }

  virtual GTWDEFS_CTRL_STAT SetValue(unsigned int newValue) override;

protected:
  virtual CStdString GetBaseName(void) override
  {
    return("Goose Timeout Count");
  }

private:
  GTW61850Client* m_pClient;
};


class GTWNumGooseDrops : public GTWInternalCounter
{
public:
  GTWNumGooseDrops() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_HEALTH)
  {
  }

  virtual GTWDEFS_CTRL_STAT SetValue(unsigned int newValue) override
  {
    if (newValue == 0)
    {
      //tmw61850::Client::ResetGooseDrops();
    }
    return GTWInternalCounter::SetValue(newValue);
  }

protected:
  virtual CStdString GetBaseName(void) override
  {
    return("Goose Drop Count");
  }
};

class GTWRequestQsize : public GTWULongControl
{
protected:
  virtual CStdString GetBaseName(void) override
  {
    return("RequestQsize");
  }

public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWRequestQsize() :
    GTWULongControl(0)
  {
  }

  virtual GTWDEFS_CTRL_STAT SetValue(unsigned int newValue) override
  {
    GTWInternalDataObjectULong::SetValue(newValue);

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWResponseQsize : public GTWULongControl
{
protected:
  virtual CStdString GetBaseName(void) override
  {
    return("ResponseQsize");
  }

public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWResponseQsize() :
    GTWULongControl(0)
  {
  }

  virtual GTWDEFS_CTRL_STAT SetValue(unsigned int newValue) override
  {
    GTWInternalDataObjectULong::SetValue(newValue);

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWNumReportMessages : public GTWInternalCounter
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWNumReportMessages() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE)
  {
  }

protected:
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsLogMask);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsOpcAELogMask);
  }

  virtual CStdString GetBaseName(void) override
  {
    return("Report Count");
  }
};

class GTWNumGooseEvents : public GTWInternalCounter
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWNumGooseEvents() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE)
  {
  }

protected:
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsLogMask);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsOpcAELogMask);
  }

  virtual CStdString GetBaseName(void) override
  {
    return("Goose Event Count");
  }
};

class GTWNumIntegrity : public GTWInternalCounter
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWNumIntegrity() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE)
  {
  }

protected:
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsLogMask);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsOpcAELogMask);
  }

  virtual CStdString GetBaseName(void) override
  {
    return("Integrity Count");
  }
};

class GTWNumReadResponse : public GTWInternalCounter
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWNumReadResponse() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE)
  {
  }

protected:
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsLogMask);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsOpcAELogMask);
  }

  virtual CStdString GetBaseName(void) override
  {
    return("Read Response Count");
  }
};

class GTWTotalBufferOverflows : public GTWInternalCounter
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWTotalBufferOverflows() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_HEALTH)
  {
  }

protected:
  virtual CStdString GetBaseName(void) override
  {
    return("TotalBufferOverflows");
  }
};

class GTWNumDisconnects : public GTWInternalCounter
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWNumDisconnects() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_HEALTH)
  {
  }

protected:
  virtual CStdString GetBaseName(void) override
  {
    return("Disconnect Count");
  }
};

class GTWNumValueChanges : public GTWInternalCounter
{
public:
  GTWNumValueChanges() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE)
  {
  }

protected:
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void)
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsLogMask);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void)
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsOpcAELogMask);
  }

  virtual CStdString GetBaseName(void)
  {
    return("Value Change Count");
  }
};

class GTWNumQualityChanges : public GTWInternalCounter
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }
  GTWNumQualityChanges() :
    GTWInternalCounter(0, GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE)
  {
  }
  virtual ~GTWNumQualityChanges() {}

protected:
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsLogMask);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) override
  {
    return ((GTWDEFS_UPDTRSN)GTWConfig::DiagnosticsOpcAELogMask);
  }

  virtual CStdString GetBaseName(void) override
  {
    return("Quality Change Count");
  }
};

class GTW61850ClientConnectedToServer : public GTWInternalDataObjectBool
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }

  // start as "Off", but later set to "On" to generate an event and timestamp
  GTW61850ClientConnectedToServer(GTW61850Client* pClient);

  virtual bool GetValue(void) override;

  //virtual void SetBaseEditor(GTWBaseEditor *p) override
  //{
  //  GTWInternalDataObjectBool::SetBaseEditor(p);
  //}

protected:

  // virtual GTWInternalDataObjectTemplate_BASE functions
  virtual CStdString GetBaseName(void) override { return "ConnectedToServer"; }

  // always logged!
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) override
  {
    return (GTWDEFS_UPDTRSN_NONE);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) override
  {
    return (GTWDEFS_UPDTRSN_NONE);
  }

  virtual void GetDbasDataId(GTWDEFS_DBAS_DATA_ID* pDbasDataId) override
  {
    pDbasDataId->dataType = GTWDEFS_INTERNAL_BOOL_STATUS;
    pDbasDataId->internalDataType = TMWDEFS_TRUE;

    GTWInternalDataObjectBool::GetDbasDataId(pDbasDataId);
  }

private:
  GTW61850Client* m_pClient;
};

class Gtw61850ClientReconnectTimerIfnfo;
class Gtw61850UpdateStatsTimerInfo;

// This class is used to represent any dynamically created 61850 datasets. We must persist these in order to restore them back on the server.
class GTW61850DynamicDataSet
{
public:
  GTW61850DynamicDataSet()
  {
    aliasName = "";
    logicalName = "";
    reportControlBlock = "";
    gooseControlBlock = "";
    dataSetName = "";
    dataSetElements.clear();
  }

  CStdString aliasName;
  CStdString logicalName;
  CStdString reportControlBlock;
  CStdString gooseControlBlock;
  CStdString dataSetName;
  std::vector<CStdString> dataSetElements;
};

class OnDataChangeItem : public WorkItemBase, tmw::PooledObject
{
public:
  OnDataChangeItem() :
    m_oda(NULL),
    m_reason(tmw61850::ClientPointChangeInfo::PointChangeReason_Unknown),
    m_cc(NULL),
    m_pControlBlock(NULL),
    m_pUserDataArray(NULL)
  {
  }

  ~OnDataChangeItem()
  {
    Reset();
  }

  void Reset()
  {
    m_value.ClearValue();
    m_oda = NULL;
    m_cc = NULL;
    m_reason = tmw61850::ClientPointChangeInfo::PointChangeReason_Unknown;
    m_pUserDataArray = NULL;
  }

  virtual void onActivate()
  {
  }
  virtual void onDeactivate() {}

  tmw61850::DataAttribute* m_oda; // original data attribute, used only for comparison in the model, the value/time/quality should not be used because it may not reflect the value/time/quality at the time of the report
  tmw61850::ClientPointChangeInfo::PointChangeReason m_reason;
  tmw61850::Client* m_cc;
  void* m_pControlBlock;

  tmw61850::Value m_value;
  tmw::Array<void*>* m_pUserDataArray;

public:
  virtual void  DoWork(void* param);

  virtual void Abort()
  {
  }
  //void *pUserData;
};

class StartEndPointChangeData
{
public:

  StartEndPointChangeData(tmw61850::DataSet* pDS, tmw61850::Client* pClient, tmw61850::ClientPointChangeInfo::PointChangeReason reason, void* userHandle)
    :
    m_ds(pDS),
    m_pClient(pClient),
    m_reason(reason),
    m_pControlBlock(userHandle),
    m_pContextObject(NULL)
  {
  }

  tmw61850::DataSet* m_ds;
  tmw61850::Client* m_pClient;
  tmw61850::ClientPointChangeInfo::PointChangeReason m_reason;
  void* m_pControlBlock; // This can be either a GTW61850PolledDataSet or a GTW61850ReportControlBlock
  void* m_pContextObject; // for now this is unused but could be used to ensure an MDO is only updated when its GTW control block is updated, e.g. it can be set to tmw::ReportControl, when relevant
  std::list<OnDataChangeItem*> m_DataChangeWorkItemList;
};

class GTW61850QueueStatus : public GTWInternalDataObjectULong
{
public:
  bool IsEditable() override { return false; }
  bool IsDeletable() override { return false; }

  GTW61850QueueStatus(GTW61850Client* pClient)
    :
    GTWInternalDataObjectULong(0),
    m_pClient(pClient)
  {
  }

private:
  /* virtual GTWInternalDataObjectTemplate_BASE functions */
  virtual CStdString GetBaseName(void) override { return ("QueueStatus"); }

  /* never logged! */
  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) override { return (GTWDEFS_UPDTRSN_NONE); }
  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) override { return (GTWDEFS_UPDTRSN_NONE); }

  virtual void getMdoValueAsString(const GTWEXPND_FORMAT& pFormat, CStdString& msg) override;
  virtual TMWTYPES_UINT GetValue() override;

  virtual void GetDbasDataId(GTWDEFS_DBAS_DATA_ID* pDbasDataId) override
  {
    pDbasDataId->dataType = GTWDEFS_INTERNAL_DIAG_CNTR;
    pDbasDataId->internalDataType = TMWDEFS_TRUE;
    GTWInternalDataObjectULong::GetDbasDataId(pDbasDataId);
  }

private:
  GTW61850Client* m_pClient;
};

class OnEndClientPointChangeWorkItem : public WorkItemBase
{
public:
  OnEndClientPointChangeWorkItem()
    :
    m_pStartEndPCData(NULL)
  {
  }

  ~OnEndClientPointChangeWorkItem()
  {
    delete m_pStartEndPCData;
    m_pStartEndPCData = NULL;
  }

  virtual void DoWork(void* param);

  virtual void Abort()
  {
  }

#ifdef DEBUG
  virtual void dump(std::ofstream& os);
  virtual void dump();
#endif

  StartEndPointChangeData* m_pStartEndPCData;
};

void GTW61850QueueStatus::getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg)
{
  bool bShowGoose = m_pClient->m_GooseQueueThread.IsActive();
  bool bShowRpt = m_pClient->m_ReportQueueThread.IsActive();

  if (bShowGoose)
  {
    if (bShowRpt)
    {
      msg.Format("Main(%d),RPT(%d),GSE(%d),Pool(%d)", m_pClient->m_responseQueueThread.GetQueueSize(),
        m_pClient->m_ReportQueueThread.GetQueueSize(), m_pClient->m_GooseQueueThread.GetQueueSize(),
        m_pClient->m_pDispenser->DispensedObjectCount());
    }
    else
    {
      msg.Format("Main(%d),GSE(%d),Pool(%d)", m_pClient->m_responseQueueThread.GetQueueSize(), m_pClient->m_GooseQueueThread.GetQueueSize(),
        m_pClient->m_pDispenser->DispensedObjectCount());
    }
  }
  else if (bShowRpt)
  {
    msg.Format("Main(%d),RPT(%d),Pool(%d)", m_pClient->m_responseQueueThread.GetQueueSize(), m_pClient->m_ReportQueueThread.GetQueueSize(),
                                m_pClient->m_pDispenser->DispensedObjectCount());
  }
  else
  {
    msg.Format("Main(%d),Pool(%d)", m_pClient->m_responseQueueThread.GetQueueSize(), m_pClient->m_pDispenser->DispensedObjectCount());
  }
}

TMWTYPES_UINT GTW61850QueueStatus::GetValue()
{
  return m_pClient->m_responseQueueThread.GetQueueSize();
}

GTW61850ClientConnectedToServer::GTW61850ClientConnectedToServer(GTW61850Client *pClient) :
  GTWInternalDataObjectBool(TMWDEFS_FALSE),
  m_pClient(pClient)
{
  m_tagPurpose = GTWTYPES_TAG_PURPOSE_MASK_HEALTH;
}

bool GTW61850ClientConnectedToServer::GetValue(void)
{
  return m_pClient && m_pClient->IsServerUp();
}

GTWDEFS_CTRL_STAT GTWNumGooseTimeOuts::SetValue(unsigned int newValue)
{
  if (newValue == 0)
  {
    m_pClient->ResetGooseTimeouts();
  }
  return GTWInternalCounter::SetValue(newValue);
}

/*
unsigned int __stdcall GTW61850Client::DisconnectClientThreadFunc( void*  pParam )
{
  GTW61850Client *pClient = (GTW61850Client*)pParam;
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "GTW61850Client::DisconnectClientThreadFunc calling disconnect on '%s', threadid=%d\n", pClient->GetFullName().c_str(), GetCurrentThreadId());
  pClient->Disconnect();

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "GTW61850Client::DisconnectClientThreadFunc finished calling disconnect '%s'\n", pClient->GetFullName().c_str());
  GTW61850Client::incrementCurrentCloseCount();
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "GTW61850Client::DisconnectClientThreadFunc cur close count=%d\n", GTW61850Client::m_CurrentCloseCount);
  
  return 0;
}
*/

void GTW61850Client::StopAllClientsOnExit()
{
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;

    LOG6(p61850Client->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Disconnecting 61850 Client '%s'", (const char *)p61850Client->GetAliasName());

    p61850Client->Disconnect();

    LOG6(p61850Client->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Disconnect complete for 61850 Client '%s'", (const char *)p61850Client->GetAliasName());
  }
    //create thread

  /*
  // Here we put each disconnect call on a separate thread for efficiency because each disconnect can take seconds.
  while (clientListPOS)
  {
    GTW61850Client* p61850Client = m_ClientList.GetNext(clientListPOS);

    //create thread
    DWORD threadId;
    HANDLE hThread = CreateThread(nullptr,
      0,
      GTW61850Client::DisconnectClientThreadFunc,
      p61850Client,
      0,
      &threadId);

    //p61850Client->Disconnect();
  }
  */
  /*
  // Now wait (do not return) until all clients are closed
  while (GTW61850Client::m_CurrentCloseCount < nExpectedCloseCount)
  {
    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "GTW61850Client::m_CurrentCloseCount = %d, expected = %d\n", GTW61850Client::m_CurrentCloseCount, nExpectedCloseCount);
    GtwOsSleep(250);
  }
  */
  //GTW61850Client::m_CurrentCloseCount = 0;
  //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "%s",  "GTW61850Client::m_CurrentCloseCount = done\n");

  RemoveAllClients();
}

void GTW61850Client::RemoveAllClients()
{
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;
    RemoveClient(p61850Client, false);
  }
  m_ClientList.clear();
}

void GTW61850Client::RemoveClient(GTW61850Client *pClient, bool bRemoveFromCollection)
{
  if (!pClient)
  {
    return;
  }

  if (bRemoveFromCollection)
  {
    for (auto pos = m_ClientList.begin(); pos != m_ClientList.end(); ++pos)
    {
      GTW61850Client const *p61850Client = *pos;
      if (p61850Client == pClient)
      {
        m_ClientList.erase(pos);
        break;
      }
    }
  }
  
  if (GTWCollectionBase *pParentCollection = pClient->GetParentCollection())
    pParentCollection->RemoveCollectionMember(pClient);

  delete pClient;  
}

class ConnectWorkItem : public WorkItemBase
{
public:
  ~ConnectWorkItem()
  {
    m_pClient->SetConnecting(false);
  }

  void DoWork(void* param) override;

  void Abort() override
  {
  }

  static void ConnectClient(GTW61850Client *pGTWClient, bool bForce = false)
  {
    if (pGTWClient->GetClientConnection()->IsConnectionAliveAndReady())
    {
      return;
    }

    tmw::CriticalSectionLock lock(pGTWClient->GetConnectSem()); // protect m_bConnecting in GTW61850Client

    if (pGTWClient->IsConnecting())
    {
      return;
    }

    pGTWClient->SetConnecting(true);

    ConnectWorkItem *pConnectWorkItem = new ConnectWorkItem();
    pConnectWorkItem->m_pClient = pGTWClient;
    pConnectWorkItem->m_bForce = bForce;

    pGTWClient->GetStatusWorkQueue()->AddWorkItemToQueue(pConnectWorkItem);
  }

private:
  ConnectWorkItem() : m_pClient(nullptr), m_bForce(false) {}

  GTW61850Client *m_pClient;
  bool m_bForce;

  void ConnectSequential(void *param);
  void Connect(void *param);
};

void GTW61850Client::HandleStartPointChange(tmw61850::Client *client, tmw61850::ClientPointChangeInfo *pci)
{
  StartEndPointChangeData *pSEPCData = new StartEndPointChangeData(pci->GetDataSet(), client, pci->GetPointChangeReason(), pci->GetUserHandle());
  if (pci->IsReadArray())
  {
    m_pPPSStartEndPCWorkItem = pSEPCData;
  }
  else if (pci->IsReadDataSet())
  {
    m_pPDSStartEndPCWorkItem = pSEPCData;
  }
  else if (pci->IsGoose())
  {
    //unsigned int iPause = 200;
    //while (m_bProcessingGoose)
   // {
     // GtwOsSleep(iPause);
    //}
    m_bProcessingGoose = true;
    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "GOOSE OnStartClientPointChange: dataset=%s, initializing m_pGooseStartEndPCWorkItem, changereason=%d\n", pci->GetDataSet()->GetName(), pci->GetPointChangeReason());
    m_pGooseStartEndPCWorkItem = pSEPCData;
    m_pGooseStartEndPCWorkItem->m_pContextObject = pci->GetGOOSEControl();
  }
  else if (pci->IsReport())
  {
    m_bProcessingRCB = true;
    //dfile << (const char*)this->GetFullName() << " : Processing RCB : " << pci->GetReportControl()->GetFullName(ss) << std::endl;

    m_pRCBStartEndPCWorkItem = pSEPCData;
    m_pRCBStartEndPCWorkItem->m_pContextObject = pci->GetReportControl();

    if (pci->GetReportOverflowed())
    {
      m_pCntrBufferOverflowMdo->IncrementBy(1);
      if (pci->GetDataSet() != nullptr)
      {
        LOG6(m_p61850ClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "IEC61850: Buffer over flow report id: (%s) (dataset:%s)", pci->GetDataSet()->GetName(), pci->GetReportID());
      }
    }
  }
  else
  {
    LOG6(m_p61850ClientConnection, GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850Client::HandleStartPointChange:assert(false)");
#ifdef _WIN32
    assert(false);
#endif

  }
}

void GTW61850Client::OnStartClientPointChange(tmw61850::Client *client, tmw61850::ClientPointChangeInfo *pci)
{
  GTW61850Client *pGTWClient = GTW61850Client::get61850Client(client);
  if (pci->GetDataSet())
  {
    if (pci->GetPointChangeReason() == tmw61850::ClientPointChangeInfo::PointChangeReason_Read_DataSet)
    {
      if (!pci->GetUserHandle())
      {
        // Nothing to do - someone issued a read dataset, but it was not a GTW61850PolledDataSet or a GTW61850ReportControlBlock object
        return;
      }
    }
  }
  // ignore start client point changes for just plain reads
  if (pci->GetPointChangeReason() == tmw61850::ClientPointChangeInfo::PointChangeReason_Read)
  {
    return;
  }

  pGTWClient->HandleStartPointChange(client, pci);
}

/*
class OnEndClientPointChangeWorkItem;

tmw::AutoCriticalSection gListCS;


static list<OnEndClientPointChangeWorkItem*> glist;

class KeepTrackOfOnEndWorkItem
{
public:
  KeepTrackOfOnEndWorkItem(OnEndClientPointChangeWorkItem *p) : pItem(p)
  {
    tmw::CriticalSectionLock lock(gListCS);
    glist.push_back(pItem);
  }

  ~KeepTrackOfOnEndWorkItem()
  {
    tmw::CriticalSectionLock lock(gListCS);
    glist.remove(pItem);
  }

  static void dumplist()
  {
    //tmw::CriticalSectionLock lock(gFileCS);

    //dfile << "OnEndClientPointChangeWorkItem active list.size=" << glist.size() << endl;
    /*
    list<OnEndClientPointChangeWorkItem*>::iterator iter = glist.begin();
    for (; iter != glist.end(); ++iter)
    {

    }
    /
  }


  OnEndClientPointChangeWorkItem *pItem;
};
*/


#ifdef DEBUG
  void OnEndClientPointChangeWorkItem::dump(std::ofstream &os)
  {
#ifdef _WIN32
    tmw::String s;

    os << "\tOnEndClientPointChangeWorkItem dump:" << endl;
    os << "\t1. m_pStartEndPCData->m_reason = " << m_pStartEndPCData->m_reason << endl;
    os << "\t2. m_pStartEndPCData->m_ds = " << ((m_pStartEndPCData->m_ds == nullptr) ? "nullptr" : (const char*)m_pStartEndPCData->m_ds->GetFullName(s)) << endl;

    GTW61850ControlBlock *pCB = (GTW61850ControlBlock*)(m_pStartEndPCData->m_pControlBlock);

    os << "\t3. m_pStartEndPCData->m_pControlBlock = " << ((pCB == nullptr) ? "nullptr" : (const char*)pCB->GetFullName()) << endl;
    os << "\t4. m_pStartEndPCData->m_DataChangeWorkItemList.size = " << m_pStartEndPCData->m_DataChangeWorkItemList.size() << endl;
#endif
  }

  void OnEndClientPointChangeWorkItem::dump()
  {
    int sz = (int)m_pStartEndPCData->m_DataChangeWorkItemList.size();

    int kkkk = 3;
  }
#endif

/*
class OnEndReadArrayWorkItem : public WorkItemBase
{
public:
  virtual void DoWork(void* param);

  virtual void Abort()
  {
  }

  GTW61850PolledPointSet *m_polledPointSet;
  tmw61850::Client *m_pClient;
};
*/

//int g_rcount = 0;

OnEndClientPointChangeWorkItem* GTW61850Client::InsertEndPointChangeWorkItem(StartEndPointChangeData *& startEndData)
{
  if (startEndData)
  {
    OnEndClientPointChangeWorkItem *pWI = new OnEndClientPointChangeWorkItem();

    pWI->m_pStartEndPCData = startEndData;

    bool bIsReport = m_pRCBStartEndPCWorkItem == startEndData;
    bool bIsGoose = m_pGooseStartEndPCWorkItem == startEndData;

    startEndData = nullptr; // set internal client pointer to nullptr, so that next work item can be processed immediately for other control blocks for this client
	   
    if (bIsReport)
    {
      m_ReportQueueThread.AddWorkItemToQueue(pWI);
    }
    else if (bIsGoose)
    {
      m_GooseQueueThread.AddWorkItemToQueue(pWI);
    }
    else
    {
      m_responseQueueThread.AddWorkItemToQueue(pWI);
    }
    this->updateQueueValue();

    return pWI;
  }
  return nullptr;
}

void GTW61850Client::HandleEndPointChange(tmw61850::ClientPointChangeInfo *pci)
{
  if (pci->IsReadDataSet() && m_pPDSStartEndPCWorkItem && m_pPDSStartEndPCWorkItem->m_pControlBlock == pci->GetUserHandle())
  {
#ifdef DBG_PDS
    LOG6(m_p61850ClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850  "PDS GTW61850Client::HandleEndPointChange: dataset=%s, reason=%s, #elements = %d\n",m_pPDSStartEndPCWorkItem->m_ds->GetName(), PointChangeReasonToString(m_pPDSStartEndPCWorkItem->m_reason), m_pPDSStartEndPCWorkItem->m_DataChangeWorkItemList.size());
#endif
    assert(pci->GetPointChangeReason() == tmw61850::ClientPointChangeInfo::PointChangeReason_Read_DataSet);
    InsertEndPointChangeWorkItem(m_pPDSStartEndPCWorkItem);
  }
  else if (pci->IsReadArray() && m_pPPSStartEndPCWorkItem && m_pPPSStartEndPCWorkItem->m_pControlBlock == pci->GetUserHandle())
  {
    assert(pci->GetPointChangeReason() == tmw61850::ClientPointChangeInfo::PointChangeReason_Read_Array);
    InsertEndPointChangeWorkItem(m_pPPSStartEndPCWorkItem);
  }
  else if (pci->IsGoose() && m_pGooseStartEndPCWorkItem && pci->GetPointChangeReason() == tmw61850::ClientPointChangeInfo::PointChangeReason_Goose)
  {
    //LOG6(m_p61850ClientConnection, GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850  "GOOSE GTW61850Client::HandleEndPointChange: dataset=%s, reason=%s\n", m_pGooseStartEndPCWorkItem->m_ds->GetName(), PointChangeReasonToString(m_pGooseStartEndPCWorkItem->m_reason));
    InsertEndPointChangeWorkItem(m_pGooseStartEndPCWorkItem);
  }
  else if (pci->IsReport() && m_pRCBStartEndPCWorkItem)
  {
    InsertEndPointChangeWorkItem(m_pRCBStartEndPCWorkItem);
  }
}

void GTW61850Client::OnEndClientPointChange(tmw61850::Client *client, tmw61850::ClientPointChangeInfo *pci)
{
  GTW61850Client *pClient = GTW61850Client::get61850Client(client);

  if (IS_READ_REASON(pci->GetPointChangeReason()))
  {
    ++pClient->m_NumReadResponse;
  }

  if (pci->GetDataSet())
  {
    if (pci->GetPointChangeReason() == tmw61850::ClientPointChangeInfo::PointChangeReason_Read_DataSet && !pci->GetUserHandle())
    {
      // Nothing to do - someone issued a read dataset, but it was not a GTW61850PolledDataSet nor a GTW81650RCB object
      return;
    }
  }
  // ignore start client point changes for just plain reads
  if (pci->GetPointChangeReason() == tmw61850::ClientPointChangeInfo::PointChangeReason_Read)
  {
    return;
  }

  pClient->HandleEndPointChange(pci);
}

void OnEndClientPointChangeWorkItem::DoWork(void *param)
{
  unique_ptr<OnEndClientPointChangeWorkItem> deleteOnReturn(this);

  GTW61850Client *pClient = (GTW61850Client *)m_pStartEndPCData->m_pClient->GetUserData();
  //KeepTrackOfOnEndWorkItem keept(this);
  if (!m_pStartEndPCData || !pClient)
  {
    LOG6(pClient->GetClientConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850Client::OnEndClientPointChangeWorkItem::DoWork:assert(false)");
#ifdef _WIN32
    assert(false);
#endif
    return;
  }

  try
  {
    if (m_pStartEndPCData->m_reason == tmw61850::ClientPointChangeInfo::PointChangeReason_Goose)
    {
      ++pClient->m_NumGooseEvents;
    }
    else if (
      m_pStartEndPCData->m_reason == tmw61850::ClientPointChangeInfo::PointChangeReason_Integrity
      || m_pStartEndPCData->m_reason == tmw61850::ClientPointChangeInfo::PointChangeReason_GeneralInterrogation
      )
    {
      ++pClient->m_NumIntegrity;
    }
    else if (m_pStartEndPCData->m_pControlBlock == nullptr && IS_REPORT_CHANGE_REASON(m_pStartEndPCData->m_reason))
    {
      //String s;
      //dfile << (const char*)pClient->GetFullName() << " : begin endclientpointchangeworkitem for RCB " << ((tmw61850::ReportControl*)m_pStartEndPCData->m_pContextObject)->GetFullName(s) << std::endl;
       
      m_pStartEndPCData->m_reason = tmw61850::ClientPointChangeInfo::PointChangeReason_DataChange;
      ++pClient->m_NumReportMessages;
    }

    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "OnEndClientPointChangeWorkItem processing m_pStartEndPCData->m_DataChangeWorkItemList, size=%d\n", m_pStartEndPCData->m_DataChangeWorkItemList.size());

    // First do OnDataChange for each DA and collect the mdo's that need to be updated. We want all of them processed before we call UpdateMDO
    list<GTW61850DataAttributeMDO*> MDOUpdateList;

    for (auto iter = m_pStartEndPCData->m_DataChangeWorkItemList.begin();
        iter != m_pStartEndPCData->m_DataChangeWorkItemList.end(); ++iter)
    {
      OnDataChangeItem *pDCItem = *iter;

      #ifdef DEBUG
        //TraceDataAtttributeChangeEvent("OnEndClientPointChangeWorkItem::DoWork ", pDCItem->m_oda, pDCItem->m_reason);
      #endif

      //Array<void*> *UserDataArray = pDCItem->_cda->GetUserDataArrayPtr();
	    if (Array<void*> *UserDataArray = pDCItem->m_pUserDataArray; UserDataArray != nullptr)
	    {
	      for (unsigned int i = 0; i < UserDataArray->size(); i++)
	      {
	        auto* p61850Mdo = (GTW61850DataAttributeMDO*)UserDataArray->getAt(i);
          if (!p61850Mdo)
          {
            continue;
          }
          if (std::find(MDOUpdateList.begin(), MDOUpdateList.end(), p61850Mdo) == MDOUpdateList.end()) // only add mdo once
          {
	          MDOUpdateList.push_back(p61850Mdo);
          }
        }
      }
      // Note: DoWork will put the OnDataChangeItem back in the pool and call Reset, so dont need to here 
      pDCItem->DoWork(nullptr);
    }

    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "OnEndClientPointChangeWorkItem MDOUpdateList.size=%d\n", MDOUpdateList.size());
    bool bEnableAlarms = GTWConfig::I61850Enable61400AlarmProcessing(pClient->Get61850ClientIndex());
    std::list<GTW61400Alarms*> alarmNodeList; // NOTE : these are alarm nodes under the client 

    // Now call UpdateMDO for all MDO's that have been modified and process alarms if needed
    for (auto iter = MDOUpdateList.begin(); iter != MDOUpdateList.end(); ++iter)
    {
      GTW61850DataAttributeMDO *p61850Mdo = *iter;
      p61850Mdo->setPointChangeReason(m_pStartEndPCData->m_reason); // reset reason because might have been unknown
      //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "Updating MDO: %s, reason=%d\n", p61850Mdo->GetFullName(), m_pStartEndPCData->m_reason);
      //bool bChanged = p61850Mdo->IsTimeChanged() || p61850Mdo->IsQualityChanged() || p61850Mdo->IsValueChanged();

      pClient->UpdateMDO(p61850Mdo);

      if (bEnableAlarms)
      {
        // keep list of alarm nodes related to this OnEndClientPointChangeWorkItem to process next
        GTW61400Alarms *pAlarmNode = pClient->FindAlarmsNode((const char*)p61850Mdo->GetFullName());
        if (pAlarmNode)
        {
          if (std::find(alarmNodeList.begin(), alarmNodeList.end(), pAlarmNode) == alarmNodeList.end())
          {
            alarmNodeList.push_back(pAlarmNode);
          }
        }
      }
    }

    if (bEnableAlarms)
    {
      // Process all alarm nodes in this OnEndClientPointChangeWorkItem
      for (std::list<GTW61400Alarms*>::iterator alarmNodeIter = alarmNodeList.begin(); alarmNodeIter != alarmNodeList.end(); ++alarmNodeIter)
      {
        GTW61400Alarms *pAlarmNode = *alarmNodeIter;
        pAlarmNode->UpdateStatusAlarms();
      }
    }

    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,"%s",  "OnEndClientPointChangeWorkItem finished all update MDO's\n");
  }
  catch (...)
  {
    //dfile << endl << "!!!!!!!!!!!!!!!!!!! Exception thrown in do work" << endl << endl;
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "%s", "Unknown exception thrown in OnEndClientPointChangeWorkItem::DoWork");
  }

  if (m_pStartEndPCData->m_pControlBlock)
  {
    // Here, m_pControlBlock can be a PDS, PPS, or a RCB, we only need to do this step for a PDS or PPS
    GTW61850ControlBlock *pCB = (GTW61850ControlBlock*)(m_pStartEndPCData->m_pControlBlock);
    GTW61850PolledDataSet *pDS = dynamic_cast<GTW61850PolledDataSet*>(pCB);
    if (pDS)
    {
      pDS->OnEndClientPointChange();
    }
    else
    {
      GTW61850PolledPointSet *pPS = dynamic_cast<GTW61850PolledPointSet*>(pCB);
      if (pPS)
      {
        pPS->OnEndClientPointChange();
      }
    }
  }

  /*
  if (m_pStartEndPCData->m_reason == tmw61850::ClientPointChangeInfo::PointChangeReason_Goose)
  {
    pClient->m_bProcessingGoose = false;
  }
  else if (m_pStartEndPCData->m_pControlBlock == nullptr)
  {
    //String s;
    //dfile << (const char*)pClient->GetFullName() << " : finished endclientpointchangeworkitem for RCB " << ((tmw61850::ReportControl*)m_pStartEndPCData->m_pContextObject)->GetFullName(s) << std::endl;

    pClient->m_bProcessingRCB = false;
  }
  */
}

//**************************************************************************
// Called by the server when data changes

void GTW61850Client::OnDataChange(tmw61850::Client *cc, tmw61850::ClientPointChangeInfo *pci)
{
  if (pci == nullptr)
  {
    return;
  }
  if (GetGTWApp()->IsShuttingDown() || cc->IsConnectionAliveAndReady() == false)
  {
    return;
  }
  tmw61850::ClientPointChangeInfo::PointChangeReason reason = pci->GetPointChangeReason();

  //static ofstream ff("C:\\pointupdates_ondatachange.txt");
  //tmw::String s;
  //ff << "ondatachange for " << pci->GetPointMetaData()->GetFullName(s) << " : reason = " << PointChangeReasonToString(reason).c_str() << endl;

  if (reason == tmw61850::ClientPointChangeInfo::PointChangeReason_Read) // for a simple read, just update the value of the MDO
  {
    tmw61850::DataAttribute *da = pci->GetPointMetaData();
    tmw::Array<void*> *pUserDataArray = da->GetUserDataArrayPtr();
    if (pUserDataArray)
    {
      //tmw::String st;
      //const char* sDaName = da->GetFullName(st);
      tmw61850::Value* pReportedValue = pci->GetPointValue();
      for (unsigned int i = 0; i < pUserDataArray->size(); i++)
      {
        GTW61850DataAttributeMDO* pMdo = (GTW61850DataAttributeMDO*)pUserDataArray->getAt(i);
        pMdo->SetValue(da, pReportedValue);
      }
    }
    return;
  }

#ifdef DEBUG
  //TraceDataAtttributeChangeEvent("1 GTW61850Client::OnDataChange", pci->GetPointMetaData(), reason);
#endif


  if (reason == tmw61850::ClientPointChangeInfo::PointChangeReason_Read_DataSet && !pci->GetUserHandle())
  {
    //String sAttrName;
    //pci->GetPointMetaData()->GetFullName(sAttrName);

    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "1 GTW61850Client::OnDataChange NOT posting for %s\n", (const char*)sAttrName);
    // Nothing to do - someone issued a read dataset, but it was not a GTW61850PolledDataSet object
    return;
  }   

  GTW61850Client *pClient = (GTW61850Client *)cc->GetUserData();
  tmw61850::DataAttribute *da = pci->GetPointMetaData();

  //tmw::String s;
  //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "GTW61850Client::OnDataChange called for %s\n", (const char*)da->GetFullName(s));

  tmw::String dsName = "N/A";
  if (pci->GetDataSet() != nullptr)
  {
    dsName = pci->GetDataSet()->GetName();
  }
  tmw::String rptName = pci->GetReportID();

  if (Array<void*> *UserDataArray = da->GetUserDataArrayPtr(); UserDataArray == nullptr || UserDataArray->size() == 0)
  {
    // then the DataAttribute has no associated MDOs (probably part of a dataset that has not been added as a point)
    return;
  }
  
  //bool bErrorDisplayed = false;

  //while (!m_bUseMainQueue && GetWorkQ()->IsFull())
  //{
  //  if (bErrorDisplayed == false)
  //  {
  //    std::string controlBlockName = "Unknown";
  //    if (UserDataArray != nullptr && UserDataArray->size() > 0)
  //    {
  //      GTW61850DataAttributeMDO* p61850Mdo = (GTW61850DataAttributeMDO*)UserDataArray->getAt(0);
  //      GTW61850ControlBlock *pCB = p61850Mdo->GetControlBlock();
  //      if (pCB != nullptr)
  //      {
  //        controlBlockName = (const char*)pCB->GetFullName();
  //      }
  //    }
  //    tmw::String daName = "Unknown";
  //    if (da != nullptr)
  //    {
  //      da->GetFullName(daName);
  //    }
  //    unsigned int qSize = 0;
  //    GetWorkQ()->SafeQueueSize(&qSize);
  //    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "GTW61850Client OnDataChange workqueue overflow. DA=%s, ControlBlock=%s, WorkQSize=%d", (const char*)daName, controlBlockName.c_str(), qSize);
  //    bErrorDisplayed = true;
  //  }
  //  GtwOsSleep(pClient->m_throttleDataChangeSleepTime);
  //}

  if (cc->IsConnectionAliveAndReady())
  {
    PostOnDataChange(cc, pci);
  }
}

void GTW61850Client::PostOnDataChange(tmw61850::Client *cc, tmw61850::ClientPointChangeInfo *pci)
{
  GTW61850Client *pClient = (GTW61850Client *)cc->GetUserData();
  if (!pClient->IsConnectionUp())
  {
    return;
  }

  tmw61850::DataAttribute *da = pci->GetPointMetaData();
  tmw61850::Value const *pReportedValue = pci->GetPointValue();
  tmw61850::DataSet const *pDS = pci->GetDataSet();
  void *pControlBlock = pci->GetUserHandle();
  tmw61850::ClientPointChangeInfo::PointChangeReason reason = pci->GetPointChangeReason();

  if (pClient->m_pDispenser->DispensedObjectCount() > pClient->m_pDispenser->GetMaxPoolSize())
  {
    // this can happen when very busy, it is usually innocuous
    LOG6(pClient->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, 
      "DataChange pool overflow..Current Size=%d, Max Size=%d", pClient->m_pDispenser->DispensedObjectCount(), pClient->m_pDispenser->GetMaxPoolSize());
  }

  //OnDataChangeItem *pDC = new OnDataChangeItem();// cc, da, reason);

  std::list<OnDataChangeItem *> *pList = nullptr;
  if (pDS)
  {
    if (pci->IsReadDataSet() && pClient->IsConnectionUp() && pClient->m_pPDSStartEndPCWorkItem && pClient->m_pPDSStartEndPCWorkItem->m_pControlBlock == pControlBlock)
    {
      //pClient->m_pPDSStartEndPCWorkItem->m_DataChangeWorkItemList.push_back(pDC);
      pList = &pClient->m_pPDSStartEndPCWorkItem->m_DataChangeWorkItemList;
    }
    else if (pci->IsGoose() && pClient->IsConnectionUp() && pClient->m_pGooseStartEndPCWorkItem)
    {
      //String s;
      //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "\tGOOSE OnDataChange: GOOSE data attribute=%s, reason=%d\n", (const char*)da->GetFullName(s), reason);
      //pClient->m_pGooseStartEndPCWorkItem->m_DataChangeWorkItemList.push_back(pDC);
      pList = &pClient->m_pGooseStartEndPCWorkItem->m_DataChangeWorkItemList;
    }
    else if (pci->IsReport() && pClient->IsConnectionUp() && pClient->m_pRCBStartEndPCWorkItem)
    {
      pList = &pClient->m_pRCBStartEndPCWorkItem->m_DataChangeWorkItemList;
    }
    else
    {
      // This happens for example if the connection goes down and the specific startendpcworkitem is already set to nullptr before this code
      LOG6(pClient->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, 
        "*** Error in GTW61850Client::PostOnDataChange: Received unexpected OnDataChange for dataset=%s, changereason=%d", pDS->GetName(), reason);
    }
  }
  else if (pci->IsReadArray())
  {
    if (pClient->m_pPPSStartEndPCWorkItem && pClient->IsConnectionUp() && pClient->m_pPPSStartEndPCWorkItem->m_pControlBlock == pControlBlock)
    {
      pList = &pClient->m_pPPSStartEndPCWorkItem->m_DataChangeWorkItemList;
    }
  }

  //OnDataChangeItem *pDC = new OnDataChangeItem();// cc, da, reason);
  if (pList)
  {
    //std::shared_ptr
    OnDataChangeItem *pDC = pClient->m_pDispenser->GetObjectFromPool();
    pDC->m_value = *pReportedValue;
    pDC->m_cc = cc;
    pDC->m_oda = da;
    pDC->m_pUserDataArray = da->GetUserDataArrayPtr();
    pDC->m_reason = reason;
    pDC->m_pControlBlock = pControlBlock;

    pList->push_back(pDC);
  }
}

void OnDataChangeItem::DoWork(void *param)
{
  class ResetOnReturn
  {
  public:
    ResetOnReturn(OnDataChangeItem *pS, GTW61850Client *pClient)
      : 
      m_pClient(pClient),
      m_ps(pS)
    {
    }

    ~ResetOnReturn()
    {
      m_ps->Reset();
      m_pClient->m_pDispenser->ReturnObjectToPool(m_ps);
    }

    GTW61850Client *m_pClient;
    OnDataChangeItem *m_ps;
  };
  //pClient->m_pDataChangePoolSize->SetValue(pClient->m_pDispenser->DispensedObjectCount());


  //unique_ptr<OnDataChangeItem> deleteOnReturn(this);

  OnDataChangeItem *pS = this;
  GTW61850Client *pClient = nullptr;
  if (pS->m_cc != nullptr)
  {
    pClient = (GTW61850Client *)pS->m_cc->GetUserData();
  }

  if (pClient == nullptr)
  {
    assert(false);
    return;
  }

  ResetOnReturn ret(this, pClient);

  if (!pClient->m_bServerUp)
  {
    return;
  }

  //String s, sv;
  //m_value.GetValueAsString(sv);

  //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "OnDataChangeItem::DoWork: da=%s, reason=%d, value=%s\n", _da->GetFullName(s), this->_reason, (const char*)sv);

  try
  {
    //unique_ptr<OnDataChangeItem> deleteOnReturn(this);
    if (pS->m_reason == tmw61850::ClientPointChangeInfo::PointChangeReason_Read) // may need to be moved into OnOperationalMsg
    {
      ++pClient->m_NumReadResponse;
    }

    //DataAttribute *da = pS->da;
    //tmw61850::ClientPointChangeInfo::PointChangeReason reason = _reason;
    Array<void*> *UserDataArray = pS->m_pUserDataArray;
    if (UserDataArray != nullptr)
    {
      GTW61850ControlBlock const *pControlBlock = (GTW61850ControlBlock *)this->m_pControlBlock;
      
      for (unsigned int i = 0; i < UserDataArray->size(); i++)
      {
        GTW61850DataAttributeMDO* p61850Mdo = (GTW61850DataAttributeMDO*)UserDataArray->getAt(i);

        if (p61850Mdo != nullptr && (!pControlBlock || p61850Mdo->GetControlBlock() == pControlBlock))
        {
          p61850Mdo->setPointChangeReason(pS->m_reason);

          if (p61850Mdo->getValueDataAttribute() == pS->m_oda)
          {
            p61850Mdo->SetValue(&pS->m_value);
            if (p61850Mdo->GetItemName() == "q")
            {
              p61850Mdo->SetQuality(&pS->m_value);
            }
            p61850Mdo->setDirty(true);
            //InterlockedIncrement(&(p61850Mdo->m_iDirtyCount));
          }

          if (p61850Mdo->getQualityDataAttribute() == pS->m_oda)
          {
            p61850Mdo->SetQuality(&pS->m_value);
            p61850Mdo->setDirty(true);
            //InterlockedIncrement(&(p61850Mdo->m_iDirtyCount));
          }

          if (p61850Mdo->getTimeDataAttribute() == pS->m_oda)
          {
            //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "Setting time to %s\n", pS->_cda->GetDateTime()->GetDateTimeAsString(s, true).c_str());
            p61850Mdo->SetTime(&pS->m_value);
            p61850Mdo->setDirty(true);
            //InterlockedIncrement(&(p61850Mdo->m_iDirtyCount));
          }

          if (p61850Mdo->getQualityDataAttribute() == nullptr)
          {
            if (p61850Mdo->GetItemName() != "q")
            {
              p61850Mdo->SetLastQuality();// m_i61850Quality);
              p61850Mdo->set61850Quality(I61850_QUALITY_VALIDITY_GOOD);
              //InterlockedIncrement(&(p61850Mdo->m_iDirtyCount));
              p61850Mdo->setDirty(true);
            }
          }

          if (p61850Mdo->getTimeDataAttribute() == nullptr)
          {
            GTWBaseDataObject const *pBdo = p61850Mdo->getBdo();
            if (pBdo != nullptr)
            {
              p61850Mdo->storeUpdatedTime(nullptr);
              //InterlockedIncrement(&(p61850Mdo->m_iDirtyCount));
              p61850Mdo->setDirty(true);
            }
          }
        }
      }
      
      //if (bKeepDAActive == false)
      //  pS->_oda->DisablePointChangeNotification();
    }
  }
  catch (...)
  {
    LOG6(pClient->GetClientConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "Unknown exception thrown in OnDataChangeItem::DoWork");
  }
}

//#define _SECOND 10000000






/**********************************************************************************\
Function :			GTW61850Client::GTW61850Client
Description : [none]
Return :			constructor	-
Parameters :
const char *alias_name	-
TMWTYPES_UINT reconnect_time	-
TMWTYPES_USHORT index	-
Note : [none]
\**********************************************************************************/
GTW61850Client::GTW61850Client(TMWTYPES_USHORT index,
                                TMWTYPES_BOOL bDoValidate,
                                bool bUseSclFile,
                                TMWTYPES_UINT reconnect_time,
                                TMWTYPES_UINT reconnect_limit,
                                TMWTYPES_UINT connect_timeout)
:
  GTWCollectionListParent(TMWDEFS_FALSE),
  m_bServerUp(false),
  //m_bCheckConfigRevOnCreate(true),
  m_p61850ConnectedToServerMdo(nullptr),
  m_pNumGooseTimeOutsmdo(nullptr),
  m_pNumGooseDropsmdo(nullptr),
  m_pNumReportMessagesmdo(nullptr),
  m_pNumValueChangesmdo(nullptr),
  m_pNumQualityChangesmdo(nullptr),
  m_pNumGooseEventsmdo(nullptr),
  m_pNumIntegritymdo(nullptr),
  m_pNumReadResponsemdo(nullptr),
  m_pNumDisconnectsmdo(nullptr),
  m_p61850ClientActiveControlmdo(nullptr),
  m_pQueueStatus(nullptr),
  m_pRequestQSizeMdo(nullptr),
  m_pResponseQSizeMdo(nullptr),
  m_pCntrBufferOverflowMdo(nullptr),
  m_throttleDataChangeSleepTime(100),
  m_iCurProfileIndex(0),
  m_bProcessingRCB(false),
  m_bProcessingGoose(false),
  m_bConnecting(false),
  m_pGooseStartEndPCWorkItem(nullptr),
  m_pRCBStartEndPCWorkItem(nullptr),
  m_pPDSStartEndPCWorkItem(nullptr),
  m_pPPSStartEndPCWorkItem(nullptr),
  m_StatusQueueThread(this, GTWConfig::I61850ClientName(index)),
  m_responseQueueThread(this, GTWConfig::I61850ClientName(index)),
  m_ReportQueueThread(this, GTWConfig::I61850ClientName(index)),
  m_GooseQueueThread(this, GTWConfig::I61850ClientName(index))
{
  m_pDispenser = new DataChangeDispenser;
  GtwOsSyncInc(&m_nTotalClientCount);
  m_StatusQueueThread.Start();

  m_pReconnectTimerInfo = new Gtw61850ClientReconnectTimerIfnfo("IEC 61850 Client RestartTimer");
  m_pReconnectTimer = new WinTimer(m_pReconnectTimerInfo);

  m_pUpdateStatsTimerInfo = new Gtw61850UpdateStatsTimerInfo("IEC 61850 Client UpdateStatsTimer");
  m_pUpdateStatsTimer = new WinTimer(m_pUpdateStatsTimerInfo);

  m_connectTimeOut = connect_timeout;

  m_reconnectTime = reconnect_time;
  m_RetryCountLimit = reconnect_limit;
  m_i61850RetryCount = 0;

  //m_bUseSclFile = bUseSclFile;
  //m_generateSCLFile = false;
  m_obIndex = index;
  m_bDoValidate = bDoValidate;

  m_ServerIpAddress = this->GetProfileAttr(GTWConfig::I61850ServerIPAddress(index),"");
  m_ServerIpPort = atoi(this->GetProfileAttr(GTWConfig::I61850ServerIPPort(index),""));

  //m_ClientIpAddress = GTWConfig::I61850ClientIPAddress(index);
  m_aliasName = GTWConfig::I61850ClientName(index);
  assert(m_aliasName.GetLength() > 0);
  if (m_aliasName.GetLength() == 0)
  {
    m_aliasName.Format("SDG61850Client-%d", index);
  }

  m_eModelDefType = (eModelDefType)GTWConfig::I61850ClientModelDefType(index);

  m_p61850ClientConnection = new tmw61850::Client();

  int requestQMaxSize = GTWConfig::I61850ClientRequestQMaxSize(index);
  int responseQMaxSize = GTWConfig::I61850ClientResponseQMaxSize(index);

  m_p61850ClientConnection->SetRequestQueueMAXSize(requestQMaxSize);
  m_p61850ClientConnection->SetResponseQueueMAXSize(tmw61850::Client::ResponseQueueType::Main_ResponseQueue, responseQMaxSize);
  m_p61850ClientConnection->SetResponseQueueMAXSize(tmw61850::Client::ResponseQueueType::Report_ResponseQueue, responseQMaxSize);
  m_p61850ClientConnection->SetResponseQueueMAXSize(tmw61850::Client::ResponseQueueType::GOOSE_ResponseQueue, responseQMaxSize);

  m_p61850ClientConnection->GetConfiguration()->SetReadDuringDiscovery(GTWConfig::I61850ReadDuringDiscovery(index));
  //m_p61850ClientConnection->SetDataSetControlBlockVerification(GTWConfig::I61850DataSetControlBlockVerification(index));

  if (CStdString(GTWConfig::I61850GooseAdapterName[Get61850ClientIndex()]) != "")
  {
    if (m_p61850ClientConnection->GetConfiguration()->GetEthernetAdapterConfig()->SetGOOSEAdapter((const char *)GTWConfig::I61850GooseAdapterName[Get61850ClientIndex()]) == false)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, m_aliasName.c_str(), "IEC 61850: failed to set GOOSE adapter: %s", (const char*)GTWConfig::I61850GooseAdapterName[Get61850ClientIndex()]);
    }
  }
  m_p61850ClientConnection->SetUserData(this);

  m_NumReadResponse = 0;
  m_NumGooseEvents = 0;
  m_NumReportMessages = 0;
  m_NumIntegrity = 0;
  m_NumValueChanges = 0;
  m_NumQualityChanges = 0;
  m_bInitialConnect = false;

#if defined(DEBUG) && defined(_WIN32)
  
  /*
  if (m_obIndex == 0)
  {
    DWORD threadId;
    HANDLE T = CreateThread(nullptr,
      0,
      GTW61850Client::DebugThreadFunc,
      this,
      0,
      &threadId);
  }
  */

  if (g_bTurnOnDataSimulation)
  {
    m_pSimContext = new Gtw61850ClientSimulateThreadContext;
    m_pSimContext->m_pClient = this;
    m_pSimContext->m_bRun = true;

    //create thread
    DWORD threadId;
    HANDLE hThread = CreateThread(nullptr,
      0,
      SimulateThreadFunc,
      m_pSimContext,
      0,
      &threadId);

    m_pSimContext->m_hThread = hThread;
  }
  else
  {
    m_pSimContext = nullptr;
  }
#endif
}

/**********************************************************************************\
Function :			GTW61850Client::~GTW61850Client
Description : [none]
Return :			destructor	-
Parameters :
Note : [none]
\**********************************************************************************/
GTW61850Client::~GTW61850Client()
{
#if defined(DEBUG) && defined(_WIN32)
  if (g_bTurnOnDataSimulation)
  {
    m_pSimContext->m_bRun = false;
    WaitForSingleObject(m_pSimContext->m_hThread, INFINITE); // wait for thread to exit
  }
#endif
  RegisterCallbacks(false);
  GtwOsSyncDec(&m_nTotalClientCount);

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, m_aliasName.c_str(), "GTW61850Client::~GTW61850Client: alias name: %s", (const char*)m_aliasName);
  
  // Do not finish any of the queues as we are deleting this client
  m_StatusQueueThread.Stop(); 
  
  m_ReportQueueThread.Stop();
  m_GooseQueueThread.Stop();
  m_responseQueueThread.Stop();

  if (m_pUpdateStatsTimer)
  {
    m_pUpdateStatsTimer->KillTimer(true);
  }
  if (m_pUpdateStatsTimerInfo)
  {
    delete m_pUpdateStatsTimerInfo;
    m_pUpdateStatsTimerInfo = nullptr;
  }
  if (m_pUpdateStatsTimer)
  {
    delete m_pUpdateStatsTimer;
    m_pUpdateStatsTimer = nullptr;
  }

  if (m_pReconnectTimer)
  {
    m_pReconnectTimer->KillTimer(true);
  }
  if (m_pReconnectTimerInfo)
  {
    delete m_pReconnectTimerInfo;
    m_pReconnectTimerInfo = nullptr;
  }
  if (m_pReconnectTimer)
  {
    delete m_pReconnectTimer;
    m_pReconnectTimer = nullptr;
  }

  if (m_p61850ClientConnection)
  {
    delete m_p61850ClientConnection;
    m_p61850ClientConnection = nullptr;
  }

  m_pDispenser->Clear();
  delete m_pDispenser;

  for (unsigned int i = 0; i < m_DataSets.size(); i++)
    delete m_DataSets[i];

  m_DataSets.clear();

  GetBaseEditor(EditorCommandDTO(MENU_CMD_NONE))->DeleteINIparms();
}

void GTW61850Client::updateQueueValue()
{
  m_pQueueStatus->SetValue(TMWTYPES_UINT(m_responseQueueThread.GetQueueSize() + m_ReportQueueThread.GetQueueSize() + m_GooseQueueThread.GetQueueSize()));
}

#ifdef DEBUG
/*
unsigned int __stdcall GTW61850Client::DebugThreadFunc(void*  pParam)
{
  while (true)
  {
    int total = 0;
    int i = 1;
    POSITION clientListPOS = m_ClientList.GetHeadPosition();
    GTW61850Client* pClient = nullptr;
    while (clientListPOS)
    {
      GTW61850Client* pClient = m_ClientList.GetNext(clientListPOS);
      total += pClient->m_pDispenser->DispensedObjectCount();
      dfile << i++ << ". " << (const char*)pClient->GetFullName() << " : pool stats: " << "max = " << pClient->m_pDispenser->GetMaxPoolSize() << ", cur free size=" << pClient->m_pDispenser->GetTotalFreeSize() << ", cur dispensed = " << pClient->m_pDispenser->DispensedObjectCount() << endl;
    }

    dfile << endl << "\tTOTAL=" << total << endl << endl;

    if (total > 10000)
    {
      pClient->GetWorkQueue()->dump();
      
    }
    GtwOsSleep(30000);
  }
}
*/
#endif

GTWBaseEditor *GTW61850Client::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
    m_pEditor = new GTW61850ClientEditor(dto, this, false);
   else 
    m_pEditor->SetDTO(dto);
  
  return m_pEditor;
}


GTWDEFS_STAT GTW61850Client::Create61850Mdo(const std::string &fullPath, GTWMasterDataObject **ppMdo, int valueType)
{
  size_t index = fullPath.find(".");
  GTW61850Client *pClient = GTW61850Client::get61850Client(fullPath.substr(0, index).c_str());
  if (pClient == nullptr)
  {
    return GTWDEFS_STAT_NOT_VALID;
  }

  //std::string sMdoPath = fullPath.substr(index + 1, fullPath.length() - index);
  *ppMdo = GetGTWApp()->find61850Mdo(pClient, fullPath);

  GTWDEFS_STAT status = GTWDEFS_STAT_NOT_VALID;
  if (*ppMdo == nullptr)
  {
    GTW61850DataAttributeMDO *p61850Mdo = nullptr;
    status = GetGTWApp()->GetPointMap()->Create61850Mdo(pClient, nullptr, fullPath.c_str(), nullptr, nullptr, (tmw61850::Value::Type)valueType, &p61850Mdo);
    if (p61850Mdo)
    {
      *ppMdo = p61850Mdo;
    }

    /*
    tmw61850::DataAttribute *pDA = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(sMdoPath.c_str(), pClient->m_p61850ClientConnection->Model());
    if (pDA)
    {

      GTW61850DataAttributeMDO *p61850Mdo = nullptr;
      status = GetGTWApp()->GetPointMap()->Create61850Mdo(pClient, nullptr, sMdoPath.c_str(), nullptr, nullptr, pDA->GetType(), &p61850Mdo);
      if (p61850Mdo)
      {
        *ppMdo = p61850Mdo;
      }
    }
    */
  }
  return status;


}

void GTW61850Client::ResetConnectRetryCount()
{
  // reset connection parameters from INI file
  m_connectTimeOut = GTWConfig::I61850ClientConnectTimeout(this->m_obIndex);
  m_reconnectTime = GTWConfig::I61850ClientReconnectTime(this->m_obIndex);
  m_RetryCountLimit = GTWConfig::I61850ClientReconnectRetryCount(this->m_obIndex);
  // reset currenty retry count to 0
  m_i61850RetryCount = 0;
}

void GTW61850Client::InsertMDOs()
{
  GTWCollectionList *pMemberCollection = GetMemberCollection();
  if (pMemberCollection != nullptr)
  {
    // NOTE: do not delete MDOs in Member collection in destructor - they are deleted
    //       in the GTWCollectionBase::DeleteGarbage method  
    pMemberCollection->InsertCollectionMember(m_p61850ConnectedToServerMdo = new GTW61850ClientConnectedToServer(this));
    pMemberCollection->InsertCollectionMember(m_pNumGooseTimeOutsmdo = new GTWNumGooseTimeOuts(this));
    pMemberCollection->InsertCollectionMember(m_pNumGooseDropsmdo = new GTWNumGooseDrops());
    pMemberCollection->InsertCollectionMember(m_pNumReportMessagesmdo = new GTWNumReportMessages());
    pMemberCollection->InsertCollectionMember(m_pNumValueChangesmdo = new GTWNumValueChanges());
    pMemberCollection->InsertCollectionMember(m_pNumQualityChangesmdo = new GTWNumQualityChanges());
    pMemberCollection->InsertCollectionMember(m_pNumGooseEventsmdo = new GTWNumGooseEvents());
    pMemberCollection->InsertCollectionMember(m_pNumIntegritymdo = new GTWNumIntegrity());
    pMemberCollection->InsertCollectionMember(m_pNumReadResponsemdo = new GTWNumReadResponse());
    pMemberCollection->InsertCollectionMember(m_pNumDisconnectsmdo = new GTWNumDisconnects());
    pMemberCollection->InsertCollectionMember(m_pRequestQSizeMdo = new GTWRequestQsize());
    pMemberCollection->InsertCollectionMember(m_pResponseQSizeMdo = new GTWResponseQsize());
    pMemberCollection->InsertCollectionMember(m_pCntrBufferOverflowMdo = new GTWTotalBufferOverflows());
    pMemberCollection->InsertCollectionMember(m_pQueueStatus = new GTW61850QueueStatus(this));
    pMemberCollection->InsertCollectionMember(m_p61850ClientActiveControlmdo = new GTW61850ClientChannelActiveControl(this));
  }
  else
  {
    LOG6(m_p61850ClientConnection, GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850Client::InsertMDOs:assert(false)");
#ifdef _WIN32
    assert(false);
#endif
  }
}

void GTW61850Client::UpdateMDO(GTW61850DataAttributeMDO *pMdo)
{
  GTWDEFS_UPDTRSN reason = pMdo->GetGTWChangeReason();
  pMdo->updateMDO(reason);
  //pMdo->GetClientNode()->UpdateEventAlarms(pMdo);

  UpdateEventAlarms(pMdo);

  pMdo->setDirty(false);
}

void GTW61850Client::GetDescriptionColText( CStdString &itemStr )
{
  tmw61850::Client *pClient = this->GetClientConnection();
  bool bConnected = pClient->IsConnectionAliveAndReady();
  CStdString serverIPAddr;
  this->GetServerIpAddress(serverIPAddr);
  itemStr.Format("61850 Client (%s,%d) : %s%s", (const char*)serverIPAddr,
    pClient->GetConfiguration()->GetServerPort(), bConnected ? "Connected" : "Not Connected", bConnected ? (m_bServerUp ? "(Running)" : "(Initializing...)") : "");
}

bool GTW61850Client::IsConnectionAliveAndReady()
{
  return GetClientConnection() && GetClientConnection()->IsConnectionAliveAndReady();
}

void GTW61850Client::Tokenize(const CStdString &s, std::vector<CStdString> &sArray)
{
  int i = 0;
  for (CStdString sItem = s.Tokenize(";", i); i >= 0; sItem = s.Tokenize(";", i))
  {
    sArray.push_back(sItem.Trim());
  }
}

CStdString GTW61850Client::GetProfileAttr(const CStdString &sProfileAttr, const CStdString &sCurValue)
{
  std::vector<CStdString> saItems;
  Tokenize(sProfileAttr, saItems);
  int sz = (int)saItems.size();
  for (int i = 0; i < sz; i++)
  {
    if (sCurValue == saItems[i])
    {
      if (i < sz-1)
      {
        return saItems[i+1];
      }
      return saItems[0];
    }
  }
  return !saItems.empty() ? saItems[0] : CStdString("");
  //int index = (m_iCurProfileIndex < (unsigned int)saItems.GetSize()) ? m_iCurProfileIndex : 0;
  //return (index < saItems.GetSize()) ? saItems[index] : "";
}


/**********************************************************************************\
Function :			GTW61850Client::ShutDown
Description : [none]	
Return :			void	-	
Parameters :
Note : [none]
\**********************************************************************************/
/*
void GTW61850Client::ShutDown()
{
  Disconnect();
  if (m_p61850ClientConnection)
  {
    delete m_p61850ClientConnection;
    m_p61850ClientConnection = nullptr;
  }

  if (m_pReconnectTimer)
  {
    m_pReconnectTimer->KillTimer();
  }
  if (m_pReconnectTimerInfo)
  {
    delete m_pReconnectTimerInfo;
    m_pReconnectTimerInfo = nullptr;
  }
  if (m_pReconnectTimer)
  {
    delete m_pReconnectTimer;
    m_pReconnectTimer = nullptr;
  }
}
*/

tmw61850::GOOSEControl *GTW61850Client::GetGOOSEControl(CStdString rpt)
{

  if (const Array<tmw61850::GOOSEControl*> *gooseControlBlocks = m_p61850ClientConnection->Model()->GetGOOSEControlBlocks(); gooseControlBlocks != nullptr)
  {
    for (unsigned int i=0;i<gooseControlBlocks->getSize();i++)
    {
      tmw61850::GOOSEControl *pRPT = gooseControlBlocks->getAt(i);
      tmw::String fullName;
      CStdString rptStr = pRPT->GetFullName(fullName);
      if (rptStr == rpt)
      {
        return pRPT;
      }
    }
  }
  return nullptr;
}

tmw61850::ReportControl *GTW61850Client::GetReportControl(CStdString rpt)
{
  if (m_p61850ClientConnection != nullptr)
  {
    if (tmw61850::i61850RootNode *pModel = dynamic_cast<tmw61850::i61850RootNode*>(m_p61850ClientConnection->Model()); pModel && !pModel->IsModelCompletlyBuilt())
      return nullptr;

    const Array<tmw61850::ReportControl*> *reportControlBlocks = m_p61850ClientConnection->Model()->GetReportControlBlocks();
    if (reportControlBlocks != nullptr)
    {
      for (unsigned int i=0;i<reportControlBlocks->getSize();i++)
      {
        tmw61850::ReportControl *pRPT = reportControlBlocks->getAt(i);
        tmw::String fullName;
        CStdString rptStr = pRPT->GetFullName(fullName);
        if (rptStr == rpt || (rptStr + "01") == rpt || (rptStr == rpt + "01"))
        {
          return pRPT;
        }
      }
    }
  }
  return nullptr;
}

tmw61850::DataSet *GTW61850Client::GetPolledDataSet(CStdString ds)
{
  if (tmw61850::i61850RootNode *pModel = dynamic_cast<tmw61850::i61850RootNode*>(m_p61850ClientConnection->Model()); pModel->IsModelCompletlyBuilt())
  {
    const tmw::Array<tmw61850::DataSet*> *dataSets = m_p61850ClientConnection->Model()->GetDataSets();

    if (dataSets != nullptr)
    {
      for (unsigned int i=0;i<dataSets->getSize();i++)
      {
        tmw61850::DataSet *pDS = dataSets->getAt(i);
        if (pDS)
        {
        tmw::String fullName;
        CStdString dsStr = pDS->GetFullName(fullName);
        if (dsStr == ds)
        {
          return pDS;
        }
      }
    }
  }
  }
  return nullptr;
}

void GTW61850Client::GetPolledDataSets(std::list<GTW61850PolledDataSet*> &ppsList)
{
  GTWCollectionBase *pCollection = GetMemberCollection();
  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850PolledDataSet *pPDS = dynamic_cast<GTW61850PolledDataSet*>(pMember);
    if (pPDS)
    {
      ppsList.push_back(pPDS);
    }
  }
}

void GTW61850Client::GetPolledPointSets(std::list<GTW61850PolledPointSet*> &ppsList)
{
  GTWCollectionBase *pCollection = GetMemberCollection();
  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850PolledPointSet *pPPS = dynamic_cast<GTW61850PolledPointSet*>(pMember);
    if (pPPS)
    {
      ppsList.push_back(pPPS);
    }
  }
}


//tmw61850::DataSet *GTW61850Client::GetPolledPointSet(CStdString ds)
//{
//  const tmw::Array<tmw61850::DataSet*> *dataSets = m_p61850ClientConnection->GetDataSets();
//
//  if (dataSets != nullptr)
//  {
//    for (UINT i=0;i<dataSets->getSize();i++)
//    {
//      DataSet *pDS = dataSets->getAt(i);
//      tmw::String fullName;
//      CStdString dsStr = pDS->GetFullName(fullName);
//      if (dsStr == ds)
//      {
//        return pDS;
//      }
//    }
//  }
//  return nullptr;
//}

bool GTW61850Client::ResetDataAttrMdo(
  GTW61850ControlBlock *p,
  GTW61850DataAttributeMDO *p61850Mdo)
{
  CStdString valueDaName = p61850Mdo->getValueTagName();
  CStdString qualityDaName = p61850Mdo->getQualityTagName();
  CStdString timeDaName = p61850Mdo->getTimeTagName();
  if (!p || dynamic_cast<GTW61850WritablePointSet*>(p))
  {
    return SetDataAttrMdo(valueDaName, qualityDaName, timeDaName, p61850Mdo);
  }
  if (!p || dynamic_cast<GTW61850CommandPointSet *>(p))
  {
    return SetDataAttrMdo(valueDaName, qualityDaName, timeDaName, p61850Mdo);
  }
  if (dynamic_cast<GTW61850GOOSEControlBlock*>(p))
  {
    return SetDataAttrMdo(valueDaName, qualityDaName, timeDaName, p61850Mdo, dynamic_cast<GTW61850GOOSEControlBlock*>(p)->GetGOOSEControl()  ? dynamic_cast<GTW61850GOOSEControlBlock *>(p)->GetGOOSEControl()->GetDataSet() : nullptr);
  }
  if (dynamic_cast<GTW61850PolledDataSet*>(p))
  {
    return SetDataAttrMdo(valueDaName, qualityDaName, timeDaName, p61850Mdo, dynamic_cast<GTW61850PolledDataSet*>(p)->GetDataSetControl());
  }
  if (dynamic_cast<GTW61850PolledPointSet*>(p))
  {
    return SetDataAttrMdo(valueDaName, qualityDaName, timeDaName, p61850Mdo, dynamic_cast<GTW61850PolledPointSet*>(p));
  }
  if (dynamic_cast<GTW61850ReportControlBlock*>(p))
  {
    return SetDataAttrMdo(valueDaName, qualityDaName, timeDaName, p61850Mdo, dynamic_cast<GTW61850ReportControlBlock *>(p)->GetReportControl() ? dynamic_cast<GTW61850ReportControlBlock*>(p)->GetReportControl()->GetDataSet() : nullptr);
  }
  return false;
}

bool GTW61850Client::IsModelPointWriteable(const CStdString &path)
{
  tmw61850::DataAttribute *pAttr = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(path.c_str(), m_p61850ClientConnection->Model());
  if (!pAttr)
  {
    return false;
  }

  const char* sFunctionalConstraint = pAttr->GetFC();
  return tmw::util::compareNoCase(sFunctionalConstraint, "sp") ||
    tmw::util::compareNoCase(sFunctionalConstraint, "cf") ||
    tmw::util::compareNoCase(sFunctionalConstraint, "sv");
}

bool GTW61850Client::SetDataAttrMdo(
                                    const CStdString &valueDaName, 
                                    const CStdString &qualityDaName, 
                                    const CStdString &timeDaName, 
                                    GTW61850DataAttributeMDO *p61850Mdo)
{
  //if (!this->m_p61850ClientConnection->IsConnectionAliveAndReady())
  //  return true;

  if (m_p61850ClientConnection == nullptr)
  {
    return false;
  }
  
  if (p61850Mdo->isWriteableNonControl()) // then a writeable non control point
  {
    String sClientName = (const char*)this->GetFullName();
    sClientName.append(".");
    CStdString modelValuePath = valueDaName;
    CStdString modelQualityPath = qualityDaName;
    CStdString modelTimePath = timeDaName;

    if (valueDaName.Find(sClientName) == 0)
    {
      modelValuePath = valueDaName.Mid(sClientName.length());
    }
    if (qualityDaName.Find(sClientName) == 0)
    {
      modelQualityPath = qualityDaName.Mid(sClientName.length());
    }
    if (timeDaName.Find(sClientName) == 0)
    {
      modelTimePath = timeDaName.Mid(sClientName.length());
    }

    p61850Mdo->m_pValueDataAttribute = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(modelValuePath, m_p61850ClientConnection->Model());
    p61850Mdo->m_pQualityDataAttribute = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(modelQualityPath, m_p61850ClientConnection->Model());
    p61850Mdo->m_pTimeDataAttribute = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(modelTimePath, m_p61850ClientConnection->Model());

    if (!p61850Mdo->m_pValueDataAttribute) // require a valid value path for writable data attributes
    {
      return false;
    }

    assert(p61850Mdo->m_pValueDataAttribute);
    p61850Mdo->m_pValueDataAttribute->AddUserDataArray(p61850Mdo);

    return (valueDaName.GetLength() == 0 || (valueDaName.GetLength() > 0 && p61850Mdo->m_pValueDataAttribute != nullptr)) &&
            (qualityDaName.GetLength() == 0 || (qualityDaName.GetLength() > 0 && p61850Mdo->m_pQualityDataAttribute != nullptr)) &&
            (timeDaName.GetLength() == 0 || (timeDaName.GetLength() > 0 && p61850Mdo->m_pTimeDataAttribute != nullptr));
  }

  const Array<tmw61850::ClientControlPoint*>* pControls = m_p61850ClientConnection->GetControls();
  if (pControls != nullptr)
  {
    p61850Mdo->m_pValueDataAttribute   = nullptr;
    p61850Mdo->m_pTimeDataAttribute    = nullptr;
    p61850Mdo->m_pQualityDataAttribute = nullptr;

    for (unsigned int i = 0; i < pControls->getSize(); i++)
    {
      tmw61850::ClientControlPoint *pCinfo = pControls->getAt(i);

      if (pCinfo->GetControlNode() != nullptr)
      {
        CStdString ctrlName = "";
        tmw::String fullName;
        ctrlName = pCinfo->GetControlNode()->GetFullName(fullName);
        //ctrlName = GetFullName() + ".commands." + ctrlName;
        ctrlName = GetFullName() + "." + ctrlName;

        if (ctrlName == valueDaName || ctrlName == (GetFullName() + "." + valueDaName))
        {
          pCinfo->GetControlNode()->AddUserDataArray((void*)p61850Mdo);
          p61850Mdo->m_pValueDataAttribute = nullptr;// da;
          p61850Mdo->m_pControlInfo = pCinfo;
          p61850Mdo->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED); // call this to at least update quality and anything mapped to it
          return true; // since no setting time and quality
        }
      }
    }
  }
  return false;
}

bool GTW61850Client::SetDataAttrMdo(
                                    const CStdString &valueDaName, 
                                    const CStdString &qualityDaName, 
                                    const CStdString &timeDaName, 
                                    GTW61850DataAttributeMDO *p61850Mdo, 
                                    tmw61850::DataSet *pDS)
{
  if (!this->m_p61850ClientConnection->IsConnectionAliveAndReady())
    return true;

  if (pDS)
  {
    p61850Mdo->m_pValueDataAttribute   = nullptr;
    p61850Mdo->m_pTimeDataAttribute    = nullptr;
    p61850Mdo->m_pQualityDataAttribute = nullptr;

    Array<tmw61850::DataAttribute*> *dsPoints = m_p61850ClientConnection->Model()->GetDSPoints(pDS);
    for (unsigned int i = 0; i < dsPoints->size(); i++)
    {
      tmw61850::DataAttribute *pDA = dsPoints->getAt(i);
      tmw::String fullName;
      CStdString cFullName = pDA->GetFullName(fullName);
      if (
        this->GetFullName() + "." + cFullName == valueDaName
        || cFullName == valueDaName
        )
      {
        pDA->AddUserDataArray((void*)p61850Mdo);
        pDA->IncrementPointChangeNotification();
        p61850Mdo->m_pValueDataAttribute = pDA;
        p61850Mdo->SetValue(pDA->GetValue());
      }
      if (cFullName == qualityDaName)
      {
        pDA->AddUserDataArray((void*)p61850Mdo);
        pDA->IncrementPointChangeNotification();
        p61850Mdo->m_pQualityDataAttribute = pDA;
        p61850Mdo->SetQuality(pDA->GetValue());
      }
      if (cFullName == timeDaName)
      {
        pDA->AddUserDataArray((void*)p61850Mdo);
        pDA->IncrementPointChangeNotification();
        p61850Mdo->m_pTimeDataAttribute = pDA;
        p61850Mdo->SetTime(pDA->GetValue());
      }
      if (p61850Mdo->m_pValueDataAttribute != nullptr &&
            p61850Mdo->m_pTimeDataAttribute != nullptr &&
            p61850Mdo->m_pQualityDataAttribute != nullptr)
      {
        break;
      }
    }
    return (p61850Mdo->m_pValueDataAttribute != nullptr ||
            p61850Mdo->m_pTimeDataAttribute != nullptr ||
            p61850Mdo->m_pQualityDataAttribute != nullptr);
  }
  return false;
}

int compare( const void *arg1, const void *arg2 )
{
  tmw61850::DataAttribute *a1 = (tmw61850::DataAttribute *)arg1;
  tmw61850::DataAttribute *a2 = (tmw61850::DataAttribute *)arg2;
  String a1Name;
  String a2Name;
  a1->GetFullName(a1Name);
  a2->GetFullName(a2Name);

  /* Compare all of both strings: */
  return _stricmp( a1Name, a2Name );
}


tmw61850::DataAttribute *binary_search(tmw::Array<tmw61850::DataAttribute*> *A, String key, int imin, int imax)
{
  // test if array is empty
  if (imax < imin)
    // set is empty, so return value showing not found
    return nullptr;
  else
  {
    // calculate midpoint to cut set in half
    int imid = (imin + imax) / 2;

    // three-way comparison
    String name;
    tmw61850::DataAttribute *pDa = A->getAt(imid);
    pDa->GetFullName(name);
    if (key.lessThanNoCase(name))
      // key is in lower subset
      return binary_search(A, key, imin, imid-1);
    else if (name.lessThanNoCase(key))
      // key is in upper subset
      return binary_search(A, key, imid+1, imax);
    else
    // key has been found
    return pDa;
  }
}

bool GTW61850Client::SetDataAttrMdo(
                                    const CStdString &valueDaName, 
                                    const CStdString &qualityDaName, 
                                    const CStdString &timeDaName, 
                                    GTW61850DataAttributeMDO *p61850Mdo, 
                                    GTW61850PolledPointSet *pPS)
{
  if (!this->m_p61850ClientConnection->IsConnectionAliveAndReady())
    return true;

  bool bvOK = valueDaName != "" ? false : true;
  bool bqOK = qualityDaName != "" ? false : true;
  bool btOK = timeDaName != "" ? false : true;

  if (pPS)
  {
    p61850Mdo->m_pValueDataAttribute   = nullptr;
    p61850Mdo->m_pTimeDataAttribute    = nullptr;
    p61850Mdo->m_pQualityDataAttribute = nullptr;

    if (bvOK == false)
    {
	    CStdString vDaName = valueDaName;
	    vDaName.Replace(this->GetFullName() + ".", "");
	    tmw61850::DataAttribute *valueDa = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(vDaName, m_p61850ClientConnection->Model());
	    if (valueDa != nullptr)
	    {
	      valueDa->AddUserDataArray((void*)p61850Mdo);
	      valueDa->IncrementPointChangeNotification();
	      p61850Mdo->m_pValueDataAttribute = valueDa;
	      p61850Mdo->SetValue(valueDa->GetValue());
	      bvOK = true;
	    }
    }

    if (bqOK == false)
    {
	    CStdString qDaName = qualityDaName;
	    qDaName.Replace(this->GetFullName() + ".", "");
	    tmw61850::DataAttribute *qualityDa = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(qDaName, m_p61850ClientConnection->Model());
	    if (qualityDa != nullptr)
	    {
	      qualityDa->AddUserDataArray((void*)p61850Mdo);
	      qualityDa->IncrementPointChangeNotification();
	      p61850Mdo->m_pQualityDataAttribute = qualityDa;
	      p61850Mdo->SetQuality(qualityDa->GetValue());
	      bqOK = true;
	    }
    }

    if (btOK == false)
    {
	    CStdString tDaName = timeDaName;
	    tDaName.Replace(this->GetFullName() + ".", "");
	    tmw61850::DataAttribute *timeDa = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(tDaName, m_p61850ClientConnection->Model());
	    if (timeDa != nullptr)
	    {
	      timeDa->AddUserDataArray((void*)p61850Mdo);
	      timeDa->IncrementPointChangeNotification();
	      p61850Mdo->m_pTimeDataAttribute = timeDa;
	      p61850Mdo->SetTime(timeDa->GetValue());
	      btOK = true;
	    }
    }
  }
  return bvOK && bqOK && btOK;
}

void GTW61850Client::EnablePolledDataSets()
{
  std::list<GTW61850PolledDataSet*> pdsList;
  GetPolledDataSets(pdsList);

  for (auto it = pdsList.begin(); it != pdsList.end(); ++it)
  {
    (*it)->Enable();
  }
}

void GTW61850Client::SubscribeGooseControlBlocks()
{
  std::list<GTW61850GOOSEControlBlock*> gcbList;
  GetGooseControlBlocks(gcbList);

  for (auto it = gcbList.begin(); it != gcbList.end(); ++it)
  {
    (*it)->Enable();
  }
}

void GTW61850Client::LoadGooseControlBlocks(TMWTYPES_BOOL bValidate)
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    bool bFoundGoose = false;
    // add report control blocks
    for (int iGCB =0;iGCB< getGTKTPARM_MAX_NUM_I61850_GOOSES_PER_CLIENT();iGCB++)
    {
      CStdString gcb_name = GTWConfig::I61850GOOSEControlBlockName(m_obIndex,iGCB);
      if (gcb_name == "" || gcb_name == "\"\"")
        continue;

      if 
        (
        gcb_name.Find("$GO$") != -1
        || gcb_name.Find("$GS$") != -1
        )
      {
        gcb_name.Replace("$GO$", "$");
        gcb_name.Replace("$GS$", "$");
        
        GTWConfig::I61850GOOSEControlBlockName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_GOOSE_INDEX((TMWTYPES_USHORT)m_obIndex,(TMWTYPES_USHORT)iGCB)] = ((const char*)gcb_name);      
      }

      if (bValidate == true)
      {
        const Array<tmw61850::GOOSEControl*> *gooseControlBlocks = this->GetClientConnection()->Model()->GetGOOSEControlBlocks();

        for (unsigned int i=0;i<gooseControlBlocks->getSize();i++)
        {
          tmw61850::GOOSEControl *pGOOSE = gooseControlBlocks->getAt(i);
          tmw::String fullName;
          CStdString gooseStr = pGOOSE->GetFullName(fullName);
          if (gooseStr == gcb_name)
          {
            GTW61850GOOSEControlBlock       *p61850GOOSE;
            GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850GOOSE(this, gooseStr, &p61850GOOSE,iGCB);
            if (status  != GTWDEFS_STAT_SUCCESS)
            {
              LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Error: Could not add \"%s\" GOOSE control block.", (const char *)gcb_name);
            }
            else
            {
              LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" GOOSE control block.", (const char *)gcb_name);
              p61850GOOSE->SetGOOSE(this->GetGOOSEControl(gcb_name)); // subscribes to goose
              bFoundGoose = true;
            }
          }
          else
          {
            if (gcb_name != "")
            {
              LOG6(GetClientConnection(), GtwLogger::Severity_Warning, GtwLogger::SDG_Category_61850, "Message: Did not add \"%s\" GOOSE control block.", (const char *)gooseStr);
            }
          }
        }
      }
      else
      {
        GTW61850GOOSEControlBlock       *p61850GOOSE;
        GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850GOOSE(this, gcb_name, &p61850GOOSE,iGCB);
        if (status  != GTWDEFS_STAT_SUCCESS)
        {
          LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not add \"%s\" GOOSE control block.", (const char *)gcb_name);
        }
        else
        {
          LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" GOOSE control block.", (const char *)gcb_name);
          p61850GOOSE->SetGOOSE(this->GetGOOSEControl(gcb_name)); // subscribes to goose
          bFoundGoose = true;
        }
      }

      if (bFoundGoose)
      {
        m_GooseQueueThread.Start();
      }
    }
  }
}

void GTW61850Client::GetGooseControlBlocks(list<GTW61850GOOSEControlBlock*> &gcbList)
{
  GTWCollectionBase *pCollection = GetMemberCollection();
  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
      {
    GTWCollectionMember *pMember = pos->second;
    GTW61850GOOSEControlBlock *pGCB = dynamic_cast<GTW61850GOOSEControlBlock*>(pMember);
    if (pGCB)
      {
      gcbList.push_back(pGCB);
        }
        }
      }

unsigned int GTW61850Client::GetGooseTimeouts()
{
  list<GTW61850GOOSEControlBlock*> gcbList;
  GetGooseControlBlocks(gcbList);
  
  unsigned int total = 0;
  for (list<GTW61850GOOSEControlBlock*>::iterator iter = gcbList.begin(); iter != gcbList.end(); ++iter)
  {
    GTW61850GOOSEControlBlock* pGCB = *iter;
    total += pGCB->GetGOOSEControl()->GetNumTimeouts();
  }

  return total;
}

unsigned int GTW61850Client::GetGooseDrops()
{
  list<GTW61850GOOSEControlBlock*> gcbList;
  GetGooseControlBlocks(gcbList);

  unsigned int total = 0;
  for (list<GTW61850GOOSEControlBlock*>::iterator iter = gcbList.begin(); iter != gcbList.end(); ++iter)
  {
    GTW61850GOOSEControlBlock* pGCB = *iter;
    total += pGCB->GetGOOSEControl()->GetNumStateDrops();
  }

  return total;
}

void GTW61850Client::ResetGooseTimeouts()
{
  list<GTW61850GOOSEControlBlock*> gcbList;
  GetGooseControlBlocks(gcbList);

  for (list<GTW61850GOOSEControlBlock*>::iterator iter = gcbList.begin(); iter != gcbList.end(); ++iter)
  {
    GTW61850GOOSEControlBlock* pGCB = *iter;
    pGCB->GetGOOSEControl()->ResetNumTimeouts();
  }
}

/*
bool GTW61850Client::CreateAndEnableReport(GTW61850ReportControlBlock *p61850Report, const CStdString &rcb_name, int iRCB, bool bInitialConnect,
                                            bool bReConnect)
{
  GTWDEFS_STAT status = GTWDEFS_STAT_SUCCESS;
  if (!p61850Report)
  {
    status = GetGTWApp()->GetPointMap()->Create61850Report(this, rcb_name, &p61850Report,iRCB);
  }

  if (status != GTWDEFS_STAT_SUCCESS)
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Error: Could not create '%s' RCB for 61850 client: %s.", (const char *)rcb_name, (const char *)this->GetAliasName());
  }
  else if (status  == GTWDEFS_STAT_SUCCESS && !p61850Report->IsA("GTW61850ReportControlBlock"))
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Error: Could not create '%s' RCB for 61850 client: %s (is not a report control block)", (const char *)rcb_name, (const char *)this->GetAliasName());
  }
  else
  {
    if (GTWConfig::getIdAuxMask() & TMWDIAG_ID_61850)
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Created \"%s\" Report for 61850 client: %s.", (const char *)rcb_name, (const char *)this->GetAliasName());
    }
          
    tmw61850::ReportControl *pRC = this->GetReportControl(rcb_name);
    if (pRC)
    {
      bool bDidPurge = false;
      p61850Report->SetReport(pRC);
      if (tmw61850::Node::IsKindOf<tmw61850::ReportControlBuffered>(pRC))
      {
        if (bInitialConnect && p61850Report->PurgeBefore1stEnable() && m_bInitialConnect)
        {
          p61850Report->DoPURGE();
          bDidPurge = true;
        }
        if (bReConnect && p61850Report->PurgeBeforeEnableOnReconnect() && m_bDisconnected)
        {
          p61850Report->DoPURGE();
          bDidPurge = true;
        }
      }

      p61850Report->EnableReport(bDidPurge);
      //m_bDisconnected = false;
    }
    return true;
  }
  return false;
}      

void GTW61850Client::ConnectReports(
  TMWTYPES_BOOL bValidate,
  TMWTYPES_BOOL bInitialConnect,
  TMWTYPES_BOOL bReConnect,
  bool bEnable
  )
{
  //if (GetUseSclFile() == true)
  //  return;
  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add report control blocks
    for (int iRCB =0;iRCB< getGTKTPARM_MAX_NUM_I61850_REPORTS_PER_CLIENT();iRCB++)
    {
      CStdString rcb_name = GTWConfig::I61850ReportControlBlockName(m_obIndex,iRCB);
      if (rcb_name == "" || rcb_name == "\"\"")
        continue;

      if (rcb_name.Find("$BR$") != -1 || rcb_name.Find("$RP$") != -1)
      {
        // Replace FC in path
        rcb_name.Replace("$BR$", "$");
        rcb_name.Replace("$RP$", "$");

        GTWConfig::I61850ReportControlBlockName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_REPORT_INDEX((TMWTYPES_USHORT)m_obIndex,(TMWTYPES_USHORT)iRCB)] = ((const char*)rcb_name);      
      }

      if (bValidate)
      {
        const Array<tmw61850::ReportControl*> *reportControlBlocks = GetClientConnection()->Model()->GetReportControlBlocks();
        if (reportControlBlocks == nullptr)
          return;

        for (UINT i = 0; i < reportControlBlocks->getSize(); i++)
        {
          tmw61850::ReportControl *pRPT = reportControlBlocks->getAt(i);
          tmw::String fullName;
          CStdString rptStr = pRPT->GetFullName(fullName);
          if (rptStr == rcb_name || (rptStr + "01") == rcb_name || rptStr == (rcb_name + "01"))
          {
            GTW61850ReportControlBlock *p61850Report = nullptr;
            p61850Report = this->get61850report(this->GetAliasName() + "." + rptStr);
            CreateAndEnableReport(p61850Report, rcb_name, iRCB, bInitialConnect, bReConnect, bEnable);
            break;
          }
        }
      }
      else
      {
        CreateAndEnableReport(nullptr, rcb_name, iRCB, bInitialConnect, bReConnect, bEnable);
      }
    }
  }
}
*/

void GTW61850Client::EnableAllReports()
{
//#ifdef _WIN32
//  CWaitCursor wait;
//#endif

  EnableReports(false);
}

void GTW61850Client::EnableReports(TMWTYPES_BOOL bReConnected)
{
  //if (GetUseSclFile() == true)
  //  return;
  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    bool bFoundReport = false;
    // add report control blocks
    for (int iRCB =0;iRCB< getGTKTPARM_MAX_NUM_I61850_REPORTS_PER_CLIENT();iRCB++)
    {
      CStdString rcb_name = GTWConfig::I61850ReportControlBlockName(m_obIndex,iRCB);
      if (rcb_name == "" || rcb_name == "\"\"")
        continue;

      if (rcb_name.Find("$BR$") != -1 || rcb_name.Find("$RP$") != -1)
      {
        // Replace FC in path
        rcb_name.Replace("$BR$", "$");
        rcb_name.Replace("$RP$", "$");

        GTWConfig::I61850ReportControlBlockName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_REPORT_INDEX((TMWTYPES_USHORT)m_obIndex,(TMWTYPES_USHORT)iRCB)] = ((const char*)rcb_name);      
      }

      const Array<tmw61850::ReportControl*> *reportControlBlocks = GetClientConnection()->Model()->GetReportControlBlocks();
      if (reportControlBlocks == nullptr)
        return;

      for (unsigned int i = 0; i < reportControlBlocks->getSize(); i++)
      {
        tmw61850::ReportControl *pRPT = reportControlBlocks->getAt(i);
        tmw::String fullName;
        CStdString rptStr = pRPT->GetFullName(fullName);
        if (rptStr == rcb_name || (rptStr + "01") == rcb_name || rptStr == (rcb_name + "01"))
        {
          GTW61850ReportControlBlock *p61850Report = this->get61850report(this->GetAliasName() + "." + rptStr);
          assert(p61850Report);
          if (p61850Report)
          {
            bFoundReport = true;
            if (bReConnected)
            {
              p61850Report->ResetEnabledReport();
            }

            if (!EnableReport(p61850Report, m_bInitialConnect, bReConnected))
            {
              p61850Report->StartRetryThread();
            }
          }
          break;
        }
      }
    }
    if (bFoundReport)
    {
      m_ReportQueueThread.Start();
    }
  }
}

void GTW61850Client::PreEnableReports()
{
  //if (GetUseSclFile() == true)
  //  return;
  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    bool bFoundReport = false;
    // add report control blocks
    for (int iRCB = 0; iRCB< getGTKTPARM_MAX_NUM_I61850_REPORTS_PER_CLIENT(); iRCB++)
    {
      CStdString rcb_name = GTWConfig::I61850ReportControlBlockName(m_obIndex, iRCB);
      if (rcb_name == "" || rcb_name == "\"\"")
        continue;

      if (rcb_name.Find("$BR$") != -1 || rcb_name.Find("$RP$") != -1)
      {
        // Replace FC in path
        rcb_name.Replace("$BR$", "$");
        rcb_name.Replace("$RP$", "$");

        GTWConfig::I61850ReportControlBlockName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_REPORT_INDEX((TMWTYPES_USHORT)m_obIndex, (TMWTYPES_USHORT)iRCB)] = ((const char*)rcb_name);
      }

      const Array<tmw61850::ReportControl*> *reportControlBlocks = GetClientConnection()->Model()->GetReportControlBlocks();
      if (reportControlBlocks == nullptr)
        return;

      for (unsigned int i = 0; i < reportControlBlocks->getSize(); i++)
      {
        tmw61850::ReportControl *pRPT = reportControlBlocks->getAt(i);
        tmw::String fullName;
        CStdString rptStr = pRPT->GetFullName(fullName);
        if (rptStr == rcb_name || (rptStr + "01") == rcb_name || rptStr == (rcb_name + "01"))
        {
          GTW61850ReportControlBlock *p61850Report = this->get61850report(this->GetAliasName() + "." + rptStr);
          assert(p61850Report);
          if (p61850Report)
          {
            p61850Report->InitializeDataSet();
          }
          break;
        }
      }
    }
  }
}

bool GTW61850Client::EnableReport(GTW61850ReportControlBlock *p61850Report, bool bInitialConnect, bool bReConnected)
{
  GTWDEFS_STAT status = GTWDEFS_STAT_SUCCESS;
  assert(p61850Report);
          
  tmw61850::ReportControl *pRC = p61850Report->GetReportControl();
  if (!pRC)
  {
    CStdString sName = GTWConfig::I61850ReportControlBlockName(p61850Report->Get61850Client()->Get61850ClientIndex(), p61850Report->GetBlockIndex());
    pRC = p61850Report->Get61850Client()->GetReportControl(sName);
    p61850Report->SetReport(pRC);
  }

  if (pRC)
  {
    bool bDidPurge = false;
    if (p61850Report->IsBuffered())
    {
      if (bInitialConnect && p61850Report->PurgeBefore1stEnable())
      {
        p61850Report->DoPURGE();
        bDidPurge = true;
      }
      if (bReConnected && p61850Report->PurgeBeforeEnableOnReconnect())
      {
        p61850Report->DoPURGE();
        bDidPurge = true;
      }
    }
    return p61850Report->EnableReport(bDidPurge);
  }
  return false;
}                     

void GTW61850Client::LoadReports(TMWTYPES_BOOL bValidate)
{
  bool bFoundReport = false;

  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add report control blocks
    for (int iRCB =0;iRCB< getGTKTPARM_MAX_NUM_I61850_REPORTS_PER_CLIENT();iRCB++)
    {
      CStdString rcb_name = GTWConfig::I61850ReportControlBlockName(m_obIndex,iRCB);
      if (rcb_name == "" || rcb_name == "\"\"")
        continue;

      bFoundReport = true;
      if (rcb_name.Find("$BR$") != -1 || rcb_name.Find("$RP$") != -1)
      {
        // Replace FC in path
        rcb_name.Replace("$BR$", "$");
        rcb_name.Replace("$RP$", "$");

        GTWConfig::I61850ReportControlBlockName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_REPORT_INDEX((TMWTYPES_USHORT)m_obIndex,(TMWTYPES_USHORT)iRCB)] = ((const char*)rcb_name);      
      }

      if (bValidate)
      {
        const Array<tmw61850::ReportControl*> *reportControlBlocks = GetClientConnection()->Model()->GetReportControlBlocks();
        if (reportControlBlocks == nullptr)
          return;

        for (unsigned int i = 0; i < reportControlBlocks->getSize(); i++)
        {
          tmw61850::ReportControl *pRPT = reportControlBlocks->getAt(i);
          tmw::String fullName;
          CStdString rptStr = pRPT->GetFullName(fullName);
          if (rptStr == rcb_name || (rptStr + "01") == rcb_name || rptStr == (rcb_name + "01"))
          {
            GTW61850ReportControlBlock *p61850Report = nullptr;
            p61850Report = get61850report(GetMemberName() + "." + rptStr);
            LoadReport(p61850Report, rcb_name, iRCB);
            break;
          }
        }
      }
      else
      {
        LoadReport(nullptr, rcb_name, iRCB);
      }
    }
  }

  if (bFoundReport)
  {
    m_ReportQueueThread.Start();
  }
}

GTW61850ReportControlBlock* GTW61850Client::Create61850Report(const char *sRCBName, TMWTYPES_USHORT index)
{
  std::string sFullName = GetMemberName() + "." + sRCBName;
  GTW61850ReportControlBlock *pGtw61850Report = get61850report(sFullName.c_str());

  if (pGtw61850Report)
  {
    return pGtw61850Report;
  }

  pGtw61850Report = Add61850Report(sRCBName,index);
  if (!pGtw61850Report)
  {
    return nullptr;
  }
  GTWCollectionBase *psClctn = GetMemberCollection();

  psClctn->InsertCollectionMember(pGtw61850Report);

  return pGtw61850Report;
}

bool GTW61850Client::LoadReport(GTW61850ReportControlBlock *p61850Report, const CStdString &rcb_name, int iRCB)
{
  GTWDEFS_STAT status = GTWDEFS_STAT_SUCCESS;
  if (!p61850Report)
  {
    p61850Report = Create61850Report(rcb_name.c_str(), iRCB);
    //status = GetGTWApp()->GetPointMap()->Create61850Report(this, rcb_name, &p61850Report,iRCB);
  }

  if (status != GTWDEFS_STAT_SUCCESS)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Error: Could not create '%s' RCB.", (const char *)rcb_name);
  }
  else if (status  == GTWDEFS_STAT_SUCCESS && !p61850Report->IsA("GTW61850ReportControlBlock"))
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Error: Could not create '%s' RCB (is not a report control block)", (const char *)rcb_name);
  }
  else
  {
//    if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
    {
      LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Created \"%s\" Report.", (const char *)rcb_name);
    }
          
    tmw61850::ReportControl *pRC = this->GetReportControl(rcb_name);
    if (pRC)
    {
      p61850Report->SetReport(pRC);
    }

    return true;
  }
  return false;
}

//void GTW61850Client::DisConnectReports()
//{
//  //if (GetUseSclFile() == true)
//  //  return;
//  // add the mdos if all is good
//  GTWCollectionBase *psClctn = this->GetMemberCollection();
//  if (psClctn)
//  {
//    // add report control blocks
//    for (int iRCB =0;iRCB< getGTKTPARM_MAX_NUM_I61850_REPORTS_PER_CLIENT();iRCB++)
//    {
//      CStdString rcb_name = GTWConfig::I61850ReportControlBlockName(m_obIndex,iRCB);
//      if 
//        (
//        rcb_name.Find("$BR$") != -1
//        || rcb_name.Find("$RP$") != -1
//        )
//      {
//        // Replace FC in path
//        rcb_name.Replace("$BR$", "$");
//        rcb_name.Replace("$RP$", "$");
//
//        GTWConfig::I61850ReportControlBlockName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_REPORT_INDEX((TMWTYPES_USHORT)m_obIndex,(TMWTYPES_USHORT)iRCB)] = ((const char*)rcb_name);      
//      }
//
//      if (rcb_name == "" || rcb_name == "\"\"")
//        continue;
//      if (GetClientConnection() != nullptr)
//      {
//        const tmw::Array<tmw61850::ReportControl*> *reportControlBlocks = GetClientConnection()->GetReportControlBlocks();
//        if (reportControlBlocks == nullptr)
//          return;
//
//        for (UINT i=0;i<reportControlBlocks->getSize();i++)
//        {
//          ReportControl *pRPT = reportControlBlocks->getAt(i);
//          GetClientConnection()->DisableReportBlock(pRPT);
//        }
//      }
//    }
//  }
//}

/*
void GTW61850Client::ConnectPolledDataSets(bool bValidate)
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add polled data sets
    for (int iDS =0;iDS< getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT();iDS++)
    {
      CStdString ds_name = GTWConfig::I61850PolledDataSetName(m_obIndex,iDS);

      if (ds_name.Find("$DS$") != -1)
      {
        ds_name.Replace("$DS$", "$");
        GTWConfig::I61850PolledDataSetName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_POLLED_DATA_SET_INDEX((TMWTYPES_USHORT)m_obIndex,(TMWTYPES_USHORT)iDS)] = ((const char*)ds_name);      
      }

      UINT ds_period = GTWConfig::I61850PolledDataSetPeriod(m_obIndex,iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;
      if (bValidate == true)
      {
        const tmw::Array<tmw61850::DataSet*> *dataSets = this->GetClientConnection()->Model()->GetDataSets();

        if (dataSets == nullptr)
          return;

        bool bDSCreated = false;

        for (UINT i=0;i<dataSets->getSize();i++)
        {
          tmw61850::DataSet *pDS = dataSets->getAt(i);
          tmw::String fullName;
          CStdString dsStr = pDS->GetFullName(fullName);
          if (dsStr == ds_name)
          {
            GTW61850PolledDataSet       *p61850PolledDataSet;
            GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledDataSet(this, dsStr, ds_period, &p61850PolledDataSet,iDS);
            if (status  != GTWDEFS_STAT_SUCCESS)
            {
              LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Error: Could not add \"%s\" Polled Data Set on 61850 client: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
            }
            else
            {
              if (GTWConfig::getIdAuxMask() & TMWDIAG_ID_61850)
              {
                LOG(GtwLogger::Debug, GtwLogger::SDG_Category_61850, nullptr, "Message: Added \"%s\" Polled Data Set on 61850 client: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
              }
              p61850PolledDataSet->SetPolledDataSet(this->GetPolledDataSet(ds_name), GetClientConnection());
              bDSCreated = true;
            }
          }
        }
        if (bDSCreated == false)
        {
          GTW61850PolledDataSet       *p61850PolledDataSet;
          GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledDataSet(this, ds_name, ds_period, &p61850PolledDataSet,iDS);
          if (status  != GTWDEFS_STAT_SUCCESS)
          {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Error: Could not add \"%s\" Polled Data Set on 61850 client: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
          }
          else
          {
            if (GTWConfig::getIdAuxMask() & TMWDIAG_ID_61850)
            {
              LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "Message: Added \"%s\" Polled Data Set on 61850 client: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
            }
            tmw61850::DataSet *pDS = this->GetPolledDataSet(ds_name);
            if (pDS != nullptr)
            {
              p61850PolledDataSet->SetPolledDataSet(pDS, GetClientConnection());
            }
            else
            {
              trace61850ErrorFmt("Failed to setup internal dataset for PolledDataSet '%s'", (const char *)ds_name);
            }
          }
        }
      }
      else
      {
        GTW61850PolledDataSet       *p61850PolledDataSet;
        GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledDataSet(this, ds_name, ds_period, &p61850PolledDataSet,iDS);
        if (status  != GTWDEFS_STAT_SUCCESS)
        {
          LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Could not add \"%s\" Polled Data Set on 61850 client: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
        }
        else
        {
          if (GTWConfig::getIdAuxMask() & TMWDIAG_ID_61850)
          {
            LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "Message: Added \"%s\" Polled Data Set on 61850 client: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
          }
          p61850PolledDataSet->SetPolledDataSet(this->GetPolledDataSet(ds_name), GetClientConnection());
        }
      }
    }
  }
}

/*
void GTW61850Client::ConnectPolledPointSets(bool bEnable)
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add Polled Point Sets
    for (int iDS =0;iDS< getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT();iDS++)
    {
      CStdString ds_name = GTWConfig::I61850PolledPointSetName(m_obIndex,iDS);
      UINT ds_period = GTWConfig::I61850PolledPointSetPeriod(m_obIndex,iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;

      GTW61850PolledPointSet       *p61850PolledPointSet;
      GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledPointSet(this, ds_name, ds_period, &p61850PolledPointSet,iDS);
      if (status  != GTWDEFS_STAT_SUCCESS)
      {
        LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Could not add \"%s\" Polled Point Set on 61850 server: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
      }
      else
      {
        if (GTWConfig::getIdAuxMask() & TMWDIAG_ID_61850)
        {
          LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "Message: Added \"%s\" Polled Point Set on 61850 server: %s.", (const char *)ds_name, (const char *)this->GetAliasName());
        }
        if (bEnable)
        {
          p61850PolledPointSet->Enable();//SetPolledPointSet(GetClientConnection());
        }
      }
    }
  }
}
*/

void GTW61850Client::LoadPolledDataSets(TMWTYPES_BOOL bValidate)
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add polled data sets
    for (int iDS =0;iDS< getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT();iDS++)
    {
      CStdString ds_name = GTWConfig::I61850PolledDataSetName(m_obIndex,iDS);

      if (ds_name.Find("$DS$") != -1)
      {
        ds_name.Replace("$DS$", "$");
        GTWConfig::I61850PolledDataSetName[(TMWTYPES_USHORT)GTKTPARM_I61850_CLIENT_POLLED_DATA_SET_INDEX((TMWTYPES_USHORT)m_obIndex,(TMWTYPES_USHORT)iDS)] = ((const char*)ds_name);      
      }

      TMWTYPES_UINT ds_period = GTWConfig::I61850PolledDataSetPeriod(m_obIndex,iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;
      if (bValidate == true)
      {
        const tmw::Array<tmw61850::DataSet*> *dataSets = this->GetClientConnection()->Model()->GetDataSets();

        if (dataSets == nullptr)
          return;

        bool bDSCreated = false;

        for (unsigned int i=0;i<dataSets->getSize();i++)
        {
          tmw61850::DataSet *pDS = dataSets->getAt(i);
          tmw::String fullName;
          CStdString dsStr = pDS->GetFullName(fullName);
          if (dsStr == ds_name)
          {
            GTW61850PolledDataSet       *p61850PolledDataSet;
            GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledDataSet(this, dsStr, ds_period, &p61850PolledDataSet,iDS);
            if (status  != GTWDEFS_STAT_SUCCESS)
            {
              LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Error: Could not add \"%s\" Polled Data Set.", (const char *)ds_name);
            }
            else
            {
//              if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
              {
                LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" Polled Data Set.", (const char *)ds_name);
              }
              p61850PolledDataSet->SetPolledDataSet(this->GetPolledDataSet(ds_name), GetClientConnection());
              bDSCreated = true;
            }
          }
        }
        if (bDSCreated == false)
        {
          GTW61850PolledDataSet       *p61850PolledDataSet;
          GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledDataSet(this, ds_name, ds_period, &p61850PolledDataSet,iDS);
          if (status  != GTWDEFS_STAT_SUCCESS)
          {
            LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Error: Could not add \"%s\" Polled Data Set.", (const char *)ds_name);
          }
          else
          {
//            if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
            {
              LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" Polled Data Set.", (const char *)ds_name);
            }
            tmw61850::DataSet *pDS = this->GetPolledDataSet(ds_name);
            if (pDS != nullptr)
            {
              p61850PolledDataSet->SetPolledDataSet(pDS, GetClientConnection());
            }
            else
            {
              LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to setup internal dataset for PolledDataSet '%s'", (const char *)ds_name);
            }
          }
        }
      }
      else
      {
        GTW61850PolledDataSet       *p61850PolledDataSet;
        GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledDataSet(this, ds_name, ds_period, &p61850PolledDataSet,iDS);
        if (status  != GTWDEFS_STAT_SUCCESS)
        {
          LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not add \"%s\" Polled Data Set.", (const char *)ds_name);
        }
        else
        {
          //if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
          {
            LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" Polled Data Set.", (const char *)ds_name);
          }
          p61850PolledDataSet->SetPolledDataSet(this->GetPolledDataSet(ds_name), GetClientConnection());
        }
      }
    }
  }
}

void GTW61850Client::LoadWritablePointSets()
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // 
    for (int iDS = 0; iDS < getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT(); iDS++)
    {
      CStdString ds_name = GTWConfig::I61850WritablePointSetName(m_obIndex, iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;

      GTW61850WritablePointSet *p61850WritablePointSet;
      GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850WritablePointSet(this, ds_name, &p61850WritablePointSet, iDS);
      if (status != GTWDEFS_STAT_SUCCESS)
      {
        LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not add \"%s\" Writable Point Set.", (const char *)ds_name);
      }
      else
      {
        //        if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
        {
          LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" Writable Point Set.", (const char *)ds_name);
        }
      }
    }
  }
}

void GTW61850Client::LoadCommandPointSets()
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add Polled Point Sets
    for (int iDS = 0; iDS < getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT(); iDS++)
    {
      CStdString ds_name = GTWConfig::I61850CommandPointSetName(m_obIndex, iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;

      GTW61850CommandPointSet *p61850CommandPointSet;
      GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850CommandPointSet(this, ds_name, &p61850CommandPointSet, iDS);
      if (status != GTWDEFS_STAT_SUCCESS)
      {
        LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not add \"%s\" Command Point Set.", (const char *)ds_name);
      }
      else
      {
        //        if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
        {
          LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" Command Point Set.", (const char *)ds_name);
        }
      }
    }
  }
}

void GTW61850Client::LoadPolledPointSets()
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add Polled Point Sets
    for (int iDS =0;iDS< getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT();iDS++)
    {
      CStdString ds_name = GTWConfig::I61850PolledPointSetName(m_obIndex,iDS);
      TMWTYPES_UINT ds_period = GTWConfig::I61850PolledPointSetPeriod(m_obIndex,iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;

      GTW61850PolledPointSet       *p61850PolledPointSet;
      GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61850PolledPointSet(this, ds_name, ds_period, &p61850PolledPointSet,iDS);
      if (status  != GTWDEFS_STAT_SUCCESS)
      {
        LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not add \"%s\" Polled Point Set.", (const char *)ds_name);
      }
      else
      {
//        if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
        {
          LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" Polled Point Set.", (const char *)ds_name);
        }
      }
    }
  }
}

void GTW61850Client::EnablePolledPointSets()
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add Polled Point Sets
    for (int iDS =0;iDS< getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT();iDS++)
    {
      CStdString ds_name = GTWConfig::I61850PolledPointSetName(m_obIndex,iDS);
      TMWTYPES_UINT ds_period = GTWConfig::I61850PolledPointSetPeriod(m_obIndex,iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;

      GTW61850PolledPointSet *pPolledPointSet = get61850polledPointSet(ds_name);
      if (pPolledPointSet)
      {
        pPolledPointSet->Enable();
      }
      else
      {
        LOG6(m_p61850ClientConnection, GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850Client::EnablePolledPointSets:assert(false)");
#ifdef _WIN32
        assert(false);
#endif
      }
    }
  }
}

void GTW61850Client::DisconnectPolledPointSets()
{
  //if (GetUseSclFile() == true)
  //  return;

  // add the mdos if all is good
  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add Polled Point Sets
    for (int iDS =0;iDS< getGTKTPARM_MAX_NUM_I61850_POLLED_DATA_SETS_PER_CLIENT();iDS++)
    {
      CStdString ds_name = GTWConfig::I61850PolledPointSetName(m_obIndex,iDS);
      TMWTYPES_UINT ds_period = GTWConfig::I61850PolledPointSetPeriod(m_obIndex,iDS);
      if (ds_name == "" || ds_name == "\"\"")
        continue;

      GTW61850PolledPointSet *p61850PolledPointSet = nullptr;
  
      CStdString csName = this->GetMemberName() + "." + ds_name;
  
      GTWCollectionMember *pMember = nullptr;
      GTWDEFS_STAT status = GetGTWApp()->getMdoCollection()->findMemberUsingName(csName,&pMember);
      if (status == GTWDEFS_STAT_SUCCESS)
      {
        p61850PolledPointSet = dynamic_cast<GTW61850PolledPointSet *>(pMember);
        p61850PolledPointSet->Disconnect();
      }
    }
  }
}

void GTW61850Client::_checkConfigRev()
{
  list<string> sMismatches;

  if (!this->GetClientConnection()->Model() || !this->GetClientConnection()->Model()->Children())
  {
    return;
  }

  tmw::Array<tmw61850::Node*> *pChildren = this->GetClientConnection()->Model()->Children();
  unsigned int size = pChildren->size();

  bool bReadErrors = false;
  for (unsigned int i = 0; i < size; i++)
  {
    LogicalDevice *pLD = dynamic_cast<LogicalDevice*>(pChildren->getAt(i));
    if (pLD)
    {
      String sLocalConfigRev = pLD->GetConfigurationRevision();
      tmw61850::DataAttribute *da = i61850RootNode::FindNode<tmw61850::DataAttribute>("LLN0.NamPlt.configRev", pLD);
      if (da)
      {
        if (GetClientConnection()->ReadNodeBlocking(da) == tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
        {
          String sRemoteConfigRev;
          da->GetValueAsString(sRemoteConfigRev);
          if (sLocalConfigRev.length() == 0 || !sLocalConfigRev.equals((const char*)sRemoteConfigRev))
          {
            string s = string("Local: ") + (sLocalConfigRev.length() == 0 ? "N/A (missing)" : (const char*)sLocalConfigRev);
            s += string(", Remote: ") + (sRemoteConfigRev.length() == 0 ? "N/A (missing)" : (const char*)sRemoteConfigRev);
            sMismatches.push_back(s);
          }
        }
        else
        {
          bReadErrors = true;
        }
      }
    }
  }

  if (sMismatches.size() > 0)
  {
    string sMsg = "Warning: the following Configuration Revision values differ (or are missing) between the local and the remote for client '";
    sMsg += this->GetFullName();
    sMsg += "'";
    for (list<string>::iterator iter = sMismatches.begin(); iter != sMismatches.end(); ++iter)
    {
      sMsg += "\n\t\t";
      sMsg += *iter;   
    }
    LOG6(GetClientConnection(), GtwLogger::Severity_Warning, GtwLogger::SDG_Category_61850, "%s", sMsg.c_str());
  }
  else
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "%s", "All Configuration Revisions match");
  }
}


bool GTW61850Client::InitControlBlocksOnCreate(tmw::String &errorStack)
{
  if (!SetupSCL(errorStack))
    return false;
  
  // Load into client for csv intialization
  LoadReports(false);
  LoadPolledDataSets(false);
  LoadPolledPointSets();
  LoadCommandPointSets();
  LoadWritablePointSets();
  LoadGooseControlBlocks(false);
  Connect61400Alarms();
  return true;
}

void GTW61850Client::InitControlBlocksOnServerUp()
{
  LoadDataSets();
  // Load again in case settings have changed that require it e.g. m_bUseSclFile changes
  LoadReports(false);
  LoadPolledDataSets(false);
  LoadCommandPointSets();
  LoadWritablePointSets();
  LoadPolledPointSets();
  LoadGooseControlBlocks(GetDoValidate());
}

void GTW61850Client::DoServerUp()
{
  try
  {
    if (m_bServerUp)
    {
      LOG6(m_p61850ClientConnection, GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850Client::DoServerUP:assert(false)");
#ifdef _WIN32
      assert(false);
#endif
      return;
    }

    m_responseQueueThread.Start();

    GtwOsSyncInc(&m_nTotalClientConnected);
    GetGTWApp()->Update61850ClientOnline();

    //TRACE("Calling kill timer from DoServerUp for %s\n", this->GetFullName());
    m_pReconnectTimer->KillTimer();
    m_pUpdateStatsTimer->SetTimer(this,1000);
    //ResetAllLeafNodes();

    InitControlBlocksOnServerUp();

    // set all points UserDataArray - the mdos they belong to
    tmw61850::i61850RootNode::ClearAllUserData(this->GetClientConnection()->Model());
    PreEnableReports();
	
    ConnectGOOSEPoints();
    ConnectNonGOOSEPoints();

    InitAlarms();

    m_i61850RetryCount = 0;

    UpdateAlarmMDOs();
    
    LOG6(m_p61850ClientConnection, GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "%s", "Successfully processed SERVER UP");

    _checkConfigRev(); // always check config revision

    //if (m_bCheckConfigRevOnCreate) // then first created and first start - check ONLY this time for now
    //{
    //  m_bCheckConfigRevOnCreate = false;
    //  CheckConfigRevOnCreate();
    //}

    //TrimModel();
    m_bServerUp = true;

    // generate file if needed
    //if (m_generateSCLFile)
    if (m_eModelDefType == GTW61850Client::DISCOVER_ONCE_CREATE_FILE)
    {
      CStdString fileName;
      CStdString fullPath = GtwSysConfig::getCurrentWorkSpacePath();
      fileName = "generated_scl_";
      GtwOsDateTime dt;
      CStdString dtString;
      dt.GetDateTimeAsStringYYYYMMDD(dtString);
      fileName.append(dtString);
      fileName.append(".cid");
      fullPath.append("/");
      fullPath.append(fileName);
      XMLWriteOptions writeOptions;
      writeOptions.SetWriteEdition2(true);
      if (!this->GetClientConnection()->Save(fullPath.c_str(), this->GetClientConnection()->Model()->GetRunningIEDName(), writeOptions))
      {
        LOG6(m_p61850ClientConnection,GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "I61850: GTW61850Client::DoServerUp() Unable to generate SCL file");
      }
      else
      {
        GTWConfig::I61850LoadModelFromFileEnabled.TMWParam_mark_element_as_being_specified(Get61850ClientIndex());
        GTWConfig::I61850LoadModelFromFileEnabled[Get61850ClientIndex()] = true;
        GTWConfig::I61850SCLFileName.TMWParam_mark_element_as_being_specified(Get61850ClientIndex());
        GTWConfig::I61850SCLFileName[Get61850ClientIndex()] = fileName;  
        // if model type is discover once, we set the generated file up as the model file and change the model type to USE_FILE to use the new generatated file
        m_eModelDefType = GTW61850Client::USE_SCL_FILE;
        GTWConfig::I61850ClientModelDefType[Get61850ClientIndex()] = m_eModelDefType;

        CStdString sMsg = "NOTE: 61850 Client '";
        sMsg += GetFullName() + "'";
        sMsg += " SCL file (" + fullPath;
        sMsg += ") was successfully generated. Please save the configuration to persist the changes.";
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Information,
          GtwLogger::SDG_Category_61850, nullptr,
          "", sMsg.c_str());

        //m_bUseSclFile = true;
        //m_generateSCLFile = false; // Done!
      }
    }

    // Enable after setting up UserData Array and after setting m_bServerUp to correctly process data initialization
    EnableReports(TMWDEFS_TRUE);
    EnablePolledPointSets();
    EnablePolledDataSets();
    SubscribeGooseControlBlocks();

    m_p61850ClientActiveControlmdo->SetValue(true);

    // Specifically set the connected MDO to true at the very end of this method - so that when this MDO turns to "On" the client should be completely initialized.
    m_p61850ConnectedToServerMdo->SetValue(true);
  }
  catch (...)
  {
    LOG6(m_p61850ClientConnection, GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "I61850: GTW61850Client::DoServerUp() exception caught");
    this->Disconnect();
  }
  if (m_bInitialConnect == true)
  {
    m_bInitialConnect = false;
  }
}

void GTW61850Client::CleanupStartEndPointChangeOnServerDown()
{
  if (m_pPDSStartEndPCWorkItem)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "%s", "PDS DoServerDown generating OnEndClientPointChange workitem");
    InsertEndPointChangeWorkItem(m_pPDSStartEndPCWorkItem);
  }
  if (m_pPPSStartEndPCWorkItem)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "%s", "PPS DoServerDown generating OnEndClientPointChange workitem");
    InsertEndPointChangeWorkItem(m_pPPSStartEndPCWorkItem);
  }
  if (m_pGooseStartEndPCWorkItem)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "%s", "GOOSE DoServerDown generating OnEndClientPointChange workitem");
    InsertEndPointChangeWorkItem(m_pGooseStartEndPCWorkItem);
  }
  if (m_pRCBStartEndPCWorkItem)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "%s", "RCB DoServerDown generating OnEndClientPointChange workitem");
    InsertEndPointChangeWorkItem(m_pRCBStartEndPCWorkItem);
  }
}

bool GTW61850Client::IsUp()
{
  return m_bServerUp && GetClientConnection()->IsConnectionAliveAndReady();
}



/*
CStdString GetCurrDateTimeString()
{
  CTime currDateTime = CTime::GetCurrentTime();

  CStdString csCurrDateTime = currDateTime.Format("%d%b%Y %H.%M.%S");

  return csCurrDateTime;
}

#include <fstream>
std::ofstream debug_file("c:\\debug_61850_motherwell.log");
*/

void GTW61850Client::DoServerDown()
{
  try 
  {
    bool bWasServerUp = m_bServerUp;
    m_bServerUp = false;

    if (bWasServerUp)
    {
      GtwOsSyncDec(&m_nTotalClientConnected);
    }
    GetGTWApp()->Update61850ClientOnline();

    m_responseQueueThread.Stop();
    m_ReportQueueThread.Stop();
    m_GooseQueueThread.Stop();

    if (GetGTWApp()->IsShuttingDown() == true)
    {
      m_pReconnectTimer->KillTimer();
      m_pUpdateStatsTimer->KillTimer();
      return;
    }

    m_pUpdateStatsTimer->KillTimer();
    m_pNumDisconnectsmdo->Increment();
    m_p61850ConnectedToServerMdo->SetValue(TMWDEFS_FALSE);

    //DisConnectReports();
    if (bWasServerUp)
    {
      InvalidateItems();//"RPT");

      DisconnectPolledPointSets();
      CleanupStartEndPointChangeOnServerDown();
    }

    if (!GTW61850Client::GetLicensed())
    {
      m_pReconnectTimer->KillTimer();
      return;
    }

    std::string serverIpAddr;
    GetServerIpAddress(serverIpAddr);

    if (m_p61850ClientActiveControlmdo->GetValue())
    {
      TMWTYPES_UINT time = GetReconnectTime();
      if (time > 0)
      {
        LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, 
          "Could not connect to or lost connection to server at %s. Retry connect timer will start.", serverIpAddr.c_str());
        LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, 
          "Server Down. Reconnect timer started, reconnect time = %d, retry count = %d, retry count limit = %d", GetReconnectTime(), m_i61850RetryCount, m_RetryCountLimit );
        m_pReconnectTimer->SetTimer(this,time);
      }
      else
      {
        LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, 
          "Could not connect to or lost connection to server at %s. Reconnect time is 0 therefore retry connect timer will not start - user will need to manually connect or change settings.", serverIpAddr.c_str());
        m_pReconnectTimer->KillTimer();
        //m_pReconnectTimer->SetTimer(this,time);
      }
    }
    else 
    {
      LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, 
        "Could not connect to or lost connection to server at %s. Channel Active MDO is turned off therefore retry connect timer will not start - user will need to manually connect or change settings.", serverIpAddr.c_str());

      m_pReconnectTimer->KillTimer();
    }
  }
  catch (...)
  {
    //debug_file << (const char*)GetCurrDateTimeString() << ": Caught exception in DoServerDown for " << (const char *)GetAliasName() << endl;

    m_p61850ConnectedToServerMdo->SetValue(TMWDEFS_FALSE);
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, 
      "%s", "GTW61850Client::DoServerDown() exception caught");
  }
}

void GTW61850Client::GetGTWTreeText(CStdString &name)
{
  if (name == "")
  {
    name = GetAliasName();
  }
  
  CStdString serverIpAddr;
  GetServerIpAddress(serverIpAddr);

  name = name + "(" + serverIpAddr + ")";
}

class OnStatusStruct : public WorkItemBase
{
public:
  virtual void   DoWork(void* param);

  virtual void Abort()
  {
  }

  tmw61850::ConnectionStatus status;
  void *pUserData;
};

void GTW61850Client::OnIECGooseStatus(tmw61850::ConnectionBase *c, tmw61850::ConnectionStatus status, void *pUserData)
{
  PostOnStatus(status, pUserData);
}

void GTW61850Client::OnStatus(tmw61850::Client *cc, tmw61850::ConnectionStatus status, void *pUserData)
{
  //GTW61850Client *pGTW61850Client = (GTW61850Client *)pUserData;
  //TRACE("OnStatus message for client '%s' : %s\n", pGTW61850Client->GetFullName().c_str(), pGTW61850Client->GetClientConnection()->ConnectionStatusToString(status));

  // The SDG does not care about any other status messages
  if (status == tmw61850::ConnectionStatus::Status_ServerUP || status == tmw61850::ConnectionStatus::Status_ServerDown)
  {
    PostOnStatus(status, pUserData);
    return;
  }
}

void GTW61850Client::PostOnStatus(tmw61850::ConnectionStatus status, void *pUserData)
{
  try
  {
    OnStatusStruct *pS = nullptr;
    pS = new OnStatusStruct;
    pS->status = status;
    pS->pUserData = pUserData;

    //GTW61850Client *pGTW61850Client = (GTW61850Client *)pUserData;
    //TRACE("OnStatus message for client '%s' : %s\n", pGTW61850Client->GetFullName().c_str(), pGTW61850Client->GetClientConnection()->ConnectionStatusToString(status));

    // NOTE : the server up and server down must be processed on a different thread because they depend on the main 61850 thread for processing
    //        and will block if processed here. They cannot be processed on main queue because it needs to be finished in the server down
    ((GTW61850Client*)pUserData)->m_StatusQueueThread.AddWorkItemToQueue(pS);
  }
  catch (...)
  {
    LOG6(((GTW61850Client*)pUserData)->GetClientConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850Client::PostOnStatus:assert(false)");
#ifdef _WIN32
    assert(false);
#endif
  }
}

const int GTW61850ClientStatus_Connected    = 1;
const int GTW61850ClientStatus_DisConnected = 2;
void OnStatusStruct::DoWork(void *param)
{
  unique_ptr<OnStatusStruct> deleteOnReturn(this);

  GTW61850Client *pGTW61850Client = (GTW61850Client *)pUserData;
  if (pGTW61850Client == nullptr)
  {
    return;
  }

  //TRACE("OnStatus message for client '%s' : %s\n", pGTW61850Client->GetFullName().c_str() , pGTW61850Client->GetClientConnection()->ConnectionStatusToString(status));

  CStdString msgStr;
  if (status == tmw61850::ConnectionStatus::Status_ServerConnecting)
    msgStr = "STATUS: CONNECTING TO SERVER";
  else if (status == tmw61850::ConnectionStatus::Status_ServerDown)
    msgStr = "STATUS: SERVER DOWN";
  else if (status == tmw61850::ConnectionStatus::Status_ServerUP)
    msgStr = "STATUS: SERVER UP";
  else
  {
    return; // intermediate state that gateway does not care about
  }

//  if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
  {
    LOG6(pGTW61850Client->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "%s", "IEC 61850");
  }

  if (status == tmw61850::ConnectionStatus::Status_ServerUP)
  {
    pGTW61850Client->DoServerUp();
  }
  else if (status == tmw61850::ConnectionStatus::Status_ServerDown)
  {
    pGTW61850Client->DoServerDown();
  }
}
/*
bool GTW61850Client::DoClientStatus(GTW61850Client *pClient, WPARAM msg)
{
  if (GetGTWApp()->IsShuttingDown())
  {
    return false;
  }

  if (pClient)
  {
    switch(msg)
    {
    case GTW61850ClientStatus_Connected:
      pClient->DoServerUp();
      break;
    case GTW61850ClientStatus_DisConnected:
      pClient->DoServerDown();
      break;
    }
  }
  return true;
}
*/
class OnOperationalMsgStruct : public WorkItemBase
{
  friend class GTW61850Client;

public:
  OnOperationalMsgStruct()
    : 
  m_pControlBlock(nullptr),
  m_pNode(nullptr),
  m_mmsErrCode(tmw61850::ClientEnumDefs::ClientMMSErrorCode::Unknown)
  {
  }
  
  virtual void DoWork(void* param);

  virtual void Abort()
  {
  }

private:
  tmw61850::ClientEnumDefs::ClientMMSErrorCode m_mmsErrCode;
  tmw61850::ClientEnumDefs::ClientRequestMessageType m_msgType;
  CStdString m_msg;
  GTW61850ControlBlock *m_pControlBlock;
  tmw61850::Node *m_pNode;
};

class OnOperationalEventStruct : public WorkItemBase
{
public:
  virtual void DoWork(void* param);

  virtual void Abort()
  {
  }

  tmw61850::ClientEnumDefs::ClientOperationalEventType m_level;
  CStdString m_msg;
};

//got to pass control block to this method
//void GTW61850Client::OnOperationalMsg(ConnectionBase *firedFrom, tmw61850::Node *node, tmw61850::ClientEnumDefs::ClientRequestMessageType errCode, tmw61850::ClientEnumDefs::ClientMMSErrorCode mmsErrorCoce, const char *msg)
//{
//  PostOnOperationalMsg(errCode, msg, nullptr, node);
//}

void GTW61850Client::OnOperationalMsgUserData(tmw61850::ConnectionBase *firedFrom, tmw61850::Node *node, const char *contextMessage, void *userData, tmw61850::ClientEnumDefs::ClientRequestMessageType msgType, tmw61850::ClientEnumDefs::ClientMMSErrorCode mmsErrorCode, const char *msg)
{
  PostOnOperationalMsg(firedFrom, msgType, mmsErrorCode, msg, userData, node);
}

void GTW61850Client::PostOnOperationalMsg(tmw61850::ConnectionBase *firedFrom, tmw61850::ClientEnumDefs::ClientRequestMessageType msgType, tmw61850::ClientEnumDefs::ClientMMSErrorCode mmsErrorCode, const char *msg, void *userData, tmw61850::Node *node)
{
  tmw61850::Client *client = (tmw61850::Client *)firedFrom;
  GTW61850Client *gtwClient = (GTW61850Client *)client->GetUserData();
  if (gtwClient->m_StatusQueueThread.IsActive() && (mmsErrorCode != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success))
  {
    OnOperationalMsgStruct *pS = new OnOperationalMsgStruct();
    pS->m_mmsErrCode = mmsErrorCode;
    pS->m_msgType = msgType;
    pS->m_msg = msg;
    pS->m_pControlBlock = (GTW61850ControlBlock*)userData;
    pS->m_pNode = node;
    gtwClient->m_StatusQueueThread.AddWorkItemToQueue(pS);
  }
}

void OnOperationalMsgStruct::DoWork(void *param)
{
  GTW61850Client* pClient = (GTW61850Client *)param;

  unique_ptr<OnOperationalMsgStruct> deleteOnReturn(this);

  bool bHandled = false;
  if (m_pControlBlock)
  {
    GTW61850ControlBlock *pControlBlock = (GTW61850ControlBlock*)m_pControlBlock;
    if (m_msgType == tmw61850::ClientEnumDefs::ClientRequestMessageType::Client_Request_Error_Read_Array)
    {
      ((GTW61850PolledPointSet*)m_pControlBlock)->ReadArrayFailed(m_mmsErrCode);
    }
    else if (m_msgType == tmw61850::ClientEnumDefs::ClientRequestMessageType::Client_Request_Error_Read)
    {
      if (m_pNode)
      {
        Array<void*> *pUserDataArray = m_pNode->GetUserDataArrayPtr();
        if (!pUserDataArray)
        {
          //tmw61850::DataSet *pDs = dynamic_cast<tmw61850::DataSet*>(m_pNode);
          GTW61850PolledDataSet *pds = dynamic_cast<GTW61850PolledDataSet*>((GTW61850ControlBlock*)m_pControlBlock);
          if (pds)
          {
            pds->OnReadError(m_mmsErrCode);
            bHandled = true;
          }
        }
        else
        {
          for (unsigned int i = 0; i < pUserDataArray->size(); i++)
          {
            GTW61850DataAttributeMDO *pMdo = (GTW61850DataAttributeMDO *)pUserDataArray->getAt(i);
            if (pMdo->GetControlBlock() && pMdo->GetControlBlock() == m_pControlBlock)
            {
              GTW61850PolledPointSet *pps = dynamic_cast<GTW61850PolledPointSet *>((GTW61850ControlBlock*)m_pControlBlock);
              if (pps)
              {
                pps->OnClientPointReadError(dynamic_cast<tmw61850::DataAttribute*>(m_pNode), m_mmsErrCode);
                bHandled = true;
                break;
              }
              else
              {
                GTW61850PolledDataSet *pds = dynamic_cast<GTW61850PolledDataSet*>((GTW61850ControlBlock*)m_pControlBlock);
                if (pds)
                {
                  pds->OnReadError(m_mmsErrCode);
                  bHandled = true;
                  break;
                }
              }
            }
          }
        }
      }
    }
  }

  if (!bHandled)
  {
    if (m_msgType >= tmw61850::ClientEnumDefs::ClientRequestMessageType::Client_Request_Error_Initiate)
    {
      LOG6(pClient->GetClientConnection(),  GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", (const char*)m_msg);
    }
  }
}


void GTW61850Client::OnOperationalEvent(tmw61850::ConnectionBase *firedFrom, tmw61850::Node *node, tmw61850::ClientEnumDefs::ClientOperationalEventType level, const char *msg)
{
  PostOnOperationalEvent(level, msg);
}

void GTW61850Client::PostOnOperationalEvent(tmw61850::ClientEnumDefs::ClientOperationalEventType level, const char *msg)
{
//  if (CWorkQueue::IsOkToInsert() && GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
  {
    // Do nothing here until decide if do any work in the DoWork method below - currently all commented out

    //OnOperationalEventStruct *pS = new OnOperationalEventStruct;
    //pS->m_level = level;
    //pS->m_msg = msg;
	  //GetWorkQueue()->InsertWorkItem(pS);
  }
}
//
//
void OnOperationalEventStruct::DoWork(void *param)
{
  unique_ptr<OnOperationalEventStruct> deleteOnReturn(this);

//  //char buf2[512];
//  //OnMsgStruct *pS = (OnMsgStruct *)param;
//  //ClientConnection::MsgLevel level = this->level;
//  //char *msg = this->msg;
//
//  //CStdString msgStr1;
//  if (level >= tmw::Message::Operational_Error_Initiate)
//  {
//    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "IEC 61850 error: %s", msg);
//
//    //fve(void)tmwtarg_snprintf(buf2, sizeof(buf2), "IEC 61850 error: %s\n", msg);
//    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "%s", buf2);
//  }
//  else
//  {
////    (void)tmwtarg_snprintf(buf, sizeof(buf), "IEC 61850%s", msg);
////    msgStr1.Format("%s",(const char *)msg);
//    if (GTWConfig::DiagIdAuxMask & TMWDIAG_ID_61850)
//    {
//      TMWDIAG_MESSAGE_NAME("IEC 61850 message: ",msg,TMWDIAG_ID_61850);
//    }
//  }
//
}

bool GTW61850Client::IsServerUp()
{
  if (m_p61850ClientConnection != nullptr)
  {
	  return (m_p61850ClientConnection->IsConnectionAliveAndReady());
  }
  return false;
}

// Should only be called from Connect(), no where else
bool GTW61850Client::ConnectToServer()
{
  if (m_p61850ClientConnection != nullptr && !m_p61850ClientConnection->IsConnectionAliveAndReady())
  {
    //if (m_p61850ClientActiveControlmdo->GetValue() == true)
    {
//      if (GtwSysConfig::pdoDiagMask() & TMWDIAG_ID_61850)
      {
        LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, (const char*)GetAliasName(), "Attempting Connect To 61850 Server for: %s",(const char *)GetAliasName());
      }

      //bool bDoDiscovery = GTWConfig::I61850LoadModelFromFileEnabled[Get61850ClientIndex()] == FALSE;
      if (m_p61850ClientConnection->Connect(false) != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
      {
        return false;
      }
    }
    return true;
  }
  return false;
}

/*
bool GTW61850Client::DisConnectFromServer()
{
  if (m_p61850ClientConnection != nullptr && m_p61850ClientConnection->IsConnectionAliveAndReady())
  {
    m_p61850ClientActiveControlmdo->SetValue(false);
    //Disconnect();
    //m_p61850ClientConnection->Disconnect();
    return true;
  }
  return false;
}
*/
/*
void GTW61850Client::SetUseSclFile(bool bUseSclFile, bool generateSCLFile)
{
  m_bUseSclFile = bUseSclFile;
  m_generateSCLFile = generateSCLFile;
}
*/

void GTW61850Client::SetLicensed(bool b)
{
  m_bIsLicensed = b;
  if (!GetGTWApp()->IsInitialized())
  {
    return;
  }

  if (!m_bIsLicensed)
  {
    if (GTW61850Client::DisconnectAll() > 0)
    {
      LOG6(nullptr, GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850,
        "%s", "Closed ALL 61850 Clients because of lost license");
    }
  }
  else
  {
    GTW61850Client::ConnectAll();
  }
}

/**********************************************************************************\
Function :			GTW61850Client::Connect
Description : [none]
Return :			void	-
Parameters :
Note : [none]
\**********************************************************************************/
bool GTW61850Client::Connect()
{
  if (GetGTWApp()->IsShuttingDown() == true)
  {
    return false;
  }

  if (m_p61850ClientConnection != nullptr && !m_p61850ClientConnection->IsConnectionAliveAndReady())
  {
    if (GTW61850Client::GetLicensed() == false)
    {
      LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "IEC 61850 Client: (no license)");

#if USE_WINGUI
      if (gtwlib_GetRunningAsService() == false)
      {
        CStdString message;
        message.Format("61850 Client is not currently licensed.\n"
          "This protocol has been disabled\n");
        CMessageBoxEx* pmsgbox = new CMessageBoxEx(nullptr, message, "SDG License", MB_OK, IDOK, 5000);
        int nResult = pmsgbox->DoMessageBox();
        delete pmsgbox;
      }
#endif
      return false;
    }

    RegisterCallbacks(true);
    SetupConnectionParams();
    // Call if doing local goose
    //m_p61850ClientConnection->EnableGOOSELoopback();
    m_p61850ClientConnection->GetConfiguration()->SetRequestTimeout(m_connectTimeOut);

    //if (m_bUseSclFile == false)
    if (m_eModelDefType != GTW61850Client::USE_SCL_FILE)
    {
      if (m_p61850ClientConnection->Connect(m_bInitialConnect) != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
      {
        return false;
      }
    }
    else
    {
      tmw::String errorStack;
      if (!SetupSCL(errorStack))
      {
        return false;
      }

      if (!ConnectToServer())
      {
        return false;
      }
    }
    //TMWTYPES_UINT time = GetReconnectTime();
    //if (time != 0)
    //  m_pReconnectTimer->SetTimer(this,time);
  }
  return TMWDEFS_TRUE;
}

void GTW61850Client::DisableAllReports(bool bUnReserve)
{
  std::list<GTW61850ReportControlBlock*> reportList;
  this->GetReportControlBlocks(reportList);

  for (std::list<GTW61850ReportControlBlock*>::iterator iter = reportList.begin();
        iter != reportList.end();
        ++iter)
  {
    GTW61850ReportControlBlock *pRCB = *iter;
    if (pRCB->IsReportEnabled())
    {
      pRCB->DisableReport(bUnReserve);
    }
  }
}

bool GTW61850Client::PreDelete()
{
  if (m_p61850ClientConnection && m_p61850ClientConnection->IsConnectionAliveAndReady())
  {
    RegisterCallbacks(false);
    DisableAllReports(true);

    const Array<tmw61850::GOOSEControl*>* gooseControlBlocks = m_p61850ClientConnection->Model()->GetGOOSEControlBlocks();
    if (gooseControlBlocks != nullptr)
    {
      for (unsigned int i = 0; i < gooseControlBlocks->getSize(); i++)
      {
        if (gooseControlBlocks->getAt(i)->IsEnabled())
          m_p61850ClientConnection->UnSubscribeGooseBlock(gooseControlBlocks->getAt(i));
      }
    }

    m_p61850ClientConnection->Disconnect();
    DoServerDown();
  }
  return true;
}

bool GTW61850Client::Disconnect()
{
  if (m_p61850ClientConnection && m_p61850ClientConnection->IsConnectionAliveAndReady())
  {
    RegisterCallbacks(false);
    DisableAllReports(false);

    const Array<tmw61850::GOOSEControl*> *gooseControlBlocks = m_p61850ClientConnection->Model()->GetGOOSEControlBlocks();
    if (gooseControlBlocks != nullptr)
    {
      for (unsigned int i=0;i<gooseControlBlocks->getSize();i++)
      {
        if (gooseControlBlocks->getAt(i)->IsEnabled())
          m_p61850ClientConnection->UnSubscribeGooseBlock(gooseControlBlocks->getAt(i));
      }
    }

    m_p61850ClientConnection->Disconnect();
    DoServerDown();
  }
  return true;
}

void GTW61850Client::RegisterCallbacks(bool bConnecting)
{
  m_p61850ClientConnection->RegisterClientPointChangeFunc(bConnecting ? OnDataChange : nullptr);
  m_p61850ClientConnection->RegisterStartEndClientPointChangeFunc(bConnecting ? OnStartClientPointChange : nullptr, bConnecting ? OnEndClientPointChange : nullptr);
  //m_p61850ClientConnection->RegisterStartEndClientPointChangeFunc(OnStartClientPointChange, bConnecting ? OnEndClientPointChange : nullptr);
  //m_p61850ClientConnection->RegisterClientRequestMessageFunc(bConnecting ? OnOperationalMsg : nullptr);
  m_p61850ClientConnection->RegisterClientRequestMessageFunc(bConnecting ? OnOperationalMsgUserData : nullptr);
  m_p61850ClientConnection->RegisterClientOperationalEventFunc(bConnecting ? OnOperationalEvent : nullptr);
  m_p61850ClientConnection->RegisterConnectionStatusFunc(bConnecting ? OnStatus : nullptr, bConnecting ? this : nullptr);
  //m_p61850ClientConnection->RegisterReportingReadArrayFunc(nullptr, bConnecting ? OnEndReadArray : nullptr);
}

void GTW61850Client::GetConnectionParam(const CStdString& configString, const CStdString& sCurValue, int &iVal, bool &bInclude)
{
  CStdString configParam = GetProfileAttr(configString, sCurValue);
  bInclude = configParam.GetLength() > 0;
  iVal = bInclude ? atoi(configParam) : 0;
}

void GTW61850Client::GetConnectionParam(const CStdString& configString, const CStdString& sCurValue, CStdString& sParam)
{
  sParam = GetProfileAttr(configString, sCurValue);
  sParam.Replace("\"","");
}

CStdString toString(int i)
{
  CStdString s;
  s.Format("%i", i);
  return s;
}

bool GTW61850Client::SetupSCL(tmw::String &stackError)
{
  stackError.resize(0);
  //if (!m_bUseSclFile)
  if (m_eModelDefType != USE_SCL_FILE)
  {
    return true;
  }

  tmw61850::i61850RootNode *pModel = dynamic_cast<tmw61850::i61850RootNode*>(m_p61850ClientConnection->Model());
  if (pModel->IsModelCompletlyBuilt())
  {
    return true;
  }

  const char *_scl_file = GTWConfig::I61850SCLFileName[Get61850ClientIndex()];

  std::string scl_file = GTWmain::GetIniRelativeFullFilePath(_scl_file, GtwSysConfig::getCurrentWorkSpacePath());
  if (std::filesystem::exists((const char *)scl_file.c_str()) == false)
  {
    if (scl_file.length() == 0) // then not specified, we assume upgrading and was always discover in prior release
    {
      TMWTYPES_USHORT index = Get61850ClientIndex();
      m_eModelDefType = ALWAYS_DISCOVER;
      GTWConfig::I61850ClientModelDefType[Get61850ClientIndex()] = m_eModelDefType;
      stackError.format("IEC 61850 Client '%s' empty SCL file specified. Client will be set to Always Discover. Contact TMW support if more help is needed.", (const char*)this->GetAliasName(), scl_file.c_str());
      LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "scl file not found : %s", scl_file.c_str());
      return true;
    }
    else
    {
      stackError.format("IEC 61850 Client '%s' SCL file not found: %s. If mappings exist, do NOT save or the mappings will be lost. Please restore the SCL file and "
        " restart the SDG without saving. Contact TMW support if more help is needed.", (const char*)this->GetAliasName(), scl_file.c_str());
      LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "scl file not found : %s", scl_file.c_str());
    }
    return false;
  }

  CStdString scl_server_ied_name = GTWConfig::I61850SCLFileIEDName[Get61850ClientIndex()];
  CStdString scl_client_ied_name = GTWConfig::I61850SCLClientIEDName[Get61850ClientIndex()];

  tmw61850::XMLReadOptions readOpts;
  // Setup server ied name
  if (scl_server_ied_name == nullptr || scl_server_ied_name == "")
  {
    readOpts.SetServerIEDName(nullptr);
  }
  else
  {
    CStdString ied_ap = scl_server_ied_name;

    CStdString ied_name = "";
    CStdString ap_name = "";

    if (ied_ap.Find("\\") > 0)
    {
      ied_name = ied_ap.Left(ied_ap.Find("\\"));
      ap_name = ied_ap.Right(ied_ap.GetLength() - ied_ap.Find("\\") - 1);
    }
    else
    {
      ied_name = ied_ap;
    }
    readOpts.SetServerIEDName(ied_name);
  }
  // Setup client ied name
  if (scl_client_ied_name == nullptr || scl_client_ied_name == "")
  {
    readOpts.SetClientIEDName(nullptr);
  }
  else
  {
    CStdString ied_ap = scl_client_ied_name;

    CStdString ied_name = "";
    CStdString ap_name = "";

    if (ied_ap.Find("\\") > 0)
    {
      ied_name = ied_ap.Left(ied_ap.Find("\\"));
      ap_name = ied_ap.Right(ied_ap.GetLength() - ied_ap.Find("\\") - 1);
    }
    else
    {
      ied_name = ied_ap;
    }

    readOpts.SetClientIEDName(ied_name);
  }


  bool bResult = false;
  try
  {
    bResult = m_p61850ClientConnection->LoadFromSCL(scl_file.c_str(), readOpts);
  }
  catch (tmw::Exception &ex)
  {
    const char* sErr = ex.getErrorMessage(0);
    if (!sErr)
    {
      sErr = "no error specified";
    }

    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Exception thrown while loading SCL file: %s. Exception description : %s. Please correct the problems and reload", scl_file.c_str(), sErr);
    return false;
  }
  catch (const std::exception& ex)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Exception thrown while loading SCL file: %s. Exception description : %s. Please correct the problems and reload.", scl_file.c_str(), ex.what());
    return false;
  }
  catch (...)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Unknown exception thrown while loading SCL file: %s. Please correct the problems and reload.", scl_file.c_str());
    return false;
  }
  if (!bResult)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Error occurred while loading SCL file: %s. See Logs. Please correct the problems and reload.", scl_file.c_str());
  }
  else
  {
    //m_p61850ClientConnection->CanConnectToIED
  }
  
  return bResult;
}

/*
void ConfigureStrongSecurity(tmw61850::MMSAuthConfig* mmsConfig, tmw61850::TLSConfig* tlsConfig)
{
  mmsConfig->SetCAFileName("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\ca_public\\tmw_sample_ca_rsa_public_certificate.pem");
  mmsConfig->SetCARevokeListFileName("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\ca_public\\tmw_sample_ca_certificate_revocation_list.pem");
  tlsConfig->SetCAFileName("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\ca_public\\tmw_sample_ca_rsa_public_certificate.pem");
  tlsConfig->SetCARevokeListFileName("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\ca_public\\tmw_sample_ca_certificate_revocation_list.pem");
  mmsConfig->SetMmsPublicCertificate("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\server_user\\tmw_sample_mms_rsa_public_cert.pem");
  mmsConfig->SetMmsPrivateKeyFile("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\server_user\\tmw_sample_mms_rsa_private_key.pem");
  tlsConfig->SetTlsRsaPublicCertificate("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\server_user\\tmw_sample_tls_rsa_public_cert.pem");
  tlsConfig->SetTlsRsaPrivateKeyFile("D:\\svn_branches\\SDG_5.2.2\\TMW61850\\i61850\\TMWCertificates\\server_user\\tmw_sample_tls_rsa_private_key.pem");
  mmsConfig->SetCAPathName("");
  tlsConfig->SetCAPathName("");

  mmsConfig->SetCaVerifyDepth(1);
  tlsConfig->SetCaVerifyDepth(1);
  tlsConfig->SetCiphers("TLSv1:TLSv1.1:TLSv1.2:SSLv3:!SSLv2:!aNULL:!eNULL:!CAMELLIA:!EXPORT40:!EXPORT56:@STRENGTH");
  mmsConfig->SetUseSiscoCompatibility(false);
  mmsConfig->SetMmsCommonName("MMS");
  mmsConfig->SetMmsPrivateKeyPassPhrase("triangle");
  tlsConfig->SetTlsCommonName("TLS");
  tlsConfig->SetTlsRenegotiationCount(10);
  tlsConfig->SetTlsRenegotiationMsTimeout(10000);
  tlsConfig->SetTlsRenegotiationSeconds(10);
  tlsConfig->SetTlsRsaPrivateKeyPassPhrase("triangle");
}
*/

void GTW61850Client::SetupConnectionParams()
{
  int index = this->Get61850ClientIndex();

  tmw61850::ClientConfiguration *pClientConfig = GetClientConnection()->GetConfiguration();
  pClientConfig->SetName(GetFullName());

  int iVal = 0;
  CStdString sVal = "";
  bool bInclude = true;

  //
  // Setup Client side 7 layer stack
  //
  tmw61850::ClientSevenLayerAddress *p7LayerAddr = pClientConfig->GetSevenLayerAddress();

  GetConnectionParam(GTWConfig::I61850ClientAEQualifier(index), toString(p7LayerAddr->GetClientAEQual()), iVal, bInclude);
  p7LayerAddr->SetClientAEQual(iVal, bInclude);

  GetConnectionParam(GTWConfig::I61850ClientAPInvokeID(index), toString(p7LayerAddr->GetClientAPInvokeID()), iVal, bInclude);
  p7LayerAddr->SetClientAPInvokeID(iVal, bInclude);
  
  GetConnectionParam(GTWConfig::I61850ClientAEInvokeID(index), toString(p7LayerAddr->GetClientAEInvokeID()), iVal, bInclude);
  p7LayerAddr->SetClientAEInvokeID(iVal, bInclude);

  // Here nullptr means dont include in the 7 layer bit mask
  GetConnectionParam(GTWConfig::I61850ClientAppID(index), (const char *)p7LayerAddr->GetClientAPTitle(), sVal);
  p7LayerAddr->SetClientAPTitle(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  GetConnectionParam(GTWConfig::I61850ClientPresentationAddress(index), (const char *)p7LayerAddr->GetClientPSel(), sVal);
  p7LayerAddr->SetClientPSel(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  GetConnectionParam(GTWConfig::I61850ClientSessionAddress(index), (const char *)p7LayerAddr->GetClientSSel(), sVal);
  p7LayerAddr->SetClientSSel(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  GetConnectionParam(GTWConfig::I61850ClientTransportAddress(index), (const char *)p7LayerAddr->GetClientTSel(), sVal);
  p7LayerAddr->SetClientTSel(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  CStdString local_ip_addr = GetProfileAttr(GTWConfig::I61850ClientIPAddress(index), pClientConfig->GetLocalIPAddress());
  CStdString slocalIp;
  convertNameToIpAddress(local_ip_addr, AF_INET, slocalIp);

  pClientConfig->SetLocalIPAddress(slocalIp);

  //
  // Setup Server side 7 layer stack
  //
  GetConnectionParam(GTWConfig::I61850ServerAEQualifier(index), toString(p7LayerAddr->GetServerAEQual()), iVal, bInclude);
  p7LayerAddr->SetServerAEQual(iVal, bInclude);

  GetConnectionParam(GTWConfig::I61850ServerAPInvokeID(index), toString(p7LayerAddr->GetServerAPInvokeID()), iVal, bInclude);
  p7LayerAddr->SetServerAPInvokeID(iVal, bInclude);
  
  GetConnectionParam(GTWConfig::I61850ServerAEInvokeID(index), toString(p7LayerAddr->GetServerAEInvokeID()), iVal, bInclude);
  p7LayerAddr->SetServerAEInvokeID(iVal, bInclude);

  // Here nullptr means dont include in the 7 layer bit mask
  GetConnectionParam(GTWConfig::I61850ServerAppID(index), (const char *)p7LayerAddr->GetServerAPTitle(), sVal);
  p7LayerAddr->SetServerAPTitle(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  GetConnectionParam(GTWConfig::I61850ServerPresentationAddress(index), (const char *)p7LayerAddr->GetServerPSel(), sVal);
  p7LayerAddr->SetServerPSel(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  GetConnectionParam(GTWConfig::I61850ServerSessionAddress(index), (const char *)p7LayerAddr->GetServerSSel(), sVal);
  p7LayerAddr->SetServerSSel(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  GetConnectionParam(GTWConfig::I61850ServerTransportAddress(index), (const char *)p7LayerAddr->GetServerTSel(), sVal);
  p7LayerAddr->SetServerTSel(sVal.GetLength() > 0 ? (const unsigned char *)(const char *)sVal : nullptr);

  CStdString server_ip_addr = GetProfileAttr(GTWConfig::I61850ServerIPAddress(index), pClientConfig->GetServerIPAddress());
  CStdString sIp;
  convertNameToIpAddress(server_ip_addr, AF_INET, sIp);

  pClientConfig->SetServerIPAddress(sIp);

  GetConnectionParam(GTWConfig::I61850ServerIPPort(index), toString(pClientConfig->GetServerPort()), iVal, bInclude);
  pClientConfig->SetServerPort(iVal);
  m_ServerIpPort = iVal;

  CStdString serverIPAddress;
  GetServerIpAddress(serverIPAddress);
  if (serverIPAddress != server_ip_addr)
  {
    SetServerIpAddress(server_ip_addr);
  }


  /*
#ifdef DEBUG
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "\n---------- 61850 Client (%s) connection params ----------\n", this->GetFullName().c_str());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "IP: %s\n", pCaddress->GetServerIPAddress());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerPort: %d\n", pCaddress->GetServerPort());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetClientAEQual: %d\n", pCaddress->GetClientAEQual());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetClientAPInvokeID: %d\n", pCaddress->GetClientAPInvokeID());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetClientAEInvokeID: %d\n", pCaddress->GetClientAEInvokeID());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetClientAPTitle: %s\n", pCaddress->GetClientAPTitle());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetClientPSel: %s\n", pCaddress->GetClientPSel());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetClientSSel: %s\n", pCaddress->GetClientSSel());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetClientTSel: %s\n", pCaddress->GetClientTSel());

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerAEQual: %d\n", pCaddress->GetServerAEQual());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerAPInvokeID: %d\n", pCaddress->GetServerAPInvokeID());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerAEInvokeID: %d\n", pCaddress->GetServerAEInvokeID());

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerAPTitle: %s\n", pCaddress->GetServerAPTitle());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerPSel: %s\n", pCaddress->GetServerPSel());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerSSel: %s\n", pCaddress->GetServerSSel());
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "GetServerTSel: %s\n", pCaddress->GetServerTSel());

  #define MMSD_CLI_S_SEL_CALLING      0x0002U
#define MMSD_CLI_P_SEL_CALLING      0x0004U
#define MMSD_CLI_AP_TTL_CALLING     0x0008U
#define MMSD_CLI_AE_QUA_CALLING     0x0010U
#define MMSD_CLI_AP_INV_CALLING     0x0020U
#define MMSD_CLI_AE_INV_CALLING     0x0040U
#define MMSD_CLI_S_SEL_CALLED       0x0100U
#define MMSD_CLI_P_SEL_CALLED       0x0200U
#define MMSD_CLI_AP_TTL_CALLED      0x0400U
#define MMSD_CLI_AE_QUA_CALLED      0x0800U
#define MMSD_CLI_AP_INV_CALLED      0x1000U
#define MMSD_CLI_AE_INV_CALLED      0x2000U

  unsigned int mask = pCaddress->GetInclusion();
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_S_SEL_CALLING : %d\n", mask & MMSD_CLI_S_SEL_CALLING);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "MMSD_CLI_P_SEL_CALLING : %d\n", mask & MMSD_CLI_P_SEL_CALLING);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_AP_TTL_CALLING : %d\n", mask & MMSD_CLI_AP_TTL_CALLING);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "MMSD_CLI_AE_QUA_CALLING : %d\n", mask & MMSD_CLI_AE_QUA_CALLING);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_AP_INV_CALLING : %d\n", mask & MMSD_CLI_AP_INV_CALLING);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_AE_INV_CALLING : %d\n", mask & MMSD_CLI_AE_INV_CALLING);

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_S_SEL_CALLED : %d\n", mask & MMSD_CLI_S_SEL_CALLED);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_P_SEL_CALLED : %d\n", mask & MMSD_CLI_P_SEL_CALLED);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_AP_TTL_CALLED : %d\n", mask & MMSD_CLI_AP_TTL_CALLED);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_AE_QUA_CALLED : %d\n", mask & MMSD_CLI_AE_QUA_CALLED);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_AP_INV_CALLED : %d\n", mask & MMSD_CLI_AP_INV_CALLED);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,  "MMSD_CLI_AE_INV_CALLED : %d\n", mask & MMSD_CLI_AE_INV_CALLED);

  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr,"%s",   "-------------------------------------------------------\n");
#endif
  */

  pClientConfig->SetAuthMechanism(tmw61850::EnumDefs::AuthMechanism::None);

  CStdString sAuthMechanism = GTWConfig::I61850AuthMechanism(index);
  if (sAuthMechanism == "Password")
  {
    CStdString encryptedPassword = GTWConfig::I61850AuthPassword(index);
    CStdString authPassword = GTW61850Client::Decrypt(encryptedPassword);
    pClientConfig->SetAuthMechanism(tmw61850::EnumDefs::AuthMechanism::MMS_Auth_Weak);
    const char *sAuthPwd = (const char*)authPassword;
    pClientConfig->SetAuthPassword((const unsigned char *)sAuthPwd);
  }
#ifdef TMW_USE_TLS
  else if (sAuthMechanism == "Certificate")
  {
    pClientConfig->SetAuthMechanism(tmw61850::EnumDefs::AuthMechanism::MMS_Auth_Strong); // Certificate
    tmw61850::MMSAuthConfig* pMmsConfig = pClientConfig->GetMMSAuthConfig();
    tmw61850::TLSConfig* pTlsConfig = pClientConfig->GetTLSConfig();

    bool bUseMMSOnly = GTWConfig::I61850ClientUseMMSOnly(index);
    bool bUseTLSOnly = GTWConfig::I61850ClientUseTLSOnly(index);
    pMmsConfig->SetDisableMMSSecurity(bUseTLSOnly);
    pTlsConfig->SetDisableTLSSecurity(bUseMMSOnly);

    // TLS Central Authority
    pTlsConfig->SetCaVerifyDepth(atoi(GTWConfig::I61850ClientCertAuthChainingVerDepth(index)));
    pTlsConfig->SetCAFileName(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientCertificateAuthorityFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str());
    pTlsConfig->SetCARevokeListFileName(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientCertificateAuthorityRevokeListFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str());

    CStdString sdir = GTWConfig::I61850ClientDirectoryToCertificateAuthority(index);
    if (sdir.length() > 0)
    {
      CStdString fullPath = GtwSysConfig::getCurrentWorkSpacePath();
      pTlsConfig->SetCAPathName(fullPath + "/" + sdir);
    }

    // MMS Central Authority
    pMmsConfig->SetCaVerifyDepth(atoi(GTWConfig::I61850ClientCertAuthChainingVerDepth(index)));
    pMmsConfig->SetCAFileName(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientCertificateAuthorityFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str());
    pMmsConfig->SetCARevokeListFileName(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientCertificateAuthorityRevokeListFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str());
    if (sdir.length() > 0)
    {
      CStdString fullPath = GtwSysConfig::getCurrentWorkSpacePath();
      pMmsConfig->SetCAPathName(fullPath + "/" + sdir);
    }

    // MMS
    pMmsConfig->SetMmsCommonName(GTWConfig::I61850ClientMMSCommonName(index));
    pMmsConfig->SetMmsPrivateKeyFile(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientMMSPrivateKeyFile(index), GtwSysConfig::getPrivateKeyPath()).c_str());
    pMmsConfig->SetMmsPrivateKeyPassPhrase(GTWConfig::GetI61850ClientMMSPrivateKeyPassPhrase(index).c_str());
    pMmsConfig->SetMmsPublicCertificate(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientMMSPublicCertificateFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str());

    // TLS
    pTlsConfig->SetTlsCommonName(GTWConfig::I61850ClientTLSCommonName(index));
    pTlsConfig->SetTlsRenegotiationCount(atoi(GTWConfig::I61850ClientTLSMaxPDUs(index)));
    pTlsConfig->SetTlsRenegotiationMsTimeout(atoi(GTWConfig::I61850ClientTLSMaxRenegotiationWaitTime(index)));
    pTlsConfig->SetTlsRenegotiationSeconds(atoi(GTWConfig::I61850ClientTLSRenegotiation(index)));

    // TLS DSA - not set by default

    // TLS RSA
    pTlsConfig->SetTlsRsaPrivateKeyFile(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientTLSRSAPrivateKeyFile(index), GtwSysConfig::getPrivateKeyPath()).c_str());
    pTlsConfig->SetTlsRsaPrivateKeyPassPhrase(GTWConfig::GetI61850ClientTLSRSAPrivateKeyPassPhrase(index).c_str());
    pTlsConfig->SetTlsRsaPublicCertificate(GTWmain::GetIniRelativeFullFilePath(GTWConfig::I61850ClientTLSRSAPublicCertFile(index), GtwSysConfig::getCurrentWorkSpacePath()).c_str());

    pMmsConfig->SetUseSiscoCompatibility(GTWConfig::I61850ClientUseSiscoCompatability(index));
  }
#endif
}

GTW61400AlarmMDO *GTW61850Client::Add61400AlarmMDO(
  GTW61400Alarms *p61400Alarms,
  const char * clctnPath,
  const char * itemID,
  unsigned short statusCode,
  GTW61400AlarmMDO* pItem,
  bool bAddToList)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61400AlarmMDO %s",itemID);

  GTW61400AlarmMDO* p61400AlarmMDO = nullptr;
  if (pItem == nullptr)
  {
    // Add a new item
    p61400AlarmMDO = new GTW61400AlarmMDO;
  }
  else
  {
    p61400AlarmMDO = pItem;
  }
  p61400AlarmMDO->SetClientNode(this);
  p61400AlarmMDO->SetAlarmsNode(p61400Alarms);
  p61400AlarmMDO->m_i61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;
  p61400AlarmMDO->m_sTagName = itemID;
  p61400AlarmMDO->m_iStatusCode = statusCode;
  p61400AlarmMDO->m_sTagName.Replace(CStdString(clctnPath)+".","");
  p61400AlarmMDO->InitializeValue();

  if (bAddToList == TMWDEFS_TRUE)
  {
    tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);
    m_61850itemMap[p61400AlarmMDO] = p61400AlarmMDO;  // store this item in the item list
  }
  return p61400AlarmMDO;
}
/**********************************************************************************\
Function :			GTW61850Client::Add61850MasterDataObject
Description : [none]
Return :			GTW61850DataAttributeMDO *	-
Parameters :
const char * itemID	-
const char * accessPath	-
VARTYPE type	-
GTW61850DataAttributeMDO* pItem	-
bool bAddToList	-
Note : [none]
\**********************************************************************************/
GTW61850DataAttributeMDO *GTW61850Client::Add61850MasterDataObject(
  GTW61850ControlBlock *p61850ControlBlock,
  const char * clctnPath,
  const char * itemID,
  const char * itemQuality,
  const char * itemTime,
  tmw61850::Value::Type type,
  GTW61850DataAttributeMDO* pItem,
  bool bAddToList)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61850MasterDataObject %s",itemID);

  GTW61850DataAttributeMDO* p61850MasterDataObject = nullptr;
  if (pItem == nullptr)
  {
    // Add a new item
    p61850MasterDataObject = new GTW61850DataAttributeMDO;
  }
  else
  {
    p61850MasterDataObject = pItem;
  }

  p61850MasterDataObject->m_sValueTagName = itemID; // must be set before call SetControlBlock
  p61850MasterDataObject->SetClientNode(this);
  p61850MasterDataObject->SetControlBlock(p61850ControlBlock);
  p61850MasterDataObject->m_i61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;

  //if (itemID[strlen(itemID)-1] == 't')
  //{
  //  int a = 0;
  //}
  CStdString sCollectionPath = clctnPath;
  if (sCollectionPath.StartsWith(p61850ControlBlock->GetMemberName().c_str()))
  {
    sCollectionPath = sCollectionPath.substr(p61850ControlBlock->GetMemberName().length() + 1);
  }
  
  p61850MasterDataObject->m_sTagName = itemID;
  p61850MasterDataObject->m_sTagName.Replace(sCollectionPath+".","");
  p61850MasterDataObject->m_sQualityTagName = itemQuality;
  p61850MasterDataObject->m_sTimeTagName = itemTime;
  p61850MasterDataObject->InitializeValue(type);

  p61850MasterDataObject->m_DesiredType = type;


  if (bAddToList == TMWDEFS_TRUE)
  {
    tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);
    m_61850itemMap.insert(std::pair<void *, GTWMasterDataObject *>(p61850MasterDataObject, p61850MasterDataObject));  // store this item in the item list
  }
  return p61850MasterDataObject;
}

void GTW61850Client::Remove61850ClientNodes(GTWCollectionBase *pClctn)
{
  GTWCollectionMember *p61850ClientNode = pClctn->GetOwnerMember();

  if (p61850ClientNode->GetMemberCollection()->getNumMembers() == 0)
  {
    GTWCollectionBase *pColl = p61850ClientNode->GetParentCollection();
    p61850ClientNode->DeleteCollectionMember();
    Remove61850ClientNodes(pColl);
  }
}

/**********************************************************************************\
Function :			GTW61850Client::Add61850GOOSE
Description : [none]
Return :			GTW61850DataAttributeMDO *	-
Parameters :
const char * itemID	-
const char * accessPath	-
VARTYPE type	-
GTW61850DataAttributeMDO* pItem	-
bool bAddToList	-
Note : [none]
\**********************************************************************************/
GTW61850GOOSEControlBlock *GTW61850Client::Add61850GOOSE(
  const char * itemID,
  TMWTYPES_USHORT index)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61850GOOSE %s",itemID);

  GTW61850GOOSEControlBlock* p61850GOOSENode = nullptr;

  // Add a new item
  p61850GOOSENode = new GTW61850GOOSEControlBlock(itemID,index);
  p61850GOOSENode->Set61850Client(this);
  p61850GOOSENode->SetName(itemID);

  // Make sure goose queue is running
  m_GooseQueueThread.Start();

  return p61850GOOSENode;
}

/**********************************************************************************\
Function :			GTW61850Client::Add61850Report
Description : [none]
Return :			GTW61850DataAttributeMDO *	-
Parameters :
const char * itemID	-
const char * accessPath	-
VARTYPE type	-
GTW61850DataAttributeMDO* pItem	-
bool bAddToList	-
Note : [none]
\**********************************************************************************/
GTW61850ReportControlBlock *GTW61850Client::Add61850Report(
  const char * itemID,
  TMWTYPES_USHORT index)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61850Report %s",itemID);

  GTW61850ReportControlBlock* p61850ReportNode = nullptr;

  // Add a new item
  p61850ReportNode = new GTW61850ReportControlBlock(itemID,index);
  p61850ReportNode->Set61850Client(this);
  p61850ReportNode->SetName(itemID);

  // Make sure report queue is running
  m_ReportQueueThread.Start();

  // get an interface pointer
  return p61850ReportNode;
}


/**********************************************************************************\
Function :			GTW61850Client::Add61850PolledDataSet
Description : [none]
Return :			GTW61850DataAttributeMDO *	-
Parameters :
const char * itemID	-
const char * accessPath	-
VARTYPE type	-
GTW61850DataAttributeMDO* pItem	-
bool bAddToList	-
Note : [none]
\**********************************************************************************/
GTW61850PolledDataSet *GTW61850Client::Add61850PolledDataSet(
  const char * itemID,
  TMWTYPES_USHORT index)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61850PolledDataSet %s",itemID);

  GTW61850PolledDataSet* p61850PolledDataSetNode = nullptr;

  // Add a new item
  p61850PolledDataSetNode = new GTW61850PolledDataSet(itemID,index);
  p61850PolledDataSetNode->Set61850Client(this);
  p61850PolledDataSetNode->SetName(itemID);

  // get an interface pointer

  return p61850PolledDataSetNode;
}

/**********************************************************************************\
Function :			GTW61850Client::Add61850PolledPointSet
Description : [none]
Return :			GTW61850DataAttributeMDO *	-
Parameters :
const char * itemID	-
const char * accessPath	-
VARTYPE type	-
GTW61850DataAttributeMDO* pItem	-
bool bAddToList	-
Note : [none]
\**********************************************************************************/
GTW61850PolledPointSet *GTW61850Client::Add61850PolledPointSet(
  const char * itemID,
  GTW61850Client *p61850Client,
  TMWTYPES_USHORT index)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61850PolledPointSet %s",itemID);

  GTW61850PolledPointSet* p61850PolledPointSetNode = nullptr;

  // Add a new item
  p61850PolledPointSetNode = new GTW61850PolledPointSet(itemID, p61850Client, index);
  p61850PolledPointSetNode->Set61850Client(this);
  p61850PolledPointSetNode->SetName(itemID);

  // get an interface pointer

  return p61850PolledPointSetNode;
}
/**********************************************************************************\
Function :			GTW61850Client::Add61850WritablePointSet
Description : [none]
Return :			GTW61850DataAttributeMDO *	-
Parameters :
const char * itemID	-
const char * accessPath	-
VARTYPE type	-
GTW61850DataAttributeMDO* pItem	-
bool bAddToList	-
Note : [none]
\**********************************************************************************/
GTW61850WritablePointSet *GTW61850Client::Add61850WritablePointSet(
  const char *itemID,
  GTW61850Client *p61850Client,
  TMWTYPES_USHORT index)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61850WritablePointSet %s", itemID);

  GTW61850WritablePointSet *p61850WritablePointSetNode = nullptr;

  // Add a new item
  p61850WritablePointSetNode = new GTW61850WritablePointSet(itemID, p61850Client, index);
  p61850WritablePointSetNode->Set61850Client(this);
  p61850WritablePointSetNode->SetName(itemID);

  // get an interface pointer

  return p61850WritablePointSetNode;
}

/**********************************************************************************\
Function :			GTW61850Client::Add61850CommandPointSet
Description : [none]
Return :			GTW61850DataAttributeMDO *	-
Parameters :
const char * itemID	-
const char * accessPath	-
VARTYPE type	-
GTW61850DataAttributeMDO* pItem	-
bool bAddToList	-
Note : [none]
\**********************************************************************************/
GTW61850CommandPointSet *GTW61850Client::Add61850CommandPointSet(
  const char *itemID,
  GTW61850Client *p61850Client,
  TMWTYPES_USHORT index)
{
  LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850Client::Add61850CommandPointSet %s", itemID);

  GTW61850CommandPointSet *p61850CommandPointSetNode = nullptr;

  // Add a new item
  p61850CommandPointSetNode = new GTW61850CommandPointSet(itemID, p61850Client, index);
  p61850CommandPointSetNode->Set61850Client(this);
  p61850CommandPointSetNode->SetName(itemID);

  // get an interface pointer

  return p61850CommandPointSetNode;
}

///**********************************************************************************\
//Function :			GTW61850Client::Writevaluetoitem
//Description : [none]
//Return :			void	-
//Parameters :
//GTW61850DataAttributeMDO *pItem	-
//Note :
//COleVariant's ChangeType converts the string into the proper format
//or else puts up a dialog that it couldn't convert.
//Write can be sync or async
//\**********************************************************************************/
//void GTW61850Client::Writevaluetoitem(GTW61850DataAttributeMDO *pItem)
//{
//}

/**********************************************************************************\
Function :			GTW61850Client::Readitem
Description : [none]
Return :			void	-
Parameters :
GTW61850DataAttributeMDO* pItem	-
Note : Test async reading by calling 4 times (server will queue them up)
\**********************************************************************************/
void GTW61850Client::Readitem(GTW61850DataAttributeMDO* pItem)
{
}

/**********************************************************************************\
Function :			GTW61850Client::Removeitem
Description : [none]
Return :			void	-
Parameters :
GTW61850DataAttributeMDO* pItem	-
Note : [none]
\**********************************************************************************/
void GTW61850Client::DeleteItem(GTWMasterDataObjectTemplate<GTWBaseDataObject>* pItem)
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);
  size_t bOK = m_61850itemMap.erase(pItem);
  if (bOK == 0)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "GTW61850Client::Removeitem failed: ");
    return;
  }
  ((GTWCollectionMember*)pItem)->DeleteCollectionMember();
}

void GTW61850Client::GetControlBlockMDOs(GTW61850ControlBlock *pControlBlock, std::list<GTW61850DataAttributeMDO*> &daList)
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  auto pos = m_61850itemMap.begin();
  for (; pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pItem = pos->second; //GTW61850DataAttributeMDO
    if (pItem != nullptr)
    {
      GTW61850DataAttributeMDO *pMdo = dynamic_cast<GTW61850DataAttributeMDO*>(pItem);
      if (pMdo && pMdo->GetControlBlock() == pControlBlock)
      {
        //tmw61850::DataAttribute *pDA =
        //  (tmw61850::DataAttribute *)tmw61850::i61850RootNode::FindNode<DataAttribute>(pMdo->m_sTagName,
        //                                                      GetClientConnection()->Model());
        daList.push_back(pMdo);
      }
    }
  }
}

void GTW61850Client::GetReportControlBlocks(std::list<GTW61850ReportControlBlock*> &reportList)
{
  GTWCollectionBase *pCollection = GetMemberCollection();
  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850ReportControlBlock *pRCB = dynamic_cast<GTW61850ReportControlBlock*>(pMember);
    if (pRCB)
    {
      reportList.push_back(pRCB);
    }
  }
}

bool GTW61850Client::IsRCBBuffered(tmw61850::ReportControl* pRpt)
{
  return pRpt && dynamic_cast<tmw61850::ReportControlBuffered*>(pRpt) != nullptr;
}

TMWTYPES_USHORT GTW61850Client::GetReserveTms(tmw61850::ReportControl* pRpt)
{
  if (IsRCBBuffered(pRpt))
  {
    // Note that reserve tms can be -1, which means the report is reserved for a specific client, but we still will use the ini parameter and even though we set it
    // it will still come back as -1
    GTW61850ControlBlock* pCB = GetAssociatedControlBlock(pRpt);
    if (pCB)
    {
      GTW61850ReportControlBlock* pGtwRcb = dynamic_cast<GTW61850ReportControlBlock*>(pCB);
      if (pGtwRcb)
      {
        uint32_t nReserveTimeMs = GTWConfig::I61850RCBResvTms(Get61850ClientIndex(), pGtwRcb->GetBlockIndex());
        return (TMWTYPES_USHORT)nReserveTimeMs;
      }
    }
  }
  return 0;
}

GTW61850ControlBlock* GTW61850Client::GetAssociatedControlBlock(tmw61850::Control *pControl)
{
  GTWCollectionBase *pCollection = GetMemberCollection();
  SHARED_LOCK_GTW_COLLECTION(pCollection);

  TMWTYPES_BOOL isRCB = dynamic_cast<tmw61850::ReportControl*>(pControl) != nullptr;

  auto pos = pCollection->GetMap()->begin();
  for (;  pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;

    if (isRCB)
    {
      GTW61850ReportControlBlock *pRCB = dynamic_cast<GTW61850ReportControlBlock*>(pMember);
      if (pRCB)
      {
        if (pRCB->GetReportControl() == pControl)
          return pRCB;
      }
    }
    else
    {
      GTW61850GOOSEControlBlock *pGooseCB = dynamic_cast<GTW61850GOOSEControlBlock*>(pMember);
      if (pGooseCB)
      {
        if (pGooseCB->GetGOOSEControl() == pControl)
          return pGooseCB;
      }
    }
  }
  return nullptr;
}

void GTW61850Client::GetAssocDataSetControlBlocks(tmw61850::DataSet *pDs, std::list<GTW61850ControlBlock*> &controlBlocks)
{
  GTWCollectionBase *pCollection = GetMemberCollection();
  SHARED_LOCK_GTW_COLLECTION(pCollection);
  auto pos = pCollection->GetMap()->begin();

  for (; pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;

    GTW61850ReportControlBlock *pRCB = dynamic_cast<GTW61850ReportControlBlock*>(pMember);
    if (pRCB && pRCB->GetDataSetControl() == pDs)
    {
      controlBlocks.push_back(pRCB);
      continue;
    }

    GTW61850GOOSEControlBlock *pGooseCB = dynamic_cast<GTW61850GOOSEControlBlock*>(pMember);
    if (pGooseCB && pGooseCB->GetDataSetControl() == pDs)
    {
      controlBlocks.push_back(pGooseCB);
      continue;
    }

    GTW61850PolledDataSet *polledDS = dynamic_cast<GTW61850PolledDataSet*>(pMember);
    if (polledDS && polledDS->GetDataSetControl() == pDs)
    {
      controlBlocks.push_back(polledDS);
    }
  }
}

void GTW61850Client::ConnectGOOSEPoints()
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  auto pos = m_61850itemMap.begin();
  for (; pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pI = pos->second;

    if (pI->getBdo()->IsA("GTW61850DataAttributeMDO"))
    {
      GTW61850DataAttributeMDO* pItem = (GTW61850DataAttributeMDO *)pI;
      if (pItem != nullptr)
      {
        if (pItem->GetControlBlock() != nullptr && pItem->GetControlBlock()->GetGOOSEControl() != nullptr)
        {
          tmw61850::GOOSEControl *pGOOSE = GetGOOSEControl(pItem->GetControlBlock()->GetMemberName());

          if (SetDataAttrMdo(
            this->GetFullName() + "." + pItem->m_sValueTagName,
            pItem->m_sQualityTagName,
            pItem->m_sTimeTagName,
            pItem,
            pGOOSE->GetDataSet()
          ) == false)
          {
            LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, (const char*)this->GetAliasName(), "Error: Could not find point: %s for goose control block: '%s' in 61850 client model: %s.",
              (const char *)pItem->m_sValueTagName, (const char *)pItem->GetControlBlock()->GetMemberName(), (const char *)this->GetAliasName());
            continue;
          }

          // the MDO point validated so do an initial read
          if (this->GetClientConnection() != nullptr && this->GetClientConnection()->IsConnectionAliveAndReady())
          {
            if (pItem->m_pValueDataAttribute)
            {
              GetClientConnection()->ReadNode(pItem->m_pValueDataAttribute);
            }
            if (pItem->m_pTimeDataAttribute)
            {
              GetClientConnection()->ReadNode(pItem->m_pTimeDataAttribute);
            }
            if (pItem->m_pQualityDataAttribute)
            {
              GetClientConnection()->ReadNode(pItem->m_pQualityDataAttribute);
            }
          }
        }
      }
    }
  }
}

void GTW61850Client::ConnectNonGOOSEPoints()
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  for (auto pos = m_61850itemMap.begin(); pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pI = pos->second;

    if (pI->getBdo()->IsA("GTW61850DataAttributeMDO"))
    {
      GTW61850DataAttributeMDO* pItem = (GTW61850DataAttributeMDO*)pI;
      if (pItem != nullptr)
      {
        GTW61850ControlBlock* pCB = pItem->GetControlBlock();
        if (pCB && pCB->GetReportControl() != nullptr)
        {
          tmw61850::ReportControl* pRPT = pCB->GetReportControl();// GetReportControl(pCB->GetMemberName());

          if (SetDataAttrMdo(
            this->GetFullName() + "." + pItem->m_sValueTagName,
            pItem->m_sQualityTagName,
            pItem->m_sTimeTagName,
            pItem,
            pRPT->GetDataSet()
          ) == false)
          {
            LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not find data attribute: %s in the dataset '%s' of the report:'%s' in the 61850 model for client: %s.",
              (const char*)pItem->m_sValueTagName, pCB->GetReportControl()->GetDataSetName(), (const char*)pCB->GetMemberName(), (const char*)this->GetAliasName());
            continue;
          }
        }
        else if (pCB && pCB->GetGOOSEControl() != nullptr)
        {
          tmw61850::GOOSEControl* pGC = pCB->GetGOOSEControl();

          if (SetDataAttrMdo(
            this->GetFullName() + "." + pItem->m_sValueTagName,
            pItem->m_sQualityTagName,
            pItem->m_sTimeTagName,
            pItem,
            pGC->GetDataSet()
          ) == false)
          {
            LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not find data attribute: '%s' in the goose control block: '%s' in the 61850 model.",
              (const char*)pItem->m_sValueTagName, (const char*)pCB->GetMemberName());
            continue;
          }
        }
        else if (pCB && pCB->GetDataSetControl() != nullptr)
        {
          tmw61850::DataSet* pDS = GetPolledDataSet(pCB->GetMemberName());

          if (!pDS || !SetDataAttrMdo(this->GetFullName() + "." + pItem->m_sValueTagName, pItem->m_sQualityTagName, pItem->m_sTimeTagName, pItem, pDS))
          {
            LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not find data attribute: '%s' in the polled data set: '%s' in the 61850 model.",
              (const char*)pItem->m_sValueTagName, (const char*)pCB->GetMemberName());
            continue;
          }
        }
        else if (pCB && pCB->GetPointSetControl() != nullptr)
        {
          GTW61850PolledPointSet* pThis = (GTW61850PolledPointSet*)pCB->GetPointSetControl();
          if (pItem->IsA("GTW61850DataAttributeMDO"))
          {
            //GTWBaseDataObject *pBdo = (GTWBaseDataObject *)pItem;
            //GTW61850DataAttributeMDO *pMdo = (GTW61850DataAttributeMDO *)pBdo->GetMdo();
            //          if (pMdo && pMdo->GetControlBlock() && pMdo->GetControlBlock()->GetPointSetControl() == pThis)
            //          {
            if (SetDataAttrMdo(
              this->GetFullName() + "." + pItem->m_sValueTagName,
              pItem->m_sQualityTagName,
              pItem->m_sTimeTagName,
              pItem,
              pThis
            ) == false)
            {
              LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not find point: '%s' for polled point set: '%s' in the 61850 model.",
                (const char*)pItem->m_sValueTagName, (const char*)pCB->GetMemberName());
              continue;
            }
          }
        }
        else if (pCB && dynamic_cast<GTW61850CommandPointSet*>(pCB) != nullptr)
        {
          if (SetDataAttrMdo(
            this->GetFullName() + "." + pItem->m_sValueTagName,
            pItem->m_sQualityTagName,
            pItem->m_sTimeTagName,
            pItem
          ) == false)
          {
            LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not find control point: '%s' in the 61850 model.",
              (const char*)pItem->m_sValueTagName);
            continue;
          }
          else // update now which will update the value and write the point
          {
            GTWMasterDataObject* pMdo = nullptr;
            if (pItem->m_MdoReadBoundList.getSize() > 0)
            {
              pMdo = pItem->m_MdoReadBoundList.getMember(0);
              GTWDEFS_STD_QLTY q = pMdo->getMdoStdQuality();
              if (q == GTWDEFS_STD_QLTY_GOOD)
              {
                pItem->updateMDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);
              }
            }
          }
        }
        else if (pCB && dynamic_cast<GTW61850WritablePointSet*>(pCB) != nullptr)
        {
          if (SetDataAttrMdo(
            this->GetFullName() + "." + pItem->m_sValueTagName,
            pItem->m_sQualityTagName,
            pItem->m_sTimeTagName,
            pItem
          ) == false)
          {
            LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Could not find writable point: '%s' in the 61850 model.",
              (const char*)pItem->m_sValueTagName);
            continue;
          }
          else // update now which will update the value and write the point
          {
            GTWMasterDataObject* pMdo = nullptr;
            if (pItem->m_MdoReadBoundList.getSize() > 0)
            {
              pMdo = pItem->m_MdoReadBoundList.getMember(0);
              GTWDEFS_STD_QLTY q = pMdo->getMdoStdQuality();
              if (q == GTWDEFS_STD_QLTY_GOOD)
              {
                pItem->updateMDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);
              }
            }
          }
        }
      }
    }
  }
}

/**********************************************************************************\
Function :			GTWOPCClient::InvalidateItems
Description : [none]	
Return :			void	-	
Parameters :
void	-	
Note : [none]
\**********************************************************************************/
void GTW61850Client::InvalidateItems()//CStdString type)
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  bool bUpdateQuality = !GTWConfig::I61850ClientDoNotChangeQualityOnDisconnect[this->m_obIndex];
  if (!bUpdateQuality)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "NOTICE : 61850 Client '%s' will not update MDO quality on a disconnect because I61850ClientDoNotChangeQualityOnDisconnect ini parameter is true",
      this->GetFullName().c_str());
  }

  for (auto pos = m_61850itemMap.begin(); pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pI = pos->second;

    if (pI && pI->getBdo() && pI->getBdo()->IsA("GTW61850DataAttributeMDO"))
    {
      GTW61850DataAttributeMDO* pItem = dynamic_cast<GTW61850DataAttributeMDO*>(pI);
      if (pItem != nullptr)
      {
        if (bUpdateQuality)
        {
          pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;
          pItem->m_bInitialized = false;
          pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
        }
          /*
        GTW61850ControlBlock *pCtrlBlock = pItem->GetControlBlock();
        if (pCtrlBlock != nullptr)
        {
            pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_INVALID;
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          if ( type == "GSE" && pCtrlBlock->GetGOOSEControl() != nullptr)
          {
            pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_INVALID;
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
          else if (type == "RPT" && pCtrlBlock->GetReportControl() != nullptr)
          {
            pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_INVALID;
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
          else if (type == "PDS" && pCtrlBlock->GetDataSetControl() != nullptr)
          {
            pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_INVALID;
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
          else if (type == "PPS" && pCtrlBlock->GetPointSetControl() != nullptr)
          {
            pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_INVALID;
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
        }
        else if ( type == "CMD")
        {
          pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_INVALID;
          pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
        }
          */
      }    
    }
    else if (pI && pI->getBdo()->IsA("GTW61400AlarmMDO"))// && type == "ALM")
    {
      GTW61400AlarmMDO* pItem = dynamic_cast<GTW61400AlarmMDO*>(pI);
      if (pItem)
      {
        pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;
        pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
      }
    }
  }
}

/*
void GTW61850Client::UpdateAllMDOs(CStdString type)
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  POSITION pos = m_61850itemMap.GetStartPosition();
  while (pos != nullptr)
  {
    GTWMasterDataObject* pI;
    void *id;
    m_61850itemMap.GetNextAssoc(pos,id,pI);
    if (pI->getBdo()->IsA("GTW61850DataAttributeMDO"))
    {
      GTW61850DataAttributeMDO* pItem = (GTW61850DataAttributeMDO*)pI;
      if (pItem != nullptr)
      {
        if (pItem->GetControlBlock() != nullptr)
        {
          if (pItem->GetControlBlock()->GetGOOSEControl() != nullptr && type == "GSE")
          {
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
          if (pItem->GetControlBlock()->GetReportControl() != nullptr && type == "RPT")
          {
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
          if (pItem->GetControlBlock()->GetDataSetControl() != nullptr && type == "PDS")
          {
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
          if (pItem->GetControlBlock()->GetPointSetControl() != nullptr && type == "PPS")
          {
            pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
          }
        }
        else if ( type == "CMD")
        {
          pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
        }
      }    
    }
    else if (pI->getBdo()->IsA("GTW61400AlarmMDO") && type == "ALM")
    {
      GTW61400AlarmMDO* pItem = (GTW61400AlarmMDO*)pI;
      pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
    }
  }
}
*/

void GTW61850Client::UpdateAlarmMDOs()
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  for (auto pos = m_61850itemMap.begin(); pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pI = pos->second;

    if (pI && pI->getBdo() && pI->getBdo()->IsA("GTW61400AlarmMDO"))
    {
      GTW61400AlarmMDO* pItem = (GTW61400AlarmMDO*)pI;
      pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
    }
  }
}

GTW61850DataAttributeMDO* GTW61850Client::FindMdo(tmw61850::DataAttribute *pValueDA)
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  for (auto pos = m_61850itemMap.begin(); pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pI = pos->second;

    GTW61850DataAttributeMDO *p61850Mdo = nullptr;
    if (pI && (p61850Mdo = dynamic_cast<GTW61850DataAttributeMDO*>(pI->getBdo())))
    {
      if (p61850Mdo->m_pValueDataAttribute == pValueDA)
      {
        return p61850Mdo;
      }
    }
  }

  return nullptr;
}

GTWCollectionMember* GTW61850Client::FindMdo(const CStdString &sFullMdoPath)
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  CStdString sFullPath = sFullMdoPath;
  CStdString sClientNameWithDot = GetMemberName() + ".";
  if (sFullPath.find(sClientNameWithDot) == std::string::npos)
  {
    sFullPath = sClientNameWithDot + sFullPath;
  }

  for (auto pos = m_61850itemMap.begin(); pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pItemMdo = pos->second;

    CStdString sPath = pItemMdo->getBdo()->GetFullName();
    if (sPath == sFullPath)
    {
      return pItemMdo->getBdo();
      //dynamic_cast<GTW61850DataAttributeMDO*>(pItemMdo->getBdo()));
      //return (GTW61850DataAttributeMDO*)pItemMdo;
    }
  }

  return nullptr;
}

GTW61850ControlBlock* GTW61850Client::FindControlBlock(const std::string &sName)
{
  GTWCollectionBase *pCollection = GetMemberCollection();
  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850ControlBlock *pCB = dynamic_cast<GTW61850ControlBlock*>(pMember);
    if (pCB && sName == pCB->GetMemberName())
    {
      return pCB;
    }
  }

  return nullptr;
}

tmw61850::DataAttribute* GTW61850Client::FindDataAttribute(const CStdString& sPath)
{
  return tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(sPath.c_str(), m_p61850ClientConnection->Model());
}

bool GTW61850Client::isControl(tmw61850::DataAttribute* pDa)
{
  CStdString sFC = pDa->GetFC();
  return sFC.Equals("CO", false);
}

/**********************************************************************************\
Function :			GTW61850Client::get61850connection
Description : [none]
Return :			GTW61850Client *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/
GTW61850Client *GTW61850Client::get61850connection(
  const char *name,
  const char *ip_addr)
{
    // Look for a matching client
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;

    CStdString serverIpAddr;
    p61850Client->GetServerIpAddress(serverIpAddr);
    if (ip_addr == nullptr || ip_addr == serverIpAddr)
    {
      // Look for a matching server name
      if (name == p61850Client->m_aliasName)
      {
        return p61850Client;
      }
    }
  }
  return nullptr;
}

GTW61850Client *GTW61850Client::get61850Client(const CStdString &name)
{
  if (name == nullptr || name == "")
  {
    return nullptr;
  }

  // Look for a matching client
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;

    // Look for a matching server name
    if (name == p61850Client->m_aliasName)
    {
      return p61850Client;
    }
  }
  return nullptr;
}

GTW61850Client *GTW61850Client::get61850Client(unsigned int index)
{
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;

    if (index == p61850Client->Get61850ClientIndex())
    {
      return p61850Client;
    }
  }
  return nullptr;
}

GTW61850Client *GTW61850Client::get61850Client(tmw61850::Client *pClient)
{
  // Look for a matching client
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;

    // Look for a matching server name
    if (pClient == p61850Client->GetClientConnection())
    {
      return p61850Client;
    }
  }
  return nullptr;
}

GTW61850ControlBlock *GTW61850Client::get61850ControlBlock(const char *sFullName)
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    if (pMember->GetFullName() == sFullName)
    {
      return (GTW61850ControlBlock*)pMember;
    }
  }
  return nullptr;
}


/**********************************************************************************\
Function :			GTW61850Client::get61850goose
Description : [none]
Return :			GTW61850Client *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/

GTW61850GOOSEControlBlock *GTW61850Client::get61850goose(const char *name)
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);
  
  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850GOOSEControlBlock *pGCB = dynamic_cast<GTW61850GOOSEControlBlock*>(pMember);
    if (pGCB)
    {
      CStdString gooseName = pGCB->GetFullName();
      if (gooseName == CStdString(name))
      {
        return pGCB;
      }
    }
  }
  return nullptr;
}

/**********************************************************************************\
Function :			GTW61850Client::get61850report
Description : [none]
Return :			GTW61850Client *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/
GTW61850ReportControlBlock *GTW61850Client::get61850report(const char *name)
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  CStdString reportName = name;
  if (!reportName.StartsWith(this->GetMemberName()))
  {
    reportName = GetMemberName() + "." + reportName;
  }

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850ReportControlBlock *pReportMember = dynamic_cast<GTW61850ReportControlBlock*>(pMember);
    if (pReportMember)
    {
      CStdString rptName = pReportMember->GetFullName();
      if (rptName == reportName || ((rptName + "01") == reportName) || (rptName == (reportName + "01")))
      {
        return pReportMember;
      }
    }
  }
  return nullptr;
}

/**********************************************************************************\
Function :			GTW61850Client::get61850polledDataSet
Description : [none]
Return :			GTW61850Client *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/
GTW61850PolledDataSet *GTW61850Client::get61850polledDataSet(const char *name)
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  for (auto pos = pCollection->GetMap()->begin(); pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850PolledDataSet *pPolledDataSetMember = (GTW61850PolledDataSet*)pMember;
    if (pPolledDataSetMember->IsA("GTW61850PolledDataSet"))
    {
      CStdString dsFullName = pPolledDataSetMember->GetFullName();
      CStdString dsName = pPolledDataSetMember->GetMemberName();
      if (dsName == name || dsFullName == name)
      {
        //pCollection->UnLock();
        return pPolledDataSetMember;
      }
    }
  }
  return nullptr;
}

/**********************************************************************************\
Function :			GTW61850Client::get61850polledPointSet
Description : [none]
Return :			GTW61850Client *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/
GTW61850PolledPointSet *GTW61850Client::get61850polledPointSet(const char *name)
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  auto pos = pCollection->GetMap()->begin();
  for (; pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850PolledPointSet *pPolledPointSetMember = (GTW61850PolledPointSet*)pMember;
    if (pPolledPointSetMember->IsA("GTW61850PolledPointSet"))
    {
      CStdString dsFullName = pPolledPointSetMember->GetFullName();
      CStdString dsName = pPolledPointSetMember->GetMemberName();
      if (dsName == name || dsFullName == name)
      {
        return pPolledPointSetMember;
      }
    }
  }
  return nullptr;
}

/**********************************************************************************\
Function :			GTW61850Client::get61850WritablePointSet
Description : [none]
Return :			GTW61850Client *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/
GTW61850WritablePointSet *GTW61850Client::get61850WritablePointSet(const char *name)
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  {
	  SHARED_LOCK_GTW_COLLECTION(pCollection);
	
	  auto pos = pCollection->GetMap()->begin();
	  for (; pos != pCollection->GetMap()->end(); ++pos)
	  {
	    GTWCollectionMember *pMember = pos->second;
	    GTW61850WritablePointSet *pWritablePointSetMember = (GTW61850WritablePointSet *)pMember;
	    if (pWritablePointSetMember->IsA("GTW61850WritablePointSet"))
	    {
	      CStdString dsFullName = pWritablePointSetMember->GetFullName();
	      CStdString dsName = pWritablePointSetMember->GetMemberName();
	      if (dsName == name || dsFullName == name)
	      {
	        return pWritablePointSetMember;
	      }
	    }
	  }
  }

  //GTW61850WritablePointSet *pWritablePointSet = nullptr;
  //GetGTWApp()->GetPointMap()->Create61850WritablePointSet(this, "WritablePointsSet", &pWritablePointSet, this->Get61850ClientIndex());
  return nullptr;// pWritablePointSet;
}

/**********************************************************************************\
Function :			GTW61850Client::get61850CommandPointSet
Description : [none]
Return :			GTW61850Client *	-
Parameters :
const char *name	-
const char *node_name	-
Note : [none]
\**********************************************************************************/
GTW61850CommandPointSet *GTW61850Client::get61850CommandPointSet(const char *name)
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  {
    SHARED_LOCK_GTW_COLLECTION(pCollection);

    auto pos = pCollection->GetMap()->begin();
    for (; pos != pCollection->GetMap()->end(); ++pos)
    {
      GTWCollectionMember *pMember = pos->second;
      GTW61850CommandPointSet *pCommandPointSetMember = (GTW61850CommandPointSet *)pMember;
      if (pCommandPointSetMember->IsA("GTW61850CommandPointSet"))
      {
        CStdString dsFullName = pCommandPointSetMember->GetFullName();
        CStdString dsName = pCommandPointSetMember->GetMemberName();
        if (dsName == name || dsFullName == name)
        {
          return pCommandPointSetMember;
        }
      }
    }
  }

  //GTW61850CommandPointSet *pCommandPointSet = nullptr;
  //GetGTWApp()->GetPointMap()->Create61850CommandPointSet(this, "CommandPointsSet", &pCommandPointSet, this->Get61850ClientIndex());
  return nullptr;// pCommandPointSet;
}


/**********************************************************************************\
Function :			GTW61850Client::UponInsert
Description : [none]
Return :			void	-
Parameters :
GTWCollectionBase *pParent	-
Note : [none]
\**********************************************************************************/
void GTW61850Client::UponInsert(GTWCollectionBase *pParent)
{
  m_pMemberCollection = new GTWCollectionList(this);
  GTWCollectionListParent::UponInsert(pParent);
}

/**********************************************************************************\
Function :			GTW61850Client::CompareTagName
Description : [none]
Return :			GTWDEFS_STAT	-
Parameters :
const char  **ppTagName	-
Note : [none]
\**********************************************************************************/
GTWDEFS_STAT GTW61850Client::CompareTagName(  CStdString &tagName)
{
  GTWDEFS_STAT stat = GTWBaseDataObject::CompareTagField(tagName,m_aliasName);
  return(stat);
}

/*
bool GTW61850Client::DataSetEntryExists(const char* sLogicalName,
                          const char* sReportControlBlock,
                          const char* sGooseControlBlock,
                          const char* sDataSetName,
                          const char* sDataSetElementName)
{
  POSITION pos = m_DataSetEntries.GetHeadPosition();
  while (pos)
  {
    DataSetEntry *pDS = m_DataSetEntries.GetNext(pos);
    if (pDS)
    {
      if (pDS->logicalName.equals(sLogicalName) && pDS->reportControlBlock.equals(sReportControlBlock) &&
        pDS->gooseControlBlock.equals(sGooseControlBlock) && pDS->dataSetName.equals(sDataSetName) &&
        pDS->dataSetElementName.equals(sDataSetElementName))
      {
        return true;
      }
    }
  }
  return false;
}
*/

void GTW61850Client::AddDataSetEntry(const char *_lnName, const char *_dsName, const char *_dsElementName)
{
  GTW61850DynamicDataSet *pDs = FindDataSet(_lnName, _dsName);
  if (!pDs)
  {
    pDs = new GTW61850DynamicDataSet();
    pDs->logicalName = _lnName;
    pDs->dataSetName = _dsName;
    m_DataSets.add(pDs);
  }

  pDs->dataSetElements.push_back(_dsElementName);

  /*
  if (DataSetEntryExists(_lnName,"","",_dsName,_dsElementName))
    return;

  DataSetEntry *dse = new DataSetEntry();
  dse->logicalName = _lnName;
  dse->dataSetName = _dsName;
  dse->dataSetElementName = _dsElementName;
  m_DataSetEntries.AddTail(dse);
  */
}

void GTW61850Client::AddPointSetEntry(const char *_lnName, const char *_dsName, const char *_dsElementName)
{
  AddDataSetEntry(_lnName, _dsName, _dsElementName);

  /*
  if (DataSetEntryExists(_lnName,"","",_dsName,_dsElementName))
    return;

  DataSetEntry *dse = new DataSetEntry();
  dse->logicalName = _lnName;
  dse->dataSetName = _dsName;
  dse->dataSetElementName = _dsElementName;
  m_DataSetEntries.AddTail(dse);
  */
}

void GTW61850Client::AddDataSetEntryRCB(const char *_lnName, const char *_rcbName, const char *_dsName, const char *_dsElementName)
{
  AddDataSetEntry(_lnName, _dsName, _dsElementName);

  /*
  if (DataSetEntryExists(_lnName,_rcbName,"",_dsName,_dsElementName))
    return;

  DataSetEntry *dse = new DataSetEntry();
  dse->logicalName = _lnName;
  dse->reportControlBlock = _rcbName;
  dse->dataSetName = _dsName;
  dse->dataSetElementName = _dsElementName;
  m_DataSetEntries.AddTail(dse);
  */
}

void GTW61850Client::AddDataSetEntryGCB(const char *_lnName, const char *_gcbName, const char *_dsName, const char *_dsElementName)
{
  AddDataSetEntry(_lnName, _dsName, _dsElementName);

  /*
  if (DataSetEntryExists(_lnName,"",_gcbName,_dsName,_dsElementName))
    return;

  DataSetEntry *dse = new DataSetEntry();
  dse->logicalName = _lnName;
  dse->gooseControlBlock = _gcbName;
  dse->dataSetName = _dsName;
  dse->dataSetElementName = _dsElementName;
  m_DataSetEntries.AddTail(dse);
  */
}

void GTW61850Client::SaveDataSets(FILE *pFile)
{
  /*
  POSITION pos = m_DataSetEntries.GetHeadPosition();
  while(pos)
  {
    DataSetEntry *pDS = m_DataSetEntries.GetNext(pos);
    if (pDS != nullptr)
    {
      SaveDataSetElement(pDS, pFile);
    }
  }
  */
  
  for (unsigned int dsIdx = 0; dsIdx < m_DataSets.getSize(); dsIdx++)
  {
    GTW61850DynamicDataSet *pDs = m_DataSets.getAt(dsIdx);
    for (unsigned int elemIdx = 0; elemIdx < pDs->dataSetElements.size(); elemIdx++)
    {
      SaveDataSetElement(pDs, elemIdx, pFile);
    }
  }
}

void GTW61850Client::SaveDataSetElement(GTW61850DynamicDataSet *pDs, unsigned int elemIdx, FILE *pFile)
//void GTW61850Client::SaveDataSetElement(DataSetEntry *pDS, FILE *pFile)
{
  char fieldDelimiter;
  util::strncpy(&fieldDelimiter, 1, GTWConfig::CSVFileFieldDelimiter, 1);
  CStdString tail;

  tail.Format("%c%s", fieldDelimiter, "I61850_DS");
  char outStr[2048];
  snprintf(outStr, 2047, "%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%c%s%s\n",
    (const char *)this->GetAliasName(),
    fieldDelimiter,
    (const char *)pDs->logicalName,
    fieldDelimiter,
    (const char *)pDs->dataSetName,
    fieldDelimiter,
    (const char *)pDs->dataSetElements[elemIdx],
    fieldDelimiter,
    (const char *)pDs->reportControlBlock,
    fieldDelimiter,
    (const char *)pDs->gooseControlBlock,
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    fieldDelimiter,
    "",
    tail.c_str()
    );
  fprintf(pFile,"%s",outStr);
}

GTW61850DynamicDataSet* GTW61850Client::FindDataSet(const CStdString& dsLogicalName, const CStdString& dsDataSetName)
{
  for (unsigned int i = 0; i < m_DataSets.getSize(); i++)
  {
    GTW61850DynamicDataSet *ds = m_DataSets.getAt(i);
    if (ds->logicalName == dsLogicalName && ds->dataSetName == dsDataSetName)
    {
      return ds;
    }
  }
  return nullptr;
}

void GTW61850Client::CreateDataSet(const CStdString& aliasName,
                                    const CStdString& dsLogicalName,
                                    const CStdString& rcbName,
                                    const CStdString& gcbName,
                                    const CStdString& dsDataSetName,
                                    const CStdString& dsDSElementName)
{
  for (unsigned int i = 0; i < m_DataSets.getSize(); i++)
  {
    if (m_DataSets.getAt(i)->aliasName == aliasName &&
        m_DataSets.getAt(i)->logicalName == dsLogicalName &&
        m_DataSets.getAt(i)->dataSetName == dsDataSetName &&
        (m_DataSets.getAt(i)->reportControlBlock == rcbName ||
            m_DataSets.getAt(i)->gooseControlBlock == gcbName))
    {
      m_DataSets.getAt(i)->dataSetElements.push_back(dsDSElementName);
      return;
    }
  }

  if (FindDataSet(dsLogicalName, dsDataSetName))
    return;

  GTW61850DynamicDataSet *ds = new GTW61850DynamicDataSet();

  ds->aliasName          = aliasName;
  ds->logicalName        = dsLogicalName;
  ds->reportControlBlock = rcbName;
  ds->gooseControlBlock  = gcbName;
  ds->dataSetName        = dsDataSetName;

  ds->dataSetElements.push_back(dsDSElementName);

  m_DataSets.add(ds);
  return;
}

void GTW61850Client::DeleteGTWDataSet(const String& lnName, const String& dsName)
{
  GTW61850DynamicDataSet *pGtwDs = FindDataSet(CStdString(lnName), CStdString(dsName));
  if (pGtwDs)
  {
    m_DataSets.removeAt(m_DataSets.find(pGtwDs));
    delete pGtwDs;
  }
}

void GTW61850Client::LoadDataSets()
{
  /*
  POSITION pos = m_DataSetEntries.GetHeadPosition();
  while(pos)
  {
    DataSetEntry *pDS = m_DataSetEntries.GetNext(pos);
    if(pDS != nullptr)
    {
      CreateDataSet(this->GetAliasName(), CStdString(pDS->logicalName), CStdString(pDS->reportControlBlock), CStdString(pDS->gooseControlBlock), CStdString(pDS->dataSetName), CStdString(pDS->dataSetElementName));
    }
  }
  */
  //Array<String> domainList;
  //tmw::Array<tmw::String> serverMemberNames;
  for (unsigned int i = 0; i < m_DataSets.getSize(); i++)
  {
    GTW61850DynamicDataSet *ds = m_DataSets.getAt(i);
    LoadDataSet(ds->logicalName, ds->reportControlBlock, ds->gooseControlBlock, ds->dataSetName,
      &ds->dataSetElements);//, domainList, serverMemberNames);
  }
}

bool GTW61850Client::LoadDataSet(const CStdString& dsLogicalName, const CStdString&  rcbName, const CStdString&  gcbName,
                                    const CStdString&  dsDataSetName, std::vector<CStdString> *dsDSElements)//, tmw::Array<tmw::String> &domainList, tmw::Array<tmw::String> &serverMemberNames)
{
  bool failed = false;
  bool bDoValidate = GetDoValidate();

  tmw61850::Client *pClient  = this->GetClientConnection();
  if (pClient == nullptr || !pClient->IsConnectionAliveAndReady())
  {
    return false;
  }

  tmw61850::LogicalNode *ln = tmw61850::i61850RootNode::FindNode<tmw61850::LogicalNode>(dsLogicalName.c_str(), pClient->Model());
  if (ln == nullptr && bDoValidate == true)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "No logical node '%s' to load datasets",dsLogicalName.c_str());
    return false;
  }

  tmw61850::ReportControl const *rpt = nullptr;
  if (rcbName.length() > 0) //!rcbName.isEmpty())
  {
    CStdString fullName = dsLogicalName;
    fullName += ".";
    fullName += rcbName;
    rpt = FindReportControlBlockInModel(fullName);//i61850RootNode::FindNode<tmw61850::ReportControl>(fullName, pClient->Model());

    if (rpt == nullptr && bDoValidate == true)
    {
      LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "No report node: %s to load datasets", rcbName.c_str());
      return false;
    }
  }
  
  tmw61850::GOOSEControl *gcb = nullptr;
  if (gcbName.length() > 0) //!gcbName.isEmpty())
  {
    CStdString fullName = dsLogicalName;
    fullName += ".";
    fullName += gcbName;
    gcb = tmw61850::i61850RootNode::FindNode<tmw61850::GOOSEControl>(fullName.c_str(), pClient->Model());

    if (gcb == nullptr && bDoValidate == true)
    {
      LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "No goose node: %s to load datasets", gcbName.c_str());
      return false;
    }
  }

  const tmw::Array<tmw61850::DataSet*> *dsNodes = pClient->Model()->GetDataSets(ln);

  if (dsNodes == nullptr && bDoValidate == true)
  {
    LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "No data set nodes on logical node '%s' to load datasets", dsLogicalName.c_str());
    return false;
  }

  bool bDsFound = false;
  tmw61850::DataSet *ds = nullptr;
  if (dsNodes != nullptr)
  {
    for (unsigned int i=0; i < dsNodes->getSize(); i++)
    {
      if (tmw::util::compareNoCase(dsNodes->getAt(i)->GetName(), dsDataSetName))
      {
        ds = dsNodes->getAt(i);
        bDsFound = true;
        break;
      }
    }
  }

  for (auto iter = dsDSElements->begin(); iter != dsDSElements->end(); ++iter)
  {
    CStdString& dsElem = *iter;
    tmw61850::Node *n = tmw61850::DataAttribute::FindDataAttributeFromMMSName(dsElem.c_str(), pClient->Model(), '.');

    if (n && !bDsFound)
    {
      if (ln && ds == nullptr)
      {
        ds = new tmw61850::DataSet();
        ds->SetName(dsDataSetName);
        ln->AddChild(ds);
      }

      tmw::Array<tmw::String> pieces;
      tmw::String fullName;
      tmw61850::Node::ParseName(dsElem, pieces);

      AddDataSetMember(n, ds, pieces[2]);
    }
  }

  if (bDsFound)
  {
    // Dataset may exist in the local client model but not in the server, so still need to create it on the server
    try
    {
      tmw61850::ClientEnumDefs::ClientMMSErrorCode errCode;
      if ( (errCode = pClient->ReadNodeBlocking(ds)) != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success )
      {
        bDsFound = false;
      }
    }
    catch (...)
    {
      if (!pClient->IsConnectionAliveAndReady())
      {
        return false;
      }
    }
  }

  if (!bDsFound)
  {
    if (ds)
    {
      //GTW61850ClientUtils utils(this);
      //if (utils.GetDataSetMembersOnServer(ds, serverMemberNames))
     // {
      //  utils.SyncNewDataSetWithServer(ds, domainList, serverMemberNames);
     // }
      
      tmw61850::ClientEnumDefs::ClientMMSErrorCode errCode = pClient->CreateDataSet(ds);
      if (errCode != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success && errCode != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Confirmed_Definition_Object_Exists)
      {
        tmw61850::ClientEnumDefs::ClientMMSErrorCode eCode = pClient->ReadNodeBlocking(ds);
        if (eCode != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
        {
          if (!pClient->IsConnectionAliveAndReady())
          {
            return false;
          }
          //ln->RemoveChild(ds);
          //delete ds;
          LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, 
            "CreateDataSet failed for %s.%s with error %s", dsLogicalName.c_str(), dsDataSetName.c_str(), tmw61850::ClientMessage::ClientMMSErrorCodeToString(errCode));
          return false;
        }
      }
    }
  }

  return true;
}

tmw61850::DataSetMember* GTW61850Client::AddDataSetMember(tmw61850::Node *node, tmw61850::DataSet *pDataSet, const char* fc)
{
  tmw61850::DataSetMember *dsm = new tmw61850::DataSetMember();
  pDataSet->AddChild(dsm);

  tmw::String fullName;
  node->GetFullName(fullName, true);

  dsm->SetName(fullName);
  dsm->SetFC(fc);

  return dsm;
}

tmw61850::ReportControl* GTW61850Client::FindReportControlBlockInModel(const CStdString &sName)
{
  tmw61850::ReportControl *pRPT = tmw61850::i61850RootNode::FindNode<tmw61850::ReportControl>(sName, GetClientConnection()->Model());
  if (pRPT == nullptr)
  {
    CStdString newName;
    int len = sName.GetLength();
    if (sName.GetLength() > 2 && sName[len-1] == '1' && sName[len-2] == '0') // see if ends in "01"
    {
      newName = sName.Mid(0,len-2);
    }
    else
    {
      newName = sName;
      newName += "01";
    }
    pRPT = tmw61850::i61850RootNode::FindNode<tmw61850::ReportControl>(newName, GetClientConnection()->Model());
  }
  return pRPT;
}

void GTW61850Client::Connect61400Alarms()
{
  //if (GetUseSclFile() == true)
  //  return;

  GTWCollectionBase *psClctn = this->GetMemberCollection();
  if (psClctn)
  {
    // add Polled Point Sets
    for (int iDS =0;iDS< getGTKTPARM_MAX_ALARMS_PER_CLIENT();iDS++)
    {
      CStdString alarms_name = GTWConfig::I61400AlarmsName(m_obIndex,iDS);
      CStdString status_alarms_array_name = GTWConfig::I61400StatusAlarmsArrayName(m_obIndex,iDS);
      CStdString event_alarms_array_name = GTWConfig::I61400EventAlarmsArrayName(m_obIndex,iDS);
      if (alarms_name == "" || status_alarms_array_name == "\"\"" || event_alarms_array_name == "\"\"")
        continue;

      GTW61400Alarms       *p61400Alarms;
      GTWDEFS_STAT status = GetGTWApp()->GetPointMap()->Create61400Alarms(this, alarms_name, status_alarms_array_name, event_alarms_array_name, &p61400Alarms,iDS);
      if (status  != GTWDEFS_STAT_SUCCESS)
      {
        LOG6(GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Error: Could not add \"%s\" Alarms Node.", (const char *)alarms_name);
      }
      else
      {
        LOG6(GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Message: Added \"%s\" Alarms Node.", (const char *)alarms_name);
      }
    }
  }
}

GTW61400Alarms *GTW61850Client::get61400alarms( const char * name )
{
  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  auto pos = pCollection->GetMap()->begin();
  for (; pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61400Alarms *pAlarmsMember = (GTW61400Alarms*)pMember;
    if (pAlarmsMember->IsA("GTW61400Alarms"))
    {
      CStdString alarmsName = pAlarmsMember->GetFullName();
      if (alarmsName == CStdString(name))
      {
        return pAlarmsMember;
      }
    }
  }
  return nullptr;
}

void GTW61850Client::UpdateEventAlarms( GTW61850DataAttributeMDO* pMdo )
{
  TMWTYPES_BOOL bEnableAlarms = GTWConfig::I61850Enable61400AlarmProcessing(this->Get61850ClientIndex());
  if (bEnableAlarms == false)
  {
    return;
  }

  CStdString name = pMdo->GetFullName();
  if (name.Find("ActEvl.actSt.[0].stVal") == -1)
  {
    return;
  }

  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  auto pos = pCollection->GetMap()->begin();
  for (; pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;

    GTW61400Alarms *pAlarmsMember = (GTW61400Alarms*)pMember;
    if (pAlarmsMember->IsA("GTW61400Alarms"))
    {
      CStdString alarmsName = pAlarmsMember->GetFullName();
      if (name.Find(pAlarmsMember->GetEventAlarmsArrayName()) != -1)
      {
        pAlarmsMember->UpdateEventAlarms(pMdo);
        break;
      }
    }
  }
  return;
}

/*
void GTW61850Client::UpdateStatusAlarms( GTW61850DataAttributeMDO* pMdo )
{
  TMWTYPES_BOOL bEnableAlarms = GTWConfig::I61850Enable61400AlarmProcessing(this->Get61850ClientIndex());
  if (bEnableAlarms == TMWDEFS_FALSE)
  {
    return;
  }

  if (pMdo->IsTimeChanged()
    || pMdo->IsQualityChanged()
    || pMdo->IsValueChanged()
    )
  {
    // only process this if value or quality has changed
   // WORD resetAlarmStatus = (WORD)pMdo->getLastAlarmStatusSaved();
    //pMdo->SetLastValue(pMdo->getValueAsVarient());

    CStdString name = pMdo->GetFullName();
    GTWCollectionMember *pMember;
    CStdString key;
    GTWCollectionBase *pCollection = GetMemberCollection();

    tmw::CriticalSectionLock lock(*pCollection);

    POSITION pos = pCollection->GetMap()->GetStartPosition();

    int count = pCollection->GetMap()->GetCount();

    while (pos)
    {
      pCollection->GetMap()->GetNextAssoc(pos,key,pMember);
      GTW61400Alarms *pAlarmsMember = dynamic_cast<GTW61400Alarms*>(pMember);
      if (pAlarmsMember)
      {
        CStdString alarmsName = pAlarmsMember->GetFullName();
        if (-1 != name.Find(pAlarmsMember->GetStatusAlarmsArrayName()))
        {
          pAlarmsMember->UpdateStatusAlarms(pMdo);
          break;
        }
        else
        {
          CStdString s = pAlarmsMember->GetStatusAlarmsArrayName();

          int llll = 3;
        }
      }

    }
  }
  return;
}
*/

GTW61400Alarms* GTW61850Client::FindAlarmsNode(const std::string &sAlarmArrayMemberName)
{
  TMWTYPES_BOOL bEnableAlarms = GTWConfig::I61850Enable61400AlarmProcessing(this->Get61850ClientIndex());
  if (bEnableAlarms == TMWDEFS_FALSE)
  {
    return nullptr;
  }

  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  auto pos = pCollection->GetMap()->begin();
  for (; pos != pCollection->GetMap()->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;

    GTW61400Alarms *pAlarmsMember = dynamic_cast<GTW61400Alarms*>(pMember);
    if (pAlarmsMember)
    {
      if (std::string::npos != sAlarmArrayMemberName.find((const char*)pAlarmsMember->GetStatusAlarmsArrayName()))
      {
        return pAlarmsMember;
      }
    }
  }

  return nullptr;
}

void GTW61850Client::InitAlarms()
{
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  auto pos = m_61850itemMap.begin();
  for (; pos != m_61850itemMap.end(); ++pos)
  {
    GTWMasterDataObject* pI = pos->second; 
    if (dynamic_cast<GTW61400AlarmMDO*>(pI->getBdo()))
    {
      GTW61400AlarmMDO* pItem = (GTW61400AlarmMDO*)pI;
      pItem->m_i61850Quality = I61850_QUALITY_VALIDITY_GOOD;
      pItem->SetValue(false);
      //pItem->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
    }
  }
}

//bool GTW61850Client::IsOnlineChanged()
//{
//  return m_eLastOnlineState != m_eOnlineState;/
//}

void GTW61850Client::SetConnectionActive( bool bActive )
{
  m_p61850ClientActiveControlmdo->SetValue(bActive);
  /*
  if (bActive)
  {
    ConnectToServer();
  }
  else
  {
    DisConnectFromServer();
  }
  */
}

void GTW61850Client::StartClientsOnStartup()
{
  ConnectAll();
  /*
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;
    if (p61850Client->m_p61850ClientActiveControlmdo->GetValue())
    {
      p61850Client->Connect();
    }
  }
  */
}
std::string GTW61850Client::GetNameFromConnection(void *pOb)
{
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;

    if (p61850Client->GetClientConnection() == pOb)
    {
      return p61850Client->GetFullName();
    }
  }
  return "";
}

void GTW61850Client::ConnectAll()
{
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;

    if (!p61850Client->GetClientConnection()->IsConnectionAliveAndReady())
    {
      if (p61850Client->m_p61850ClientActiveControlmdo->GetValue())
      {
        ConnectWorkItem::ConnectClient(p61850Client);
      }
    }
  }
}

uint32_t GTW61850Client::DisconnectAll()
{
  uint32_t nCountDisconnected = 0;
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;
    if (p61850Client->m_p61850ClientConnection && p61850Client->m_p61850ClientConnection->IsConnectionAliveAndReady())
    {
      p61850Client->Disconnect();
      nCountDisconnected++;
    }
  }
  return nCountDisconnected;
}

void GTW61850Client::SetInitialConnect( bool b )
{
  m_bInitialConnect = b;
}

void GTW61850Client::SetStatisticsMdos()
{
  // The GoosTimeout and GooseDrops do not have callbacks to must be set on a timer and to be consistent
  // We set everything here
  m_pNumReadResponsemdo->SetValue(m_NumReadResponse);
  m_pNumGooseEventsmdo->SetValue(m_NumGooseEvents);
  m_pNumReportMessagesmdo->SetValue(m_NumReportMessages);
  m_pNumValueChangesmdo->SetValue(m_NumValueChanges);
  m_pNumQualityChangesmdo->SetValue(m_NumQualityChanges);
  m_pNumIntegritymdo->SetValue(m_NumIntegrity);

  m_pNumGooseTimeOutsmdo->SetValue(GetGooseTimeouts());
  m_pNumGooseDropsmdo->SetValue(GetGooseDrops());
  if (GetClientConnection() != nullptr)
  {
    int reqQsize = GetClientConnection()->GetRequestQueueSize();
    int respQsize = GetClientConnection()->GetResponseQueueSize(tmw61850::Client::ResponseQueueType::Main_ResponseQueue);
    m_pRequestQSizeMdo->SetValue(reqQsize);
    m_pResponseQSizeMdo->SetValue(respQsize);
  }
}

void GTW61850Client::AddClient(GTW61850Client *pClient)
{
  m_ClientList.push_back(pClient);
}

void GTW61850Client::SaveAllDataSets(FILE *pFilePtr)
{
  // Save 61850 datasets
  for (auto clientListPOS = m_ClientList.begin(); clientListPOS != m_ClientList.end(); ++clientListPOS)
  {
    GTW61850Client* p61850Client = *clientListPOS;
    if(p61850Client != nullptr)
    {
      p61850Client->SaveDataSets(pFilePtr);
    }
  }
}

void Gtw61850UpdateStatsTimerInfo::OnTimer( void *pCallBackData )
{
  GTW61850Client *pClient = (GTW61850Client *)pCallBackData;
  pClient->SetStatisticsMdos();
}

void ConnectWorkItem::DoWork(void *param)
{
  bool bSequential = GTWConfig::I61850ClientSequentialConnect;
  if (bSequential)
  {
    ConnectSequential(param);
  }
  else
  {
    Connect(param);
  }
}

void ConnectWorkItem::ConnectSequential(void *param)
{
  tmw::CriticalSectionLock lock(GTW61850Client::GetSequentialConnectSem()); // take global semaphore to force sequential connects - only one workitem thread can call connect at a time

  Connect(param);
}

void ConnectWorkItem::Connect(void *param)
{
  unique_ptr<ConnectWorkItem> autoDeleteThis(this);
  
  if (!m_pClient->GetClientConnection()->IsConnectionAliveAndReady())
  {
    LOG6(m_pClient->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "%s", "Trying connect...");
  }
  else // Already connected, no need to try again
  {
    return;
  }

  if (!m_bForce && !m_pClient->m_p61850ClientActiveControlmdo->GetValue())
  {
    m_pClient->m_pReconnectTimer->KillTimer();

    LOG6(m_pClient->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "%s", "ActiveControl MDO is turned off. The client will no longer try to automatically reconnect");
    return;
  }

  if (!m_bForce && m_pClient->m_RetryCountLimit > 0)
  {
    if (m_pClient->m_i61850RetryCount >= m_pClient->m_RetryCountLimit)
    {
      m_pClient->m_pReconnectTimer->KillTimer();
      
      LOG6(m_pClient->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Reconnect retry count has reached limit %d and will no longer try to automatically reconnect", m_pClient->m_RetryCountLimit);
      return;
    }
  }

  if (!m_pClient->Connect() && !m_pClient->GetClientConnection()->IsConnectionAliveAndReady())
  {
    CStdString serverIPAddr;
    m_pClient->GetServerIpAddress(serverIPAddr);
    tmw61850::ConnectionStatus connectStatus = ((tmw61850::i61850RootNode*)m_pClient->GetClientConnection()->Model())->GetConnectionStatus();

    LOG6(m_pClient->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Connect call failed (to %s, %d). Model State = %s",
                  (const char *)serverIPAddr,
                  m_pClient->GetClientConnection()->GetConfiguration()->GetServerPort(),
                  m_pClient->GetClientConnection()->ConnectionStatusToString(connectStatus));
    

    if (connectStatus == tmw61850::ConnectionStatus::Status_ServerUP && !m_pClient->GetClientConnection()->IsConnectionAliveAndReady())
    {
      LOG6(m_pClient->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "\t%s Client model state is SERVER UP but it is not connected..trying disconnect/connect...", m_pClient->GetFullName().c_str());

      m_pClient->GetClientConnection()->Disconnect();
      m_pClient->Connect();
    }
  }
  ++m_pClient->m_i61850RetryCount;
}

void Gtw61850ClientReconnectTimerIfnfo::OnTimer( void *pCallBackData )
{
  ConnectWorkItem::ConnectClient((GTW61850Client*)pCallBackData);
}

GTW61850ClientChannelActiveControl::GTW61850ClientChannelActiveControl(GTW61850Client *p)
  :
  GTWChannelActiveControlBase(TMWDEFS_TRUE),
  m_pClient(p)
{
}

GTWDEFS_CTRL_STAT GTW61850ClientChannelActiveControl::SetValue(bool newValue)
{
  if (m_pClient == nullptr)
  {
    return GTWDEFS_CTRL_STAT_FAILURE;
  }

  // Otherwise change online status if newValue != currentValue
  //if (GetValue() != newValue)
  //{
    //SetActiveControl(newValue);
  //}
  GTWInternalDataObjectBool::SetValue(newValue);
  if (newValue)
  {
    if (GetGTWApp()->IsShuttingDown() || GetGTWApp()->IsStartingUp())
    {
      return GTWDEFS_CTRL_STAT_SUCCESS;
    }
    ConnectWorkItem::ConnectClient(m_pClient);
  }
  else
  {
    m_pClient->Disconnect();
  }
  return GTWDEFS_CTRL_STAT_SUCCESS;
}

/*
void GTW61850ClientChannelActiveControl::SetActiveControl(bool bActive)
{
  if (_pClient == nullptr)
  {
    return;
  }
  
  _pClient->SetActive(bActive);
}
*/

CStdString GTW61850Client::Encrypt(const CStdString &authPassword)
{
  CStdString errMsg;
  return TmwAsymCrypto::encryptRSA(authPassword, errMsg);
}

CStdString GTW61850Client::Decrypt(const CStdString& encryptedAuthPassword)
{
  CStdString errMsg;
  return TmwAsymCrypto::decryptRSA(encryptedAuthPassword, errMsg);
}

void GTW61850Client::ForceTryConnect()
{
  ConnectWorkItem::ConnectClient(this, true);
}

#ifdef DEBUG
void TRACEDSMEMBERS(tmw61850::DataSet *pDS)
{
  String s;
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "DataSet %s, Parent = %s\n", pDS->GetFullName(s), (pDS->GetParent() ? pDS->GetParent()->GetName() : "NULL"));

  String dsName;
  pDS->GetFullName(dsName);
  Array<Node*> *pChildren = pDS->Children();
  for (unsigned int i = 0; i < pChildren->size(); i++)
  {
    String memberName;
    Node *n = pChildren->getAt(i);
    n->GetFullName(memberName);
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "\t%s Child/MEMBER: %s\n", (const char*) dsName, (const char*)memberName);
  }
}

void TRACE_CHILD(Node *pNode, const CStdString& sIndent)
{
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "%s Child:%s", sIndent.c_str(), pNode->GetName());

  Array<Node*> *pChildren = pNode->Children();

  if (!pChildren || pChildren->size() == 0)
  {
    return;
  }

  CStdString si = sIndent + "\t";

  unsigned int size = pChildren->size();
  for (unsigned int i = 0; i < size; i++)
	{
    Node* pChild = pChildren->getAt(i);
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "%s Child:%s", si.c_str(), pNode->GetName());
    TRACE_CHILD(pChild, si);
  }
}

void GTW61850Client::TRACE_ALL_CHILDREN()
{
  tmw61850::i61850RootNode *pModel = dynamic_cast<tmw61850::i61850RootNode*>(m_p61850ClientConnection->Model());
  Array<Node*> *pChildren = pModel->Children();

  if (!pChildren || pChildren->size() == 0)
  {
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "No Children for %s", + this->GetFullName().c_str());
    return;
  }

  for (unsigned int i = 0; i < pChildren->size(); i++)
  {
    Node* pChild = pChildren->getAt(i);
    TRACE_CHILD(pChild, "\t");
  }
}
#endif


void GTW61850Client::SixTSDateTimeToTMWDTime(const tmw61850::DateTime &tsDateTime, TMWDTIME &tmwDTime)
{
	memset(&tmwDTime, 0, sizeof(TMWDTIME));
	tmwDTime.invalid = false;
	tmwDTime.qualifier = TMWDTIME_ABSOLUTE;
	tmwDTime.dayOfMonth = tsDateTime.GetDay();
	tmwDTime.month = tsDateTime.GetMonth();
	tmwDTime.year = tsDateTime.GetYear();
	tmwDTime.minutes = tsDateTime.GetMinute();
	tmwDTime.hour = tsDateTime.GetHour();
	tmwDTime.mSecsAndSecs = (tsDateTime.GetSecond() * 1000) + tsDateTime.GetMillSec();
}

void GTW61850Client::TMWDTimeToSixTSDateTime(const TMWDTIME &tmwDTime, tmw61850::DateTime &tsDateTime)
{
	tsDateTime.Clear();

	tsDateTime.SetYear(tmwDTime.year);
	tsDateTime.SetMonth(tmwDTime.month);
	tsDateTime.SetDay(tmwDTime.dayOfMonth);

	tsDateTime.SetHour(tmwDTime.hour);
	tsDateTime.SetMinute(tmwDTime.minutes);
	tsDateTime.SetSecond(tmwDTime.mSecsAndSecs / 1000);
	tsDateTime.SetMillSec(tmwDTime.mSecsAndSecs % 1000);
	tsDateTime.SetMicroSec(0);
}

/*
#include <iostream>

std::ofstream modelfile("c:\\clientmodel.txt");
void TRACEDSMEMBERS(tmw61850::DataSet *pDS)
{
  String s;
  modelfile << "DataSet %s, Parent = %s\n", pDS->GetFullName(s), (pDS->GetParent() ? pDS->GetParent()->GetName() : "NULL"));

  String dsName;
  pDS->GetFullName(dsName);
  Array<Node*> *pChildren = pDS->Children();
  for (unsigned int i = 0; i < pChildren->size(); i++)
  {
    String memberName;
    Node *n = pChildren->getAt(i);
    n->GetFullName(memberName);
    LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "\t%s Child/MEMBER: %s\n", (const char*)dsName, (const char*)memberName);
  }
}

void TRACE_CHILD(Node *pNode, const CStdString& sIndent)
{
  tmw::String s;
  modelfile << (const char*)sIndent << "Child:" << (const char*)pNode->GetFullName(s) << std::endl;

  Array<Node*> *pChildren = pNode->Children();

  if (!pChildren || pChildren->size() == 0)
  {
    return;
  }

  CStdString si = sIndent + "\t";

  int size = pChildren->size();
  for (unsigned int i = 0; i < size; i++)
  {
    Node* pChild = pChildren->getAt(i);
    //modelfile << (const char*)si << "Child:" << pChild->GetName() << std::endl;
    TRACE_CHILD(pChild, si);
  }
}

void GTW61850Client::TRACE_ALL_CHILDREN()
{
  modelfile << "Model for " << (const char*)GetFullName() << endl << endl;

  tmw61850::i61850RootNode *pModel = dynamic_cast<tmw61850::i61850RootNode*>(m_p61850ClientConnection->Model());
  Array<Node*> *pChildren = pModel->Children();

  if (!pChildren || pChildren->size() == 0)
  {
    modelfile << "No Children for " << (const char*)this->GetFullName() << std::endl;
    return;
  }

  for (int i = 0; i < pChildren->size(); i++)
  {
    Node* pChild = pChildren->getAt(i);
    TRACE_CHILD(pChild, "\t");
  }
}
*/


// This ifdef is for DataSimulation
#if defined(DEBUG) && defined(_WIN32)
void GTW61850Client::SimulateChangeData(tmw61850::Node *node)
{
#define _TRACEDA(pDA,eCode) \
  String sv; \
  ((tmw61850::DataAttribute*)pDA)->GetValueAsString(sv);\
  String sn;\
  pDA->GetFullName(sn);\
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "SimulateWrite..wrote da '%s', value=%s, result=%s\n", (const char*)sn, (const char*)sv, tmw61850::ClientMessage::ClientMMSErrorCodeToString(eCode));

  if (((tmw61850::DataAttribute*)node)->GetType() == tmw61850::Value::INTEGER || ((tmw61850::DataAttribute*)node)->GetType() == tmw61850::Value::UNSIGNED)
  {
    int value = ((tmw61850::DataAttribute*)node)->GetIntValue();
    if (((tmw61850::DataAttribute*)node)->GetSize() == 8 && value > 20)
      ((tmw61850::DataAttribute*)node)->SetIntValue(1);
    else
    {
      ((tmw61850::DataAttribute*)node)->SetIntValue(value + 1);
    }
    ClientEnumDefs::ClientMMSErrorCode eCode = GetClientConnection()->WriteNode(node);
    _TRACEDA(node, eCode);
  }
  else if (((tmw61850::DataAttribute*)node)->GetType() == tmw61850::Value::FLOAT)
  {
    double value = ((tmw61850::DataAttribute*)node)->GetDoubleValue();
    ((tmw61850::DataAttribute*)node)->SetDoubleValue(value + 0.5);
    ClientEnumDefs::ClientMMSErrorCode eCode = GetClientConnection()->WriteNode(node);
    _TRACEDA(node, eCode);
  }
  else if (((tmw61850::DataAttribute*)node)->GetType() == tmw61850::Value::BOOLEAN)
  {
    bool value = ((tmw61850::DataAttribute*)node)->GetBooleanValue();
    ((tmw61850::DataAttribute*)node)->SetBooleanValue(!value);

    ClientEnumDefs::ClientMMSErrorCode eCode = GetClientConnection()->WriteNode(node);
    _TRACEDA(node, eCode);
  }

}

void GTW61850Client::OperateControl(tmw61850::ClientControlPoint *pControl)
{
#define _TRACECTRL(f, pDA,eCode) \
  String sn;\
  pDA->GetControlNode()->GetFullName(sn);\
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr  "SimulateWrite..Operate control '%s', value=%f, result=%s\n", (const char*)sn, f, tmw61850::ClientMessage::ClientMMSErrorCodeToString(eCode));

  float f;
  tmw61850::Value::Type t;
  pControl->GetControlValue(f, t);
  f += .25;
  pControl->SetControlValue(f);

  pControl->SetOriginOrCat((tmw61850::EnumDefs::ORCAT)1);
  pControl->SetOriginOrIdentFromString("01");

  switch (pControl->GetCtlModel())
  {
  case tmw61850::EnumDefs::CTLMODEL_statusonly:
    break;

  case tmw61850::EnumDefs::CTLMODEL_directwithnormalsecurity:
  case tmw61850::EnumDefs::CTLMODEL_directwithenhancedsecurity:
  {
    ClientEnumDefs::ClientMMSErrorCode eCode = pControl->Operate();
    _TRACECTRL(f, pControl, eCode);
  }
  break;

  case tmw61850::EnumDefs::CTLMODEL_sbowithnormalsecurity:
  case tmw61850::EnumDefs::CTLMODEL_sbowithenhancedsecurity:
  {
    pControl->Select();
    ClientEnumDefs::ClientMMSErrorCode eCode = pControl->Operate();
    _TRACECTRL(f, pControl, eCode);
  }
  break;
  }
}

unsigned int __stdcall GTW61850Client::SimulateThreadFunc(void*  pParam)
{
  Gtw61850ClientSimulateThreadContext *pContext = (Gtw61850ClientSimulateThreadContext*)pParam;
  tmw61850::Node *pModel = pContext->m_pClient->GetClientConnection()->Model();

  while (pContext->m_bRun)
  {
    if (pContext->m_pClient->m_bServerUp && pContext->m_pClient->GetClientConnection() && pContext->m_pClient->GetClientConnection()->IsConnectionAliveAndReady())
    {
      // Go thru all leaf nodes
      Array<tmw61850::DataAttribute*> *dsPoints = pContext->m_pClient->GetClientConnection()->Model()->GetAllLeafNodesWithValues();
      if (dsPoints != nullptr && dsPoints->size() > 0)
      {
        int index = 0;
        for (unsigned int i = 0; i < dsPoints->size(); i++)
        {
          tmw61850::DataAttribute *pDA = dsPoints->getAt(i);

          //tmw61850::DataAttribute *pQualityDA = nullptr;
          //tmw61850::DataAttribute *pTimestampDA = nullptr;
          //pDA->GetQualityAndTimeStamp(pQualityDA, pTimestampDA);

          const char* sFunctionalConstraint = pDA->GetFC();
          if (!tmw::util::compareNoCase(sFunctionalConstraint, "sp") && !tmw::util::compareNoCase(sFunctionalConstraint, "cf") &&
            !tmw::util::compareNoCase(sFunctionalConstraint, "sv"))
          {
            continue;
          }
          pContext->m_pClient->SimulateChangeData(pDA);
        }
      }

      const tmw::Array<tmw61850::ClientControlPoint*> *pControlsArray = pContext->m_pClient->GetClientConnection()->GetControls();
      if (pControlsArray != nullptr)
      {
        for (unsigned int i = 0; i < pControlsArray->getSize(); i++)
        {
          tmw61850::ClientControlPoint* cInfo = pControlsArray->getAt(i);
          pContext->m_pClient->OperateControl(cInfo);
        }
      }
    }
    GtwOsSleep(g_iSimulateThrottleTimeMS);
  }

  return 0;
}
#endif


/* Code below is for doing a TrimModel - remove unused points

void GetBaseDataSetControls(const std::list<GTW61850GOOSEControlBlock*> &gtwControlBlockList, tmw::Array<tmw61850::DataSetControl*> &dsControlList)
{
  for (std::list<GTW61850GOOSEControlBlock*>::const_iterator iter = gtwControlBlockList.begin(); iter != gtwControlBlockList.end(); ++iter)
  {
    tmw61850::DataSetControl *pDSC = (*iter)->GetBaseDataSetControl();
    dsControlList.add(pDSC);
  }
}

void GetBaseDataSetControls(const std::list<GTW61850ReportControlBlock*> &gtwControlBlockList, tmw::Array<tmw61850::DataSetControl*> &dsControlList)
{
  for (std::list<GTW61850ReportControlBlock*>::const_iterator iter = gtwControlBlockList.begin(); iter != gtwControlBlockList.end(); ++iter)
  {
    tmw61850::DataSetControl *pDSC = (*iter)->GetBaseDataSetControl();
    dsControlList.add(pDSC);
  }
}

void GTW61850Client::TrimModel()
{
  //
  // Do RCBs and GooseCBs
  //
  std::list<GTW61850ReportControlBlock*> RCBList;
  std::list<GTW61850GOOSEControlBlock*> GCBList;

  GetReportControlBlocks(RCBList);
  GetGooseControlBlocks(GCBList);

  tmw::Array<tmw61850::DataSetControl*> dsControlList;
  //dsControlList.resize(RCBList.size() + GCBList.size());

  GetBaseDataSetControls(RCBList, dsControlList);
  GetBaseDataSetControls(GCBList, dsControlList);

  tmw61850::i61850RootNode *pModel = dynamic_cast<tmw61850::i61850RootNode*>(m_p61850ClientConnection->Model());
  pModel->MarkControlBlocksToKeep(&dsControlList);

  // Do DataSets
  std::list<GTW61850PolledDataSet*> PDSList;
  tmw::Array<tmw61850::DataSet*> DSList;

  GetPolledDataSets(PDSList);
  //DSList.resize(PDSList.size());
  for (std::list<GTW61850PolledDataSet*>::iterator iter = PDSList.begin(); iter != PDSList.end(); ++iter)
  {
    GTW61850PolledDataSet *pPPS = *iter;
    DSList.add(pPPS->GetDataSetControl());
  }

  pModel->MarkDataSetsToKeep(&DSList);

  //
  // Do polled point sets
  //
  std::list<GTW61850PolledPointSet*> PPSList;
  GetPolledPointSets(PPSList);
  for (std::list<GTW61850PolledPointSet*>::iterator iter = PPSList.begin(); iter != PPSList.end(); ++iter)
  {
    GTW61850PolledPointSet *pPPS = *iter;
    list<GTW61850DataAttributeMDO*> mdoList;
    GetControlBlockMDOs(pPPS, mdoList);

    for (std::list<GTW61850DataAttributeMDO*>::iterator iter2 = mdoList.begin(); iter2 != mdoList.end(); ++iter2)
    {
      GTW61850DataAttributeMDO *pMdo = *iter2;
      if (pMdo)
      {
        if (pMdo->m_pValueDataAttribute)
        {
          pModel->MarkDataAttribute(pMdo->m_pValueDataAttribute);
        }

        if (pMdo->m_pTimeDataAttribute)
        {
          pModel->MarkDataAttribute(pMdo->m_pTimeDataAttribute);
        }

        if (pMdo->m_pQualityDataAttribute)
        {
          pModel->MarkDataAttribute(pMdo->m_pQualityDataAttribute);
        }
      }
    }
  }

  //
  // Do rest of points
  //
  tmw::CriticalSectionLock itemMapLock(m_ItemMapCriticalSection);

  POSITION pos = m_61850itemMap.GetStartPosition();
  while (pos != nullptr)
  {
    GTWMasterDataObject* pItem = nullptr; //GTW61850DataAttributeMDO
    void *id;
    m_61850itemMap.GetNextAssoc(pos, id, pItem);
    if (pItem != nullptr)
    {
      GTW61850DataAttributeMDO *pMdo = dynamic_cast<GTW61850DataAttributeMDO*>(pItem);
      if (pMdo && !pMdo->GetControlBlock())
      {
        if (pMdo->m_pValueDataAttribute)
        {
          pModel->MarkDataAttribute(pMdo->m_pValueDataAttribute);
        }

        if (pMdo->m_pTimeDataAttribute)
        {
          pModel->MarkDataAttribute(pMdo->m_pTimeDataAttribute);
        }

        if (pMdo->m_pQualityDataAttribute)
        {
          pModel->MarkDataAttribute(pMdo->m_pQualityDataAttribute);
        }
      }
    }
  }

  pModel->RemoveAllNonMarkedNodes();
}
*/

