/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2010 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTW61850DataAttributeMDO.cpp                                */
/* DESCRIPTION:  Implementation of IEC 61850-MDO                             */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 6 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"

#include "WinTimer.h"
#include "GTW61850Client.h"
#include "GTW61850ControlBlock.h"
#include "GTW61850DataAttributeMDO.h"
#include "gateway/GTWOsUtils/TMWStringUtils.h"
#include "GTW61850CommandPointEditor.h"
#include "GTW61850DataAttributeMDOEditor.h"
#include "GTW61850WritablePointSet.h"
#include "GTW61850CommandPointSet.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

using namespace tmw;

ImplementClassBaseInfo (GTW61850DataAttributeMDO,GTWBaseDataObject,pClassInfo,new GTW61850DataAttributeMDO());

class GTWM61850_CNVTR_WRITE_STRING : public GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  GTWM61850_CNVTR_WRITE_STRING(GTW61850DataAttributeMDO* p61850Mdo_) :
    m_p61850Mdo(p61850Mdo_)
  {
  }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(CStdString* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    m_p61850Mdo->getValueAsString(*pValue);
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject* pCallbackSdo, CStdString value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOString(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
    {
      return GTWDEFS_CTRL_STAT_FAILURE;
    }
    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWM61850_CNVTR_WRITE_DOUBLE : public GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  GTWM61850_CNVTR_WRITE_DOUBLE(GTW61850DataAttributeMDO *p61850Mdo_) :
      m_p61850Mdo(p61850Mdo_)
      {
      }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }
  virtual void getValue(TMWTYPES_DOUBLE *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_DOUBLE)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_DOUBLE value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDODouble(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWM61850_CNVTR_WRITE_SFLOAT : public GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  GTWM61850_CNVTR_WRITE_SFLOAT(GTW61850DataAttributeMDO *p61850Mdo_) :
      m_p61850Mdo(p61850Mdo_)
      {
      }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }
      
  virtual void getValue(TMWTYPES_SFLOAT *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_SFLOAT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_SFLOAT value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOFloat(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWM61850_CNVTR_WRITE_LONG : public GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  GTWM61850_CNVTR_WRITE_LONG(GTW61850DataAttributeMDO *p61850Mdo_) :
      m_p61850Mdo(p61850Mdo_)
      {
      }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(TMWTYPES_INT *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_INT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_INT value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOLong(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWM61850_CNVTR_WRITE_CHAR : public GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  GTWM61850_CNVTR_WRITE_CHAR(GTW61850DataAttributeMDO *p61850Mdo_) :
      m_p61850Mdo(p61850Mdo_)
      {
      }
  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }
      
  virtual void getValue(TMWTYPES_CHAR *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_CHAR)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_CHAR value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOLong(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWM61850_CNVTR_WRITE_SHORT : public GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  GTWM61850_CNVTR_WRITE_SHORT(GTW61850DataAttributeMDO *p61850Mdo) :
      m_p61850Mdo(p61850Mdo)
      {
      }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(TMWTYPES_SHORT *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_SHORT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_SHORT value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOLong(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWM61850_CNVTR_WRITE_101_STEP : public GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  GTWM61850_CNVTR_WRITE_101_STEP(GTW61850DataAttributeMDO *p61850Mdo) :
    m_p61850Mdo(p61850Mdo)
  {
  }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(TMWTYPES_SHORT *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_SHORT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, GTWAnalogValue value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    TMWTYPES_UCHAR sv = value.getValueAsUCHAR();
    return writeValue(pCallbackSdo, sv, pBinCtrl, ctrlMode);
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_UCHAR value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    switch (ctrlMode)
    {
    case GTWDEFS_CTRL_MODE_DESELECT:
    case GTWDEFS_CTRL_MODE_SELECT:
    case GTWDEFS_CTRL_MODE_TEST:
      if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      {
        return GTWDEFS_CTRL_STAT_FAILURE;
      }
      return GTWDEFS_CTRL_STAT_SUCCESS;
      //case GTWDEFS_CTRL_MODE_EXECUTE:
      //case GTWDEFS_CTRL_MODE_AUTO:
      //  break;
    }

    long lValue = 0;
    if (value & I14DEF_RCS_STEP_LOWER)
    {
      lValue = I14DEF_RCS_STEP_LOWER;
    }
    else if (value & I14DEF_RCS_STEP_HIGHER)
    {
      lValue = I14DEF_RCS_STEP_HIGHER;
    }
    else
    {
      CStdString sMsg;
      sMsg.Format("Operating control '%s' with invalid value: %d", m_p61850Mdo->GetFullName(), value);
      TMWDIAG_ERROR(sMsg.c_str());
      return GTWDEFS_CTRL_STAT_FAILURE;
    }

    m_p61850Mdo->updateMDOLong(lValue);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

/* class: GTWM61850_CNVTR_WRITE_UCHAR
*/
class GTWM61850_CNVTR_WRITE_UCHAR : public GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_WRITE_UCHAR(GTW61850DataAttributeMDO *p61850Mdo_) :
      m_p61850Mdo(p61850Mdo_)
      {
      }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }
      
  virtual void getValue(TMWTYPES_UCHAR *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_UCHAR)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_UCHAR value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOULong(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

/* class: GTWM61850_CNVTR_WRITE_ULONG
*/
class GTWM61850_CNVTR_WRITE_ULONG : public GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_WRITE_ULONG(GTW61850DataAttributeMDO *p61850Mdo_) :
      m_p61850Mdo(p61850Mdo_)
      {
      }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(TMWTYPES_UINT *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_UINT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_UINT value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOULong(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

/* class: GTWM61850_CNVTR_WRITE_USHORT
*/
class GTWM61850_CNVTR_WRITE_USHORT : public GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_WRITE_USHORT(GTW61850DataAttributeMDO *p61850Mdo_) :
      m_p61850Mdo(p61850Mdo_)
      {
      }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(TMWTYPES_USHORT *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = (TMWTYPES_USHORT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  virtual GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, GTWAnalogValue aValue, GTWDEFS_EXTRA_CTRL_INFO_PTR pCtrlDesc, GTWDEFS_CTRL_MODE ctrlMode)
  {
    TMWTYPES_USHORT value = aValue.getValueAsUSHORT();
    return writeValue(pCallbackSdo, value, pCtrlDesc, ctrlMode);
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_USHORT value, GTWDEFS_EXTRA_CTRL_INFO_PTR pBinCtrl, GTWDEFS_CTRL_MODE ctrlMode) override
  {
    m_p61850Mdo->updateMDOULong(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};


/* class: GTWM61850_CNVTR_WRITE_BOOL
*/
class GTWM61850_CNVTR_WRITE_BOOL : public GTWWriteConverterTemplate<bool, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_WRITE_BOOL(GTW61850DataAttributeMDO *pMdo_) :
      m_p61850Mdo(pMdo_)
      {
      }

  ~GTWM61850_CNVTR_WRITE_BOOL(void)
  {
  }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(bool *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = fabs(m_p61850Mdo->getValue() - 0) > DBL_EPSILON ? true : false;
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, bool value, GTWDEFS_EXTRA_CTRL_INFO_PTR pCtrlDesc, GTWDEFS_CTRL_MODE ctrlMode)
  {
    m_p61850Mdo->updateMDOBool(value);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

/* class: GTWM61850_CNVTR_WRITE_BOOL
*/
class GTWM61850_CNVTR_WRITE_DPI_BOOL : public GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_WRITE_DPI_BOOL(GTW61850DataAttributeMDO *pMdo_) :
      m_p61850Mdo(pMdo_)
  {
  }

  ~GTWM61850_CNVTR_WRITE_DPI_BOOL(void)
  {
  }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(bool *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = fabs(m_p61850Mdo->getValue() - 0) > DBL_EPSILON ? true : false;
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_UINT value, GTWDEFS_EXTRA_CTRL_INFO_PTR pCtrlDesc, GTWDEFS_CTRL_MODE ctrlMode)
  {
    TMWTYPES_UINT nMdoValue = value & 0x3; // mask off first two bits
    if (nMdoValue == 1) // now translate 1 to zero
    {
      nMdoValue = 0;
    }

    m_p61850Mdo->updateMDOULong(nMdoValue);
    //m_p61850Mdo->m_pValueDataAttribute->SetULongValue(nMdoValue);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

class GTWM61850_CNVTR_WRITE_SPI_BOOL : public GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject>
{
private:
  GTW61850DataAttributeMDO *m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_WRITE_SPI_BOOL(GTW61850DataAttributeMDO *pMdo_) :
      m_p61850Mdo(pMdo_)
  {
  }

  ~GTWM61850_CNVTR_WRITE_SPI_BOOL(void)
  {
  }

  virtual GTWMasterDataObject* getMDO() override
  {
    return m_p61850Mdo;
  }

  virtual void getValue(bool *pValue, GTWDEFS_STD_QLTY *pStdQuality)
  {
    *pValue = fabs(m_p61850Mdo->getValue() - 0) > DBL_EPSILON ? true : false;
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }

  GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo, TMWTYPES_UINT value, GTWDEFS_EXTRA_CTRL_INFO_PTR pCtrlDesc, GTWDEFS_CTRL_MODE ctrlMode)
  {
    TMWTYPES_UINT nMdoValue = value & 0x3; // mask off first two bits

    m_p61850Mdo->updateMDOULong(nMdoValue);
    //m_p61850Mdo->m_pValueDataAttribute->SetValueFromVariant(m_p61850Mdo->m_vValue);
    if (m_p61850Mdo->WriteMe(ctrlMode) == false)
      return GTWDEFS_CTRL_STAT_FAILURE;

    return(GTWDEFS_CTRL_STAT_SUCCESS);
  }
};

/* class: GTWM61850_CNVTR_READ_TMWDTIME
*/
class GTWM61850_CNVTR_READ_TMWDTIME : public GTWReadConverterTemplate<TMWDTIME>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_TMWDTIME(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWDTIME* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    m_p61850Mdo->getTime(pValue);
    /*
    if (m_p61850Mdo->m_pTimeDataAttribute != NULL)
    {
      m_p61850Mdo->m_pTimeDataAttribute->GetDateTime()->GetTMWTime(*pValue);
    }
    else if (m_p61850Mdo->m_pValueDataAttribute != NULL && m_p61850Mdo->m_pValueDataAttribute->IsValueDateTime())
    {
      m_p61850Mdo->m_pValueDataAttribute->GetDateTime()->GetTMWTime(*pValue);
    }
    else
    {
      LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Could not get time for item: %s ",(const char *)m_p61850Mdo->GetFullName());
    }
    */
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};



/* class: GTWM61850_CNVTR_READ_STRING
*/
class GTWM61850_CNVTR_READ_STRING : public GTWReadConverterTemplate<CStdString>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_STRING(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, CStdString* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    m_p61850Mdo->getValueAsString(*pValue);
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

class GTWM61850_CNVTR_READ_INT64 : public GTWReadConverterTemplate<TMWTYPES_INT64>
{
private:
  // Reference to the MDO from which to read the current value
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  // Constructor
  GTWM61850_CNVTR_READ_INT64(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_INT64* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_INT64)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

//class: GTWM61850_CNVTR_READ_DOUBLE
class GTWM61850_CNVTR_READ_DOUBLE : public GTWReadConverterTemplate<TMWTYPES_DOUBLE>
{
private:
  //Reference to the MDO from which to read the current value
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  // Constructor
  GTWM61850_CNVTR_READ_DOUBLE(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_DOUBLE* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_DOUBLE)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

/* class: GTWM61850_CNVTR_READ_SFLOAT
*/
class GTWM61850_CNVTR_READ_SFLOAT : public GTWReadConverterTemplate<TMWTYPES_SFLOAT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_SFLOAT(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_SFLOAT* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_SFLOAT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

/* class: GTWM61850_CNVTR_READ_LONG
*/
class GTWM61850_CNVTR_READ_LONG : public GTWReadConverterTemplate<TMWTYPES_INT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_LONG(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_INT* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_INT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};
/* class: GTWM61850_CNVTR_READ_ULONG
*/
class GTWM61850_CNVTR_READ_ULONG : public GTWReadConverterTemplate<TMWTYPES_UINT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_ULONG(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_UINT* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_UINT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

/* class: GTWM61850_CNVTR_READ_CHAR
*/
class GTWM61850_CNVTR_READ_CHAR : public GTWReadConverterTemplate<TMWTYPES_CHAR>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_CHAR(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_CHAR* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_CHAR)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

/* class: GTWM61850_CNVTR_READ_SHORT
*/
class GTWM61850_CNVTR_READ_SHORT : public GTWReadConverterTemplate<TMWTYPES_SHORT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_SHORT(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_SHORT* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_SHORT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

/* class: GTWM61850_CNVTR_READ_USHORT
*/
class GTWM61850_CNVTR_READ_USHORT : public GTWReadConverterTemplate<TMWTYPES_USHORT>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_USHORT(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, TMWTYPES_USHORT* pValue, GTWDEFS_STD_QLTY* pStdQuality)
  {
    *pValue = (TMWTYPES_USHORT)m_p61850Mdo->getValue();
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();
  }
};

/* class: GTWM61850_CNVTR_READ_BOOL
*/
class GTWM61850_CNVTR_READ_BOOL : public GTWReadConverterTemplate<bool>
{
private:
  /* Reference to the MDO from which to read the current value
  */
  GTW61850DataAttributeMDO* m_p61850Mdo;

public:
  /* Constructor
  */
  GTWM61850_CNVTR_READ_BOOL(GTW61850DataAttributeMDO* pMdo) :
    m_p61850Mdo(pMdo)
  {
  }
  virtual GTWMasterDataObject* getMDO() { return m_p61850Mdo; }

  virtual TMWTYPES_UINT getFlags(void)
  {
    return m_p61850Mdo->getQuality();
  }

  virtual void getValue(GTWMasterDataObject* pMdo, bool* pValue, GTWDEFS_STD_QLTY* pStdQuality);
};



GTWDEFS_STD_QLTY GTW61850DataAttributeMDO::getMdoStdQuality()
{
  if (m_pQualityDataAttribute == NULL)
  {
    if (this->GetClientNode()->IsConnectionAliveAndReady())
    {
      return GTWDEFS_STD_QLTY_GOOD;
    }
  }

  return getStdQuality(m_i61850Quality);
}

bool GTW61850DataAttributeMDO::FormatQualityString(char* buffer, int bufferSize, GTWEXPND_EXPANSION* pExpansion)
{
  CStdString sQualityBS;
  Get61850QualityBitString(m_i61850Quality, sQualityBS);

  snprintf(buffer, bufferSize, "gtw:%04x,6:%s",
    getMdoStdQuality(), sQualityBS.c_str());// , binaryStr);

  return true;
}

GTWDEFS_STD_QLTY GTW61850DataAttributeMDO::getStdQuality(const tmw::BitString* p61850Qual)
{
  return getStdQuality(getGTW61850Quality(p61850Qual));
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::getMdoStdQuality
Description : [none]
Return :			GTWDEFS_STD_QLTY	-
Parameters :
void	-
Note : [none]
\**********************************************************************************/
GTWDEFS_STD_QLTY GTW61850DataAttributeMDO::getStdQuality(I61850_QUALITY_TYPE quality)
{
  tmw61850::Quality qual;
  qual.SetFromInt(quality, 13);

  GTWDEFS_STD_QLTY retQuality = GTWDEFS_STD_QLTY_GOOD;
  //I61850_QUALITY_TYPE qValidity = quality & I61850_QUALITY_VALIDITY_MASK;

  if (qual.IsValidityGood()) //qValidity == I61850_QUALITY_VALIDITY_GOOD)
  {
    retQuality = GTWDEFS_STD_QLTY_GOOD;
  }
  else if (qual.IsValidityInvalid()) //qValidity & I61850_QUALITY_VALIDITY_INVALID)
  {
    retQuality = GTWDEFS_STD_QLTY_INVALID;
  }
  else if (qual.IsValidityQuestionable()) //qValidity & I61850_QUALITY_VALIDITY_QUESTIONABLE)
  {
    retQuality = GTWDEFS_STD_QLTY_UNINITIALIZED;
  }

  if (qual.IsOverflow()) //(quality & I61850_QUALITY_OVERFLOW) == I61850_QUALITY_OVERFLOW)
  {
    retQuality |= GTWDEFS_STD_QLTY_OVERFLOW;
  }
  if (qual.IsOutOfRange()) //(quality & I61850_QUALITY_OUTOFRANGE) == I61850_QUALITY_OUTOFRANGE)
  {
    retQuality |= GTWDEFS_STD_QLTY_OVERFLOW;
  }
  if (qual.IsBadReference()) //(quality & I61850_QUALITY_BADREFERENCE) == I61850_QUALITY_BADREFERENCE)
  {
    retQuality |= GTWDEFS_STD_QLTY_REF_ERROR;
  }
  if (qual.IsOscillatory()) //(quality & I61850_QUALITY_OSCILLIATORY) == I61850_QUALITY_OSCILLIATORY)
  {
    retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
  }
  if (qual.IsFailure()) //(quality & I61850_QUALITY_FAILURE) == I61850_QUALITY_FAILURE)
  {
    retQuality |= GTWDEFS_STD_QLTY_INVALID;
  }
  if (qual.IsOldData()) //(quality & I61850_QUALITY_OLDDATA) == I61850_QUALITY_OLDDATA)
  {
    retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
  }
  if (qual.IsInconsistent()) //(quality & I61850_QUALITY_INCONSISTENT) == I61850_QUALITY_INCONSISTENT)
  {
    retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
  }
  if (qual.IsInaccurate()) //(quality & I61850_QUALITY_INACCURATE) == I61850_QUALITY_INACCURATE)
  {
    retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
  }
  if (qual.IsSourceSubstituted()) //(quality & I61850_QUALITY_SOURCE_SUBSTITUTED) == I61850_QUALITY_SOURCE_SUBSTITUTED)
  {
    retQuality |= GTWDEFS_STD_QLTY_SUBSTITUTED;
  }
  if (qual.IsTest()) //(quality & I61850_QUALITY_TEST) == I61850_QUALITY_TEST)
  {
    retQuality |= GTWDEFS_STD_QLTY_TEST;
  }
  if (qual.IsOperatorBlocked()) //(quality & I61850_QUALITY_OPERATORBLOCKED) == I61850_QUALITY_OPERATORBLOCKED)
  {
    retQuality |= GTWDEFS_STD_QLTY_BLOCKED;
  }
  return retQuality;
}


/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::getMdoDbasDataId
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_DBAS_DATA_ID *pDbasDataId	-
Note : [none]
\**********************************************************************************/
void GTW61850DataAttributeMDO::getMdoDbasDataId(GTWDEFS_DBAS_DATA_ID *pDbasDataId)
{
  memset(pDbasDataId,0,sizeof(GTWDEFS_DBAS_DATA_ID));  // 61850 MDOs don't have a Dbase ID
}

void GTW61850DataAttributeMDO::getValueAsString(CStdString &sValue)
{
  m_vValue.GetValueAsString(sValue);

  /*
  // Try value data attribute first to utilize 61850 library Value class string formatting
  if (m_pValueDataAttribute)
  {
    try
    {
      // Use m_pInternalValueDataAttribute which is in sync with m_vValue and do not use m_pValueDataAttribute which may have already changed to something else
      // because it is pointing to the real internal model  - which for example gets updated on a read or a report on a different thread
      // and may have a different value than m_vValue
      tmw::CriticalSectionLock lock(m_InternalValueCriticalSection);
      if (m_pInternalValueDataAttribute)
      {
        tmw::String sVal;
        /*
        if (m_pInternalValueDataAttribute->IsValueBitString())
        {
          tmw::BitString bitString;
          m_pInternalValueDataAttribute->GetBitStringValue(bitString);
          bitString.AsString(sVal);
          sValue = (const char*)sVal;
          return;
        }
        /
        if (m_pInternalValueDataAttribute->GetType() == tmw61850::Value::Type::UTCTIME) // We have to treat this type as a special case because the 61850 library includes the quality in the string, which we dont want in the SDG
        {
          const tmw61850::DateTime *dt = m_pInternalValueDataAttribute->GetDateTime();
          dt->GetDateTimeAsString(sVal, false);
          sValue = (const char*)sVal;
          return;
        }
        else if (m_pInternalValueDataAttribute->GetValueAsString(sVal))
        {
          sValue = (const char*)sVal;
          return;
        }

        LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850DataAttributeMDO::getValueAsString:assert(false)");
#ifdef _WIN32
        assert(false);
#endif
      }
    }
    catch (...)
    {
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Unknown exception occurred in GTW61850DataAttributeMDO::getMdoValueAsString for MDO: %s", (const char *)GetFullName());
#ifdef _WIN32
      assert(false);
#endif

      sValue.Format("Unknown");
    }
  }

  m_vValue.GetValueAsString(sValue);

  /*
  switch(m_vValue.getGtwType())
  {
  case	GTWDEFS_TYPE_CHAR:
    {
      msg.Format(pFormat.signedFormat, TMWTYPES_CHAR(m_vValue.asI1()));
      break;
    }
  case	GTWDEFS_TYPE_UCHAR:
    {
      msg.Format(pFormat.unsignedFormat, TMWTYPES_UCHAR(m_vValue.asUI1()));
      break;
    }
  case(GTWDEFS_TYPE_BOOL):
    {
      msg.Format(pFormat.stringFormat, m_vValue.asBOOL()?"On":"Off");
      break;
    }
  case(GTWDEFS_TYPE_SHORT):
    {
      msg.Format(pFormat.signedFormat, TMWTYPES_INT(m_vValue.asI2()));
      break;
    }
  case GTWDEFS_TYPE_LONG:
     {
      msg.Format(pFormat.signedFormat, TMWTYPES_INT(m_vValue.asI4()));
      break;
    }
  case(GTWDEFS_TYPE_USHORT):
    {
      msg.Format(pFormat.unsignedFormat, TMWTYPES_UINT(m_vValue.asUI2()));
      break;
    }
  case GTWDEFS_TYPE_ULONG:
    {
      msg.Format(pFormat.unsignedFormat, TMWTYPES_UINT(m_vValue.asUI4()));
      break;
    }
  case(GTWDEFS_TYPE_SFLOAT):
    {
      msg.Format(pFormat.floatFormat, m_vValue.asR4());
      break;
    }
  case(GTWDEFS_TYPE_DOUBLE):
    {
      msg.Format(pFormat.floatFormat, m_vValue.asR8());
      break;
    }
  case(GTWDEFS_TYPE_UNKNOWN):
    {
      msg.Format(pFormat.stringFormat, "unknown");
      break;
    }
  case(GTWDEFS_TYPE_STRING):
    {
      CStdString value;
      value = m_vValue.asBSTR();
      msg.Format(pFormat.stringFormat, value);
      break;
    }
  case(GTWDEFS_TYPE_TIME):
    {
      TMWDTIME tmwdtime;
      getTime(&tmwdtime);
      SYSTEMTIME       systemTime;
      systemTime.wYear = tmwdtime.year;
      systemTime.wMonth = tmwdtime.month;
      systemTime.wDayOfWeek = tmwdtime.dayOfWeek % 7;
      systemTime.wDay = tmwdtime.dayOfMonth;
      systemTime.wHour = tmwdtime.hour;
      systemTime.wMinute = tmwdtime.minutes;
      systemTime.wSecond = tmwdtime.mSecsAndSecs / 1000;
      systemTime.wMilliseconds = tmwdtime.mSecsAndSecs % 1000;

#define dt_format(num) (num < 10 ? "0" : "") << num
#define dt_format_ms(num) (num < 10 ? "00" : (num < 100 ? "0" : "")) << num

      stringstream ss;
      ss << dt_format(systemTime.wDay);

      unsigned short monthIndex = systemTime.wMonth - 1;
      if (monthIndex < 0 || monthIndex > 11) // must be between 0 and 11.
        monthIndex = 0;

      ss << tmwdiag_monthNames[monthIndex];
      ss << systemTime.wYear << " ";

      ss << dt_format(systemTime.wHour) << ":";
      ss << dt_format(systemTime.wMinute) << ":";
      ss << dt_format(systemTime.wSecond) << ":";
      ss << dt_format_ms(systemTime.wMilliseconds);

      msg.Format("%s", ss.str().c_str());
      break;
    }
  default:
    {
      msg.Format("error- Unsupported type");
      break;
    }
  }

  GTWEXPND_FORMAT format;
  format.setFormatPrefix("%");
  getMdoValueAsString(format, sValue);
  */
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::getMdoValueAsString
Description : [none]
Return :			void	-
Parameters :
const GTWEXPND_FORMAT &pFormat	-
TMWTYPES_CHAR          *msg	-
TMWTYPES_USHORT         msgLen	-
Note : [none]
\**********************************************************************************/
void GTW61850DataAttributeMDO::getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg)
{
  getValueAsString(msg);
}

void GTW61850DataAttributeMDO::getMdoValueAsVariant(GtwVariant &variant)
{
  variant = m_vValue;
}

void GTW61850DataAttributeMDO::GetDescriptionColText( CStdString &itemStr )
{
  CStdString desc,cb;
  GTW61850ControlBlock *pCB = GetControlBlock();
  if (pCB)
  {
    cb = CStdString("ControlBlock: ") + pCB->GetFullName();
  }
  else
  {
    if (isControl())
    {
      cb = "IEC 61850 Control";
    }
    else if (isWriteable())
    {
      cb = "IEC 61850 Writeable MDO";
    }
  }

  if (getMdoDescription() != "")
  {
    if (cb.length() > 0)
      desc = cb + " - " + getMdoDescription();
    else
      desc = getMdoDescription();
  }
  else
  {
    desc = cb;
  }

  //if (isBoundToMdo())
  //{
  //  CStdString boundToName = GetBoundMdoAt(0)->getMdoUserTagName();
  //  boundToName += " ";
  //  desc = CStdString("BoundTo:") + boundToName + "," + desc;
  //}
  
  itemStr = desc;
}

bool GTW61850DataAttributeMDO::GetValueColText(CStdString &itemStr)
{
  if (this->isWriteable())
  {
    getValueAsString(itemStr);
    itemStr = "value=" + itemStr;
    return true;
  }
  return false;
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::getValue
Description : return the current values as a double
Return :			TMWTYPES_DOUBLE	-
Parameters :
Note : [none]
\**********************************************************************************/
//CStdString GTW61850DataAttributeMDO::getValueAsString()
//{
//  CStdString result;
//  m_vValue.getValueAsString(result);
//  return result;
//}


/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::getValue
Description : return the current values as a double
Return :			TMWTYPES_DOUBLE	-
Parameters :
Note : [none]
\**********************************************************************************/
GtwVariant GTW61850DataAttributeMDO::getValueAsVarient()
{
  return m_vValue;
}

TMWTYPES_DOUBLE GTW61850DataAttributeMDO::getValue()
{
  TMWTYPES_DOUBLE result = m_vValue.GetDoubleValue();

  //switch(m_vValue.vt)
  //{
  //case VT_UI2:
  //  result = m_vValue.uiVal;
  //  break;
  //case VT_I2:
  //  result = m_vValue.iVal;
  //  break;
  //case VT_UI4:
  //case VT_UINT:
  //  result = m_vValue.ulVal;
  //  break;
  //case VT_INT:
  //case VT_I4:
  //  result = m_vValue.lVal;
  //  break;
  //case VT_R4:
  //  result = m_vValue.fltVal;
  //  break;
  //case VT_R8:
  //  result = m_vValue.dblVal;
  //  break;
  //case	VT_I1	:
  //  result = m_vValue.cVal;
  //  break;
  //case	VT_UI1	:
  //  result = m_vValue.bVal;
  //  break;
  //case VT_BOOL:
  //  result = m_vValue.boolVal;
  //  break;
  //case VT_BSTR:
  //  {
  //    CStdString value;
  //    value = m_vValue.bstrVal;
  //    result = atof(value);
  //  }
  //  break;
  //}

  return(result);
}

unsigned short GTW61850DataAttributeMDO::getLastAlarmStatusSaved()
{
  return m_vAlarmStatusSaved;
}

TMWTYPES_DOUBLE GTW61850DataAttributeMDO::getLastValue()
{
  return m_vLastValue.GetDoubleValue();
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::bindMdoToSdoForReading
Description : [none]
Return :			void *	-
Parameters :
GTWSlaveDataObject *pUpdateSdo	-
GTWCNVTR_TYPE cnvtrType	-
Note : [none]
\**********************************************************************************/
void *GTW61850DataAttributeMDO::bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo, GTWCNVTR_TYPE cnvtrType)
{
  void *gtwcnvtr = TMWDEFS_NULL;

  switch(cnvtrType)
  {
  case GTWCNVTR_TYPE_FLAGS:
    gtwcnvtr = new GTWM61850_CNVTR_READ_BOOL(this);
    break;
  case GTWCNVTR_TYPE_BOOL:
    gtwcnvtr = new GTWM61850_CNVTR_READ_BOOL(this);
    break;
  case GTWCNVTR_TYPE_CHAR:
    gtwcnvtr = new GTWM61850_CNVTR_READ_CHAR(this);
    break;
  case GTWCNVTR_TYPE_SHORT:
    gtwcnvtr = new GTWM61850_CNVTR_READ_SHORT(this);
    break;
  case GTWCNVTR_TYPE_LONG:
    gtwcnvtr = new GTWM61850_CNVTR_READ_LONG(this);
    break;
  case GTWCNVTR_TYPE_USHORT:
    gtwcnvtr = new GTWM61850_CNVTR_READ_USHORT(this);
    break;
  case GTWCNVTR_TYPE_ULONG:
    gtwcnvtr = new GTWM61850_CNVTR_READ_ULONG(this);
    break;
  case GTWCNVTR_TYPE_SFLOAT:
    gtwcnvtr = new GTWM61850_CNVTR_READ_SFLOAT(this);
    break;
  case GTWCNVTR_TYPE_INT64:
    gtwcnvtr = new GTWM61850_CNVTR_READ_INT64(this);
    break;
  case GTWCNVTR_TYPE_DOUBLE:
    gtwcnvtr = new GTWM61850_CNVTR_READ_DOUBLE(this);
    break;
  case GTWCNVTR_TYPE_STRING:
    gtwcnvtr = new GTWM61850_CNVTR_READ_STRING(this);
    break;
  case GTWCNVTR_TYPE_TMWDTIME:
    gtwcnvtr = new GTWM61850_CNVTR_READ_TMWDTIME(this);
    break;
  }

  if (gtwcnvtr != TMWDEFS_NULL)
    addSdoToReadBoundList(pUpdateSdo);

  return(gtwcnvtr);
}

bool canConvertToType(GTWCNVTR_TYPE source, GTWDEFS_TYPE targ)
{
  return (source - GTWCNVTR_TYPE_BOOL) <= (targ - GTWDEFS_TYPE_BOOL);
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::bindMdoToSdoForWriting
Description : [none]
Return :			void *	-
Parameters :
GTWSlaveDataObject *pWriteFromSdo	-
GTWCNVTR_TYPE  cnvtrType	-
Note : [none]
\**********************************************************************************/
void *GTW61850DataAttributeMDO::bindMdoToSdoForWriting(GTWSlaveDataObject *pWriteFromSdo, GTWCNVTR_TYPE  cnvtrType)
{
  void *gtwcnvtr = nullptr;
  if (!isWriteable())
  {
    return nullptr;
  }

  GTWDEFS_TYPE mdoType = this->getMdoType();

  switch (cnvtrType)
  {
  case GTWCNVTR_TYPE_101_STEP:
    if (isWriteable())
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_101_STEP(this);
    }
    break;
  case GTWCNVTR_TYPE_BOOL:
    if (canConvertToType(cnvtrType, mdoType))// == GTWCNVTR_TYPE_BOOL)
    {
      /*
        if (cnvtrType == GTWCNVTR_TYPE_101_DPI)
        {
          gtwcnvtr = new GTWM61850_CNVTR_WRITE_DPI_BOOL(this);
        }
        else if (cnvtrType == GTWCNVTR_TYPE_101_SPI)
        {
          gtwcnvtr = new GTWM61850_CNVTR_WRITE_SPI_BOOL(this);
        }
        else
        */
        {
          gtwcnvtr = new GTWM61850_CNVTR_WRITE_BOOL(this);
        }
    }
    break;
  case GTWCNVTR_TYPE_CHAR:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_CHAR(this);
    }
    break;
  case GTWCNVTR_TYPE_UCHAR:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_UCHAR(this);
    }
    break;
  case GTWCNVTR_TYPE_SHORT:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_SHORT(this);
    }
    break;
  case GTWCNVTR_TYPE_USHORT:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_USHORT(this);
    }
    break;
  case GTWCNVTR_TYPE_LONG:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_LONG(this);
    }
    break;
  case GTWCNVTR_TYPE_ULONG:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_ULONG(this);
    }
    break;
  case GTWCNVTR_TYPE_SFLOAT:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_SFLOAT(this);
    }
    break;
  case GTWCNVTR_TYPE_DOUBLE:
    if (canConvertToType(cnvtrType, mdoType))
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_DOUBLE(this);
    }
    break;
  case GTWCNVTR_TYPE_STRING:
    if (GTWDEFS_TYPE_STRING == mdoType)
    {
      gtwcnvtr = new GTWM61850_CNVTR_WRITE_STRING(this);
    }
    break;
  }

  if (gtwcnvtr != TMWDEFS_NULL)
  {
    addSdoToWriteBoundList(pWriteFromSdo);
  }
  return gtwcnvtr;
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::CompareTagName
Description : [none]
Return :			GTWDEFS_STAT	-
Parameters :
const char  **ppTagName	-
Note : [none]
\**********************************************************************************/
GTWDEFS_STAT GTW61850DataAttributeMDO::CompareTagName(  CStdString &tagName)
{
  GTWDEFS_STAT stat = GTWBaseDataObject::CompareTagField(tagName,m_sTagName);
  return(stat);
}


void GTW61850DataAttributeMDO::getTime(TMWDTIME *pValue)
{
  if (m_pTimeDataAttribute != NULL)
  {
	  GTW61850Client::SixTSDateTimeToTMWDTime(*m_pTimeDataAttribute->GetDateTime(), *pValue);
  }
  else if (m_pValueDataAttribute != NULL && m_pValueDataAttribute->IsValueDateTime())
  {
	  GTW61850Client::SixTSDateTimeToTMWDTime(*m_pValueDataAttribute->GetDateTime(), *pValue);
  }
  else // assume updated time since there is no reported time (i.e. m_pTimeDataAttribute is null and m_pValueDataAttribute is not a date time)
  {
    *pValue = this->UpdatedTime();
  }
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::updateMDO
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_UPDTRSN updateReason	-
Note : [none]
\**********************************************************************************/
void GTW61850DataAttributeMDO::updateMDO(GTWDEFS_UPDTRSN updateReason)
{
  GTWBaseDataObject* pBdo = this->getBdo();
  if (pBdo == NULL)
  {
    return;
  }

  GTW61850Client* pClientNode = this->GetClientNode();

  CStdString sTime;
  if (this->m_pTimeDataAttribute)
  {
    sTime = GetReportedTimeString();
  }
  else
  {
    sTime = GetUpdatedTimeString();
  }
  CStdString valueStr;
  LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, 
    "Updating item: %s reason %u value=%s, quality=%u, time=%s",
    GetFullName().c_str(), updateReason, m_vValue.GetValueAsString(valueStr), m_i61850Quality, sTime.c_str());

  _updateMdo(updateReason);// GTWMasterDataObject::updateMDO(updateReason);

  if (this->IsValueChanged())
  {
    this->ClearValueChanged();
  }
  if (this->IsQualityChanged())
  {
    this->ClearQualityChanged();
  }
  if (this->IsTimeChanged())
  {
    this->ClearTimeChanged();
  }
}

/*
bool GTW61850DataAttributeMDO::IsWritableDA()
{
  if (!m_pValueDataAttribute || !GetClientNode()->IsUp())
  {
    return false;
  }

  // make MX,ST,EX,SG, and SR leaf nodes readonly
  if (util::compareNoCase(m_pValueDataAttribute->GetFC(), "MX") || 
      util::compareNoCase(m_pValueDataAttribute->GetFC(), "EX") || 
      util::compareNoCase(m_pValueDataAttribute->GetFC(), "ST") || 
      util::compareNoCase(m_pValueDataAttribute->GetFC(), "SG") || 
      util::compareNoCase(m_pValueDataAttribute->GetFC(), "SR"))
  {
    return false;
  }
  
  if (isCommandMDO())
  {
    return true;
  }
  return GetClientNode()->GetClientConnection()->WriteNode(m_pValueDataAttribute) == tmw61850::ClientEnumDefs::MMSErrorCode_Success;
}
*/

bool GTW61850DataAttributeMDO::WriteCurrentValueToServer()
{
  //bool bResult = GetClientNode()->GetClientConnection()->WriteNode(m_pValueDataAttribute) == tmw61850::ClientEnumDefs::MMSErrorCode_Success;
  //return bResult;
  return true;
}

bool GTW61850DataAttributeMDO::SetCommandValue(GtwVariant &variantValue)
{
  tmw::CriticalSectionLock lock(m_MdoCS);
  GTW61850Client* pClientNode = this->GetClientNode();
  if (variantValue.ChangeType(getMdoType()) == false)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to write %s (type mismatch)",(const char *)GetFullName());
      return false;
    }

  m_vValue = variantValue;
  updateMDO(GTWDEFS_UPDTRSN_BY_OPERATOR | GTWDEFS_UPDTRSN_CHNG_INDICATED, nullptr);
  return true;
}

void GTW61850DataAttributeMDO::DoClientWrite()
{
  if (isWriteable())
  {
    tmw61850::Client *pClient = GetClientNode()->GetClientConnection();

    if (this->m_pValueDataAttribute)
    {
      // Make sure same type as mdo
      //m_vValue.vt = getVariantTypeFromMdoType();//m_pValueDataAttribute->GetType();
      //m_pValueDataAttribute->SetValueFromVariant(m_vValue, false);      
      pClient->WriteNode(this->m_pValueDataAttribute);
    }
    if (this->m_pQualityDataAttribute)
    {
      pClient->WriteNode(this->m_pQualityDataAttribute);
    }
    if (this->m_pTimeDataAttribute)
    {
      pClient->WriteNode(this->m_pTimeDataAttribute);
    }
  }
}

void GTW61850DataAttributeMDO::SetVariantValue(double value)
{
  GTW61850Client* pClientNode = this->GetClientNode();

  SetLastValue();
  switch(m_vValue.GetType())
  {
  case GTWDEFS_TYPE_USHORT:
    m_vValue = (TMWTYPES_USHORT)value;
    break;
  case GTWDEFS_TYPE_SHORT:
    m_vValue = (TMWTYPES_SHORT)value;
    break;
  case GTWDEFS_TYPE_ULONG:
    m_vValue = (TMWTYPES_UINT)value;
    break;
   case GTWDEFS_TYPE_LONG:
    m_vValue = (TMWTYPES_INT)value;
    break;
  case GTWDEFS_TYPE_SFLOAT:
    m_vValue = (TMWTYPES_SFLOAT)value;
    break;
  case GTWDEFS_TYPE_DOUBLE:
    m_vValue = (TMWTYPES_DOUBLE)value;
    break;
  case	GTWDEFS_TYPE_CHAR:
    m_vValue = (TMWTYPES_CHAR)value;
    break;
  case	GTWDEFS_TYPE_UCHAR:
    m_vValue = (TMWTYPES_UCHAR)value;
    break;
  case GTWDEFS_TYPE_BOOL:
    m_vValue = value == 0 ? false : true;
    break;
  case GTWDEFS_TYPE_STRING:
    
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "GTW61850DataAttributeMDO::SetVariantValue:assert(false)");
#ifdef _WIN32
    assert(false);
#endif

    //if (m_vValue.bstrVal)
    //{
    //  //strncpy(m_vValue.bstrVal, itoa(value), strlen(m_vValue.bstrVal));
    //}
    break;
  }
}

void GTW61850DataAttributeMDO::updateMDOBool(bool b)
{
  if (m_pValueDataAttribute)
  {
    m_pValueDataAttribute->SetBooleanValue(b);
  }
  SetVariantValue(b);
  _updateMdo(GTWDEFS_UPDTRSN_CHNG_INDICATED);// GTWMasterDataObject::updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
}

void GTW61850DataAttributeMDO::updateMDOLong(int l)
{
  if (m_pValueDataAttribute)
  {
    m_pValueDataAttribute->SetIntValue(l);
  }
  SetVariantValue(l);
  _updateMdo(GTWDEFS_UPDTRSN_CHNG_INDICATED);// GTWMasterDataObject::updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
}

void GTW61850DataAttributeMDO::updateMDOULong(unsigned int l)
{
  if (m_pValueDataAttribute)
  {
    m_pValueDataAttribute->SetUIntValue(l);
  }
  SetVariantValue(l);
  _updateMdo(GTWDEFS_UPDTRSN_CHNG_INDICATED);// GTWMasterDataObject::updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
}

void GTW61850DataAttributeMDO::updateMDOFloat(float f)
{
  if (m_pValueDataAttribute)
  {
    m_pValueDataAttribute->SetDoubleValue(f);
  }
  SetVariantValue(f);
  _updateMdo(GTWDEFS_UPDTRSN_CHNG_INDICATED);
}

void GTW61850DataAttributeMDO::updateMDODouble(double d)
{
  if (m_pValueDataAttribute)
  {
    m_pValueDataAttribute->SetDoubleValue(d);
  }
  SetVariantValue(d);
  _updateMdo(GTWDEFS_UPDTRSN_CHNG_INDICATED);
}

void GTW61850DataAttributeMDO::updateMDOString(const CStdString& sValue)
{
  if (m_pValueDataAttribute)
  {
    m_pValueDataAttribute->SetValueFromString(sValue.c_str());
  }
  m_vValue = sValue;
  _updateMdo(GTWDEFS_UPDTRSN_CHNG_INDICATED);
}

/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::updateMDO
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_UPDTRSN updateReason	-
GTWMasterDataObject *pMdo	-
Note : [none]
\**********************************************************************************/
void GTW61850DataAttributeMDO::updateMDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo)
{
  GTW61850Client* pClientNode = this->GetClientNode();

  if (pMdo != NULL && pMdo->getBdo() != NULL)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "GTW61850DataAttributeMDO::updateMDO:: %s from %s",(const char *)GetFullName(),(const char *)pMdo->getBdo()->GetFullName());
  }

  GtwVariant variantValue;

  if (variantValue.ChangeType(getMdoType()) != true)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to write %s (type conversion error)",(const char *)GetFullName());
    return;
  }

  if (m_pVariantReadCnvtr)
  {
    m_pVariantReadCnvtr->getValue(variantValue);
    // Change type back to original
    if (variantValue.ChangeType(getMdoType()) != true)
    {
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to write %s (type mismatch)", (const char*)GetFullName());
      return;
    }
    m_vValue = variantValue;
  }

  if (this->m_pValueDataAttribute)
  {
    if (m_vValue.AssignTo61850DataAttribute(*m_pValueDataAttribute) == false)
    {
      tmw::String s;
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to set data attribute to internal variant value %s\n", this->m_pValueDataAttribute->GetFullName(s));
    }
    //updateInternalValue();
  }

  //if (this->m_pValueDataAttribute)
  //{    
  //  if (m_vValue.AssignTo61850DataAttribute(*m_pValueDataAttribute) == false)
  //  {
  //    tmw::String s;
  //    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "IEC 61850 Client (%s): Failed to set data attribute to internal variant value %s\n",(const char *)GetFullName(), this->m_pValueDataAttribute->GetFullName(s));
  //  }
  //}

  if (!isControl())
  {
    DoClientWrite();
  }
  else if (WriteMe(GTWDEFS_CTRL_MODE_AUTO) == false)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to write %s (control mode failure)\n",(const char *)GetFullName());
  }

}

void GTW61850DataAttributeMDO::PrepareControl(const tmw61850::Value &value)
{
  assert(m_pControlInfo);
  if (m_pControlInfo)
  {
    // increment ctlNum if appropriate
    //int ctlNum = 0;
    //if (m_pControlInfo->GetCtlNum(ctlNum))
   // {
     // m_pControlInfo->SetCtlNum(ctlNum + 1);
    //}

    m_pControlInfo->SetOriginOrCat((tmw61850::EnumDefs::OrCat)m_orCat);
    m_pControlInfo->SetCheck((tmw61850::EnumDefs::Check)m_controlCheck);

    m_pControlInfo->SetTest(m_bControlTest);
    m_pControlInfo->SetOriginOrIdentFromString(m_sOrIdent.c_str());

    m_pControlInfo->SetControlValue((float)value.GetDoubleValue());

    tmw61850::DateTime dt;
    dt.UTCNow();
    m_pControlInfo->SetTimeStamp(dt);
  }
}

bool GTW61850DataAttributeMDO::WriteMe(GTWDEFS_CTRL_MODE ctrlMode)
{
  GTW61850Client* pClientNode = this->GetClientNode();

  //LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_General,  "61850: Calling WriteMe with %d\n", (int)ctrlMode);
  if (!isControl())
  {
    assert(isWriteable());
    if (!isWriteable())
    {
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Trying to write a non-writable point: %s", GetFullName().c_str());
      return false;
    }
    if (ctrlMode == GTWDEFS_CTRL_MODE_EXECUTE || ctrlMode == GTWDEFS_CTRL_MODE_AUTO || ctrlMode == GTWDEFS_CTRL_MODE_TEST)
    {
      DoClientWrite();
      return true;
    }
    else
    {
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to write writable point: %s. Invalid control mode : %d", GetFullName().c_str(), ctrlMode);
      return false;
    }
    return false;
  }

  if (ctrlMode == GTWDEFS_CTRL_MODE_TEST)
  {
    return true;
  }

  CStdString msgStr;
  CStdString valueStr;
  CStdString clientName = "unknown";

  GTW61850Client *p61850Client = m_pClientNode;

  if (p61850Client)
  {
    clientName = p61850Client->GetFullName();
  }
  else
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Write item: %s failed (no client)",(const char *)GetFullName());
    return  false;
  }

  if (GetClientNode()->GetLicensed() == false)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Write item: %s failed (no license)",(const char *)GetFullName());
    return  false;
  }

  if (p61850Client->IsServerUp() == false)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Write item: %s failed (server is down)",(const char *)GetFullName());
    return  false;
  }

  tmw61850::Value value;

  if (!m_vValue.AssignToValue(value))
  {
    tmw::String tStr;
    value.GetValueAsString(tStr);
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Write item: %s=(%s) failed (bad type)",(const char *)GetFullName(),(const char *)tStr);
    return  false;
  }

  if (this->m_pControlInfo == nullptr)
  {
    tmw::String tStr;
    value.GetValueAsString(tStr);
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Write item: %s=(%s) failed (Control info is null)",(const char *)GetFullName(),(const char *)tStr);    
    return  false;
  }

  tmw61850::ClientEnumDefs::ClientMMSErrorCode mmsResultCode = tmw61850::ClientEnumDefs::ClientMMSErrorCode::ControlOperation_NoError;
  PrepareControl(value);

  switch(this->m_pControlInfo->GetCtlModel())
  {
  case tmw61850::EnumDefs::CtlModel::statusonly:
    {
      tmw::String tStr;
      value.GetValueAsString(tStr);
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Can not select or operate item: %s=(%s): Status Only Control",(const char *)GetFullName(),(const char *)tStr);
      return  false;
    }
    break;
  case tmw61850::EnumDefs::CtlModel::directwithnormalsecurity:
  case tmw61850::EnumDefs::CtlModel::directwithenhancedsecurity:
    if (ctrlMode == GTWDEFS_CTRL_MODE_EXECUTE || ctrlMode == GTWDEFS_CTRL_MODE_AUTO)
    {
      mmsResultCode = this->m_pControlInfo->Operate();
      if (mmsResultCode != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
      {
        tmw::String tStr;
        value.GetValueAsString(tStr);
        LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Operate control %s=(%s) failed: %s", (const char *)GetFullName(),
                                                                                    (const char *)tStr, tmw61850::ClientMessage::ClientMMSErrorCodeToString(mmsResultCode));
        return  false;
      }
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "IEC 61850 Operate NonSbo Control %s succeeded.", (const char *)GetFullName());
    }
    break;
  case tmw61850::EnumDefs::CtlModel::sbowithnormalsecurity:
  case tmw61850::EnumDefs::CtlModel::sbowithenhancedsecurity:
    if (ctrlMode == GTWDEFS_CTRL_MODE_AUTO || ctrlMode == GTWDEFS_CTRL_MODE_SELECT)
    {
      //
      // Select
      //
      mmsResultCode = this->m_pControlInfo->Select();
      if (mmsResultCode != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
      {
        tmw::String tStr;
        value.GetValueAsString(tStr);
        LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Select item: %s=(%s) failed: %s", (const char *)GetFullName(),
                                                                                    (const char *)tStr, tmw61850::ClientMessage::ClientMMSErrorCodeToString(mmsResultCode));
#if !USE_FAKE_OPC_CLIENT
        return  false;
#endif
      }
      else
      {
        LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Auto Select Sbo Control %s succeeded.", (const char *)GetFullName());
      }
    }
    if (ctrlMode == GTWDEFS_CTRL_MODE_AUTO || ctrlMode == GTWDEFS_CTRL_MODE_EXECUTE)
    {
      //
      // Operate
      //
      mmsResultCode = this->m_pControlInfo->Operate();
      if (mmsResultCode != tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
      {
        tmw::String tStr;
        value.GetValueAsString(tStr);
        LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Operate item: %s=(%s) failed:%s", (const char *)GetFullName(),
                                                                                    (const char *)tStr, tmw61850::ClientMessage::ClientMMSErrorCodeToString(mmsResultCode));
        return  false;
      }
      else
      {
        LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Auto Operate Sbo Control %s succeeded.", (const char *)GetFullName());
      }
    }
    break;
  default:
    tmw::String tStr;
    value.GetValueAsString(tStr);
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Can not select or operate item: %s=(%s): Invalid Control Mode",(const char *)GetFullName(),(const char *)tStr);
    return false;
    break;
  }

  // Call to update any mapped points, such as status points for slave controls
  _updateMdo(GTWDEFS_UPDTRSN_CHNG_INDICATED);// GTWMasterDataObject::updateMDO(GTWDEFS_UPDTRSN_REFRESH);

  return true;
}


/**********************************************************************************\
Function :			GTW61850DataAttributeMDO::RemoveMe
Description : [none]
Return :			void	-
Parameters :
void	-
Note : [none]
\**********************************************************************************/
void GTW61850DataAttributeMDO::DeleteMe(void)
{
  if (m_pValueDataAttribute)
  {
    if (m_pValueDataAttribute->GetUserDataArrayPtr())
    {
      m_pValueDataAttribute->GetUserDataArrayPtr()->remove(this);
    }
    m_pValueDataAttribute->DisablePointChangeNotification();
  }
  if (m_pQualityDataAttribute)
  {
    if (m_pQualityDataAttribute->GetUserDataArrayPtr())
    {
      m_pQualityDataAttribute->GetUserDataArrayPtr()->remove(this);
    }
    m_pQualityDataAttribute->DisablePointChangeNotification();
  }
  if (m_pTimeDataAttribute)
  {
    if (m_pTimeDataAttribute->GetUserDataArrayPtr())
    {
      m_pTimeDataAttribute->GetUserDataArrayPtr()->remove(this);
    }
    m_pTimeDataAttribute->DisablePointChangeNotification();
  }

  GTW61850ControlBlock *pControlBlock = this->GetControlBlock();
  if (pControlBlock)
  {
    pControlBlock->OnRemoveMDO(this);
    //GTW61850PolledPointSet *pps = dynamic_cast<GTW61850PolledPointSet*>(pControlBlock);
    //if (pps)
    //{
    //  pps->ClearPointsCache();
    //}
  }
  m_pClientNode->DeleteItem(this);
}

void GTW61850DataAttributeMDO::InitializeValue(tmw61850::Value::Type type)
{
  switch(type)
  {
  case tmw61850::Value::Type::FLOAT:
    m_vValue = 0.0;
    m_vValue.ChangeType(GTWDEFS_TYPE_SFLOAT);
    break;
  case tmw61850::Value::Type::INTEGER:
    m_vValue = (TMWTYPES_INT)0;
    m_vValue.ChangeType(GTWDEFS_TYPE_LONG);
    break;
  case tmw61850::Value::Type::UNSIGNED:
    m_vValue= (TMWTYPES_UINT)0;
    m_vValue.ChangeType(GTWDEFS_TYPE_ULONG);
    break;
  case tmw61850::Value::Type::BINARYTIME:
  case tmw61850::Value::Type::UTCTIME:
    m_vValue = (TMWTYPES_UINT)0;
    m_vValue.ChangeType(GTWDEFS_TYPE_TIME);
    break;
  case tmw61850::Value::Type::BITSTRING:
    m_vValue = (TMWTYPES_INT)0;
    m_vValue.ChangeType(GTWDEFS_TYPE_LONG);
    break;
  case tmw61850::Value::Type::BOOLEAN:
    m_vValue = false;
    m_vValue.ChangeType(GTWDEFS_TYPE_BOOL);
    break;
  case tmw61850::Value::Type::OCTETSTRING:
  case tmw61850::Value::Type::MMSSTRING:
  case tmw61850::Value::Type::STRING:
  case tmw61850::Value::Type::GENERALTIME:
    m_vValue = "";
    break;
  }
  //updateInternalValue();
}

/*
void GTW61850DataAttributeMDO::updateInternalValue()
{
  try
  {
    bool bResult = false;

    tmw::CriticalSectionLock lock(m_InternalValueCriticalSection);
    if (!m_pInternalValueDataAttribute)
    {
      if (m_pValueDataAttribute)
      {
        m_pInternalValueDataAttribute = new tmw61850::DataAttribute();
        *m_pInternalValueDataAttribute = *m_pValueDataAttribute;
        bResult = m_vValue.AssignTo61850DataAttribute(*(m_pInternalValueDataAttribute->GetValue()));
      }
    }
    else
    {
      bResult = m_vValue.AssignTo61850DataAttribute(*(m_pInternalValueDataAttribute->GetValue()));
    }
    if (m_pInternalValueDataAttribute)
    {
      assert(bResult);
      if (!bResult)
      {
        LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to set internal MDO value for '%s'", GetFullName().c_str());
      }
    }
  }
  catch (...)
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Unknown exception occurred in Update Internal 61850 MDO : %s", GetFullName().c_str());
#ifdef _WIN32
    assert(false);
#endif
   
  }
}
*/

void GTW61850DataAttributeMDO::SetValue(tmw61850::DataAttribute *pDa, tmw61850::Value *pVal)
{
  if (pDa == m_pValueDataAttribute)
  {
    SetValue(pVal);
  }
  else if (pDa == m_pQualityDataAttribute)
  {
    SetQuality(pVal);
  }
  else if (pDa == m_pTimeDataAttribute)
  {
    SetTime(pVal);
  }
}

void GTW61850DataAttributeMDO::SetValue(tmw61850::Value *pVal)
{
  tmw::CriticalSectionLock lock(m_MdoCS);
  GTW61850Client* pClientNode = this->GetClientNode();

  this->SetLastValue();
  if (!m_vValue.AssignFrom61850Value(*pVal))
  {
    LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s has an unsuported type : %s\n", GetFullName().c_str(), pVal->GetTypeAsXMLString());
  }

  //updateInternalValue();
}

void GTW61850DataAttributeMDO::SetQuality(tmw61850::Value *pVal)
{
  tmw::CriticalSectionLock lock(m_MdoCS);
  this->SetLastQuality();

  assert(pVal->GetType() == tmw61850::Value::Type::BITSTRING);
  tmw::BitString q;
  m_pQualityDataAttribute->GetBitStringValue(q);
  m_i61850Quality = q.GetIntValue();
}

void GTW61850DataAttributeMDO::Get61850QualityBitString(unsigned short qualityValue , CStdString& sBS)
{
  tmw61850::Quality q;
  q.SetFromInt(qualityValue, 13);
  tmw::String s;
  sBS = q.AsString(s);
}

I61850_QUALITY_TYPE GTW61850DataAttributeMDO::getGTW61850Quality(const tmw::BitString* pBS)
{
  I61850_QUALITY_TYPE quality = pBS->GetIntValue();

  /*
  int nNumBits = (std::min)(16, pBS->NumBits());
  for (int i = 0; i < nNumBits; i++)
  {
    TMWTYPES_UINT bOn = pBS->IsBitOn(i);
    quality |= bOn << i;
  }
  */
  return quality;
}

void GTW61850DataAttributeMDO::SetTime(tmw61850::Value *pVal)
{
  tmw::CriticalSectionLock lock(m_MdoCS);

  this->SetLastReportedTime();
  switch(pVal->GetType())
  {
  case tmw61850::Value::Type::UTCTIME:
    {
      TMWDTIME tempTime;
      GtwTime::InitTMWDTIME(&tempTime);

	    GTW61850Client::SixTSDateTimeToTMWDTime(*pVal->GetDateTime(), tempTime);
      
      // adjust by an additional bias. Some 61850 servers are not reporting in UTC
      TMWTYPES_SHORT bias = (TMWTYPES_SHORT)GTWConfig::I61850TimeZoneBias(GetClientNode()->Get61850ClientIndex());
      tmwdtime_adjustMinutes(&tempTime, bias);
      storeReportedTime(&tempTime);
      storeUpdatedTime(NULL);
    }
    break;
  }
}

CStdString GTW61850DataAttributeMDO::GetControlBlockName()
  {
    if (m_pControlBlock)
    {
      return m_pControlBlock->GetFullName();
    }
    // Must be either control or writeable point, so return empty string
    return "";
  }

CStdString GTW61850DataAttributeMDO::GetFullName()
{
  CStdString sCBName = "";
  if (m_pControlBlock != nullptr)
  {
    CStdString sCBName = m_pControlBlock->GetMemberName();
    if (sCBName == "commands")
    {
      sCBName += ".";
    }
    else
    {
      sCBName = "";
    }
  }
  if (m_pClientNode != nullptr)
  {
    return m_pClientNode->GetMemberName() + "." + sCBName + m_sValueTagName;
  }

  assert(false); // should not happen
  return "";
}

GTWDEFS_UPDTRSN GTW61850DataAttributeMDO::GetGTWChangeReason(void)
  {
    switch(m_changeReason)
    {
    case tmw61850::ClientPointChangeInfo::PointChangeReason_Unknown:
    return GTWDEFS_UPDTRSN_UNKNOWN;
      break;
    case tmw61850::ClientPointChangeInfo::PointChangeReason_GeneralInterrogation:
    case tmw61850::ClientPointChangeInfo::PointChangeReason_Integrity:
    case tmw61850::ClientPointChangeInfo::PointChangeReason_DataUpdate:
    case tmw61850::ClientPointChangeInfo::PointChangeReason_Goose:
    case tmw61850::ClientPointChangeInfo::PointChangeReason_Read:
    case tmw61850::ClientPointChangeInfo::PointChangeReason_Read_DataSet:
    case tmw61850::ClientPointChangeInfo::PointChangeReason_Read_Array:
      if (m_bValueChanged || m_bTimeChanged || m_bQualityChanged)
      {
        return GTWDEFS_UPDTRSN_CHNG_INDICATED;
      }
      return GTWDEFS_UPDTRSN_REFRESH;
      break;
    case tmw61850::ClientPointChangeInfo::PointChangeReason_QualityChange:
      return GTWDEFS_UPDTRSN_CHNG_INDICATED;
      //if (m_bValueChanged || m_bTimeChanged || m_bQualityChanged)
     // {
       // return GTWDEFS_UPDTRSN_CHNG_INDICATED;
      //}
      break;
    case tmw61850::ClientPointChangeInfo::PointChangeReason_DataChange:
      return GTWDEFS_UPDTRSN_CHNG_INDICATED;
      break;
    }
    return GTWDEFS_UPDTRSN_UNKNOWN;
  }

GTW61850DataAttributeMDO::GTW61850DataAttributeMDO()
  :
  m_bInitialized(false),
  m_cWritableNC(-1),
  m_LastReportedTime{ 0 }  
  //,m_pInternalValueDataAttribute(NULL)
{
  //m_bAllowWrites = false;
  m_i61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;
  m_iLast61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;

  m_bQualityChanged = false;
  m_changeReason = tmw61850::ClientPointChangeInfo::PointChangeReason_Unknown;
  m_sAliasName = "";
  m_sTagName = "";
  m_sValueTagName = "";
  m_sQualityTagName = "";
  m_sTimeTagName = "";
    
  m_pValueDataAttribute = NULL;
  m_pQualityDataAttribute = NULL;
  m_pTimeDataAttribute = NULL;
  m_pControlInfo = NULL;
  m_DesiredType = tmw61850::Value::Type::UNKNOWN;

  m_bDirty = false;
  m_pControlBlock = NULL;
  m_pClientNode = NULL;
  m_vValue = 0;
  m_vLastValue = 0;
  m_vAlarmStatusSaved = 0;
  m_bValueChanged = false;
  m_bTimeChanged = false;
  m_bIsControl = false;

  setControlParamDefaults();  
}

GTW61850DataAttributeMDO::~GTW61850DataAttributeMDO()
{
  //delete m_pInternalValueDataAttribute;

  //GetBaseEditor(EditorCommandDTO(MENU_CMD_NONE))->DeleteINIparms();
}

bool GTW61850DataAttributeMDO::isControl(void)
{
  if (m_bIsControl)
  {
    return true;
  }

  if (m_pControlInfo != NULL)
  {
    m_bIsControl = true;
  }
  if (m_pControlBlock)
  {
    if (dynamic_cast<GTW61850CommandPointSet*>(m_pControlBlock)) // here we assume an upgrade
    {
      m_bIsControl = true;
    }
  }
  return m_bIsControl;
}

bool GTW61850DataAttributeMDO::isWriteableFC(const char* sFunctionalConstraint)
{
  return  tmw::util::compareNoCase(sFunctionalConstraint, "sp") ||
    tmw::util::compareNoCase(sFunctionalConstraint, "cf") ||
    tmw::util::compareNoCase(sFunctionalConstraint, "sv");
}

bool GTW61850DataAttributeMDO::isWriteableNonControl()
{
  if (m_cWritableNC == -1)
  {
    if (m_pControlBlock)
    {
      if (dynamic_cast<GTW61850WritablePointSet*>(m_pControlBlock)) // here we assume an upgrade
      {
        m_cWritableNC = true;
        return true;
      }
    }
    //bool bConnected = m_pClientNode->IsConnectionAliveAndReady();
    //if (bConnected == false)
    //{
    //  m_cWritableNC = -1;
    //  return this->m_bAllowCommand;
    //}

    tmw61850::DataAttribute* pAttr = m_pValueDataAttribute;
    if (!pAttr)
    {
      CStdString n = GetFullName();
      CStdString sClientName = m_pClientNode->GetMemberName();
      n = n.substr(sClientName.length() + 1); // remote client name from path to lookup in model
      pAttr = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(n.c_str(), m_pClientNode->GetClientConnection()->Model());
    }

    m_cWritableNC = pAttr ? isWriteableFC(pAttr->GetFC()) : false;
  }
  return m_cWritableNC > 0 || this->m_bAllowCommand;
}

bool GTW61850DataAttributeMDO::isWriteable()
{
  return isCommandMDO() || isWriteableNonControl();
}

bool GTW61850DataAttributeMDO::isCommandMDO()
{
  return isControl();
}

void GTW61850DataAttributeMDO::setControlParamDefaults()
{
  m_orCat = static_cast<int>(tmw61850::EnumDefs::OrCat::stationcontrol);
  m_controlCheck = static_cast<int>(tmw61850::EnumDefs::Check::both);
  m_sOrIdent = "01";
  m_bControlTest = false;
}

GTWBaseEditor *GTW61850DataAttributeMDO::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
  {
    if (isControl())
      m_pEditor = new GTW61850CommandPointEditor(dto, this, GetClientNode(), m_pControlBlock, false);
    else
      m_pEditor = new GTW61850DataAttributeMDOEditor(dto, this, GetParentMember(), false);
  }
  else
  {
    m_pEditor->SetDTO(dto);
  }
  
  return m_pEditor;
}

void GTW61850DataAttributeMDO::ReadDataAttribute(tmw61850::DataAttribute *pAttr)
{
  GTW61850Client* pClientNode = this->GetClientNode();
  if (pAttr)
  {
    tmw61850::Client *pClient = GetClientNode()->GetClientConnection();
    tmw61850::ClientEnumDefs::ClientMMSErrorCode err = pClient->ReadNodeBlocking(pAttr);
          
    String sName;
    pAttr->GetFullName(sName);
    if (err == tmw61850::ClientEnumDefs::ClientMMSErrorCode::Success)
    {
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Successfully read %s", (const char*)sName);
      this->updateMDO(GTWDEFS_UPDTRSN_BY_OPERATOR | GTWDEFS_UPDTRSN_CHNG_INDICATED);
    }
    else
    {
      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Failed to read %s . MMS Error returned: %s", (const char*)sName, tmw61850::ClientMessage::ClientMMSErrorCodeToString(err));
    }
  }
}

void GTW61850DataAttributeMDO::ReadDataAttributes()
{
  if (this->GetClientNode())
  {
    if (GetClientNode()->IsUp())
    {
      tmw61850::Client *pClient = GetClientNode()->GetClientConnection();
      if (pClient)
      {
        ReadDataAttribute(m_pValueDataAttribute);
        ReadDataAttribute(m_pQualityDataAttribute);
        ReadDataAttribute(m_pTimeDataAttribute);
      }
    }
  }
}

bool GTW61850DataAttributeMDO::IsValueChanged()
{
  return m_bValueChanged;
}

bool GTW61850DataAttributeMDO::IsQualityChanged()
{
  return m_bQualityChanged;
}

bool GTW61850DataAttributeMDO::IsTimeChanged()
{
  return m_bTimeChanged;
}

void GTW61850DataAttributeMDO::ClearValueChanged()
{
  m_bValueChanged = false;
}

void GTW61850DataAttributeMDO::ClearQualityChanged()
{
  m_bQualityChanged = false;
}

void GTW61850DataAttributeMDO::ClearTimeChanged()
{
  m_bTimeChanged = false;
}

bool GTW61850DataAttributeMDO::IsTime1970()
{
  TMWDTIME ReportedTime;
  GTWDEFS_TIME_QLTY reportedTimeQuality;
  TMWDTIME UpdatedTime;
  GTWDEFS_TIME_QLTY updatedTimeQuality;

  GTWBaseDataObject *pBdo = this->getBdo();
  if (pBdo == NULL)
  {
    return false;
  }

  pBdo->getMdoReportedTime(&ReportedTime,&reportedTimeQuality, GetGTWApp()->gtwUtcTimeZone);
  pBdo->getMdoUpdatedTime(&UpdatedTime,&updatedTimeQuality, GetGTWApp()->gtwUtcTimeZone);

  if (
    (ReportedTime.year == 1970)
    && (ReportedTime.dayOfMonth == 1)
    && ReportedTime.hour == 0
    && ReportedTime.minutes == 0
    && ReportedTime.month == 1
    && reportedTimeQuality == GTWDEFS_TIME_QLTY_REMOTE
    )
  {
    return true;
  }
  if (
    (UpdatedTime.year == 1970)
    && (UpdatedTime.dayOfMonth == 1)
    && UpdatedTime.hour == 0
    && UpdatedTime.minutes == 0
    && UpdatedTime.month == 1
    && updatedTimeQuality != GTWDEFS_TIME_QLTY_REMOTE
    )
  {
    return true;
  }
  return false;
}

void GTW61850DataAttributeMDO::SetLastValueSaved(unsigned short v )
{
  m_vAlarmStatusSaved = v;
}

void GTW61850DataAttributeMDO::SetLastValue()
{
  tmw::CriticalSectionLock lock(m_MdoCS);
  if (m_vLastValue != m_vValue)
  {
    GTW61850Client *pClient = GetClientNode();
    ++pClient->m_NumValueChanges;
    m_bValueChanged = true;
  }
  m_vLastValue = m_vValue;
}


void GTW61850DataAttributeMDO::SetLastQuality()
{
  tmw::CriticalSectionLock lock(m_MdoCS);
  if (m_iLast61850Quality != m_i61850Quality)
  {
    GTW61850Client *pClient = GetClientNode();
    ++pClient->m_NumQualityChanges;
    m_bQualityChanged = true;
  }
  m_iLast61850Quality = m_i61850Quality;
}

void GTW61850DataAttributeMDO::SetLastReportedTime()
{
  tmw::CriticalSectionLock lock(m_MdoCS);
  TMWDTIME curReportedTime = ReportedTime();
  if (tmwdtime_compareTime(&curReportedTime, &m_LastReportedTime) != 0)
  {
    m_bTimeChanged = true;
  }
  m_LastReportedTime = curReportedTime;
}

/*
bool GTW61850DataAttributeMDO::IsWritable()
{
  GTW61850ControlBlock *pCB = GetControlBlock();
  if (pCB == NULL || m_bAllowWrites)
  {
    return true;
  }
  return false;
}
*/

//void GTW61850DataAttributeMDO::SetLastReportedGMTTime( TMWDTIME t )
//{
//  if (tmwdtime_compareTime(&m_tLastReportedGMTTime, &m_tReportedGMTTime) != 0)
//  {
//    GTW61850Client *pClient = GetClientNode();
//    m_bTimeChanged = true;
//  }
//  m_tLastReportedGMTTime = t;
//}
//

void GTW61850DataAttributeMDO::_updateMdo(TMWTYPES_ULONG reason)
{
  if (m_bInitialized)
  {
    if (GTWConfig::I61850ClientDoNotUpdateOnDuplicate[GetClientNode()->Get61850ClientIndex()])
    {
      if (m_vLastValue == m_vValue && m_iLast61850Quality == m_i61850Quality)
      {
        // If using updated time because there is no time DA, then value is always different - never a duplicate time
        TMWDTIME tmwDTime = ReportedTime();
        if (getTimeDataAttribute() != NULL && tmwdtime_compareTime(&tmwDTime, &m_LastReportedTime) == 0)
        {
          //TRACE("Found duplicate for %s\n", GetFullName().c_str());
          return;
        }
      }
    }
  }
  else
  {
    //TRACE("61850 point unitialized for %s\n", GetFullName().c_str());
    m_bInitialized = true;
  }

  GTWMasterDataObject::updateMDO(reason);
}

void GTWM61850_CNVTR_READ_BOOL::getValue(GTWMasterDataObject *pMdo, bool *pValue, GTWDEFS_STD_QLTY *pStdQuality)
{
  if (m_p61850Mdo != NULL && m_p61850Mdo->getBdo()->IsA("GTW61850DataAttributeMDO"))
  {
    *pStdQuality = m_p61850Mdo->getMdoStdQuality();

    CStdString sValue;
    m_p61850Mdo->getValueAsString(sValue);
    if (TMWIsNumber(sValue.c_str()))
    {
      TMWTYPES_INT iVal = atoi(sValue.c_str());
      *pValue = iVal != 0;
    }
    else
    {
      tmw::String strValue = sValue.c_str();

      if (strValue.equalsNoCase("on") || strValue.equalsNoCase("true"))
      {
        *pValue = true;
      }
      else
      {
        if (m_p61850Mdo->getValueDataAttribute() && m_p61850Mdo->getValueType() == tmw61850::Value::Type::BITSTRING)
        {
          tmw61850::DataAttribute *pAttr = m_p61850Mdo->getValueDataAttribute();
          // If this is an enum we set to false - only accept "on" and "true" to be true
          if (pAttr->IsEnum() && pAttr->GetEnumType())
          {
            *pValue = false;
          }
          else
          {
            TMWTYPES_DOUBLE dVal = m_p61850Mdo->getValue();
            const tmw::BitString *bitString = pAttr->GetBitStringValue();
            if (bitString && bitString->NumBits() == 2) // For now we treat this as a DbPos since it is mapped to a boolean
            {
              *pValue = dVal == 2; // the 61850 protocol defines DbPos bitstring value as 0,1,3 as (not on) and 2 (=[10]) as on/true
            }
            else // if this is not an enum and not a bitstring of size 2, we use the actual bit string value
            {
              *pValue = dVal != 0;
            }
          }
        }
        else // if its not a bitstring
        {
          *pValue = false;
        }

        /* old code that we probably should not implement
        if ((strValue.findNoCase("[00]", 0) >= 0))
        {
        *pStdQuality = m_p61850Mdo->getMdoStdQuality() | GTWDEFS_STD_QLTY_IN_TRANSIT;
        }
        else if ((strValue.findNoCase("[11]", 0) >= 0))
        {
        *pStdQuality = m_p61850Mdo->getMdoStdQuality() | GTWDEFS_STD_QLTY_INVALID;
        }
        else
        {
        *pStdQuality = m_p61850Mdo->getMdoStdQuality();
        }
        */
      }
    }
  }
  else
  {
    *pValue = false;
    *pStdQuality = GTWDEFS_STD_QLTY_UNINITIALIZED;
  }
}


