Comment: This is actually for .NET 4.6 NOT 4.6.1.  I used the template for 4.6.1, which is why the script name says 461.
 
Set Variable DOTNET461_X86X64 to TRUE
Get System Setting Windows 10 Threshold 2 into DOTNET461_IS10
Get System Setting Windows in 64 bit Mode into ISWINDOWS64BIT
if Variable ISWINDOWS64BIT Equals TRUE
  Set x64 - Native 64 bit Windows, AMD64 and EM64T Architectures - installation mode
end
Read Registry Key HKLM\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\Version into DOTNET461_X86X64
if Variable ISWINDOWS64BIT Equals TRUE
  Set Win32 - Native 32 bit Windows and Windows 32 on Windows 64 (WOW64) - installation mode
end
 
Comment: if Variable DOTNET461_X86X64 Greater Than (Version) 4.6.01054
if Variable DOTNET461_X86X64 not Greater Than (Version) 4.6
  Set Variable DOTNET461_X86X64 to TRUE
else
  if Variable DOTNET461_IS10 Equals TRUE
    Comment: .NET 4.6.1 is pre-installed and uninstallable on Windows 10 Threshold 2
    Set Variable DOTNET461_X86X64 to TRUE
  else
    Set Variable DOTNET461_X86X64 to FALSE
  end
end
 
if Variable DOTNET461_X86X64 Equals FALSE
  Get System Setting Windows 8.1 Update 1 into DOTNET461_IS81UP1
  Get System Setting Windows 8.1 into DOTNET461_IS81
  Get System Setting Windows 8 into DOTNET461_IS8
  Get System Setting Windows 7 into DOTNET461_IS7
  Get System Setting Service Pack 1 into DOTNET461_SP1
   
  if Variable DOTNET461_IS7 Equals FALSE
    Comment: .NET 4.6.1 requires at least Windows 7
    MessageBox: $TITLE$ Setup, $TITLE$ requires at least Windows 7 or later. Please upgrade your operating system and try again.$NEWLINE$$NEWLINE$$TITLE$ Setup cannot continue.
    Terminate Installation
  else
    if Variable DOTNET461_IS8 Equals FALSE
      if Variable DOTNET461_SP1 Equals FALSE
        Comment: .NET 4.6.1 requires Service Pack 1 on Windows 7
        MessageBox: $TITLE$ Setup, $TITLE$ requires at least Service Pack 1 or later. Please upgrade your operating system and try again.$NEWLINE$$NEWLINE$$TITLE$ Setup cannot continue.
        Terminate Installation
      end
    else
      if Variable DOTNET461_IS81 Equals TRUE
        if Variable DOTNET461_IS10 Equals FALSE
          if Variable DOTNET461_IS81UP1 Equals FALSE
            Comment: .NET 4.6.1 requires Update 1 on Windows 8.1
            MessageBox: $TITLE$ Setup, $TITLE$ requires at least Update 1 or later. Please upgrade your operating system and try again.$NEWLINE$$NEWLINE$$TITLE$ Setup cannot continue.
            Terminate Installation
          end
        end
      end
    end
  end
   
  Set Variable PREREQ to TRUE
  Comment: Set Variable PRELIST to $PRELIST$$NEWLINE$Microsoft .NET Framework 4.6 with Service Pack 1 (x86/x64)
  Set Variable PRELIST to $PRELIST$$NEWLINE$Microsoft .NET Framework 4.6 (x86/x64)
end
 
