<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>MQTT C Client Libraries Internals: MQTTAsync_command Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MQTT C Client Libraries Internals
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">MQTTAsync_command Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Collaboration diagram for MQTTAsync_command:</div>
<div class="dyncontent">
<div class="center"><img src="structMQTTAsync__command__coll__graph.png" border="0" usemap="#MQTTAsync__command_coll__map" alt="Collaboration graph"/></div>
<map name="MQTTAsync__command_coll__map" id="MQTTAsync__command_coll__map">
<area shape="rect" title=" " alt="" coords="67,303,225,329"/>
<area shape="rect" href="structMQTTSubscribe__options.html" title="The MQTT V5 subscribe options, apart from QoS which existed before V5." alt="" coords="5,199,172,225"/>
<area shape="rect" href="structMQTTProperties.html" title="MQTT version 5 property list." alt="" coords="196,199,315,225"/>
<area shape="rect" href="structMQTTProperty.html" title="Structure to hold an MQTT version 5 property of any type." alt="" coords="202,109,309,136"/>
<area shape="rect" href="structMQTTLenString.html" title="The data for a length delimited string." alt="" coords="198,5,313,32"/>
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a3f4f9d5b64ea79fa1183f9a5e8839085"><td class="memItemLeft" align="right" valign="top"><a id="a3f4f9d5b64ea79fa1183f9a5e8839085"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>type</b></td></tr>
<tr class="separator:a3f4f9d5b64ea79fa1183f9a5e8839085"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6165e2bc01a5a5735ea5632df4063ad1"><td class="memItemLeft" align="right" valign="top"><a id="a6165e2bc01a5a5735ea5632df4063ad1"></a>
MQTTAsync_onSuccess *&#160;</td><td class="memItemRight" valign="bottom"><b>onSuccess</b></td></tr>
<tr class="separator:a6165e2bc01a5a5735ea5632df4063ad1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad01c9c522151ee9ad0c331a3a83cb862"><td class="memItemLeft" align="right" valign="top"><a id="ad01c9c522151ee9ad0c331a3a83cb862"></a>
MQTTAsync_onFailure *&#160;</td><td class="memItemRight" valign="bottom"><b>onFailure</b></td></tr>
<tr class="separator:ad01c9c522151ee9ad0c331a3a83cb862"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada429695a72f12f3531e3034dbf72580"><td class="memItemLeft" align="right" valign="top"><a id="ada429695a72f12f3531e3034dbf72580"></a>
MQTTAsync_onSuccess5 *&#160;</td><td class="memItemRight" valign="bottom"><b>onSuccess5</b></td></tr>
<tr class="separator:ada429695a72f12f3531e3034dbf72580"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a391c122e049026c9f6621791bc73f895"><td class="memItemLeft" align="right" valign="top"><a id="a391c122e049026c9f6621791bc73f895"></a>
MQTTAsync_onFailure5 *&#160;</td><td class="memItemRight" valign="bottom"><b>onFailure5</b></td></tr>
<tr class="separator:a391c122e049026c9f6621791bc73f895"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7f32ac021afca6365c0294c41c4dd54"><td class="memItemLeft" align="right" valign="top"><a id="ac7f32ac021afca6365c0294c41c4dd54"></a>
MQTTAsync_token&#160;</td><td class="memItemRight" valign="bottom"><b>token</b></td></tr>
<tr class="separator:ac7f32ac021afca6365c0294c41c4dd54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d4e143a28e27720481268bfaf3cce10"><td class="memItemLeft" align="right" valign="top"><a id="a0d4e143a28e27720481268bfaf3cce10"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>context</b></td></tr>
<tr class="separator:a0d4e143a28e27720481268bfaf3cce10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a49bd0dd1f17c682a66efd2703d4e4c6e"><td class="memItemLeft" align="right" valign="top"><a id="a49bd0dd1f17c682a66efd2703d4e4c6e"></a>
START_TIME_TYPE&#160;</td><td class="memItemRight" valign="bottom"><b>start_time</b></td></tr>
<tr class="separator:a49bd0dd1f17c682a66efd2703d4e4c6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d3d7b2d4b47a5cb7cdee3ea9ea7e25e"><td class="memItemLeft" align="right" valign="top"><a id="a8d3d7b2d4b47a5cb7cdee3ea9ea7e25e"></a>
<a class="el" href="structMQTTProperties.html">MQTTProperties</a>&#160;</td><td class="memItemRight" valign="bottom"><b>properties</b></td></tr>
<tr class="separator:a8d3d7b2d4b47a5cb7cdee3ea9ea7e25e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac92c38bbe7e9e1427f1caeda4c34102f"><td class="memItemLeft" ><a id="ac92c38bbe7e9e1427f1caeda4c34102f"></a>
union {</td></tr>
<tr class="memitem:a25750812639f2399e0df1bd8b6d83523"><td class="memItemLeft" >
&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:adb2d6722317219bce4478ccee55a9bb9"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>count</b></td></tr>
<tr class="separator:adb2d6722317219bce4478ccee55a9bb9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef3c1ad4aaeab214e51ac56daafd7be5"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;char **&#160;&#160;&#160;<b>topics</b></td></tr>
<tr class="separator:aef3c1ad4aaeab214e51ac56daafd7be5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad378b3a8e4d5ea744e4d3095cf4dca09"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int *&#160;&#160;&#160;<b>qoss</b></td></tr>
<tr class="separator:ad378b3a8e4d5ea744e4d3095cf4dca09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab97680917a2590dc71dd787d90881836"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;<a class="el" href="structMQTTSubscribe__options.html">MQTTSubscribe_options</a>&#160;&#160;&#160;<b>opts</b></td></tr>
<tr class="separator:ab97680917a2590dc71dd787d90881836"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee4dfb2ed7cf214c22a4f0aeefb3baf5"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;<a class="el" href="structMQTTSubscribe__options.html">MQTTSubscribe_options</a> *&#160;&#160;&#160;<b>optlist</b></td></tr>
<tr class="separator:aee4dfb2ed7cf214c22a4f0aeefb3baf5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25750812639f2399e0df1bd8b6d83523"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<b>sub</b></td></tr>
<tr class="separator:a25750812639f2399e0df1bd8b6d83523"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a791db641d8abee4ca0cd0712c0485b9c"><td class="memItemLeft" >
&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:a54cea4be66b80d3f296f1f5ab197f7bd"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>count</b></td></tr>
<tr class="separator:a54cea4be66b80d3f296f1f5ab197f7bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b4e87a30d292ceca4a7d17c13d56e5d"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;char **&#160;&#160;&#160;<b>topics</b></td></tr>
<tr class="separator:a8b4e87a30d292ceca4a7d17c13d56e5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a791db641d8abee4ca0cd0712c0485b9c"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<b>unsub</b></td></tr>
<tr class="separator:a791db641d8abee4ca0cd0712c0485b9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0702c44b1879a006353147518869b138"><td class="memItemLeft" >
&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:a3c7da512f90ba99b6a6a1b28fd86abc4"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;char *&#160;&#160;&#160;<b>destinationName</b></td></tr>
<tr class="separator:a3c7da512f90ba99b6a6a1b28fd86abc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a692d7f532f095237beca9c882e3532d1"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>payloadlen</b></td></tr>
<tr class="separator:a692d7f532f095237beca9c882e3532d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b0e929d115a828fe4c83c7a56ae879d"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;void *&#160;&#160;&#160;<b>payload</b></td></tr>
<tr class="separator:a6b0e929d115a828fe4c83c7a56ae879d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af113f9c8c1bb1a14f6f70b8e001b63f6"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>qos</b></td></tr>
<tr class="separator:af113f9c8c1bb1a14f6f70b8e001b63f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b7b31807d10280fb748becb81941c2b"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>retained</b></td></tr>
<tr class="separator:a4b7b31807d10280fb748becb81941c2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0702c44b1879a006353147518869b138"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<b>pub</b></td></tr>
<tr class="separator:a0702c44b1879a006353147518869b138"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac71eb343372b5de0bcd2201a90e3e2cb"><td class="memItemLeft" >
&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:afb35eb7c95518329aff18025da8a7726"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>internal</b></td></tr>
<tr class="separator:afb35eb7c95518329aff18025da8a7726"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20d585de129a64a3ce855c0146d809dc"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>timeout</b></td></tr>
<tr class="separator:a20d585de129a64a3ce855c0146d809dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af91771abdef0e4b1f53123ee982fbbe8"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;enum MQTTReasonCodes&#160;&#160;&#160;<b>reasonCode</b></td></tr>
<tr class="separator:af91771abdef0e4b1f53123ee982fbbe8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac71eb343372b5de0bcd2201a90e3e2cb"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<b>dis</b></td></tr>
<tr class="separator:ac71eb343372b5de0bcd2201a90e3e2cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4224e72e9de946f3f5994775672482bb"><td class="memItemLeft" >
&#160;&#160;&#160;struct {</td></tr>
<tr class="memitem:a30b9ebf572a507a414b634085614b1c8"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<b>currentURI</b></td></tr>
<tr class="separator:a30b9ebf572a507a414b634085614b1c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac13f7f0265dfa952b5637ad81fba0725"><td class="memItemLeft" >
&#160;&#160;&#160;&#160;&#160;&#160;int&#160;&#160;&#160;<a class="el" href="structMQTTAsync__command.html#aab15c3354653ca0d03644bcaa98f77fb">MQTTVersion</a></td></tr>
<tr class="memdesc:ac13f7f0265dfa952b5637ad81fba0725"><td class="mdescLeft">&#160;</td><td class="mdescRight">current MQTT version being used to connect <br /></td></tr>
<tr class="separator:ac13f7f0265dfa952b5637ad81fba0725"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4224e72e9de946f3f5994775672482bb"><td class="memItemLeft" valign="top">&#160;&#160;&#160;}&#160;&#160;&#160;<b>conn</b></td></tr>
<tr class="separator:a4224e72e9de946f3f5994775672482bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac92c38bbe7e9e1427f1caeda4c34102f"><td class="memItemLeft" valign="top">}&#160;</td><td class="memItemRight" valign="bottom"><b>details</b></td></tr>
<tr class="separator:ac92c38bbe7e9e1427f1caeda4c34102f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>MQTTAsyncUtils.h</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:46 for MQTT C Client Libraries Internals by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
