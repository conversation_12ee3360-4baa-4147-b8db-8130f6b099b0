/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWMasterDataObject.h                                       */
/* DESCRIPTION:  Definition of TMW Gateway Master Data Object Class          */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 45 ***_NOBODY_*** "                                                     */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#pragma once
#include "GTWLibDll.h"

#include "GTWConverter.h"
#include "GTWIecBinConverter.h"
#include "gateway/GTWOsUtils/gtwexpnd.h"
#include "GTWSequenceOfEventsFile.h" 
#include "gateway/GTWOsUtils/TMWVectorTemplate.h"
#include "gateway/GTWOsUtils/GtwVariant.h"
#include "GTWBaseDataObject.h"
#include <mutex>

#if USE_OPC_44
namespace SoftingOPCToolboxServer
{
  class AeCondition;
  class AeCategory;
}
class Variant;
#endif

class GTWOpcUaSlaveDataObject;
typedef GTWSlaveDataObject *GTWSlaveDataObject_PTR;

typedef enum {
  GTW_BIND_STATUS_FAILED=0,
  GTW_BIND_STATUS_DUPLICATE,
  GTW_BIND_STATUS_NOT_COMMAND,
  GTW_BIND_STATUS_NO_TYPE_CONVERSION,
  GTW_BIND_STATUS_SUCCESS
} GTW_BIND_STATUS;

typedef enum {
  GTW_BIND_MODE_NORMAL=0,
  GTW_BIND_MODE_ALLOW_REPLACE
} GTW_BIND_MODE;

class MdoWriteTimerInfo;
class GTWMMBMultiPoint;

class GTWLIB_API GTWMasterDataObject
{
  friend class MdoWriteTimerInfo;

public:
  //static std::vector<GTWMasterDataObject *> *g_mdoArray;
  GTWMdoVariantReadConverter *m_pVariantReadCnvtr;
  GtwVariant      m_vValue;
  TMWTYPES_USHORT m_iOpcUaSeverity;
  CStdString m_sOpcUaMessage;

  TMWVectorTemplate<GTWMasterDataObject *, 1>  GetMdoReadBoundList() const 
  { 
    return m_MdoReadBoundList;
  }
  TMWVectorTemplate<GTWMasterDataObject *, 1>  GetMdoWriteBoundList() const
  { 
    return m_MdoWriteBoundList;
  }
  TMWVectorTemplate<GTWSlaveDataObject_PTR, 1> GetSdoReadBoundList() const  { return m_SdoReadBoundList; }
  TMWVectorTemplate<GTWSlaveDataObject_PTR, 1> GetSdoWriteBoundList() const { return m_SdoWriteBoundList; }

protected:
  TMWVectorTemplate<GTWMasterDataObject *, 1>  m_MdoReadBoundList;  // list of MDOs that are sources for this mdo - can only be one
  TMWVectorTemplate<GTWMasterDataObject *, 1>  m_MdoWriteBoundList; // list of MDO's that are destinations of this mdo which is a source 

  TMWVectorTemplate<GTWSlaveDataObject_PTR, 1> m_SdoReadBoundList; // list of SDOs that this writes to
  TMWVectorTemplate<GTWSlaveDataObject_PTR, 1> m_SdoWriteBoundList; // list of SDO controls that write to this

  // list of MDO names used by other MDOs (needs to be verified after MDOs are created)
  static std::list<CStdString> g_sMdoXrefList;

  GTWDEFS_STD_QLTY   m_stdQuality;
  //MdoWriteTimerInfo *m_pMdoWriteTimerInfo;
  //WinTimer          *m_pMdoWriteTimer;

#if USE_OPC_44

  void SetActiveCondition( GtwVariant Value, FILETIME timestamp );

  void SetInactiveCondition( GtwVariant Value, FILETIME timestamp );

  void SetupInActiveConditionNames(void);

  void SetupActiveConditionNames(void);

  bool validateConditionName( CStdString n );
  TMWTYPES_ULONG getEventSeverity(void)
  {
    ASSERT(TMWDEFS_FALSE);  // override this for a real simple event
    return 1;
  }
#endif
  CStdString getEventMessage(void)
  {
    ASSERT(TMWDEFS_FALSE);  // override this for a real simple event
    return "Not Defined";
  }


  // These values are used to determine how an OPC SDO, generated
  //  from this MDO, will report the event time etc
  GTWDEFS_OPC_REPORT_MODE m_eOPCReportingMode;
  //GTWDEFS_OPC_TAG_MODE m_eOPCTagMode;
  bool m_bOPCReportingModeSpecified;
  //bool m_bOPCTagModeSpecified;

  GTWDEFS_TIME_SOURCE m_eOPCTimeSource;
  GTWDEFS_TIME_SOURCE m_eOPCAETimeSource;

  bool        m_bOPCTimeSourceSpecified;
  bool        m_bOPCAETimeSourceSpecified;
  bool        m_bOPCAESimpleEventSpecified;
  bool        m_bOPCAEUseValueAsMessage;
  bool        m_bAllowCommand;
  bool        m_bScaleSpecified;
  bool        m_bOPCUAEventSpecified;
  bool        m_bOPCPublish;
  bool        m_bOPCUAPublish;

  bool        m_bOPCAEActiveAckRequired;
  bool        m_bOPCAEInActiveAckRequired;

  bool        m_bOPCAEFireCondOnAttrChange;

  TMWTYPES_ULONG m_OPCAESeverity;
  TMWTYPES_ULONG m_OPCAEInActiveSeverity;
  CStdString m_sAEMessage;
  CStdString m_sAEConditionName;
  CStdString m_sAEInActiveConditionName;
  CStdString m_sAEAlarmValue;
  CStdString m_subCondName;
  CStdString m_subCondInActiveName;
  CStdString m_condName;
  CStdString m_condInActiveName;
#if USE_OPC_44
  bool m_bIsSubCond;
  bool m_bIsInActiveSubCond;
  std::vector<Variant> m_opcEventAttributeList;
  Variant *m_pOpcEventAttribute;

  SoftingOPCToolboxServer::AeCategory* m_pCategoryActive;
  SoftingOPCToolboxServer::AeCategory* m_pCategoryInActive;
#endif
  void setLogFileUpdtrsnMask(GTWDEFS_UPDTRSN mask)
  {
    m_iLogFileUpdtrsnMask = mask;

    if (mask) // if non-zero, then create an log file handle
    {
      m_pEventLogFile = GTWSequenceOfEventsFile::gtwmsoef_findEventFile(this);
    }
    else
    {
      if (m_pEventLogFile)
      {
        m_pEventLogFile->gtwmsoef_unuseEventFile();
      }
      m_pEventLogFile = TMWDEFS_NULL;
    }
  }

  //void setOpcAEUpdtrsnMask(GTWDEFS_UPDTRSN mask)
  //{
  //  m_iOpcAEUpdtrsnMask = mask;
  //}

  TMWTYPES_ULONG    m_iOPCAE_ACTSTATE;
  TMWTYPES_ULONG    m_iOPCAE_INACTSTATE;
  void setOPCAE_ACTSTATE(TMWTYPES_ULONG mask)
  {
    m_iOPCAE_ACTSTATE = mask;
  }
  void setOPCAE_INACTSTATE(TMWTYPES_ULONG mask)
  {
    m_iOPCAE_INACTSTATE = mask;
  }

  TMWTYPES_ULONG    m_iOPCAE_ACTMASK;
  TMWTYPES_ULONG    m_iOPCAE_INACTMASK;
  void setOPCAE_ACTMASK(TMWTYPES_ULONG mask)
  {
    m_iOPCAE_ACTMASK = mask;
  }
  void setOPCAE_INACTMASK(TMWTYPES_ULONG mask)
  {
    m_iOPCAE_INACTMASK = mask;
  }


public:
  tmw::AutoCriticalSection m_MdoCS;

  static TMWTYPES_ULONG g_updateCount;

  // scale values
  TMWTYPES_DOUBLE      m_dScaleRawMin;
  TMWTYPES_DOUBLE      m_dScaleRawMax;
  TMWTYPES_DOUBLE      m_dScaleEguMin;
  TMWTYPES_DOUBLE      m_dScaleEguMax;

  GTWMasterDataObject(void);
  virtual ~GTWMasterDataObject();

  static TMWTYPES_DOUBLE getVarientValueAsDouble(GtwVariant &varient);

#if USE_OPC_44
  VARENUM getVariantType()
  {
    return m_vValue.getVariantTypeFromMdoType(getMdoType());
  }
#endif

  // All must be overridden to support new persistence
  ////////////////////////////////////////////////////////////////////
  virtual bool SupportsNewPersistence() { return false; }
  virtual std::string ToPersistString() { return ""; }
  virtual bool RestoreFromPersistString(const std::string &sData) { return false; }
  virtual std::string GetPersistClassName() { return ""; }
  ////////////////////////////////////////////////////////////////////

#if USE_OPC_44
  SoftingOPCToolboxServer::AeCondition *  m_activeOrNotAcknowledgedCond;
#endif

  virtual bool isEditable() { return true; }
  virtual bool isDeletable() { return true; }
  virtual bool isOPCBrowsable() { return m_bOPCPublish; }
  virtual bool isOPCUABrowsable() { return m_bOPCUAPublish; }
  virtual void SetValueAndQuality(double dValue, unsigned int iQuality)
  {
    // by default do nothing (override this to implement)
  }
  void SetODBCMdo(bool b)
  {
    m_bIsODBCMdo = b;
  }

  /* virtual functions */
  bool IsODBCMdo(void)
  {
    return m_bIsODBCMdo;
  }

  bool getSaveThisMdo(void)
  {
    return m_bSaveThisMdo;
  }
  void setSaveThisMdo(bool b)
  {
    m_bSaveThisMdo = b;
  }

  bool getAlarmAcked(void)
  {
    return m_bAlarmAcked;
  }
  void setAlarmAcked(bool b)
  {
    m_bAlarmAcked = b;
  }

  static GTWMasterDataObject *gtwusrtg_findMDO(CStdString &userTagName);
  static bool    gtwusrtg_insertInMap(GTWMasterDataObject *pMdo, const CStdString& newName);
  static bool    gtwusrtg_removeFromMap(GTWMasterDataObject *pMdo);

  static bool ValidateXrefMdos(CStdString &mdoName);

  void setBdo(GTWBaseDataObject *p) 
  { 
    m_pBdo = p; 
  }

  GTWBaseDataObject *getBdo(void) 
  { 
    if (m_pBdo == NULL)
    {
      this->setBdo(dynamic_cast<GTWBaseDataObject*>(this));
    }
    return m_pBdo; 
  }

  unsigned short getOPCQuality(GTWDEFS_UPDTRSN updateReason);

  void setOPCsubscribed(bool b) 
  { 
    m_bOPCsubscribed = b; 
  }
  bool getOPCsubscribed(void) 
  { 
    return m_bOPCsubscribed; 
  }

  // pure virtual functions
  virtual void getMdoDbasDataId(GTWDEFS_DBAS_DATA_ID *pDbasDataId) = 0;
  virtual GTWDEFS_TYPE getMdoType(void) = 0;

  virtual GTWDEFS_STAT setOptions(const char *connectionToken, const char *pOptionString) = 0;
  virtual GTWDEFS_STAT updateOptions(const char *connectionToken, const char *pOptionString) = 0;
  virtual CStdString getOptionsString(void) = 0;

  // virtual functions
  virtual GTWDEFS_STD_QLTY getMdoStdQuality(void) { return m_stdQuality; }
  virtual void setMdoStdQuality(GTWDEFS_STD_QLTY q) { m_stdQuality = q; }

  virtual void getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg)
  {
    if (m_vValue.GetType() == GTWDEFS_TYPE_UCHAR)
    {
      msg.Format("%d", m_vValue.GetUCharValue());
      return;
    }
    m_vValue.GetValueAsString(pFormat, msg);
  }
  virtual void getMdoValueAsVariant(GtwVariant &variant) { variant = m_vValue; }
  virtual void *bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo,GTWCNVTR_TYPE cnvtrType);
  virtual void *bindMdoReportedTime(GTWCNVTR_TYPE cnvtrType);
  virtual void *bindMdoToSdoForWriting(GTWSlaveDataObject *pWriteFromSdo,GTWCNVTR_TYPE cnvtrType);
  virtual GTWCNVTR_TYPE getCnvtrType(void);
  virtual bool FormatQualityString(char* buffer, int bufferSize, GTWEXPND_EXPANSION *pExpansion) { return false; }

  virtual bool checkMdoToMdoConverter(GTWCNVTR_TYPE cnvtrType)
  {
    return false;
  }
  
  virtual bool isBoundToMdo(void)
  {
    return (m_MdoReadBoundList.getSize() > 0);
  }

  GTWEquationDataObject* IsBoundToEquation();
  GTWMasterDataObject *GetBoundMdoAt(int index)
  {
    if (index < m_MdoReadBoundList.getSize())
    {
      return m_MdoReadBoundList[index];
    }
    return NULL;
  }

  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void)    /* Is object to be logged to event file? */
  {
    return GTWDEFS_UPDTRSN_NONE;
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void)
  {
    return (GTWDEFS_UPDTRSN_NONE);
  }

  // called when data is stored in the MDO             
  virtual void updateMDO(GTWDEFS_UPDTRSN updateReason);
  virtual void updateNonWriteableMDOs(GTWDEFS_UPDTRSN updateReason);

  void updateOPCMappings(GTWDEFS_UPDTRSN updateReason);
  void RefreshMdo (void);

  // Called by MDO when a data update occurs this is for binding
  virtual void updateMDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo);

  virtual GTW_BIND_STATUS bindMdoWithMdo(GTWMasterDataObject *pSourceMdo ,GTW_BIND_MODE bindMode = GTW_BIND_MODE_NORMAL);

  void unbindAllForRemoval(void);
  void unbindSdos(bool bDeleteSdos);
  void unbindMdo(GTWSlaveDataObject *pSdo);
  void unbindMdo(GTWMasterDataObject *pMdo);

  virtual bool isScaleOptionImplemented()
  {
    return false;
  }

  virtual bool isDeadBandOptionImplemented()
  {
    return false;
  }

  virtual bool isCommandMDO()
  {
    if (this->getBdo())
    {
      CStdString sOptions = this->getBdo()->gtwbdo_getOptionsString();
      return sOptions.Find("FORCE_ALLOW_COMMAND") != std::string::npos;
    }
    return false;
  }

  virtual bool isControl() // a true control, not just a writable point
  {
    return false;
  }

  virtual bool isWriteable()
  {
    return isCommandMDO() || this->m_bAllowCommand;
  }

  virtual bool isWriteableNonControl()
  {
    return !isCommandMDO() && !isControl() && isWriteable();
  }

#if USE_OPC_UA
  GTWOpcUaSlaveDataObject* GetOpcUaSDO();
#endif

  bool HasMappedSdo(bool checkDeletable = false);
  bool HasNonOpcMappedSdo();
  bool HasMappedSdoAndCannotDelete();

  bool HasMappedCommandSdo();
  bool HasMappedMdo();

  bool IsMapped(CStdString& sMappedTo);
  virtual bool IsInDualRegister(GTWMMBMultiPoint*&) { return false; }

  GTWSlaveDataObject *loopThroughBoundSdos(
    bool *bFoundBoundSdo,
    bool (*stopLoopingFunction)(GTWSlaveDataObject *pSdo, GTW_MAPPING_DIR dir, void *pParam),
    void *pParam,
    bool checkDeletable = false);

  GTWSlaveDataObject *loopThroughBoundCommandSdos(
    bool *bFoundBoundSdo, 
    bool(*stopLoopingFunction)(GTWSlaveDataObject *pSdo, GTW_MAPPING_DIR dir, void *pParam),
    void *pParam);

  GTWMasterDataObject *loopThroughBoundMdos(
    bool *bFoundBoundMdo,
    bool (*stopLoopingFunction)(GTWMasterDataObject *pMdo, GTW_MAPPING_DIR dir, void *pParam),
    void *pParam);

  GTWMasterDataObject *loopThroughBoundMdosListView(
    bool *bFoundBoundMdo,
    bool(*stopLoopingFunction)(GTWMasterDataObject *pMdo, GTW_MAPPING_DIR dir, void *pParam),
    void *pParam);

  //const CStdString getMdoUserTagName(void)
  const CStdString getMdoUserTagName(void) 
  {
    return(m_sUserTagName);
  }

  bool setMdoUserTagName(const char *UserTagName);

  CStdString getMdoDescription(void) 
  {
    return(m_sDescriptionString);
  }
  void setMdoDescription(const char *pDescription) 
  {
    if (pDescription && strlen(pDescription) > 0)
    {
      m_sDescriptionString = CStdString(pDescription);
    }
    else
    {
      m_sDescriptionString = "";
    }
  }

  CStdString getParenUserTagName(void);

#if 0
  void addDrawObjectToUpdateList(void *pDrawObj);
  void removeDrawObjectFromUpdateList(void *pDrawObj);
#endif
  virtual void addSdoToReadBoundList(GTWSlaveDataObject *pUpdateSdo);
  void addSdoToWriteBoundList(GTWSlaveDataObject *pWriteFromSdo);

  void addMdoToReadBoundList(GTWMasterDataObject *pUpdateMdo);
  void addMdoToWriteBoundList(GTWMasterDataObject *pWriteFromMdo);
  void removeMdoFromWriteBoundList(GTWMasterDataObject *pMdo);
  
  bool isOPCTimeSpecified()
  {
    return m_bOPCTimeSourceSpecified; 
  }

  bool isOPCAETimeSpecified()
  {
    return m_bOPCAETimeSourceSpecified; 
  }

  bool isOPCAESimpleEventSpecified()
  {
    return m_bOPCAESimpleEventSpecified; 
  }

  bool isOPCUAEventSpecified()
  {
    return m_bOPCUAEventSpecified;
  }

  bool isScaleSpecified()
  {
    return m_bScaleSpecified; 
  }

#if USE_OPC_44
  GTWDEFS_OPC_REPORT_MODE GetOPCReportingMode()
  {
    if(m_bOPCReportingModeSpecified)
      return m_eOPCReportingMode;
    else
      return (GTWDEFS_OPC_REPORT_MODE)(int)GTWConfig::OPCServerReportMode;
  }
#endif

  //GTWDEFS_OPC_TAG_MODE GetOPCTagMode()
  //{
  //  if(m_bOPCTagModeSpecified)
  //    return m_eOPCTagMode;
  //  else
  //    return (GTWDEFS_OPC_TAG_MODE)(int)GTWConfig::OPCServerTagMode;
  //}

  GTWDEFS_TIME_SOURCE getOPCTimeSource()
  {
    if(!isOPCTimeSpecified())
    {
      return (GTWDEFS_TIME_SOURCE)(int)GTWConfig::OPCTimeSource;
    }
    else
    {
      return m_eOPCTimeSource;
    }
  }

#if USE_OPC_44
  GTWDEFS_TIME_SOURCE getOPCAETimeSource()
  {
    if(!isOPCAETimeSpecified())
    {
      return (GTWDEFS_TIME_SOURCE)(int)GTWConfig::OPCAETimeSource;
    }
    else
    {
      return m_eOPCAETimeSource;
    }
  }
#endif

  bool GetIsDNP() { return m_isDnp; };
  void SetIsDNP(bool b) { m_isDnp = b; };

  static CStdString getBindErrorString(GTW_BIND_STATUS bindStatus);

  virtual bool SetCommandValue(GtwVariant &value)
  {
    return false;
  }

  bool IsInEquation(); // true if this mdo is an argument in an equation
  GTWEquationDataObject* GetUniqueEquation(); // will return non null equation data object if this mdo is an argument in the equation AND this MDO is not the source of anything else
  void GetDependentEquations(std::list<GTWEquationDataObject*>& list); // will return a list of all equations this mdo is in

  GTWDEFS_UPDTRSN m_upateReason;
#if USE_OPC_44
  virtual void reportOPCConditionEvent(GTWDEFS_UPDTRSN updateReason, bool bOnlyActive);
  virtual void reportOPCSimpleEvent(GTWDEFS_UPDTRSN updateReason);
#endif
  void setOpcAEUpdtrsnMask(GTWDEFS_UPDTRSN mask)
  {
    m_iOpcAEUpdtrsnMask = mask;
  }

  GTWDEFS_UPDTRSN getOpcAEUpdtrsnMask()
  {
    return m_iOpcAEUpdtrsnMask;
  }


#ifdef _DEBUG
  void dumpMappings();
#endif

protected:
  //TMWVectorTemplate<GTWMasterDataObject *, 1>  m_mdoBoundList; // the list of MDOs that write to this

  void updateOPCSDOs();

#if USE_OPC_44
  void FireActiveCondition(GtwVariant cvAttrValue, FILETIME timestamp, bool attrChanged, GTWDEFS_UPDTRSN updateReason);
  void FireInactiveCondition(GtwVariant cvAttrValue, FILETIME timestamp, bool attrChanged, GTWDEFS_UPDTRSN updateReason);
private:
  void fireChanges(GTWDEFS_UPDTRSN updateReason, CStdString msg);

  void OPCAEServerDebugMsg(GTWDEFS_UPDTRSN updateReason, CStdString fireMsg);

#endif

private:
  void updateMDOCommon(GTWDEFS_UPDTRSN updateReason);
  //tmw::AutoCriticalSection m_newConditionLock;

  tmw::AutoCriticalSection m_OPCConditionEventCS;
  // map of user tag names to MDO objects
  static std::unordered_map<std::string, GTWMasterDataObject *> gtwusrtg_map;

  //SOPC  OPCConditionEvent* m_pAlarm;           // A&E

  /* The most recent reported time
  */
  bool       m_bSaveThisMdo;
  bool       m_bAlarmAcked;
  bool       m_bOPCsubscribed;
  CStdString             m_sUserTagName;
  CStdString            m_sDescriptionString;
  GTWSequenceOfEventsFile          *m_pEventLogFile;
  GTWDEFS_UPDTRSN    m_iLogFileUpdtrsnMask;
  GTWDEFS_UPDTRSN    m_iOpcAEUpdtrsnMask;
  bool       m_isDnp;
  bool m_bIsODBCMdo;
  bool       m_bIsUpdating; // Flag to prevent circular reference updates
  GTWBaseDataObject *m_pBdo;

  //TMWVectorTemplate<void*, 4> m_DrawObjectBoundList;
  virtual void addMdoToMdoReadBoundList(GTWMasterDataObject *pMdo);
  GTW_BIND_STATUS bindMdoToMdoForReading(GTWMasterDataObject *pSourceMdo, GTW_BIND_MODE bindMode);
  virtual GTW_BIND_STATUS bindMdoToMdoForWriting(GTWMasterDataObject *pWriteFromMdo, GTWCNVTR_TYPE cnvtrType, GTWMdoVariantReadConverter **pConverter);
  void _updateMDO(GTWDEFS_UPDTRSN updateReason, bool bUpdateWriteableMDOs);
};

class GTWMasterDataObjectTimeConverter : public GTWReadConverterTemplate<TMWDTIME>
{
public:
  GTWMasterDataObjectTimeConverter(GTWMasterDataObject *pMdo) : m_pMdo(pMdo) {}
  virtual GTWMasterDataObject* getMDO() { return m_pMdo; }
  
  virtual TMWTYPES_ULONG getFlags(void)
  {
    GTWDEFS_STD_QLTY stdQuality = m_pMdo->getMdoStdQuality();
    TMWTYPES_UCHAR flagValue = (TMWTYPES_UCHAR)(stdQuality & GTWDEFS_STD_QLTY_IEC_FLAGS_MASK);
    return flagValue;
  }
  
  virtual void getValue(GTWMasterDataObject *pMdoArg, 
    TMWDTIME          *pDateTime,
    GTWDEFS_TIME_QLTY *pTimeQuality)
  {
    GTWBaseDataObject *pBdo = m_pMdo->getBdo();
    if (pBdo == NULL)
    {
      return;
    }
		GtwTimeZone tz;
		GtwTimeZone::GetTimeZoneFromRegion(CStdString(GTWConfig::UTCTimeZoneName), GTWConfig::IgnoreDST, &tz);
    pBdo->getMdoReportedTime(pDateTime, pTimeQuality, tz);
  }
  
private:
  GTWMasterDataObject *m_pMdo;
};

class GTWMDO_CNVTR_READ_VARIANT : public GTWMdoVariantReadConverter
{
public:

  GTWMDO_CNVTR_READ_VARIANT(GTWMasterDataObject *pSourceMdo) :
      m_pSourceMdo(pSourceMdo)
  {
  }

  GTWMasterDataObject* getMDO() override { return m_pSourceMdo; }

  virtual TMWTYPES_ULONG getFlags(void)
  {
    GTWDEFS_STD_QLTY q = m_pSourceMdo->getMdoStdQuality();

    return (q);
  }
  virtual void getValue(GtwVariant &value)
  {
    m_pSourceMdo->getMdoValueAsVariant(value);
  }

private:
  GTWMasterDataObject *m_pSourceMdo;
};

class MdoWriteTimerInfo : public WinTimerInfo
{
public:
  MdoWriteTimerInfo(CStdString name)
    : WinTimerInfo(name)
  {
  }
protected:
  virtual void OnTimer (void *pCallBackData);
};

  
