<?xml version="1.0" encoding="utf-8"?>
<GtaTestDurations xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Executable>F:\work\SDG_5.2.3\bind_x64\GTWLibTest.exe</Executable>
  <TestDurations>
    <TestDuration Test="MdoToMdoMappingTest.CircularReferencePreventionTest" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MultipleCircularReferencePreventionTest" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MappingConflictErrorTest" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MapBoolToBool1" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MapBoolToBool2" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MapBoolToFloat" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MapFloatToBool" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MapShortToFloat" Duration="1" />
    <TestDuration Test="MdoToMdoMappingTest.MapFloatToShort" Duration="1" />
    <TestDuration Test="MdoToSdoMappingTest.MapDNPanaCmdToMBhr" Duration="1" />
  </TestDurations>
</GtaTestDurations>