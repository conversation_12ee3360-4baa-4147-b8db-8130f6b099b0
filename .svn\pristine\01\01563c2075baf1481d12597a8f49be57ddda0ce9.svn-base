/*****************************************************************************
*                                                                            *
* PROJECT_NAME             OPC UA C++ Toolkit                                *
*                                                                            *
* VERSION                  5.54.0                                            *
*                                                                            *
* DATE                     04.05.2018                                        *
*                                                                            *
*                                                                            *
* NOTICE:                                                                    *
*   This code and information is provided "as is" without warranty of        *
*   any kind, either expressed or implied, including but not limited         *
*   to the implied warranties of merchantability and/or fitness for a        *
*   particular purpose.                                                      *
*****************************************************************************/
#ifndef _UTILITIES_SYNC_H_
#define _UTILITIES_SYNC_H_

#include <string>

namespace Utilities
{
	/*! This is a synchronization class for threads. Its implementation relies on:
	*		- CRITICAL_SECTION for Microsoft Windows operating systems
	*		- pthread_mutex_t for POSIX-compatible operating systems */
	class Sync
	{
	public:
		/*! Default constructor, creates an unlocked mutex. */
		Sync();

		/*! Destructor. */
		virtual ~Sync();

		/*! Tries to lock the object.
		* @return True if the mutex has been locked, false otherwise. */
		virtual bool lock();

		/*! Unlocks the mutex.
		* @return True if the unlock was successful, false otherwise. */
		virtual bool unlock();

	private:
#ifdef SOOS_POSIX
		pthread_mutex_t m_mutex;				/*! Linux: The underlying mutex structure, used on POSIX systems */
#endif
#ifdef SOOS_WINDOWS
		CRITICAL_SECTION m_criticalSection;		/*! Windows: The underlying CRITICAL_SECTION structure */
#endif
#ifdef _DEBUG
	protected:
		std::string m_name;			/*! In debug mode, the mutex will have a name. This is used for debugging purpose. */
	public:
		inline std::string getName() const { return m_name; }
		inline void setName(const std::string& name) { m_name = name; }
#endif
	};
}

#endif // _UTILITIES_SYNC_H_
