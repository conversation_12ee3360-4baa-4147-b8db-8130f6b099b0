/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWm3t20.h                                                  */
/* DESCRIPTION:  Class definition for 103 MDO for type 20 general command    */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 1 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#pragma once

static GTWDEFS_PARSED_OPTION_TYPE s_GTWM103_DOUBLE_BIN_CMD_MDO_allowedOptions[] = {
  "LONG_PULSE",PARSED_OPTION_BOOL_VALUE,NULL,NULL,  "TR_OPT_HELP_I3_T46_LONG_PULSE", "set control mode to long pulse",
  "SHORT_PULSE",PARSED_OPTION_BOOL_VALUE,NULL,NULL, "TR_OPT_HELP_I3_T46_SHORT_PULSE", "set control mode to short pulse"
};

class GTWM3DoubleCommandMdo : public GTWIecCommandTemplate<TMWTYPES_UCHAR,GTW103BaseDataObject>
{
public:
  DeclareClassInfo();
  static void init(void);
  GTWM3DoubleCommandMdo(
    TMWTYPES_UCHAR infoNum,
    TMWTYPES_UCHAR elementIndex,
    TMWTYPES_UCHAR functionType);
  
  virtual GTWDEFS_TYPE getMdoType(void)
  {
    return GTWDEFS_TYPE_BOOL;
  }
  

  virtual bool checkMdoToMdoConverter(GTWCNVTR_TYPE cnvtrType)
  {
    if (
         cnvtrType == GTWCNVTR_TYPE_BOOL
      )
    {
      return TMWDEFS_TRUE;
    }
    return TMWDEFS_FALSE;
  }

  bool setValueFromVariant(const GtwVariant &variantValue, TMWTYPES_UCHAR &setValue) override;
  static GTWDEFS_STAT createBdo( CStdString &tagName, GTWBaseDataObject **ppBdo, GTWCollectionMember *pOwner);
  TMWTYPES_UCHAR getControlBool(void)
  {
    return((getControlValue()&I3DEF_DCS_MASK)==I3DEF_DCS_ON);
  }


private:
  virtual void *bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo,GTWCNVTR_TYPE  cnvtrType);
  virtual void *bindMdoToSdoForWriting(GTWSlaveDataObject *pWriteFromSdo,GTWCNVTR_TYPE  cnvtrType);
  virtual bool sendCommand(void *pRequestDesc, TMWTYPES_UCHAR commandValue, GTWDEFS_CTRL_MODE ctrlMode);
  virtual void getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg);
  
  virtual void getMdoValueAsVariant(GtwVariant &variant);

  virtual GTWDEFS_UPDTRSN GetLogFileUpdtrsnMask(void) /* Is object to be logged to event file? */
  {
    return(GTWConfig::M103Type46logMask);
  }

  virtual GTWDEFS_UPDTRSN GetOpcAEUpdtrsnMask(void) /* Is object to be logged to event file? */
  {
    return(GTWConfig::M103Type46OpcAElogMask);
  }

  GTWDEFS_BIN_CTRL_MODE  m_eDefaultControlMode; // default command code
  virtual void SetDefaultOptions(void)
  {
    m_eDefaultControlMode = GTWDEFS_BIN_CTRL_MODE_LATCH;
      return(GTWIecCommandTemplate<TMWTYPES_UCHAR,GTW103BaseDataObject>::SetDefaultOptions());
  }

  GTWDEFS_STAT ParseOptionsField(const char *connectionToken, const char **ppOptionString) override
  {

    if (ParseOptionsString(ppOptionString, s_GTWM103_DOUBLE_BIN_CMD_MDO_allowedOptions[0].name))
    {
      m_eDefaultControlMode = GTWDEFS_BIN_CTRL_MODE_LONG_PULSE;
    }
    else if (ParseOptionsString(ppOptionString, s_GTWM103_DOUBLE_BIN_CMD_MDO_allowedOptions[1].name))
    {
      m_eDefaultControlMode = GTWDEFS_BIN_CTRL_MODE_SHORT_PULSE;
    }
    else
    {
      return(GTWIecCommandTemplate<TMWTYPES_UCHAR,GTW103BaseDataObject>::ParseOptionsField(connectionToken, ppOptionString));
    }

    return(GTWDEFS_STAT_SUCCESS);
  }

  void GetAllowedOptions( GTWDEFS_PARSED_OPTION_ARRAY &optionsArray  ) override
  {
    int i;
    for (i=0;i<TMWDEFS_ARRAY_LENGTH(s_GTWM103_DOUBLE_BIN_CMD_MDO_allowedOptions);i++)
    {
      optionsArray.push_back(s_GTWM103_DOUBLE_BIN_CMD_MDO_allowedOptions[i]);
    }
    GTWIecCommandTemplate<TMWTYPES_UCHAR,GTW103BaseDataObject>::GetAllowedOptions(optionsArray);
    return; 
  }

}; /* end class GTWM3DoubleCommandMdo */

/*****************************************************************************/

class GTWM3T46_CNVTR_READ_BOOL : public GTWReadConverterTemplate<TMWTYPES_UCHAR>
{
  private:

    GTWM3DoubleCommandMdo *pT20mdo;

  public:

    GTWM3T46_CNVTR_READ_BOOL(GTWM3DoubleCommandMdo *pT20mdo) : pT20mdo(pT20mdo) {}
    virtual GTWMasterDataObject* getMDO() { return pT20mdo; }
    
    virtual TMWTYPES_UINT getFlags(void)
    {
      GTWDEFS_STD_QLTY stdQuality = pT20mdo->getMdoStdQuality();
      TMWTYPES_UCHAR flagValue = (TMWTYPES_UCHAR)(stdQuality & GTWDEFS_STD_QLTY_IEC_FLAGS_MASK);
      return flagValue;
    }

    virtual void getValue(GTWMasterDataObject *pMdo, 
      TMWTYPES_UCHAR     *pValue,
      GTWDEFS_STD_QLTY *pStdQuality)
    {
      *pValue      = pT20mdo->getControlBool();
      *pStdQuality = pT20mdo->getControlQuality();
    }
};

/*****************************************************************************/

class GTWM3T46_CNVTR_READ_DOUBLE : public GTWReadConverterTemplate<TMWTYPES_DOUBLE>
{
  private:

    GTWM3DoubleCommandMdo *pT20mdo;

  public:

    GTWM3T46_CNVTR_READ_DOUBLE(GTWM3DoubleCommandMdo *pT20mdo) : pT20mdo(pT20mdo) {}
    virtual GTWMasterDataObject* getMDO() { return pT20mdo; }

    virtual TMWTYPES_UINT getFlags(void)
    {
      GTWDEFS_STD_QLTY stdQuality = pT20mdo->getMdoStdQuality();
      TMWTYPES_UCHAR flagValue = (TMWTYPES_UCHAR)(stdQuality & GTWDEFS_STD_QLTY_IEC_FLAGS_MASK);
      return flagValue;
    }

    virtual void getValue(GTWMasterDataObject *pMdo, 
      TMWTYPES_DOUBLE     *pValue,
      GTWDEFS_STD_QLTY *pStdQuality)
    {
      *pValue      = pT20mdo->getControlValue();
      *pStdQuality = pT20mdo->getControlQuality();
    }
};

/*****************************************************************************/

class GTWM3T46_CNVTR_WRITE_BIN : public GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject>
{
  private:
    GTWM3DoubleCommandMdo *pT20mdo;

  public:
    GTWM3T46_CNVTR_WRITE_BIN(GTWM3DoubleCommandMdo *pT20mdo) : pT20mdo(pT20mdo) {}

    virtual GTWMasterDataObject* getMDO() override
    {
      return pT20mdo;
    }

    virtual GTWDEFS_CTRL_STAT writeValue(GTWSlaveDataObject *pCallbackSdo,TMWTYPES_UCHAR bValue, GTWDEFS_EXTRA_CTRL_INFO_PTR pCtrlDesc,GTWDEFS_CTRL_MODE ctrlMode)
    {
      TMWTYPES_UCHAR sendValue;
      if (bValue != 0)
      {
        sendValue = 2;
      }
      else
      {
        sendValue = 1;
      }

      void *pExtraCBdata = NULL;
      if (pCtrlDesc != NULL)
        pExtraCBdata = pCtrlDesc->pExtraCallBackData;

      return(pT20mdo->executeControl(pCallbackSdo,pExtraCBdata,sendValue,ctrlMode,TMWDEFS_FALSE));  // use2pass
    }
};

