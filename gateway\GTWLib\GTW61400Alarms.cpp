/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWsctr.cpp                                                 */
/* DESCRIPTION:  Definition of sector collections of BDOs                    */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 42 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#include "stdafx.h"
#include "GTW61400Alarms.h"
#include "GTW61850DataAttributeMDO.h"
#include "GTW61850ClientNode.h"
#include "GTW61850Client.h"
#include "GTW61400Alarms.h"
#include "GTW61400AlarmsEditor.h"

#include "GTWBaseEditor.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

ImplementClassBaseInfo (GTW61400Alarms,GTWCollectionListParent, pClassInfo1,NULL);
ImplementClassBaseInfo (GTW61400AlarmsCollection,GTWCollectionList,pClassInfo2,NULL);

void GTW61400Alarms::getName(CStdString &sName)
{
  sName = m_AlarmsNodeName;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTW61400Alarms::GTW61400Alarms(
  TMWTYPES_USHORT   clientIndex,
  TMWTYPES_USHORT   alarmsIndex,
  TMWTYPES_BOOL     bDelaySCLInit)
  :
  GTWCollectionListParent(bDelaySCLInit != 0),
  m_iClientIndex(clientIndex),
  m_iAlarmsIndex(alarmsIndex)
{
  
}

GTW61400Alarms::~GTW61400Alarms()
{
  tmw::CriticalSectionLock lock(m_AlarmsMapLock);
  m_AlarmsMap.clear();

  GetBaseEditor(EditorCommandDTO(MENU_CMD_NONE))->DeleteINIparms();
}

GTWBaseEditor *GTW61400Alarms::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
    m_pEditor = new GTW61400AlarmsEditor(dto, this, (GTW61850Client *)GetParentMember(), false);
  else
    m_pEditor->SetDTO(dto); 
  
  return m_pEditor;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_STAT GTW61400Alarms::CompareTagName(CStdString &tagName)
{
  CStdString fieldString;

  getName(fieldString);

  return(GTWBaseDataObject::CompareTagField(tagName,fieldString));
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

CStdString GTW61400Alarms::GetMemberName(void)
{
  CStdString fieldString;
  getName(fieldString);
  m_AlarmsNodeName = fieldString;
  return(m_AlarmsNodeName);
}

void GTW61400Alarms::GetDescriptionColText(CStdString &itemStr)
{
  size_t nCount = m_AlarmsMap.size();

  if (nCount == 0)
  {
    itemStr = "No Alarms configured";
  }
  else
  {
    itemStr.Format("Alarm count = %d", nCount);
  }
}


/*****************************************************************************/
TMWTYPES_BOOL GTW61400Alarms::UponRemove(GTWCollectionBase *pParent)
{
  return GTWCollectionMember::UponRemove(pParent);
}

void GTW61400Alarms::UponInsert(GTWCollectionBase *pParent)
{
  GTWCollectionListParent::UponInsert(pParent);
  m_pMemberCollection = new GTW61400AlarmsCollection(this,m_iClientIndex);

  int clientIndex = ((GTW61850Client*)GetParentMember())->Get61850ClientIndex();
  TMWTYPES_BOOL bEnableAlarms = GTWConfig::I61850Enable61400AlarmProcessing(clientIndex);
  if (bEnableAlarms == TMWDEFS_FALSE)
  {
    LOG6(((GTW61850Client*)GetParentMember())->GetClientConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "61400 Alarm node '%s' was added but the INI parameter I61850Enable61400AlarmProcessing[%d] is set to FALSE. No alarm processing will occur for this client until I61850Enable61400AlarmProcessing[%d] is set to TRUE in the INI file.",      (const char*)GetFullName(), clientIndex, clientIndex);
  }
}

void GTW61400Alarms::OnAddAlarmMDO(GTW61400AlarmMDO *pMdo)
{
  tmw::CriticalSectionLock lock(m_AlarmsMapLock);

  m_AlarmsMap[pMdo->m_iStatusCode] = pMdo;
}

void GTW61400Alarms::OnRemoveAlarmMDO(GTW61400AlarmMDO *pMdo)
{
  tmw::CriticalSectionLock lock(m_AlarmsMapLock);

  m_AlarmsMap.erase(pMdo->m_iStatusCode);
}

void GTW61400Alarms::OnModifyAlarmMDO(GTW61400AlarmMDO *pMdo, TMWTYPES_USHORT originalStatus)
{
  tmw::CriticalSectionLock lock(m_AlarmsMapLock);

  m_AlarmsMap.erase(originalStatus);
  m_AlarmsMap[pMdo->m_iStatusCode] = pMdo;
}


/// Need to modify when edited

/*****************************************************************************/

GTW61400AlarmsCollection::GTW61400AlarmsCollection(GTWCollectionMember  *pOwner, TMWTYPES_USHORT clientIndex) :
 GTWCollectionList(pOwner),
 m_pOwner(pOwner),
 m_nClientIndex(clientIndex)
{
}



/*****************************************************************************/

GTWDEFS_STAT GTW61400Alarms::CreateAlarmsNode(
  TMWTYPES_USHORT   clientIndex,
  TMWTYPES_USHORT   alarmsIndex,
  const CStdString& name,
  const CStdString& status_array_name,
  const CStdString& event_array_name,
  GTW61400Alarms    **ppAlarms,
  TMWTYPES_BOOL     bDelaySCLInit)
{
  GTW61400Alarms        *pAlarms;
  pAlarms = *ppAlarms = new GTW61400Alarms(clientIndex,alarmsIndex,bDelaySCLInit);

  if (*ppAlarms == TMWDEFS_NULL)
  {
    return(GTWDEFS_STAT_OUT_OF_MEMORY);
  }
  else
  {
    pAlarms->SetName(name);
    pAlarms->SetStatusArrayName(status_array_name);
    pAlarms->SetEventArrayName(event_array_name);
    return(GTWDEFS_STAT_SUCCESS);
  }
}

void GTW61400Alarms::SetName(const CStdString& name )
{
  m_AlarmsNodeName = name;
}

void GTW61400Alarms::SetStatusArrayName(const CStdString& status_alarms_array_name )
{
  m_StatusAlarmsArrayName = status_alarms_array_name;
}

void GTW61400Alarms::SetEventArrayName(const CStdString& event_alarms_array_name )
{
  m_EventAlarmsArrayName = event_alarms_array_name;
}

void GTW61400Alarms::UpdateEventAlarms( GTW61850DataAttributeMDO* pMdo )
{
  CStdString name = pMdo->GetFullName();
  if (-1 != name.Find("ActEvl.actSt.[0].stVal"))
  {

    //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "GTW61400Alarms::UpdateEventAlarms : %s\n", (const char*)pMdo->GetFullName());

    ComeGoEnum::Me comeGoVal = ComeGoEnum::OpMode;
    EventIndexEnum::Me idxVal = EventIndexEnum::First;
    TMWTYPES_USHORT stVal = (TMWTYPES_USHORT)pMdo->getValue();
    CStdString sComeGo = name;
    sComeGo.Replace(".stVal",".comego");

    CStdString sIdx = name;
    sIdx.Replace(".stVal",".idx");

    GTW61850DataAttributeMDO *pComeGoMdo = NULL;
    GTW61850DataAttributeMDO *pIdxMdo = NULL;
    GTW61850Client *pClient = pMdo->GetClientNode();
    if (pClient == NULL)
    {
      return;
    }
    
    GTWCollectionMember *pComeGoMember;
    if(pClient->FindMemberUsingName(sComeGo, &pComeGoMember) == GTWDEFS_STAT_SUCCESS)
      pComeGoMdo = (GTW61850DataAttributeMDO *) dynamic_cast<GTWBaseDataObject *>(pComeGoMember)->GetMdo();
    
    GTWCollectionMember *pIdxMember;
    if (pClient->FindMemberUsingName(sIdx, &pIdxMember) == GTWDEFS_STAT_SUCCESS)
      pIdxMdo = (GTW61850DataAttributeMDO *) dynamic_cast<GTWBaseDataObject *>(pIdxMember)->GetMdo();
    
    //GTWDEFS_STAT statusComeGo = GetGTWApp()->findMdo(sComeGo,(GTWMasterDataObject **)&pComeGoMdo);
    //GTWDEFS_STAT statusIdx = GetGTWApp()->findMdo(sIdx,(GTWMasterDataObject **)&pIdxMdo);
    if (pComeGoMdo != NULL && pIdxMdo != NULL)
    {
      comeGoVal = (ComeGoEnum::Me)(int)pComeGoMdo->getValue();
      idxVal = (EventIndexEnum::Me)(int)pIdxMdo->getValue();
      DoSetEventAlarm(pMdo, stVal, comeGoVal, idxVal);
    }
  }
  return;
}

void GTW61400Alarms::UpdateStatusAlarms()
{
  //SYSTEMTIME time;
  //GetSystemTime(&time);
  //LONG start_time = (time.wSecond * 1000) + time.wMilliseconds;

  // First retrieve the MDO array corresponding to this Alarms node
  GTW61850Client *pClient = (GTW61850Client*)this->GetParentMember();
  tmw61850::Node *pArrayParent = tmw61850::i61850RootNode::FindNode<tmw61850::Node>((const char*)this->m_StatusAlarmsArrayName, pClient->GetClientConnection()->Model());
  
  if (!pClient || !pArrayParent || !pArrayParent->Children() || pArrayParent->Children()->getSize() == 0 || !pArrayParent->Children()->getAt(0)->Children() ||
        pArrayParent->Children()->getAt(0)->Children()->getSize() == 0)
  {
    return;
  }

  // Get the first MDO
  GTW61850DataAttributeMDO *pFirstMDO = pClient->FindMdo((tmw61850::DataAttribute*)pArrayParent->Children()->getAt(0)->Children()->getAt(0));

  if (!pFirstMDO)
  {
    return;
  }

  // This collection is the entire MDO array
  GTWCollectionBase *pDaMdoParentCollection = pFirstMDO->GetParentMember()->GetParentMember()->GetMemberCollection();

  // Now iterate through the array to get the active alarms
  N_SHARED_LOCK_GTW_COLLECTION(daMdoParentLock, pDaMdoParentCollection);

  std::map<TMWTYPES_USHORT, GTW61850DataAttributeMDO*> currentActiveAlarms; // The current active alarms

  auto pDaMdoParentMap = pDaMdoParentCollection->GetMap();  // this is all of the WALM1.AlmSt.actSt.[X].stVal MDOs (usually 50)
  for (auto pos = pDaMdoParentMap->begin(); pos != pDaMdoParentMap->end(); ++pos)
  {
    GTWCollectionMember *pMember = pos->second;
    GTW61850ClientNode *pCltNode = dynamic_cast<GTW61850ClientNode*>(pMember);
    assert(pCltNode);

    GTWCollectionBase *pCltCollection = pCltNode->GetMemberCollection(); // should only have ONE member - the array element
    N_SHARED_LOCK_GTW_COLLECTION(cltCollectionLock, pCltCollection);
    CStdString mdoKey;
    assert(pCltCollection->GetMap()->size() == 1);

    auto mdoPos = pCltCollection->GetMap()->begin();
    GTW61850DataAttributeMDO *pModelArrayElementMdo = dynamic_cast<GTW61850DataAttributeMDO*>(mdoPos->second);
    assert(pModelArrayElementMdo);

    if (pModelArrayElementMdo && !pModelArrayElementMdo->IsTime1970()) // then the alarm MDO array element is active
    {
      currentActiveAlarms[(WORD)pModelArrayElementMdo->getValue()] = pModelArrayElementMdo; // NOTE : indexed in the hastable by value so if they all have zero as the value, there is only one element in the hashtable
      //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "adding active alarm %d for %s\n", (WORD)pModelArrayElementMdo->getValue(), (const char*)pModelArrayElementMdo->GetFullName());
    }
  }

  //
  // Now iterate through the alarm mdos to update according to the active alarms (currentActiveAlarms)
  //

  GTWCollectionMember *pAlarmMdoMember;
  CStdString key;
  GTWCollectionBase *pCollection = GetMemberCollection();

  SHARED_LOCK_GTW_COLLECTION(pCollection);

  auto pos = pCollection->GetMap()->begin();//pCollection->GetMap()->GetStartPosition();

  for (; pos != pCollection->GetMap()->end(); ++pos)
  {
    pAlarmMdoMember = pos->second;
    //pCollection->GetMap()->GetNextAssoc(pos, key, pAlarmMdoMember);
    GTW61400AlarmMDO *pAlarmMDO = dynamic_cast<GTW61400AlarmMDO*>(pAlarmMdoMember);
    assert(pAlarmMDO);

    if (pAlarmMDO)
    {
      TMWTYPES_USHORT iStatAlarm = (TMWTYPES_USHORT)pAlarmMDO->m_iStatusCode;
      //std::map<WORD, GTW61850DataAttributeMDO*>::iterator eIter = currentActiveAlarms.find(iStatAlarm);
      auto eIter = currentActiveAlarms.find(iStatAlarm);
      if (eIter != currentActiveAlarms.end()) // then pAlarmMDO has the corresponding value of the array element MDO
      {
        GTW61850DataAttributeMDO *pMdo = eIter->second;
        SetAlarm(pAlarmMDO, pMdo);
      }
      else if (pAlarmMDO->GetBoolValue()) // only turn off if already on
      {
        ResetAlarm(pAlarmMDO);
      }
    }
  }

  //GetSystemTime(&time);
  //LONG end_time = (time.wSecond * 1000) + time.wMilliseconds;
  //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "Took %d ms for %d items with %d active alarms\n", (end_time - start_time), pCollection->GetMap()->GetSize(), currentActiveAlarms.size());
}

CStdString GTW61400Alarms::GetStatusAlarmsArrayName()
{
  return m_StatusAlarmsArrayName;
}

CStdString GTW61400Alarms::GetEventAlarmsArrayName()
{
  return m_EventAlarmsArrayName;
}

void GTW61400Alarms::DoSetEventAlarm( GTW61850DataAttributeMDO* pMdo, TMWTYPES_USHORT daSt, ComeGoEnum::Me comego, EventIndexEnum::Me idx )
{
  GTW61400AlarmMDO *pAlarmMDO = FindAlarmMDO(daSt);
  if (pAlarmMDO && pAlarmMDO->m_iStatusCode == daSt)
  {
    if (idx == EventIndexEnum::First)
    {
      if (comego == ComeGoEnum::come)
      {
        pAlarmMDO->SetValue(TMWDEFS_TRUE);
      }
      if (comego == ComeGoEnum::go)
      {
        pAlarmMDO->SetValue(TMWDEFS_FALSE);
      }

      GTWBaseDataObject *pAlarmBdo = pAlarmMDO->getBdo();
      GTWBaseDataObject *pBdo = pMdo->getBdo();
      if (pBdo != NULL && pAlarmBdo != NULL)
      {
        pAlarmBdo->SetReportedTime(pBdo->GetReportedTimeAddr());
      }

      pAlarmMDO->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
    }
  }
}

void GTW61400Alarms::ResetAlarm(GTW61400AlarmMDO *pAlarmMDO)
{
  if (pAlarmMDO == NULL)
  {
    return;
  }

  GTWBaseDataObject *pAlarmBdo = pAlarmMDO->getBdo();
  if (pAlarmBdo == NULL)
  {
    return;
  }
  pAlarmMDO->SetValue(TMWDEFS_FALSE);
  pAlarmMDO->set61850Quality(I61850_QUALITY_VALIDITY_GOOD);
  pAlarmBdo->storeReportedTime(NULL);
  pAlarmBdo->storeUpdatedTime(NULL);
  if (pAlarmMDO->IsChanged())
  {
    pAlarmMDO->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
    pAlarmMDO->m_LastValue = pAlarmMDO->m_Value;
  }
}

void GTW61400Alarms::SetAlarm(GTW61400AlarmMDO *pAlarmMDO, GTW61850DataAttributeMDO *pDaMdo)
{
  assert((TMWTYPES_USHORT)pDaMdo->getValue() == pAlarmMDO->m_iStatusCode); // should be the same always

  GTWBaseDataObject *pAlarmBdo = pAlarmMDO->getBdo();
  if (pAlarmBdo == NULL)
  {
    return;
  }
  GTWBaseDataObject *pDaBdo = pDaMdo->getBdo();
  if (pDaBdo == NULL)
  {
    return;
  }

  //WORD almSt = pAlarmMDO->m_iStatusCode;
  //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "set alarm ON: %s with status code=%d\n", (const char*)pAlarmMDO->GetFullName(), pAlarmMDO->m_iStatusCode);


  pAlarmMDO->SetValue(TMWDEFS_TRUE);
  pAlarmBdo->SetReportedTime(pDaBdo->GetReportedTimeAddr());
  pAlarmBdo->SetUpdatedTime(pDaBdo->GetReportedTimeAddr());
  if (pDaMdo->getMdoStdQuality() != GTWDEFS_STD_QLTY_GOOD)
  {
    pAlarmMDO->set61850Quality(I61850_QUALITY_VALIDITY_QUESTIONABLE);
  }
  else
  {
    pAlarmMDO->set61850Quality(I61850_QUALITY_VALIDITY_GOOD);
  }
  if (pAlarmMDO->IsChanged())
  {
    pAlarmMDO->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
    pAlarmMDO->m_LastValue = pAlarmMDO->m_Value;
  }
}

GTW61400AlarmMDO *GTW61400Alarms::FindAlarmMDO(TMWTYPES_USHORT daSt )
{
  tmw::CriticalSectionLock lock(m_AlarmsMapLock);

  auto pair = m_AlarmsMap.find(daSt);
  if (pair == m_AlarmsMap.end())
  {
    GTWCollectionBase *pCollection = GetMemberCollection();

    SHARED_LOCK_GTW_COLLECTION(pCollection);

    auto alarmPos = pCollection->GetMap()->begin();
    while (alarmPos != pCollection->GetMap()->end())
    {
      GTWCollectionMember *pMember = alarmPos->second;
      GTW61400AlarmMDO *pAlarmMDO = (GTW61400AlarmMDO*)pMember;
      if (pAlarmMDO->IsA("GTW61400AlarmMDO"))
      {
        if (daSt == pAlarmMDO->m_iStatusCode)
        {
          m_AlarmsMap.insert(std::pair<TMWTYPES_USHORT, GTW61400AlarmMDO *>(daSt, pAlarmMDO));
          return pAlarmMDO;
        }
      }
      ++alarmPos;
    }
    return NULL;
  }
  else
  {
    return pair->second;
  }
  return nullptr;
}

void GTW61400Alarms::ClearAlarmsMap()
{
  tmw::CriticalSectionLock lock(m_AlarmsMapLock);
  m_AlarmsMap.clear();
}



/*
void GTW61400Alarms::UpdateStatusAlarm(GTW61850DataAttributeMDO* pMdo)
{
if (pMdo->IsTime1970() == false)
{
WORD daSt = (WORD)pMdo->getValue();
SetAlarm(pMdo);
pMdo->SetLastValueSaved(daSt);
}
}

*/

/*
void GTW61400Alarms::UpdateStatusAlarms( GTW61850DataAttributeMDO* pMdo)
{
ResetAlarms(pMdo);

//if (
//  pMdo->IsTime1970() == true
//  )
//{
//  WORD daSt = (WORD)pMdo->getLastAlarmStatusSaved();
//  //ResetAlarm(pMdo, daSt);
//  ResetAlarms(pMdo);
//}

if (
pMdo->IsTime1970() == false
)
{
WORD daSt = (WORD)pMdo->getValue();
SetAlarm(pMdo);
pMdo->SetLastValueSaved(daSt);
}
}
*/
/*
void GTW61400Alarms::ResetAlarm(GTW61850DataAttributeMDO* pMdo, WORD daSt)
{

GTW61400AlarmMDO *pAlarmMDO = FindAlarmMDO(daSt);
if (pAlarmMDO == NULL)
{
return;
}

GTWBaseDataObject *pAlarmBdo = pAlarmMDO->getBdo();
if (pAlarmBdo == NULL)
{
return;
}

//LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, pAlarmMDO->GetFullName() + " = false\n");
if (daSt == pAlarmMDO->m_iStatusCode)
{

pAlarmMDO->SetValue(false);
if (pMdo->getMdoStdQuality() != GTWDEFS_STD_QLTY_GOOD)
{
pAlarmMDO->setQuality(I61850_QUALITY_VALIDITY_INVALID);
}
else
{
pAlarmMDO->setQuality(I61850_QUALITY_VALIDITY_GOOD);
}
if (pAlarmBdo->ReportedTimeQuality() == GTWDEFS_TIME_QLTY_REMOTE)
{
pAlarmBdo->storeReportedTime(NULL);
}
if (pAlarmMDO->IsChanged())
{
pAlarmMDO->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
pAlarmMDO->m_LastValue = pAlarmMDO->m_Value;
}
}
else
{
assert(false);
}
}
*/

/*
void GTW61400Alarms::ResetAlarms2( GTW61850DataAttributeMDO* pMdo )
{
  GTWCollectionMember *pMember1;
  CStdString key1;
  GTWCollectionBase *pCollection = GetMemberCollection();

  tmw::CriticalSectionLock lock(*pCollection);
  //pCollection->Lock();

  POSITION pos1 = pCollection->GetMap()->GetStartPosition();

  // for each alarm mdo see if it's in the status collection
  while (pos1)
  {
    pCollection->GetMap()->GetNextAssoc(pos1,key1,pMember1);
    GTW61400AlarmMDO *pAlarmMDO = dynamic_cast<GTW61400AlarmMDO*>(pMember1);
    assert(pMember1);

    if (pAlarmMDO->IsA("GTW61400AlarmMDO"))
    {
      DWORD almSt = pAlarmMDO->m_iStatusCode;
      GTWCollectionMember *pMember;
      CStdString key;
      GTWCollectionBase *pDaMdoParentCollection = pMdo->GetParentMember()->GetMemberCollection();

      tmw::CriticalSectionLock daMdoParentLock(*pDaMdoParentCollection);
      //pDaMdoParentCollection->Lock();

      POSITION pos = pDaMdoParentCollection->GetMap()->GetStartPosition();

      bool bClearIt = true;
      while (pos)
      {
        pDaMdoParentCollection->GetMap()->GetNextAssoc(pos,key,pMember);
        GTW61850DataAttributeMDO *pDaMdo = (GTW61850DataAttributeMDO*)pMember;
        if (pDaMdo->IsA("GTW61850DataAttributeMDO"))
        {
          CStdString daName = pDaMdo->GetFullName();
          CStdString almName = pAlarmMDO->GetFullName();
          //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "da=%s, alm = %s\n", daName, almName);

          if (std::find(m_CurrentAlarms.begin(), m_CurrentAlarms.end(), almSt) == m_CurrentAlarms.end())
          //if (currentAlarms.Lookup(almSt) == -1)
          {

            ResetAlarm( pDaMdo, almSt);
          }
          
          //DWORD daSt = pDaMdo->getValue();
          //if (almSt == daSt)
          //{
          //  if (pDaMdo->IsTime1970())
          //  {
          //    ResetAlarm( pDaMdo, daSt);
          //  }
          //}
        }
      }
      //if (bClearIt)
      //{
      //  pAlarmMDO->SetValue(false);
      //  pAlarmMDO->m_tReportedTime = pMdo->m_tReportedTime;
      //}
      //pDaMdoParentCollection->UnLock();
    }
  }
  //pCollection->UnLock();
}
*/

/*
// call at construction and edit times
void GTW61400Alarms::ResetActiveAlarms(GTWCollectionBase *pMdoCollection)
{
  m_ModelAlarmMap.clear();

  GTWCollectionMember *pMember;
  CStdString key;
  tmw::CriticalSectionLock lock(*pMdoCollection);

  POSITION pos = pMdoCollection->GetMap()->GetStartPosition();

  while (pos)
  {
    pMdoCollection->GetMap()->GetNextAssoc(pos, key, pMember);
    GTW61850ClientNode *pCltNode = dynamic_cast<GTW61850ClientNode*>(pMember);
    if (pCltNode)
    {
      CStdString arrayKey;
      GTWCollectionBase *pCltCollection = pCltNode->GetMemberCollection(); // should only have ONE member - the array element

      tmw::CriticalSectionLock cltCollectionLock(*pCltCollection);
      POSITION arrayPos = pCltCollection->GetMap()->GetStartPosition();
      pCltCollection->GetMap()->GetNextAssoc(arrayPos, arrayKey, pMember);
      GTW61850DataAttributeMDO *pModelArrayElementMdo = dynamic_cast<GTW61850DataAttributeMDO*>(pMember);
      assert(pModelArrayElementMdo);

      //if (!pModelArrayElementMdo->IsTime1970()) // Add all alarm statuses for MDOs that are not at time 1970
      WORD daSt = (WORD)pModelArrayElementMdo->getValue();
      if (m_ModelAlarmMap.find(daSt) == m_ModelAlarmMap.end())
      {
        m_ModelAlarmMap[daSt] = pModelArrayElementMdo;
      }
    }
  }

  int size = m_ModelAlarmMap.size();

  int llllll = 3;
}
*/

/*
void GTW61400Alarms::ResetCurrentAlarms(GTWCollectionBase *pMdoCollection)
{
  //GTW61850Client *pClient = (GTW61850Client*)this->GetParentMember();

  //tmw61850::Node *pArray = tmw61850::i61850RootNode::FindNode<tmw61850::Node>((const char*)this->m_StatusAlarmsArrayName, pClient->GetClientConnection()->Model());




  m_CurrentAlarms.clear();

  GTWCollectionMember *pMember;
  CStdString key;
  tmw::CriticalSectionLock lock(*pMdoCollection);

  POSITION pos = pMdoCollection->GetMap()->GetStartPosition();

  while (pos)
  {
    pMdoCollection->GetMap()->GetNextAssoc(pos, key, pMember);
    GTW61850ClientNode *pCltNode = dynamic_cast<GTW61850ClientNode*>(pMember);
    if (pCltNode) 
    {
      CStdString arrayKey;
      GTWCollectionBase *pCltCollection = pCltNode->GetMemberCollection(); // should only have ONE member - the array element

      tmw::CriticalSectionLock cltCollectionLock(*pCltCollection);
      POSITION arrayPos = pCltCollection->GetMap()->GetStartPosition();
      pCltCollection->GetMap()->GetNextAssoc(arrayPos, arrayKey, pMember);
      GTW61850DataAttributeMDO *pModelArrayElementMdo = dynamic_cast<GTW61850DataAttributeMDO*>(pMember);
      assert(pModelArrayElementMdo);

      if (!pModelArrayElementMdo->IsTime1970()) // Add all alarm statuses for MDOs that are not at time 1970
      {
        WORD daSt = (WORD)pModelArrayElementMdo->getValue();
        if (std::find(m_CurrentAlarms.begin(), m_CurrentAlarms.end(), daSt) == m_CurrentAlarms.end())
        {
          m_CurrentAlarms.push_back(daSt);
        }
      }
    }
  }
}
*/

/*
void GTW61400Alarms::ResetAlarms( GTW61850DataAttributeMDO* pMdo )
{
  m_CurrentAlarms.clear();

  GTWCollectionMember *pMember1;
  CStdString key1;
  GTWCollectionBase *pCollection;

  pCollection = GetMemberCollection();

  int count = pCollection->GetMap()->GetCount();

  tmw::CriticalSectionLock collectionLock(*pCollection);

  POSITION pos1 = pCollection->GetMap()->GetStartPosition();

  while (pos1)
  {
    pCollection->GetMap()->GetNextAssoc(pos1,key1,pMember1);
    GTW61400AlarmMDO *pAlarmMDO = dynamic_cast<GTW61400AlarmMDO*>(pMember1);
    assert(pAlarmMDO);
    if (pAlarmMDO)
    {
     // DWORD almSt = pAlarmMDO->m_iStatusCode;
      GTWCollectionMember *pMember;
      CStdString key;


      CStdString mdoname = pMdo->GetFullName();
      CStdString pname = pMdo->GetParentMember()->GetFullName();
      CStdString ppname = pMdo->GetParentMember()->GetParentMember()->GetFullName();

      // Get the collection in the tree that represents the array of DataAttributes in the model
      GTWCollectionBase *pDaMdoParentCollection = pMdo->GetParentMember()->GetParentMember()->GetMemberCollection();
      
      int cntttt = pDaMdoParentCollection->GetMap()->GetCount();


      tmw::CriticalSectionLock daMdoParentLock(*pDaMdoParentCollection);
      
      POSITION pos = pDaMdoParentCollection->GetMap()->GetStartPosition();

      while (pos)
      {
        pDaMdoParentCollection->GetMap()->GetNextAssoc(pos,key,pMember);
        GTW61850ClientNode *pCltNode = (GTW61850ClientNode*)pMember;
        if (pCltNode->IsA("GTW61850ClientNode"))
        {

          CStdString scltnodename = pCltNode->GetFullName();


          CStdString key3;
          GTWCollectionBase *pCltCollection = pCltNode->GetMemberCollection();
      
          tmw::CriticalSectionLock cltCollectionLock(*pCltCollection);

          int cnt = pCltCollection->GetMap()->GetCount();
          
          POSITION pos3 = pCltCollection->GetMap()->GetStartPosition();
          while (pos3)
          {
            pCltCollection->GetMap()->GetNextAssoc(pos3,key3,pMember);
            GTW61850DataAttributeMDO *pModelArrayElementMdo = dynamic_cast<GTW61850DataAttributeMDO*>(pMember);
            assert(pModelArrayElementMdo);

            //if (pArrayElementMdo->IsA("GTW61850DataAttributeMDO"))
            {

              CStdString daName = pModelArrayElementMdo->GetFullName();
              CStdString almName = pAlarmMDO->GetFullName();
              //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "da=%s, alm = %s\n", daName.c_str(), almName.c_str());
              WORD daSt = (WORD)pModelArrayElementMdo->getValue();
              if (pModelArrayElementMdo->IsTime1970() == false) // Add all alarm statuses for MDOs that are not at time 1970
              {
                if (std::find(m_CurrentAlarms.begin(), m_CurrentAlarms.end(), daSt) == m_CurrentAlarms.end())
                {
                  m_CurrentAlarms.push_back(daSt);
                }

              }

              //if (almSt == daSt)
              //{
              //  if (pDaMdo->IsTime1970())
              //  {
              //    ResetAlarm( pDaMdo, daSt);
              //  }
              //}
            }
          }
        }
      }
    }
  }

  ResetAlarms2(pMdo);
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "done calling GTW61400Alarms::ResetAlarms for %s, size=%d\n", pMdo->GetFullName(), m_CurrentAlarms.size());
}
*/


/*
void GTW61400Alarms::SetAlarm( GTW61850DataAttributeMDO *pDaMdo)
{
WORD daSt = (WORD)pDaMdo->getValue();
GTW61400AlarmMDO *pAlarmMDO = FindAlarmMDO(daSt);
if (pAlarmMDO == NULL)
{
return;
}

GTWBaseDataObject *pAlarmBdo = pAlarmMDO->getBdo();
if (pAlarmBdo == NULL)
{
return;
}
GTWBaseDataObject *pDaBdo = pDaMdo->getBdo();
if (pDaBdo == NULL)
{
return;
}

//WORD almSt = pAlarmMDO->m_iStatusCode;
//LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "set alarm ON: %s with status code=%d\n", (const char*)pAlarmMDO->GetFullName(), pAlarmMDO->m_iStatusCode);


pAlarmMDO->SetValue(true);
pAlarmBdo->ReportedTime(pDaBdo->ReportedTime());
pAlarmBdo->UpdatedTime(pDaBdo->ReportedTime());
if (pDaMdo->getMdoStdQuality() != GTWDEFS_STD_QLTY_GOOD)
{
pAlarmMDO->setQuality(I61850_QUALITY_VALIDITY_INVALID);
}
else
{
pAlarmMDO->setQuality(I61850_QUALITY_VALIDITY_GOOD);
}
if (pAlarmMDO->IsChanged())
{
pAlarmMDO->updateMDO(GTWDEFS_UPDTRSN_CHNG_INDICATED);
pAlarmMDO->m_LastValue = pAlarmMDO->m_Value;
}
}
*/
