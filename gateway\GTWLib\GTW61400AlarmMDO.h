/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2008 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTW61400AlarmMDO.h                                  */
/* DESCRIPTION:  Definitions of an IEC 61400 Alarm MDO                             */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 22 ***_NOBODY_*** "                                                     */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#pragma once

#include "GTWLibDll.h"

#include "GTWBaseDataObject.h"
#include "GTWMasterDataObjectTemplate.h"
#include "GTW61850QualityDefs.h"

//static GTWDEFS_PARSED_OPTION_TYPE s_GTW61850MasterDataObject_allowedOptions[] = {
//  "ALIAS",PARSED_OPTION_STRING_VALUE,NULL,NULL
//};

class GTW61850Client;

namespace tmw61850 {
class DataSet;
class DataAttribute;
class ReportControl;
class Value;
};

class GTWLIB_API GTW61400AlarmMDO : public GTWMasterDataObjectTemplate<GTWBaseDataObject>
{
  friend class GTW61400Alarms;
  friend class GTW61850Client;
  friend class GTW61400AlarmMDOEditor;
  friend class GTWPointMap;

public:
  GTWBaseEditor *GetBaseEditor(const EditorCommandDTO &dto) override;
  GTWDEFS_TYPE getMdoType(void) override
  {
    return GTWDEFS_TYPE_BOOL;
  }

  bool checkMdoToMdoConverter(GTWCNVTR_TYPE cnvtrType) override;
  bool isCommandMDO(void) override
  {
    return false;
  }

  void DeleteMe(void);
  DeclareClassInfo();
  GTW61400AlarmMDO();

  virtual ~GTW61400AlarmMDO()
  {
    GetBaseEditor(EditorCommandDTO(MENU_CMD_NONE))->DeleteINIparms();
  }

  void GetDescriptionColText(CStdString &itemStr) override;

  void SetClientNode(GTW61850Client *p) 
  {
    m_pClientNode = p;
  }
  
  GTW61850Client *GetClientNode() 
  {
    return m_pClientNode;
  }
  
  void InitializeValue();
  void SetValue(bool v)    
  { 
    m_Value = v; 
  }

  bool GetBoolValue()
  {
    return m_Value;
  }

  bool IsChanged()
  {
    return (m_Value == true ? 1 : 0) != m_LastValue;
  }

  void set61850Quality(I61850_QUALITY_TYPE q) { m_i61850Quality = q; }
  I61850_QUALITY_TYPE get61850Quality() { return m_i61850Quality; }

  GTWDEFS_STD_QLTY getMdoStdQuality() override;

  CStdString GetFullName() override;

  void getMdoDbasDataId(GTWDEFS_DBAS_DATA_ID *pDbasDataId) override;

  void getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg) override;
  void getMdoValueAsVariant(GtwVariant &variant) override;

  bool UponRemove(GTWCollectionBase *pParent) override;
  void UponInsert(GTWCollectionBase *pParent) override;

  TMWTYPES_DOUBLE getValue();
  CStdString getValueAsString();

  /* virtual functions */
  void *bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo, GTWCNVTR_TYPE cnvtrType) override;

  //virtual const char *GetBaseName(void) { return (const char *)name; }
  void updateMDO(GTWDEFS_UPDTRSN updateReason) override;
  void updateMDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo) override;

  CStdString GetMemberNameWithUserTagName() override
  {
    return m_sTagName;
  }

  CStdString GetUtagOrMemberName() override
  {
    return m_sTagName;
  }

  CStdString GetMemberName() override
  {
    return (m_sTagName);
  }

  CStdString GetItemName() override
  {
    return m_sTagName;
  }
  CStdString GetAlarmsNodeName();
  CStdString GetStatusAlarmsArrayName();
  CStdString GetEventAlarmsArrayName();
  void SetAlarmsNode( GTW61400Alarms * p61400Alarms );

  GTW61400Alarms *GetAlarmsNode(void)
  {
    return m_pAlarmsNode;
  }

protected:
  void SetDefaultOptions() override;
  GTWDEFS_STAT ParseOptionsField(const char* connectionToken, const char** ppOptionString) override;
  void GetAllowedOptions( GTWDEFS_PARSED_OPTION_ARRAY &optionsArray) override;

private:
  GTWDEFS_STAT CompareTagName( CStdString &tagName) override;
  void GetTime( TMWDTIME *pValue );
  GTW61850Client *m_pClientNode;
  GTW61400Alarms *m_pAlarmsNode;
  CStdString      m_sTagName;
  unsigned short  m_iStatusCode;
  bool            m_Value;
  int             m_LastValue;
  I61850_QUALITY_TYPE m_i61850Quality;
  //TMWDTIME     m_tReportedTime;
  //GTWDEFS_TIME_QLTY m_iReportedTimeQuality;
};

