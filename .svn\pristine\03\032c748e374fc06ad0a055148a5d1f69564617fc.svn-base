// stdafx.h : include file for standard system include files,
// or project specific include files that are used frequently, but
// are changed infrequently
//

#ifndef GTWENGINE_STDAFX
#define GTWENGINE_STDAFX

#include "gateway/GTWLib/UseSettings.h"


#define __STR2__(x) #x
#define __STR1__(x) __STR2__(x)
#define __LOC__ __FILE__ "(" __STR1__(__LINE__) ") : Note: "

#ifdef _WIN32
#include <winsock2.h>  // socket API
#include <windows.h>

#define _CRTDBG_MAP_ALLOC  
#include <stdlib.h>  
#include <crtdbg.h>  

#ifdef _DEBUG
#define DEBUG_CLIENTBLOCK   new( _CLIENT_BLOCK, __FILE__, __LINE__)
#else
#define DEBUG_CLIENTBLOCK
#endif // _DEBUG
#endif

// C RunTime Header Files
#include <stdio.h>
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>     // for strlen()
#include <sstream>
#include <assert.h>


// Common Gateway Types:
#include "gateway/GTWOsUtils/StdString.h"
#include "gateway/GTWOsUtils/GtwOsUtils.h"
#include "Common/TMWBaseCPP/TMWBaseCPP.h"

#endif

// TODO: reference additional headers your program requires here
