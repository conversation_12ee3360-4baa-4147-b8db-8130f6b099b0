/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWs1cse.cpp                                                */
/* DESCRIPTION:  SDO definitions for Slave 101 short set point commands      */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 11 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif


#include "GTWS14ScaledCommandSdo.h"
#include "GTWS101Protocol.h"
#include "GTWS104Protocol.h"

ImplementClassBaseInfo(GTWS14ScaledCommandSdo, GTWS14AnalogCommandSdo, pClassInfo1, new GTWS14ScaledCommandSdo(0, 0, false))

/* By declaring instances of GTWS14CommandDataTypeGroup, they        */
/* automatically register themselves with the list of other  */
/* GTWS14CommandDataTypeGroup's, which allows creation of 101 SDO's. */
static GTWS14CommandDataTypeGroup
  csenbDatypGroup(I14DEF_TYPE_CSENB1, I14DEF_TSTR_CSENB1,
                           GTWS14ScaledCommandSdo::createCsenbBdo);

static GTWS104CommandDataTypeGroup
  gtws4cse_csenbDatypGroup(I14DEF_TYPE_CSENB1, I14DEF_TSTR_CSENB1, 
                           GTWS14ScaledCommandSdo::createCsenbBdo);


/**********************************************************************************\
	Function :			GTWS14ScaledCommandSdo::GTWS14ScaledCommandSdo
	Description : [none]	
	Return :			constructor	-	
	Parameters :
			  TMWTYPES_UINT     infoObjAddr	-	
			  S870SCL_CTRL_MASK ctrlMask	-	
			  bool      synchronizeDownstream	-	
	Note : [none]
\**********************************************************************************/
GTWS14ScaledCommandSdo::GTWS14ScaledCommandSdo(
  TMWTYPES_UINT     infoObjAddr,
  S870SCL_CTRL_MASK ctrlMask,
  bool      synchronizeDownstream)
  :
  GTWS14AnalogCommandSdo(infoObjAddr,
               ctrlMask,
               synchronizeDownstream)
{
}

/**********************************************************************************\
	Function :			GTWS14ScaledCommandSdo::init
	Description : [none]	
	Return :			void	-	
	Parameters :
	Note : [none]
\**********************************************************************************/
void GTWS14ScaledCommandSdo::init()
{
}

/**********************************************************************************\
	Function :			GTWS14ScaledCommandSdo::bindSdoWithMdo
	Description : [none]	
	Return :			bool	-	
	Parameters :
			GTWMasterDataObject *pMdo	-	
	Note : [none]
\**********************************************************************************/
bool GTWS14ScaledCommandSdo::bindSdoWithMdo(GTWMasterDataObject *pMdo)
{
  bool bRetVal = TMWDEFS_FALSE;
  writeCnvtrDouble = (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)pMdo->bindMdoToSdoForWriting(this,GTWCNVTR_TYPE_DOUBLE);
  if(writeCnvtrDouble)
  {
    bRetVal = TMWDEFS_TRUE;
  }
  if (bRetVal == TMWDEFS_FALSE)
  {
    writeCnvtrSFloat = (GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *)pMdo->bindMdoToSdoForWriting(this,GTWCNVTR_TYPE_SFLOAT);
    if(writeCnvtrSFloat)
    {
      bRetVal = TMWDEFS_TRUE;
    }
  }
  if (bRetVal == TMWDEFS_FALSE)
  {
    writeCnvtrLong = (GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject> *)pMdo->bindMdoToSdoForWriting(this,GTWCNVTR_TYPE_LONG);
    if(writeCnvtrLong)
    {
      bRetVal = TMWDEFS_TRUE;
    }
  }
  if (bRetVal == TMWDEFS_FALSE)
  {
    writeCnvtrShort = (GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *)pMdo->bindMdoToSdoForWriting(this,GTWCNVTR_TYPE_SHORT);
    if(writeCnvtrShort)
    {
      bRetVal = TMWDEFS_TRUE;
    }
  }

  if (bRetVal == TMWDEFS_TRUE)
  {
    updateSDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);
  }

  return(bRetVal);
}

/**********************************************************************************\
	Function :			GTWS14ScaledCommandSdo::createCsenbBdo
	Description : [none]	
	Return :			GTWDEFS_STAT	-	
	Parameters :
			CStdString &tagName	-	
			GTWBaseDataObject **ppBdo	-	
	Note : [none]
\**********************************************************************************/
GTWDEFS_STAT GTWS14ScaledCommandSdo::createCsenbBdo(CStdString &tagName, GTWBaseDataObject **ppBdo, GTWCollectionMember *pOwner)
{
  TMWTYPES_UINT infoObjAddr;
  GTWDEFS_STAT  status;

  status = GTWIecBaseDataObject::parseTagName(tagName,&infoObjAddr);

  if (status == GTWDEFS_STAT_SUCCESS)
  {
    S870SCL_CTRL_MASK ctrlMask = S870SCL_CTRL_MASK_NONE;
    if (GTWConfig::S14CSENBAllowOnePass)
    {
      ctrlMask = S870SCL_CTRL_MASK_NO_SELECT;
    }
    *ppBdo = new GTWS14ScaledCommandSdo(infoObjAddr,
                              ctrlMask,
                              GTWConfig::S101CSENBsyncDwnstrm);
  }
  return(status);
}

/**********************************************************************************\
	Function :			GTWS14ScaledCommandSdo::select
	Description : [none]	
	Return :			TMWDEFS_COMMAND_STATUS	-	
	Parameters :
			void *pPoint	-	
			TMWTYPES_UCHAR cot	-	
			TMWTYPES_SHORT shortValue	-	
			TMWTYPES_UCHAR qos	-	
	Note : [none]
\**********************************************************************************/
TMWDEFS_COMMAND_STATUS GTWS14ScaledCommandSdo::select(void *pPoint, TMWTYPES_UCHAR cot, TMWTYPES_SHORT shortValue, TMWTYPES_UCHAR qos)
{
  if (pPoint == TMWDEFS_NULL)
  {
    return(TMWDEFS_CMD_STAT_FAILED); 
  }

  GTWS14ScaledCommandSdo *pCseSdo = (GTWS14ScaledCommandSdo *)pPoint;

  TMWTYPES_SFLOAT value = shortValue;
  return(pCseSdo->GTWS14AnalogCommandSdo::select(I14DEF_TYPE_CSENB1,value,cot,qos));
}

/**********************************************************************************\
	Function :			GTWS14ScaledCommandSdo::control
	Description : [none]	
	Return :			TMWDEFS_COMMAND_STATUS	-	
	Parameters :
			void *pPoint	-	
			TMWTYPES_UCHAR cot	-	
			TMWTYPES_SHORT shortValue	-	
			TMWTYPES_UCHAR qos	-	
	Note : [none]
\**********************************************************************************/
TMWDEFS_COMMAND_STATUS GTWS14ScaledCommandSdo::control(void *pPoint, TMWTYPES_UCHAR cot, TMWTYPES_SHORT shortValue, TMWTYPES_UCHAR qos)
{
  if (pPoint == TMWDEFS_NULL)
  {
    return(TMWDEFS_CMD_STAT_FAILED); 
  }

  GTWS14ScaledCommandSdo *pCseSdo = (GTWS14ScaledCommandSdo *)pPoint;
  TMWTYPES_SFLOAT value = shortValue;

  S870SCL_CTRL_MASK allowedCtrlMask = pCseSdo->getS14AllowedCtrlMask();
  S870SCL_CTRL_MASK requestedCtrlMask = 0;
  TMWDEFS_COMMAND_STATUS status = pCseSdo->GTWS14AnalogCommandSdo::control(I14DEF_TYPE_CSENB1,value,allowedCtrlMask,requestedCtrlMask,qos);
  return(status);
}

