#pragma once

typedef unsigned short I61850_QUALITY_TYPE;

#define I61850_QUALITY_VALIDITY_GOOD          0x0000 // 00
#define I61850_QUALITY_VALIDITY_QUESTIONABLE  0x1800 //[1100000000000]
#define I61850_QUALITY_VALIDITY_INVALID       0x800  //[0100000000000]

/*

#define I61850_QUALITY_VALIDITY_MASK          0x0003
#define I61850_QUALITY_VALIDITY_RESERVED      0x0001 // 10
#define I61850_QUALITY_VALIDITY_INVALID       0x0002 // 01
#define I61850_QUALITY_VALIDITY_QUESTIONABLE  0x0003 // 11

#define I61850_QUALITY_OVERFLOW               0x0004
#define I61850_QUALITY_OUTOFRANGE             0x0008
#define I61850_QUALITY_BADREFERENCE           0x0010
#define I61850_QUALITY_OSCILLIATORY           0x0020
#define I61850_QUALITY_FAILURE                0x0040
#define I61850_QUALITY_OLDDATA                0x0080
#define I61850_QUALITY_INCONSISTENT           0x0100
#define I61850_QUALITY_INACCURATE             0x0200
#define I61850_QUALITY_SOURCE_MASK            0x0400
#define I61850_QUALITY_SOURCE_PROCESS         0x0000
#define I61850_QUALITY_SOURCE_SUBSTITUTED     0x0400
#define I61850_QUALITY_TEST                   0x0800
#define I61850_QUALITY_OPERATORBLOCKED        0x1000
*/