/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2002 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWdnpaction.cpp                                            */
/* DESCRIPTION:  Implementation of DNP action masks                          */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 44 ***_NOBODY_*** "                                                     */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

#include "GTWDnpSlaveProtocol.h"
#include "GTWActionDefs.h"
#include "GTWDnpSessionAction.h"
#include "WinTimer.h"

ImplementClassBaseInfo(GTWDnpSessionActionMask, GTWDnpAction, pClassInfo1, new GTWDnpSessionActionMask(0, 0))
ImplementClassBaseInfo(GTWDnpSessionActionPeriod, GTWDnpAction, pClassInfo2, new GTWDnpSessionActionPeriod(0, 0, nullptr, 0))
ImplementClassBaseInfo(GTWDnpSessionActionNow, GTWDnpSessionActionPeriod, pClassInfo3, new GTWDnpSessionActionNow(0, nullptr, 0))

void GTWDnpSessionActionMask::gtwaction_shutdown(GTWCollectionList *pCollection, TMWTYPES_USHORT sessionIndex, GTWSession *pMySession )
{
  GTWCollectionMember *pCollectionMember;
  GTWCollectionBase *pDataTypeCollection = pMySession->GetMemberCollection();
  if (pDataTypeCollection != NULL)
  {
    // look for Sectors and open them
    
    SHARED_LOCK_GTW_COLLECTION(pDataTypeCollection);
    auto datatypePos = pDataTypeCollection->GetMap()->begin();

    while(datatypePos != pDataTypeCollection->GetMap()->end())
    {
      pCollectionMember = datatypePos->second;
      if (pCollectionMember->IsA("GTWDnpSessionActionPeriod"))
      {
        CStdString str = pCollectionMember->GetFullName();
        LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_DNP, nullptr, "GTWDnpSessionActionPeriod::Stop :: %s",(const char *)str);
        ((GTWDnpSessionActionPeriod*)pCollectionMember)->Stop();
      }
      ++datatypePos;
    }
  }
}
/**********************************************************************************\
	Function :			GTWAction::gtwaction_init
	Description : Initialize the master data objects for all Action Masks	Defined
	Return :			void	-	
	Parameters :
			GTWCollectionList *pCollection	-	
			GTWTYPES_PROTOCOL protocol	-	
			TMWTYPES_USHORT sessionIndex	-	
			TMWTYPES_USHORT sectorIndex	-	
	Note : Number of masks must equal number of periods
\**********************************************************************************/
void GTWDnpSessionActionMask::gtwaction_init(GTWCollectionList *pCollection, TMWTYPES_USHORT sessionIndex, GTWSession *pMySession )
{
  GTWTYPES_PROTOCOL protocol = GTWConfig::getSessionProtocol(sessionIndex);

  switch (protocol)
  {  
    case(GTWTYPES_PROTOCOL_MDNP):
    {
      GTWDnpSessionActionNow *pBdoNow;
      GTWDnpActionNowStatus *pBdoNowComplete;
      GTWDnpSessionActionPeriod *pBdoPrd;
      GTWDnpSessionActionMask *pBdoMask;

      // verify dimension of mask and period
      TMWTYPES_USHORT nActions = 5;

      if(nActions > 0)
      {
        pBdoNow = new GTWDnpSessionActionNow(sessionIndex, pCollection, GTWConfig::DNPSessionActionNow(sessionIndex));
        pBdoNowComplete = new GTWDnpActionNowStatus(pCollection, "DNPSessionActionNowStatus");

        if(pBdoNow) 
        {
          pBdoNow->SetActionCompleteMdo(pBdoNowComplete);
          pCollection->InsertCollectionMember(pBdoNow);
        }

        if(pBdoNowComplete) 
        {
          pCollection->InsertCollectionMember(pBdoNowComplete);
        }

        GTWDnpSessionActionMaskOnLineStatus *pActionMaskOnlineMDO = new GTWDnpSessionActionMaskOnLineStatus();
        pCollection->InsertCollectionMember(pActionMaskOnlineMDO);

        for(TMWTYPES_SHORT actionIndex = 0; actionIndex < nActions; actionIndex++)
        {
          pBdoPrd = new GTWDnpSessionActionPeriod(sessionIndex, actionIndex, pCollection, gtwaction_get_dnp_session_mask(sessionIndex,actionIndex));
          pBdoMask = new GTWDnpSessionActionMask(sessionIndex, actionIndex);
          
          pBdoMask->SetPrdBdo(pBdoPrd);
          pCollection->InsertCollectionMember(pBdoMask);
          pBdoPrd->SetMask(pBdoMask->GetValue());
          pCollection->InsertCollectionMember(pBdoPrd);
          
          pActionMaskOnlineMDO->AddPeriod(pBdoPrd);
          pBdoPrd->SetOnlineControl(pActionMaskOnlineMDO);
        }
      }

      break;
    }
  }
}

void GTWDnpSessionActionPeriod::Stop(void)
{
  if (GetActionIndex() >= 0)
  {
    pActionTimer->KillTimer();
  }
}

void GTWDnpSessionActionPeriod::Start(void)
{
  if (GetActionIndex() >= 0)
  {
    if (GetValue() > 0)
    {
      if (!CanActionStart())
        return;

//    DoActions(GetSession());
      QueueDoSessionActionsWorkItem(GetSession());
      pActionTimer->SetTimer(this,GetValue());
    }
  }
}

void GTWDnpSessionActionPeriod::UponInsert(GTWCollectionBase *pParent)
{
  GTWAction::UponInsert(pParent);
  GTWSession *pSession = GetSession();

  if (pSession && !pSession->GetDelaySCLInit())
  {
    Start();
  }
}

void GTWDnpSessionActionPeriod::LogInProgressError()
{
  LOGCSS(GtwLogger::Severity_Error, GtwLogger::SDG_Category_DNP, nullptr, GetSession() != nullptr ? GetSession()->GetSclSession() : nullptr, nullptr, "Could not perform action mask=%0X, name = %s, at %d(ms), for session index %d. The previous transaction has not completed", m_mask, (const char *)m_name, GetValue(),m_nSessionIndex);
  return;
}

