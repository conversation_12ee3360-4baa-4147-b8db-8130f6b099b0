﻿<?xml version="1.0" encoding="utf-8"?>
<SCL revision="B" version="2007" xmlns="http://www.iec.ch/61850/2003/SCL" xsi:schemaLocation="http://www.iec.ch/61850/2003/SCL SCL.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <Header version="3" id=""><History><Hitem version="Add RptEnabled Max 5" revision="0.1" what="" why="" who="Eric with TMW SCL Navigator v1.3.1" when="Jun Fri 18 14:39 2021" />
</History></Header>
  <Communication>
    <SubNetwork name="SubNetworkName">
      <ConnectedAP apName="AP" iedName="BayController">
        <Address>
          <P type="OSI-AP-Title">1,1,9999,1</P>
          <P type="OSI-AE-Qualifier">12</P>
          <P type="OSI-PSEL">00000001</P>
          <P type="OSI-SSEL">0001</P>
          <P type="OSI-TSEL">0001</P>
          <P type="IP">127.0.0.1</P>
        </Address>
        <GSE ldInst="Q" cbName="gcbINTERLOCK">
          <Address>
            <P type="VLAN-ID">000</P>
            <P type="VLAN-PRIORITY">4</P>
            <P type="MAC-Address">09-ba-ad-c0-ff-ee</P>
            <P type="APPID">1000</P>
          </Address>
        </GSE>
        <GSE ldInst="Q" cbName="gcbSTAT">
          <Address>
            <P type="VLAN-ID">000</P>
            <P type="VLAN-PRIORITY">4</P>
            <P type="MAC-Address">09-ba-ad-c0-ff-ee</P>
            <P type="APPID">2000</P>
          </Address>
        </GSE>
        <GSE ldInst="Q" cbName="gcbMEAS">
          <Address>
            <P type="VLAN-ID">000</P>
            <P type="VLAN-PRIORITY">4</P>
            <P type="MAC-Address">09-ba-ad-c0-ff-ee</P>
            <P type="APPID">3000</P>
          </Address>
        </GSE>
        <SMV ldInst="Q" cbName="MSVCB01">
          <Address>
            <P type="VLAN-ID">000</P>
            <P type="VLAN-PRIORITY">4</P>
            <P type="MAC-Address">09-ba-ad-c0-ff-ee</P>
            <P type="APPID">4000</P>
          </Address>
        </SMV>
        <SMV ldInst="Q" cbName="MSVCB02">
          <Address>
            <P type="VLAN-ID">000</P>
            <P type="VLAN-PRIORITY">4</P>
            <P type="MAC-Address">09-ba-ad-c0-ff-e5</P>
            <P type="APPID">5000</P>
          </Address>
        </SMV>
      </ConnectedAP>
    </SubNetwork>
  </Communication>
  <IED name="BayController" manufacturer="TMW" configVersion="1.0" originalSclRevision="B" originalSclVersion="2007">
    <Services>
      <DynAssociation max="10" />
      <ConfLogControl max="10" />
      <GOOSE max="10" />
      <GSSE max="50" />
      <SMVsc max="20" />
      <GetDirectory />
      <GetDataObjectDefinition />
      <DataObjectDirectory />
      <GetDataSetValue />
      <DataSetDirectory />
      <ConfDataSet modify="false" maxAttributes="50" max="10" />
      <DynDataSet max="100" maxAttributes="50" />
      <ReadWrite />
      <ConfReportControl bufConf="true" bufMode="both" max="10" />
      <GetCBValues />
      <ReportSettings rptID="Dyn" trgOps="Dyn" intgPd="Dyn" optFields="Dyn" cbName="Conf" datSet="Dyn" bufTime="Dyn" resvTms="true" owner="true" />
      <LogSettings trgOps="Dyn" intgPd="Dyn" datSet="Dyn" logEna="Dyn" />
      <GSESettings appID="Dyn" dataLabel="Dyn" datSet="Dyn" />
      <FileHandling />
      <ConfLNs />
      <ClientServices sv="true" readLog="true" bufReport="true" goose="true" unbufReport="true" />
    </Services>
    <AccessPoint name="AP">
      <Server>
        <Authentication />
        <LDevice inst="Q">
          <LN0 lnType="LLN0_TYPE" lnClass="LLN0" inst="">
            <DataSet name="STAT">
              <FCDA prefix="QA1" ldInst="Q" lnClass="CSWI" lnInst="1" doName="Pos" fc="ST" />
              <FCDA prefix="QA1" ldInst="Q" lnClass="XCBR" lnInst="1" doName="Pos" fc="ST" />
              <FCDA prefix="QA1" ldInst="Q" lnClass="CILO" lnInst="1" doName="EnaOpn" fc="ST" />
              <FCDA prefix="QA1" ldInst="Q" lnClass="CILO" lnInst="1" doName="EnaCls" fc="ST" />
              <FCDA prefix="QC1" ldInst="Q" lnClass="CSWI" lnInst="3" doName="Pos" fc="ST" />
              <FCDA prefix="QC1" ldInst="Q" lnClass="XSWI" lnInst="3" doName="Pos" fc="ST" />
              <FCDA prefix="QC1" ldInst="Q" lnClass="CILO" lnInst="3" doName="EnaOpn" fc="ST" />
              <FCDA prefix="QC1" ldInst="Q" lnClass="CILO" lnInst="3" doName="EnaCls" fc="ST" />
              <FCDA prefix="QB1" ldInst="Q" lnClass="CSWI" lnInst="2" doName="Pos" fc="ST" />
              <FCDA prefix="QB1" ldInst="Q" lnClass="XSWI" lnInst="2" doName="Pos" fc="ST" />
              <FCDA prefix="QB1" ldInst="Q" lnClass="CILO" lnInst="2" doName="EnaOpn" fc="ST" />
              <FCDA prefix="QB1" ldInst="Q" lnClass="CILO" lnInst="2" doName="EnaCls" fc="ST" />
            </DataSet>
            <DataSet name="MEAS">
              <FCDA prefix="T1" ldInst="Q" lnClass="MMXU" lnInst="1" doName="A" fc="MX" />
              <FCDA prefix="T1" ldInst="Q" lnClass="MMXU" lnInst="1" doName="PhV" fc="MX" />
            </DataSet>
            <ReportControl name="brcbSTAT" confRev="1" buffered="true" bufTime="1000" datSet="STAT" intgPd="0">
              <TrgOps dchg="true" qchg="true" dupd="true" period="true" />
              <OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" entryID="true" configRef="true" bufOvfl="false" />
              <RptEnabled max="5" />
            </ReportControl>
            <ReportControl name="brcbMEAS" confRev="1" buffered="true" bufTime="1000" datSet="MEAS" intgPd="0">
              <TrgOps dchg="true" qchg="true" dupd="true" period="true" />
              <OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" entryID="true" configRef="true" bufOvfl="false" />
              <RptEnabled max="5" />
            </ReportControl>
            <LogControl name="LogMX" logName="Q" datSet="STAT" intgPd="5000">
              <TrgOps dchg="true" qchg="true" dupd="true" />
            </LogControl>
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="NamPlt">
              <DAI name="vendor">
                <Val>Triangle MicroWorks</Val>
              </DAI>
              <DAI name="configRev">
                <Val>10</Val>
              </DAI>
              <DAI name="ldNs">
                <Val>IEC 61850-7-4:2007</Val>
              </DAI>
            </DOI>
            <Log name="Q" />
            <GSEControl appID="tmwInterlock" name="gcbINTERLOCK" type="GOOSE" datSet="INTERLOCK" confRev="1" />
            <GSEControl appID="tmwStat" name="gcbSTAT" type="GOOSE" datSet="STAT" confRev="1" />
            <GSEControl appID="tmwMeas" name="gcbMEAS" type="GOOSE" datSet="MEAS" confRev="1" />
            <SampledValueControl name="MSVCB01" datSet="PhsMeas1" confRev="1" smvID="MU01" smpRate="80" nofASDU="1">
              <SmvOpts sampleSynchronized="true" />
            </SampledValueControl>
            <SampledValueControl name="MSVCB02" datSet="PhsMeas1" confRev="1" smvID="MU02" smpRate="256" nofASDU="8">
              <SmvOpts sampleSynchronized="true" />
            </SampledValueControl>
          </LN0>
          <LN lnClass="LPHD" inst="1" lnType="LPHD_TYPE">
            <DOI name="PhyNam">
              <DAI name="vendor">
                <Val>Triangle MicroWorks</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QA1" lnClass="CSWI" inst="1" lnType="CSWI_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="NamPlt">
              <DAI name="vendor">
                <Val>Triangle MicroWorks</Val>
              </DAI>
            </DOI>
            <DOI name="Pos">
              <SDI name="origin">
                <DAI name="orCat">
                  <Val>remote-control</Val>
                </DAI>
              </SDI>
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="stSeld">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>sbo-with-enhanced-security</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>30000</Val>
              </DAI>
              <DAI name="sboClass">
                <Val>operate-once</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QA1" lnClass="XCBR" inst="1" lnType="XCBR_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="NamPlt">
              <DAI name="vendor">
                <Val>TriangleMicroWorks</Val>
              </DAI>
            </DOI>
            <DOI name="Pos">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="BlkOpn">
              <DAI name="stVal">
                <Val>True</Val>
              </DAI>
              <DAI name="ctlModel" valKind="RO">
                <Val>status-only</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>60000</Val>
              </DAI>
            </DOI>
            <DOI name="BlkCls">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>60000</Val>
              </DAI>
            </DOI>
            <DOI name="CBOpCap">
              <DAI name="stVal">
                <Val>Close-Open</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QA1" lnClass="CILO" inst="1" lnType="CILO_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="EnaOpn">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
            </DOI>
            <DOI name="EnaCls">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QB1" lnClass="CSWI" inst="2" lnType="CSWI_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="Pos">
              <SDI name="origin">
                <DAI name="orCat">
                  <Val>remote-control</Val>
                </DAI>
              </SDI>
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="stSeld">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>sbo-with-enhanced-security</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>30000</Val>
              </DAI>
              <DAI name="sboClass">
                <Val>operate-once</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QB1" lnClass="XSWI" inst="2" lnType="XSWI_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="Pos">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="stSeld">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>60000</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
            </DOI>
            <DOI name="BlkOpn">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>30000</Val>
              </DAI>
            </DOI>
            <DOI name="BlkCls">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>30000</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QB1" lnClass="CILO" inst="2" lnType="CILO_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="EnaOpn">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
            </DOI>
            <DOI name="EnaCls">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QC1" lnClass="CSWI" inst="3" lnType="CSWI_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="NamPlt">
              <DAI name="vendor">
                <Val>TMW</Val>
              </DAI>
            </DOI>
            <DOI name="Pos">
              <SDI name="origin">
                <DAI name="orCat">
                  <Val>remote-control</Val>
                </DAI>
              </SDI>
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="stSeld">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>sbo-with-enhanced-security</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>30000</Val>
              </DAI>
              <DAI name="sboClass">
                <Val>operate-once</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QC1" lnClass="XSWI" inst="3" lnType="XSWI_TYPE_2">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="Pos">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="BlkOpn">
              <DAI name="stVal">
                <Val>True</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>30000</Val>
              </DAI>
            </DOI>
            <DOI name="BlkCls">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
              <DAI name="operTimeout">
                <Val>10000</Val>
              </DAI>
              <DAI name="sboTimeout">
                <Val>30000</Val>
              </DAI>
            </DOI>
            <DOI name="SwOpCap">
              <DAI name="stVal">
                <Val>Open and Close</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="QC1" lnClass="CILO" inst="3" lnType="CILO_TYPE_1">
            <DOI name="Mod">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="Beh">
              <DAI name="stVal">
                <Val>on</Val>
              </DAI>
            </DOI>
            <DOI name="Health">
              <DAI name="stVal">
                <Val>Ok</Val>
              </DAI>
            </DOI>
            <DOI name="EnaOpn">
              <DAI name="stVal">
                <Val>True</Val>
              </DAI>
            </DOI>
            <DOI name="EnaCls">
              <DAI name="stVal">
                <Val>False</Val>
              </DAI>
            </DOI>
          </LN>
          <LN prefix="T1" lnClass="MMXU" inst="1" lnType="MMXU_TYPE">
            <DOI name="Mod">
              <DAI name="ctlModel">
                <Val>status-only</Val>
              </DAI>
            </DOI>
            <DOI name="NamPlt">
              <DAI name="vendor">
                <Val>Triangle MicroWorks</Val>
              </DAI>
            </DOI>
            <DOI name="A">
              <SDI name="phsA">
                <SDI name="cVal">
                  <SDI name="mag">
                    <DAI name="f" sAddr="AV0" />
                  </SDI>
                </SDI>
                <DAI name="q" sAddr="Q0" />
                <DAI name="t" sAddr="T0" />
                <SDI name="units">
                  <DAI name="SIUnit">
                    <Val>A</Val>
                  </DAI>
                </SDI>
                <DAI name="db" sAddr="D0">
                  <Val>10000</Val>
                </DAI>
                <DAI name="zeroDb">
                  <Val>12000</Val>
                </DAI>
                <DAI name="range">
                  <Val>normal</Val>
                </DAI>
                <SDI name="rangeC">
                  <SDI name="hhLim">
                    <DAI name="f">
                      <Val>1600.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="hLim">
                    <DAI name="f">
                      <Val>1500.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="lLim">
                    <DAI name="f">
                      <Val>300.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="llLim">
                    <DAI name="f">
                      <Val>200.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="min">
                    <DAI name="f">
                      <Val>0.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="max">
                    <DAI name="f">
                      <Val>1800.00</Val>
                    </DAI>
                  </SDI>
                  <DAI name="limDb">
                    <Val>3</Val>
                  </DAI>
                </SDI>
              </SDI>
              <SDI name="phsB">
                <SDI name="cVal">
                  <SDI name="mag">
                    <DAI name="f" sAddr="AV1" />
                  </SDI>
                </SDI>
                <DAI name="q" sAddr="Q1" />
                <DAI name="t" sAddr="T1" />
                <SDI name="units">
                  <DAI name="SIUnit">
                    <Val>A</Val>
                  </DAI>
                </SDI>
                <DAI name="db">
                  <Val>10000</Val>
                </DAI>
                <DAI name="zeroDb">
                  <Val>12000</Val>
                </DAI>
                <DAI name="range">
                  <Val>normal</Val>
                </DAI>
                <SDI name="rangeC">
                  <SDI name="hhLim">
                    <DAI name="f">
                      <Val>1600.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="hLim">
                    <DAI name="f">
                      <Val>1500.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="lLim">
                    <DAI name="f">
                      <Val>300.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="llLim">
                    <DAI name="f">
                      <Val>200.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="min">
                    <DAI name="f">
                      <Val>0.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="max">
                    <DAI name="f">
                      <Val>1800.00</Val>
                    </DAI>
                  </SDI>
                  <DAI name="limDb">
                    <Val>4</Val>
                  </DAI>
                </SDI>
              </SDI>
              <SDI name="phsC">
                <SDI name="cVal">
                  <SDI name="mag">
                    <DAI name="f" sAddr="AV2" />
                  </SDI>
                </SDI>
                <DAI name="q" sAddr="Q2" />
                <DAI name="t" sAddr="T2" />
                <SDI name="units">
                  <DAI name="SIUnit">
                    <Val>A</Val>
                  </DAI>
                </SDI>
                <DAI name="db">
                  <Val>10000</Val>
                </DAI>
                <DAI name="zeroDb">
                  <Val>12000</Val>
                </DAI>
                <DAI name="range">
                  <Val>normal</Val>
                </DAI>
                <SDI name="rangeC">
                  <SDI name="hhLim">
                    <DAI name="f">
                      <Val>1600.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="hLim">
                    <DAI name="f">
                      <Val>1500.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="lLim">
                    <DAI name="f">
                      <Val>300.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="llLim">
                    <DAI name="f">
                      <Val>200.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="min">
                    <DAI name="f">
                      <Val>0.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="max">
                    <DAI name="f">
                      <Val>1800.00</Val>
                    </DAI>
                  </SDI>
                  <DAI name="limDb">
                    <Val>4</Val>
                  </DAI>
                </SDI>
              </SDI>
            </DOI>
            <DOI name="PhV">
              <SDI name="phsA">
                <SDI name="cVal">
                  <SDI name="mag">
                    <DAI name="f" sAddr="AV3" />
                  </SDI>
                </SDI>
                <DAI name="q" sAddr="Q3" />
                <DAI name="t" sAddr="T3" />
                <SDI name="units">
                  <DAI name="SIUnit">
                    <Val>V</Val>
                  </DAI>
                </SDI>
                <DAI name="db">
                  <Val>342</Val>
                </DAI>
                <DAI name="zeroDb">
                  <Val>354</Val>
                </DAI>
                <DAI name="range">
                  <Val>normal</Val>
                </DAI>
                <SDI name="rangeC">
                  <SDI name="hhLim">
                    <DAI name="f">
                      <Val>23200</Val>
                    </DAI>
                  </SDI>
                  <SDI name="hLim">
                    <DAI name="f">
                      <Val>23100</Val>
                    </DAI>
                  </SDI>
                  <SDI name="lLim">
                    <DAI name="f">
                      <Val>21900</Val>
                    </DAI>
                  </SDI>
                  <SDI name="llLim">
                    <DAI name="f">
                      <Val>21800</Val>
                    </DAI>
                  </SDI>
                  <SDI name="min">
                    <DAI name="f">
                      <Val>21500</Val>
                    </DAI>
                  </SDI>
                  <SDI name="max">
                    <DAI name="f">
                      <Val>23500</Val>
                    </DAI>
                  </SDI>
                  <DAI name="limDb">
                    <Val>2</Val>
                  </DAI>
                </SDI>
              </SDI>
              <SDI name="phsB">
                <SDI name="cVal">
                  <SDI name="mag">
                    <DAI name="f" sAddr="AV4" />
                  </SDI>
                </SDI>
                <DAI name="q" sAddr="Q4" />
                <DAI name="t" sAddr="T4" />
                <SDI name="units">
                  <DAI name="SIUnit">
                    <Val>V</Val>
                  </DAI>
                </SDI>
                <DAI name="db">
                  <Val>342</Val>
                </DAI>
                <DAI name="zeroDb">
                  <Val>354</Val>
                </DAI>
                <DAI name="range">
                  <Val>normal</Val>
                </DAI>
                <SDI name="rangeC">
                  <SDI name="hhLim">
                    <DAI name="f">
                      <Val>23200</Val>
                    </DAI>
                  </SDI>
                  <SDI name="hLim">
                    <DAI name="f">
                      <Val>23100</Val>
                    </DAI>
                  </SDI>
                  <SDI name="lLim">
                    <DAI name="f">
                      <Val>21900</Val>
                    </DAI>
                  </SDI>
                  <SDI name="llLim">
                    <DAI name="f">
                      <Val>21800</Val>
                    </DAI>
                  </SDI>
                  <SDI name="min">
                    <DAI name="f">
                      <Val>21500</Val>
                    </DAI>
                  </SDI>
                  <SDI name="max">
                    <DAI name="f">
                      <Val>23500</Val>
                    </DAI>
                  </SDI>
                </SDI>
              </SDI>
              <SDI name="phsC">
                <SDI name="cVal">
                  <SDI name="mag">
                    <DAI name="f" sAddr="AV5" />
                  </SDI>
                </SDI>
                <DAI name="q" sAddr="Q5" />
                <DAI name="t" sAddr="T5" />
                <SDI name="units">
                  <DAI name="SIUnit">
                    <Val>V</Val>
                  </DAI>
                </SDI>
                <DAI name="range">
                  <Val>normal</Val>
                </DAI>
                <SDI name="rangeC">
                  <SDI name="hhLim">
                    <DAI name="f">
                      <Val>23200.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="hLim">
                    <DAI name="f">
                      <Val>23100.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="lLim">
                    <DAI name="f">
                      <Val>21900.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="llLim">
                    <DAI name="f">
                      <Val>21800.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="min">
                    <DAI name="f">
                      <Val>21500.00</Val>
                    </DAI>
                  </SDI>
                  <SDI name="max">
                    <DAI name="f">
                      <Val>23500.00</Val>
                    </DAI>
                  </SDI>
                  <DAI name="limDb">
                    <Val>3</Val>
                  </DAI>
                </SDI>
                <DAI name="db">
                  <Val>344</Val>
                </DAI>
                <DAI name="zeroDb">
                  <Val>344</Val>
                </DAI>
              </SDI>
            </DOI>
          </LN>
          <LN prefix="SAV1" lnClass="TVTR" inst="1" lnType="TVTR_TYPE">
            <DOI name="VolSv">
              <SDI name="sVC">
                <DAI name="scaleFactor">
                  <Val>0.0100000</Val>
                </DAI>
              </SDI>
            </DOI>
          </LN>
          <LN prefix="SAV1" lnClass="TCTR" inst="1" lnType="TCTR_TYPE">
            <DOI name="AmpSv">
              <SDI name="sVC">
                <DAI name="scaleFactor">
                  <Val>0.00100000</Val>
                </DAI>
              </SDI>
            </DOI>
          </LN>
          <LN lnClass="LTRK" inst="1" lnType="LTRK_TYPE" />
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <DataTypeTemplates>
    <LNodeType id="LLN0_TYPE" lnClass="LLN0">
      <DO name="Mod" type="ENC" />
      <DO name="Beh" type="ENS_1" />
      <DO name="Health" type="ENS_2" />
      <DO name="NamPlt" type="LPL_1" />
    </LNodeType>
    <LNodeType id="LPHD_TYPE" lnClass="LPHD">
      <DO name="PhyNam" type="DPL" />
      <DO name="PhyHealth" type="ENS_2" />
      <DO name="Proxy" type="SPS" />
    </LNodeType>
    <LNodeType id="CSWI_TYPE_1" lnClass="CSWI">
      <DO name="Mod" type="ENC" />
      <DO name="Beh" type="ENS_1" />
      <DO name="Health" type="ENS_2" />
      <DO name="NamPlt" type="LPL_2" />
      <DO name="Pos" type="DPC2" />
    </LNodeType>
    <LNodeType id="XCBR_TYPE_1" lnClass="XCBR">
      <DO name="Mod" type="ENC" />
      <DO name="Beh" type="ENS_1" />
      <DO name="Health" type="ENS_2" />
      <DO name="NamPlt" type="LPL_2" />
      <DO name="Loc" type="SPS" />
      <DO name="OpCnt" type="INS" />
      <DO name="Pos" type="DPC_2" />
      <DO name="BlkOpn" type="SPC" />
      <DO name="BlkCls" type="SPC" />
      <DO name="CBOpCap" type="ENS_5" />
    </LNodeType>
    <LNodeType id="CILO_TYPE_1" lnClass="CILO">
      <DO name="Mod" type="ENC" />
      <DO name="Beh" type="ENS_1" />
      <DO name="Health" type="ENS_2" />
      <DO name="NamPlt" type="LPL_2" />
      <DO name="EnaOpn" type="SPS_1" />
      <DO name="EnaCls" type="SPS_1" />
    </LNodeType>
    <LNodeType id="XSWI_TYPE_1" lnClass="XSWI">
      <DO name="Mod" type="ENC" />
      <DO name="Beh" type="ENS_1" />
      <DO name="Health" type="ENS_2" />
      <DO name="NamPlt" type="LPL_2" />
      <DO name="Loc" type="SPS" />
      <DO name="OpCnt" type="INS" />
      <DO name="Pos" type="DPC_6" />
      <DO name="BlkOpn" type="SPC" />
      <DO name="BlkCls" type="SPC" />
      <DO name="SwTyp" type="ENS_3" />
      <DO name="SwOpCap" type="ENS_4" />
    </LNodeType>
    <LNodeType id="XSWI_TYPE_2" lnClass="XSWI">
      <DO name="Mod" type="ENC" />
      <DO name="Beh" type="ENS_1" />
      <DO name="Health" type="ENS_2" />
      <DO name="NamPlt" type="LPL_2" />
      <DO name="Loc" type="SPS" />
      <DO name="OpCnt" type="INS" />
      <DO name="Pos" type="DPC_2" />
      <DO name="BlkOpn" type="SPC" />
      <DO name="BlkCls" type="SPC" />
      <DO name="SwTyp" type="ENS_3" />
      <DO name="SwOpCap" type="ENS_4" />
    </LNodeType>
    <LNodeType id="MMXU_TYPE" lnClass="MMXU">
      <DO name="Mod" type="ENC" />
      <DO name="Beh" type="ENS_1" />
      <DO name="Health" type="ENS_2" />
      <DO name="NamPlt" type="LPL_2" />
      <DO name="A" type="WYE_1" />
      <DO name="PhV" type="WYE_1" />
    </LNodeType>
    <LNodeType id="TVTR_TYPE" lnClass="TVTR">
      <DO name="Beh" type="ENS" />
      <DO name="VolSv" type="SAV" />
    </LNodeType>
    <LNodeType id="TCTR_TYPE" lnClass="TCTR">
      <DO name="Beh" type="ENS" />
      <DO name="AmpSv" type="SAV" />
    </LNodeType>
    <LNodeType id="LTRK_TYPE" lnClass="LTRK">
      <DO name="Beh" type="ENS" />
      <DO name="SpcTrk" type="SPC_CTS" />
      <DO name="DpcTrk" type="SPC_CTS" />
      <DO name="IncTrk" type="INC_CTS" />
      <DO name="EncTrk1" type="CTS" />
      <DO name="ApcFTrk" type="APC_CTS" />
      <DO name="ApcIntTrk" type="APC_CTS" />
      <DO name="BscTrk" type="BSC_CTS" />
      <DO name="IscTrk" type="ISC_CTS" />
      <DO name="BacTrk" type="BSC_CTS" />
      <DO name="GenTrk" type="CST" />
      <DO name="UrcbTrk" type="UTS" />
      <DO name="BrcbTrk" type="BTS" />
      <DO name="LocbTrk" type="LTS" />
      <DO name="GocbTrk" type="GTS" />
      <DO name="SgcbTrk" type="STS" />
    </LNodeType>
    <DOType id="ENC" cdc="ENC">
      <DA name="stVal" bType="Enum" type="BehaviourModeKind" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
      <DA name="ctlModel" bType="Enum" type="CtlModelStatusOnlyKind" fc="CF" />
    </DOType>
    <DOType id="ENS_1" cdc="ENS">
      <DA name="stVal" bType="Enum" type="BehaviourModeKind" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
    </DOType>
    <DOType id="ENS_2" cdc="ENS">
      <DA name="stVal" bType="Enum" type="HealthKind" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
    </DOType>
    <DOType id="LPL_1" cdc="LPL">
      <DA name="vendor" bType="VisString255" fc="DC" />
      <DA name="swRev" bType="VisString255" fc="DC" />
      <DA name="configRev" bType="VisString255" fc="DC" />
      <DA name="ldNs" bType="VisString255" fc="EX" />
    </DOType>
    <DOType id="DPL" cdc="DPL">
      <DA name="vendor" bType="VisString255" fc="DC" />
    </DOType>
    <DOType id="LPL_2" cdc="LPL">
      <DA name="vendor" bType="VisString255" fc="DC" />
      <DA name="swRev" bType="VisString255" fc="DC" />
      <DA name="d" bType="VisString255" fc="DC" />
    </DOType>
    <DOType id="SPC" cdc="SPC">
      <DA name="origin" bType="Struct" type="Originator" fc="ST" />
      <DA name="ctlNum" bType="INT8U" fc="ST" />
      <DA name="stVal" bType="BOOLEAN" fc="ST" dchg="true" />
      <DA name="q" bType="Quality" fc="ST" qchg="true" />
      <DA name="t" bType="Timestamp" fc="ST" />
      <DA name="stSeld" bType="BOOLEAN" fc="ST" dchg="true" />
      <DA name="opRcvd" bType="BOOLEAN" fc="OR" dchg="true" />
      <DA name="opOk" bType="BOOLEAN" fc="OR" dchg="true" />
      <DA name="operTimeout" bType="INT32U" fc="CF" dchg="true" />
      <DA name="tOpOk" bType="Timestamp" fc="OR" />
      <DA name="subEna" bType="BOOLEAN" fc="SV" />
      <DA name="subVal" bType="BOOLEAN" fc="SV" />
      <DA name="subQ" bType="Quality" fc="SV" />
      <DA name="subID" bType="VisString64" fc="SV" />
      <DA name="blkEna" bType="BOOLEAN" fc="BL" />
      <DA name="pulseConfig" bType="Struct" type="PulseConfig" fc="CF" dchg="true" />
      <DA name="ctlModel" bType="Enum" type="CtlModelKind" fc="CF" dchg="true" />
      <DA name="sboTimeout" bType="INT32U" fc="CF" dchg="true" />
      <DA name="sboClass" bType="Enum" type="SboClassKind" fc="CF" dchg="true" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="SBO" bType="VisString129" fc="CO" />
      <DA name="SBOw" bType="Struct" type="OperBOOLEAN" fc="CO" />
      <DA name="Oper" bType="Struct" type="OperBOOLEAN" fc="CO" />
      <DA name="Cancel" bType="Struct" type="CancelBOOLEAN" fc="CO" />
    </DOType>
    <DOType id="ENS_5" cdc="ENS">
      <DA name="stVal" bType="Enum" type="BreakerOpCapabilityKind" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
    </DOType>
    <DOType id="SPS_1" cdc="SPS">
      <DA name="stVal" bType="BOOLEAN" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
    </DOType>
    <DOType id="DPC_6" cdc="DPC">
      <DA name="stVal" bType="Dbpos" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
      <DA name="stSeld" bType="BOOLEAN" fc="ST" />
      <DA name="ctlModel" bType="Enum" type="CtlModelKind" fc="CF" />
      <DA name="sboTimeout" bType="INT32U" fc="CF" dchg="true" />
      <DA name="operTimeout" bType="INT32U" fc="CF" dchg="true" />
      <DA name="SBO" bType="VisString129" fc="CO" />
      <DA name="SBOw" bType="Struct" type="SBOwBOOLEAN" fc="CO" />
      <DA name="Oper" bType="Struct" type="SBOwBOOLEAN" fc="CO" />
      <DA name="Cancel" bType="Struct" type="CancelBOOLEAN1" fc="CO" />
    </DOType>
    <DOType id="ENS_3" cdc="ENS">
      <DA name="stVal" bType="Enum" type="SwitchTypeKind" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
    </DOType>
    <DOType id="ENS_4" cdc="ENS">
      <DA name="stVal" bType="Enum" type="SwitchingCapabilityKind" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
    </DOType>
    <DOType id="DPC_2" cdc="DPC">
      <DA name="stVal" bType="Dbpos" fc="ST" />
      <DA name="q" bType="Quality" fc="ST" />
      <DA name="t" bType="Timestamp" fc="ST" />
      <DA name="ctlModel" bType="Enum" type="CtlModelStatusOnlyKind" fc="CF" />
    </DOType>
    <DOType id="WYE_1" cdc="WYE">
      <SDO name="phsA" type="CMV_1" />
      <SDO name="phsB" type="CMV_1" />
      <SDO name="phsC" type="CMV_1" />
    </DOType>
    <DOType id="CMV_1" cdc="CMV">
      <DA name="cVal" bType="Struct" type="Vector" fc="MX" />
      <DA name="range" fc="MX" bType="Enum" type="RangeKind" />
      <DA name="q" bType="Quality" fc="MX" />
      <DA name="t" bType="Timestamp" fc="MX" />
      <DA name="subEna" bType="BOOLEAN" fc="SV" />
      <DA name="subCVal" bType="Struct" type="Vector" fc="SV" />
      <DA name="subQ" bType="Quality" fc="SV" />
      <DA name="subID" bType="VisString64" fc="SV" />
      <DA name="blkEna" bType="BOOLEAN" fc="BL" />
      <DA name="units" bType="Struct" type="Unit" fc="CF" />
      <DA name="db" fc="CF" bType="INT32U" />
      <DA name="zeroDb" fc="CF" bType="INT32U" />
      <DA name="rangeC" fc="CF" bType="Struct" type="RangeConfig_0" />
      <DA name="magSVC" fc="CF" bType="Struct" type="ScaledValueConfig" />
      <DA name="angSVC" fc="CF" bType="Struct" type="ScaledValueConfig" />
      <DA name="angRef" fc="CF" bType="Enum" type="WYE_angRef" />
      <DA name="smpRate" fc="CF" bType="INT32U" />
      <DA name="d" fc="DC" bType="VisString255" />
      <DA name="dU" fc="DC" bType="Unicode255" />
    </DOType>
    <DOType id="ENS" cdc="ENS">
      <DA name="stVal" bType="Enum" type="BehaviourModeKind" fc="ST" dchg="true" dupd="true" />
      <DA name="q" bType="Quality" fc="ST" qchg="true" />
      <DA name="t" bType="Timestamp" fc="ST" />
    </DOType>
    <DOType id="SAV" cdc="SAV">
      <DA name="instMag" bType="Struct" type="AnalogueValue" fc="MX" />
      <DA name="q" bType="Quality" fc="MX" qchg="true" />
      <DA name="sVC" bType="Struct" type="ScaledValueConfig" fc="CF" dchg="true" />
    </DOType>
    <DOType id="CST" cdc="CST">
      <DA name="objRef" bType="ObjRef" fc="SR" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
    </DOType>
    <DOType id="SPC_CTS" cdc="CTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="ctlVal" bType="BOOLEAN" fc="SR" />
      <DA name="origin" bType="Struct" type="Originator" fc="SR" />
      <DA name="ctlNum" bType="INT8U" fc="SR" />
      <DA name="T" bType="Timestamp" fc="SR" />
      <DA name="Test" bType="BOOLEAN" fc="SR" />
      <DA name="Check" bType="Check" fc="SR" />
      <DA name="respAddCause" bType="Enum" type="AddCause" fc="SR" />
    </DOType>
    <DOType id="INC_CTS" cdc="CTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="ctlVal" bType="INT32" fc="SR" />
      <DA name="origin" bType="Struct" type="Originator" fc="SR" />
      <DA name="ctlNum" bType="INT8U" fc="SR" />
      <DA name="T" bType="Timestamp" fc="SR" />
      <DA name="Test" bType="BOOLEAN" fc="SR" />
      <DA name="Check" bType="Check" fc="SR" />
      <DA name="respAddCause" bType="Enum" type="AddCause" fc="SR" />
    </DOType>
    <DOType id="CTS" cdc="CTS">
      <DA name="objRef" bType="ObjRef" fc="SR" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="ctlVal" bType="BOOLEAN" fc="SR" />
      <DA name="operTm" bType="Timestamp" fc="SR" />
      <DA name="origin" bType="Struct" type="Originator" fc="SR" />
      <DA name="ctlNum" bType="INT8U" fc="SR" />
      <DA name="T" bType="Timestamp" fc="SR" />
      <DA name="Test" bType="BOOLEAN" fc="SR" />
      <DA name="Check" bType="Check" fc="SR" />
      <DA name="respAddCause" bType="Enum" type="AddCause" fc="SR" />
    </DOType>
    <DOType id="APC_CTS" cdc="CTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="ctlVal" bType="Struct" type="AnalogueValue" fc="SR" />
      <DA name="origin" bType="Struct" type="Originator" fc="SR" />
      <DA name="ctlNum" bType="INT8U" fc="SR" />
      <DA name="T" bType="Timestamp" fc="SR" />
      <DA name="Test" bType="BOOLEAN" fc="SR" />
      <DA name="Check" bType="Check" fc="SR" />
      <DA name="respAddCause" bType="Enum" type="AddCause" fc="SR" />
    </DOType>
    <DOType id="BSC_CTS" cdc="CTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="ctlVal" bType="Tcmd" fc="SR" />
      <DA name="origin" bType="Struct" type="Originator" fc="SR" />
      <DA name="ctlNum" bType="INT8U" fc="SR" />
      <DA name="T" bType="Timestamp" fc="SR" />
      <DA name="Test" bType="BOOLEAN" fc="SR" />
      <DA name="Check" bType="Check" fc="SR" />
      <DA name="respAddCause" bType="Enum" type="AddCause" fc="SR" />
    </DOType>
    <DOType id="ISC_CTS" cdc="CTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="ctlVal" bType="INT8" fc="SR" />
      <DA name="origin" bType="Struct" type="Originator" fc="SR" />
      <DA name="ctlNum" bType="INT8U" fc="SR" />
      <DA name="T" bType="Timestamp" fc="SR" />
      <DA name="Test" bType="BOOLEAN" fc="SR" />
      <DA name="Check" bType="Check" fc="SR" />
      <DA name="respAddCause" bType="Enum" type="AddCause" fc="SR" />
    </DOType>
    <DOType id="UTS" cdc="UTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="rptID" bType="VisString129" fc="SR" />
      <DA name="rptEna" bType="BOOLEAN" fc="SR" />
      <DA name="resv" bType="BOOLEAN" fc="SR" />
      <DA name="datSet" bType="ObjRef" fc="SR" />
      <DA name="confRev" bType="INT32U" fc="SR" />
      <DA name="optFlds" bType="OptFlds" fc="SR" />
      <DA name="bufTm" bType="INT32U" fc="SR" />
      <DA name="sqNum" bType="INT8U" fc="SR" />
      <DA name="trgOps" bType="TrgOps" fc="SR" />
      <DA name="intgPd" bType="INT32U" fc="SR" />
      <DA name="gi" bType="BOOLEAN" fc="SR" />
    </DOType>
    <DOType id="BTS" cdc="BTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="rptID" bType="VisString129" fc="SR" />
      <DA name="rptEna" bType="BOOLEAN" fc="SR" />
      <DA name="datSet" bType="ObjRef" fc="SR" />
      <DA name="confRev" bType="INT32U" fc="SR" />
      <DA name="optFlds" bType="OptFlds" fc="SR" />
      <DA name="bufTm" bType="INT32U" fc="SR" />
      <DA name="sqNum" bType="INT16U" fc="SR" />
      <DA name="trgOps" bType="TrgOps" fc="SR" />
      <DA name="intgPd" bType="INT32U" fc="SR" />
      <DA name="gi" bType="BOOLEAN" fc="SR" />
      <DA name="purgeBuf" bType="BOOLEAN" fc="SR" />
      <DA name="entryID" bType="EntryID" fc="SR" />
      <DA name="timeOfEntry" bType="EntryTime" fc="SR" />
      <DA name="resvTms" bType="INT16" fc="SR" />
    </DOType>
    <DOType id="LTS" cdc="LTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="logEna" bType="BOOLEAN" fc="SR" />
      <DA name="datSet" bType="ObjRef" fc="SR" />
      <DA name="optFlds" bType="OptFlds" fc="SR" />
      <DA name="bufTm" bType="INT32U" fc="SR" />
      <DA name="trgOps" bType="TrgOps" fc="SR" />
      <DA name="intgPd" bType="INT32U" fc="SR" />
      <DA name="logRef" bType="ObjRef" fc="SR" />
    </DOType>
    <DOType id="GTS" cdc="GTS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="goEna" bType="BOOLEAN" fc="SR" />
      <DA name="goID" bType="VisString129" fc="SR" />
      <DA name="datSet" bType="ObjRef" fc="SR" />
      <DA name="confRev" bType="INT32U" fc="SR" />
      <DA name="ndsCom" bType="BOOLEAN" fc="SR" />
      <DA name="dstAddress" bType="PhyComAddr" fc="SR" />
    </DOType>
    <DOType id="STS" cdc="STS">
      <DA name="objRef" bType="ObjRef" fc="SR" dupd="true" />
      <DA name="serviceType" bType="Enum" type="ServiceType" fc="SR" />
      <DA name="errorCode" bType="Enum" type="ServiceError" fc="SR" />
      <DA name="originatorID" bType="Octet64" fc="SR" />
      <DA name="t" bType="Timestamp" fc="SR" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
      <DA name="numOfSG" bType="INT8U" fc="SR" />
      <DA name="actSG" bType="INT8U" fc="SR" />
      <DA name="editSG" bType="INT8U" fc="SR" />
      <DA name="cnfEdit" bType="BOOLEAN" fc="SR" />
      <DA name="lActTm" bType="Timestamp" fc="SR" />
      <DA name="resvTms" bType="INT16U" fc="SR" />
    </DOType>
    <DOType id="SPS" cdc="SPS">
      <DA name="stVal" bType="BOOLEAN" fc="ST" dchg="true" />
      <DA name="q" bType="Quality" fc="ST" qchg="true" />
      <DA name="t" bType="Timestamp" fc="ST" />
      <DA name="subEna" bType="BOOLEAN" fc="SV" />
      <DA name="subVal" bType="BOOLEAN" fc="SV" />
      <DA name="subQ" bType="Quality" fc="SV" />
      <DA name="subID" bType="VisString64" fc="SV" />
      <DA name="blkEna" bType="BOOLEAN" fc="BL" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
    </DOType>
    <DOType id="INS" cdc="INS">
      <DA name="stVal" bType="INT32" fc="ST" dchg="true" dupd="true" />
      <DA name="q" bType="Quality" fc="ST" qchg="true" />
      <DA name="t" bType="Timestamp" fc="ST" />
      <DA name="subEna" bType="BOOLEAN" fc="SV" />
      <DA name="subVal" bType="INT32" fc="SV" />
      <DA name="subQ" bType="Quality" fc="SV" />
      <DA name="subID" bType="VisString64" fc="SV" />
      <DA name="blkEna" bType="BOOLEAN" fc="BL" />
      <DA name="units" bType="Struct" type="Unit" fc="CF" />
      <DA name="d" bType="VisString255" fc="DC" />
      <DA name="dU" bType="Unicode255" fc="DC" />
      <DA name="cdcName" bType="VisString255" fc="EX" />
    </DOType>
    <DOType id="DPC2" cdc="DPC">
      <DA name="origin" bType="Struct" type="Originator" fc="ST" />
      <DA name="stVal" bType="Dbpos" fc="ST" dchg="true" />
      <DA name="q" bType="Quality" fc="ST" qchg="true" />
      <DA name="t" bType="Timestamp" fc="ST" />
      <DA name="stSeld" bType="BOOLEAN" fc="ST" dchg="true" />
      <DA name="ctlModel" bType="Enum" type="CtlModelKind" fc="CF" dchg="true" />
      <DA name="sboTimeout" bType="INT32U" fc="CF" dchg="true" />
      <DA name="sboClass" bType="Enum" type="SboClassKind" fc="CF" dchg="true" />
      <DA name="operTimeout" bType="INT32U" fc="CF" dchg="true" />
      <DA name="SBO" bType="VisString129" fc="CO" />
      <DA name="SBOw" bType="Struct" type="SBOwBOOLEAN" fc="CO" />
      <DA name="Oper" bType="Struct" type="OperBOOLEAN" fc="CO" />
      <DA name="Cancel" bType="Struct" type="CancelBOOLEAN" fc="CO" />
    </DOType>
    <DAType id="Originator">
      <BDA name="orCat" bType="Enum" type="OriginatorCategoryKind" />
      <BDA name="orIdent" bType="Octet64" />
    </DAType>
    <DAType id="PulseConfig">
      <BDA name="cmdQual" bType="Enum" type="ControlOutputKind" />
      <BDA name="onDur" bType="INT32U" />
      <BDA name="offDur" bType="INT32U" />
      <BDA name="numPls" bType="INT32U" />
    </DAType>
    <DAType id="OperBOOLEAN">
      <BDA name="ctlVal" bType="BOOLEAN" />
      <BDA name="operTm" bType="Timestamp" />
      <BDA name="origin" bType="Struct" type="Originator" />
      <BDA name="ctlNum" bType="INT8U" />
      <BDA name="T" bType="Timestamp" />
      <BDA name="Test" bType="BOOLEAN" />
      <BDA name="Check" bType="Check" />
    </DAType>
    <DAType id="CancelBOOLEAN">
      <BDA name="ctlVal" bType="BOOLEAN" />
      <BDA name="operTm" bType="Timestamp" />
      <BDA name="origin" bType="Struct" type="Originator" />
      <BDA name="ctlNum" bType="INT8U" />
      <BDA name="T" bType="Timestamp" />
      <BDA name="Test" bType="BOOLEAN" />
    </DAType>
    <DAType id="Unit">
      <BDA name="SIUnit" bType="Enum" type="SIUnitKind" />
      <BDA name="multiplier" bType="Enum" type="MultiplierKind" />
    </DAType>
    <DAType id="AnalogueValue">
      <BDA name="f" bType="FLOAT32" />
    </DAType>
    <DAType id="ScaledValueConfig">
      <BDA name="scaleFactor" bType="FLOAT32" />
      <BDA name="offset" bType="FLOAT32" />
    </DAType>
    <DAType id="Vector">
      <BDA name="mag" bType="Struct" type="AnalogueValue" />
      <BDA name="ang" bType="Struct" type="AnalogueValue" />
    </DAType>
    <DAType id="RangeConfig_0">
      <BDA name="hhLim" bType="Struct" type="AnalogueValue" />
      <BDA name="hLim" bType="Struct" type="AnalogueValue" />
      <BDA name="lLim" bType="Struct" type="AnalogueValue" />
      <BDA name="llLim" bType="Struct" type="AnalogueValue" />
      <BDA name="min" bType="Struct" type="AnalogueValue" />
      <BDA name="max" bType="Struct" type="AnalogueValue" />
      <BDA name="limDb" bType="INT32U" />
    </DAType>
    <DAType id="SBOwBOOLEAN">
      <BDA name="ctlVal" bType="BOOLEAN" />
      <BDA name="operTm" bType="Timestamp" />
      <BDA name="origin" bType="Struct" type="Originator" />
      <BDA name="ctlNum" bType="INT8U" />
      <BDA name="T" bType="Timestamp" />
      <BDA name="Test" bType="BOOLEAN" />
      <BDA name="Check" bType="Check" />
      <ProtNs type="8-MMS">IEC 61850-8-1:2003</ProtNs>
    </DAType>
    <DAType id="CancelBOOLEAN1">
      <BDA name="ctlVal" bType="BOOLEAN" />
      <BDA name="operTm" bType="Timestamp" />
      <BDA name="origin" bType="Struct" type="Originator" />
      <BDA name="ctlNum" bType="INT8U" />
      <BDA name="T" bType="Timestamp" />
      <BDA name="Test" bType="BOOLEAN" />
      <ProtNs type="8-MMS">IEC 61850-8-1:2003</ProtNs>
    </DAType>
    <EnumType id="BehaviourModeKind">
      <EnumVal ord="1">on</EnumVal>
      <EnumVal ord="2">blocked</EnumVal>
      <EnumVal ord="3">test</EnumVal>
      <EnumVal ord="4">test/blocked</EnumVal>
      <EnumVal ord="5">off</EnumVal>
    </EnumType>
    <EnumType id="HealthKind">
      <EnumVal ord="1">Ok</EnumVal>
      <EnumVal ord="2">Warning</EnumVal>
      <EnumVal ord="3">Alarm</EnumVal>
    </EnumType>
    <EnumType id="CtlModelKind">
      <EnumVal ord="0">status-only</EnumVal>
      <EnumVal ord="1">direct-with-normal-security</EnumVal>
      <EnumVal ord="2">sbo-with-normal-security</EnumVal>
      <EnumVal ord="3">direct-with-enhanced-security</EnumVal>
      <EnumVal ord="4">sbo-with-enhanced-security</EnumVal>
    </EnumType>
    <EnumType id="WYE_angRef">
      <EnumVal ord="0">Va</EnumVal>
      <EnumVal ord="1">Vb</EnumVal>
      <EnumVal ord="2">Vc</EnumVal>
      <EnumVal ord="3">Aa</EnumVal>
      <EnumVal ord="4">Ab</EnumVal>
      <EnumVal ord="5">Ac</EnumVal>
      <EnumVal ord="6">Vab</EnumVal>
      <EnumVal ord="7">Vbc</EnumVal>
      <EnumVal ord="8">Vca</EnumVal>
      <EnumVal ord="9">Vother</EnumVal>
      <EnumVal ord="10">Aother</EnumVal>
    </EnumType>
    <EnumType id="SboClassKind">
      <EnumVal ord="0">operate-once</EnumVal>
      <EnumVal ord="1">operate-many</EnumVal>
    </EnumType>
    <EnumType id="BreakerOpCapabilityKind">
      <EnumVal ord="1">None</EnumVal>
      <EnumVal ord="2">Open</EnumVal>
      <EnumVal ord="3">Close-Open</EnumVal>
      <EnumVal ord="4">Open-Close-Open</EnumVal>
      <EnumVal ord="5">Close-Open-Close-Open</EnumVal>
      <EnumVal ord="6">Open-Close-Open-Close-Open</EnumVal>
      <EnumVal ord="7">more</EnumVal>
    </EnumType>
    <EnumType id="SwitchTypeKind">
      <EnumVal ord="1">Load Break</EnumVal>
      <EnumVal ord="2">Disconnector</EnumVal>
      <EnumVal ord="3">Earthing Switch</EnumVal>
      <EnumVal ord="4">High Speed Earthing Switch</EnumVal>
    </EnumType>
    <EnumType id="SwitchingCapabilityKind">
      <EnumVal ord="1">None</EnumVal>
      <EnumVal ord="2">Open</EnumVal>
      <EnumVal ord="3">Close</EnumVal>
      <EnumVal ord="4">Open and Close</EnumVal>
    </EnumType>
    <EnumType id="ServiceType">
      <EnumVal ord="0">Unknown</EnumVal>
      <EnumVal ord="1">Associate</EnumVal>
      <EnumVal ord="2">Abort</EnumVal>
      <EnumVal ord="3">Release</EnumVal>
      <EnumVal ord="4">GetServerDirectory</EnumVal>
      <EnumVal ord="5">GetLogicalDeviceDirectory</EnumVal>
      <EnumVal ord="6">GetAllDataValues</EnumVal>
      <EnumVal ord="7">GetDataValues</EnumVal>
      <EnumVal ord="8">SetDataValues</EnumVal>
      <EnumVal ord="9">GetDataDirectory</EnumVal>
      <EnumVal ord="10">GetDataDefinition</EnumVal>
      <EnumVal ord="11">GetDataSetValues</EnumVal>
      <EnumVal ord="12">SetDataSetValues</EnumVal>
      <EnumVal ord="13">CreateDataSet</EnumVal>
      <EnumVal ord="14">DeleteDataSet</EnumVal>
      <EnumVal ord="15">GetDataSetDirectory</EnumVal>
      <EnumVal ord="16">SelectActiveSG</EnumVal>
      <EnumVal ord="17">SelectEditSG</EnumVal>
      <EnumVal ord="18">SetEditSGValue</EnumVal>
      <EnumVal ord="19">ConfirmEditSGValues</EnumVal>
      <EnumVal ord="20">GetEditSGValue</EnumVal>
      <EnumVal ord="21">GetSGCBValues</EnumVal>
      <EnumVal ord="22">Report</EnumVal>
      <EnumVal ord="23">GetBRCBValues</EnumVal>
      <EnumVal ord="24">SetBRCBValues</EnumVal>
      <EnumVal ord="25">GetURCBValues</EnumVal>
      <EnumVal ord="26">SetURCBValues</EnumVal>
      <EnumVal ord="27">GetLCBValues</EnumVal>
      <EnumVal ord="28">SetLCBValues</EnumVal>
      <EnumVal ord="29">QueryLogByTime</EnumVal>
      <EnumVal ord="30">QueryLogAfter</EnumVal>
      <EnumVal ord="31">GetLogStatusValues</EnumVal>
      <EnumVal ord="32">SendGOOSEMessage</EnumVal>
      <EnumVal ord="33">GetGoCBValues</EnumVal>
      <EnumVal ord="34">SetGoCBValues</EnumVal>
      <EnumVal ord="35">GetGoReference</EnumVal>
      <EnumVal ord="36">GetGOOSEElementNumber</EnumVal>
      <EnumVal ord="37">SendMSVMessage</EnumVal>
      <EnumVal ord="38">GetMSVCBValues</EnumVal>
      <EnumVal ord="39">SetMSVCBValues</EnumVal>
      <EnumVal ord="40">SendUSVMessage</EnumVal>
      <EnumVal ord="41">GetUSVCBValues</EnumVal>
      <EnumVal ord="42">SetUSVCBValues</EnumVal>
      <EnumVal ord="43">Select</EnumVal>
      <EnumVal ord="44">SelectWithValue</EnumVal>
      <EnumVal ord="45">Cancel</EnumVal>
      <EnumVal ord="46">Operate</EnumVal>
      <EnumVal ord="47">CommandTermination</EnumVal>
      <EnumVal ord="48">TimeActivatedOperate</EnumVal>
      <EnumVal ord="49">GetFile</EnumVal>
      <EnumVal ord="50">SetFile</EnumVal>
      <EnumVal ord="51">DeleteFile</EnumVal>
      <EnumVal ord="52">GetFileAttributeValues</EnumVal>
      <EnumVal ord="53">TimeSynchronization</EnumVal>
      <EnumVal ord="54">InternalChange</EnumVal>
      <EnumVal ord="55">GetLogicalNodeDirectory</EnumVal>
    </EnumType>
    <EnumType id="ServiceError">
      <EnumVal ord="0">no-error</EnumVal>
      <EnumVal ord="1">instance-not-available</EnumVal>
      <EnumVal ord="2">instance-in-use</EnumVal>
      <EnumVal ord="3">access-violation</EnumVal>
      <EnumVal ord="4">access-not-allowed-in-current-state</EnumVal>
      <EnumVal ord="5">parameter-value-inappropriate</EnumVal>
      <EnumVal ord="6">parameter-value-inconsistent</EnumVal>
      <EnumVal ord="7">class-not-supported</EnumVal>
      <EnumVal ord="8">instance-locked-by-other-client</EnumVal>
      <EnumVal ord="9">control-must-be-selected</EnumVal>
      <EnumVal ord="10">type-conflict</EnumVal>
      <EnumVal ord="11">failed-due-to-communications-constraint</EnumVal>
      <EnumVal ord="12">failed-due-to-server-constraint</EnumVal>
    </EnumType>
    <EnumType id="AddCause">
      <EnumVal ord="0">Unknown</EnumVal>
      <EnumVal ord="1">Not-supported</EnumVal>
      <EnumVal ord="2">Blocked-by-switching-hierarchy</EnumVal>
      <EnumVal ord="3">Select-failed</EnumVal>
      <EnumVal ord="4">Invalid-position</EnumVal>
      <EnumVal ord="5">Position-reached</EnumVal>
      <EnumVal ord="6">Parameter-change-in-execution</EnumVal>
      <EnumVal ord="7">Step-limit</EnumVal>
      <EnumVal ord="8">Blocked-by-Mode</EnumVal>
      <EnumVal ord="9">Blocked-by-process</EnumVal>
      <EnumVal ord="10">Blocked-by-interlocking</EnumVal>
      <EnumVal ord="11">Blocked-by-synchrocheck</EnumVal>
      <EnumVal ord="12">Command-already-in-execution</EnumVal>
      <EnumVal ord="13">Blocked-by-health</EnumVal>
      <EnumVal ord="14">1-of-n-control</EnumVal>
      <EnumVal ord="15">Abortion-by-cancel</EnumVal>
      <EnumVal ord="16">Time-limit-over</EnumVal>
      <EnumVal ord="17">Abortion-by-trip</EnumVal>
      <EnumVal ord="18">Object-not-selected</EnumVal>
      <EnumVal ord="19">Object-already-selected</EnumVal>
      <EnumVal ord="20">No-access-authority</EnumVal>
      <EnumVal ord="21">Ended-with-overshoot</EnumVal>
      <EnumVal ord="22">Abortion-due-to-deviation</EnumVal>
      <EnumVal ord="23">Abortion-by-communication-loss</EnumVal>
      <EnumVal ord="24">Blocked-by-command</EnumVal>
      <EnumVal ord="25">None</EnumVal>
      <EnumVal ord="26">Inconsistent-parameters</EnumVal>
      <EnumVal ord="27">Locked-by-other-client</EnumVal>
    </EnumType>
    <EnumType id="RangeKind">
      <EnumVal ord="0">normal</EnumVal>
      <EnumVal ord="1">high</EnumVal>
      <EnumVal ord="2">low</EnumVal>
      <EnumVal ord="3">high-high</EnumVal>
      <EnumVal ord="4">low-low</EnumVal>
    </EnumType>
    <EnumType id="OriginatorCategoryKind">
      <EnumVal ord="0">not-supported</EnumVal>
      <EnumVal ord="1">bay-control</EnumVal>
      <EnumVal ord="2">station-control</EnumVal>
      <EnumVal ord="3">remote-control</EnumVal>
      <EnumVal ord="4">automatic-bay</EnumVal>
      <EnumVal ord="5">automatic-station</EnumVal>
      <EnumVal ord="6">automatic-remote</EnumVal>
      <EnumVal ord="7">maintenance</EnumVal>
      <EnumVal ord="8">process</EnumVal>
    </EnumType>
    <EnumType id="ControlOutputKind">
      <EnumVal ord="0">pulse</EnumVal>
      <EnumVal ord="1">persistent</EnumVal>
    </EnumType>
    <EnumType id="SIUnitKind">
      <EnumVal ord="1">No Units</EnumVal>
      <EnumVal ord="2">m</EnumVal>
      <EnumVal ord="3">kg</EnumVal>
      <EnumVal ord="4">s</EnumVal>
      <EnumVal ord="5">A</EnumVal>
      <EnumVal ord="6">K</EnumVal>
      <EnumVal ord="7">mol</EnumVal>
      <EnumVal ord="8">cd</EnumVal>
      <EnumVal ord="9">deg</EnumVal>
      <EnumVal ord="10">rad</EnumVal>
      <EnumVal ord="11">sr</EnumVal>
      <EnumVal ord="21">Gy</EnumVal>
      <EnumVal ord="22">Bq</EnumVal>
      <EnumVal ord="23">°C</EnumVal>
      <EnumVal ord="24">Sv</EnumVal>
      <EnumVal ord="25">F</EnumVal>
      <EnumVal ord="26">C</EnumVal>
      <EnumVal ord="27">S</EnumVal>
      <EnumVal ord="28">H</EnumVal>
      <EnumVal ord="29">V</EnumVal>
      <EnumVal ord="30">ohm</EnumVal>
      <EnumVal ord="31">J</EnumVal>
      <EnumVal ord="32">N</EnumVal>
      <EnumVal ord="33">Hz</EnumVal>
      <EnumVal ord="34">lx</EnumVal>
      <EnumVal ord="35">Lm</EnumVal>
      <EnumVal ord="36">Wb</EnumVal>
      <EnumVal ord="37">T</EnumVal>
      <EnumVal ord="38">W</EnumVal>
      <EnumVal ord="39">Pa</EnumVal>
      <EnumVal ord="41">m²</EnumVal>
      <EnumVal ord="42">m³</EnumVal>
      <EnumVal ord="43">m/s</EnumVal>
      <EnumVal ord="44">m/s²</EnumVal>
      <EnumVal ord="45">m³/s</EnumVal>
      <EnumVal ord="46">m/m³</EnumVal>
      <EnumVal ord="47">M</EnumVal>
      <EnumVal ord="48">kg/m³</EnumVal>
      <EnumVal ord="49">m²/s</EnumVal>
      <EnumVal ord="50">W/m K</EnumVal>
      <EnumVal ord="51">J/K</EnumVal>
      <EnumVal ord="52">ppm</EnumVal>
      <EnumVal ord="53">1/s</EnumVal>
      <EnumVal ord="54">rad/s</EnumVal>
      <EnumVal ord="61">VA</EnumVal>
      <EnumVal ord="62">Watts</EnumVal>
      <EnumVal ord="63">VAr</EnumVal>
      <EnumVal ord="64">phi</EnumVal>
      <EnumVal ord="65">cos(phi)</EnumVal>
      <EnumVal ord="66">Vs</EnumVal>
      <EnumVal ord="67">V²</EnumVal>
      <EnumVal ord="68">As</EnumVal>
      <EnumVal ord="69">A²</EnumVal>
      <EnumVal ord="70">A²t</EnumVal>
      <EnumVal ord="71">VAh</EnumVal>
      <EnumVal ord="72">Wh</EnumVal>
      <EnumVal ord="73">VArh</EnumVal>
      <EnumVal ord="74">V/Hz</EnumVal>
      <EnumVal ord="75">Hz/s</EnumVal>
      <EnumVal ord="76">char</EnumVal>
      <EnumVal ord="77">char/s</EnumVal>
      <EnumVal ord="78">kgm²</EnumVal>
      <EnumVal ord="79">dB</EnumVal>
      <EnumVal ord="80">J/Wh</EnumVal>
      <EnumVal ord="81">W/s</EnumVal>
      <EnumVal ord="82">l/s</EnumVal>
      <EnumVal ord="83">dBm</EnumVal>
    </EnumType>
    <EnumType id="MultiplierKind">
      <EnumVal ord="-24">y</EnumVal>
      <EnumVal ord="-21">z</EnumVal>
      <EnumVal ord="-18">a</EnumVal>
      <EnumVal ord="-15">f</EnumVal>
      <EnumVal ord="-12">p</EnumVal>
      <EnumVal ord="-9">n</EnumVal>
      <EnumVal ord="-6">µ</EnumVal>
      <EnumVal ord="-3">m</EnumVal>
      <EnumVal ord="-2">c</EnumVal>
      <EnumVal ord="-1">d</EnumVal>
      <EnumVal ord="0">No Multiplier</EnumVal>
      <EnumVal ord="1">da</EnumVal>
      <EnumVal ord="2">h</EnumVal>
      <EnumVal ord="3">k</EnumVal>
      <EnumVal ord="6">M</EnumVal>
      <EnumVal ord="9">G</EnumVal>
      <EnumVal ord="12">T</EnumVal>
      <EnumVal ord="15">P</EnumVal>
      <EnumVal ord="18">E</EnumVal>
      <EnumVal ord="21">Z</EnumVal>
      <EnumVal ord="24">Y</EnumVal>
    </EnumType>
    <EnumType id="CtlModelStatusOnlyKind">
      <EnumVal ord="0">status-only</EnumVal>
    </EnumType>
  </DataTypeTemplates>
</SCL>