// GTWSectorEditor.cpp: implementation of the GTWSectorEditor class.
//
//////////////////////////////////////////////////////////////////////
#include "stdafx.h"
#include "../GTWWebLib/HttpServerCommon.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif
#include "GTWMain.h"
#include "GTWSectorEditor.h"
#include "GTWSessionEditor.h"
#include "GTWdataTypeEditor.h"
#include "gateway/GTWLib/WinEventManager.h"

#include "gateway/GTWLib/GTWProtocol.h"
#include "gateway/GTWLib/GTWM14Sector.h"
#include "gateway/GTWLib/GTWS14Sector.h"
#include "tmwscl/i870/m101sctr.h"
#include "tmwscl/i870/s101sctr.h"
#include "tmwscl/i870/m104sctr.h"
#include "tmwscl/i870/s104sctr.h"
#include "tmwscl/i870/m103sctr.h"
#include "tmwscl/i870/m14brm.h"
#include "tmwscl/i870/m103brm.h"
#include "tmwscl/i870/i14auth.h"
#include "GTWconfigres.h"


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

GTWSectorEditor::GTWSectorEditor(const EditorCommandDTO &dto, GTWCollectionMember *pEditableObject, GTWSession *pSession, bool bAddMode)
  :
  GTWBaseEditor(dto, pEditableObject, bAddMode, -1, -1, -1),
  m_pSession(pSession)
{
  if (pSession != nullptr)
  {
    m_iSessionIndex = pSession->GetSessionIndex();
    m_protocol = GTWConfig::getSessionProtocol(m_iSessionIndex);
    getSectorIndex();

    GetDefaults();
  }
}

GTWSectorEditor::~GTWSectorEditor()
{

}

void GTWSectorEditor::GetDefaults()
{
#define SET_DEFAULT_STRING(source, value) source = value; source.Replace("\"","");

  bool bIsSlave = false;
  if (m_protocol == GTWTYPES_PROTOCOL_S101
    || m_protocol == GTWTYPES_PROTOCOL_S102
    || m_protocol == GTWTYPES_PROTOCOL_S103
    || m_protocol == GTWTYPES_PROTOCOL_S104
    )
  {
    bIsSlave = true;
  }
  bool bIs101 = false;
  if (m_protocol == GTWTYPES_PROTOCOL_S101
    || m_protocol == GTWTYPES_PROTOCOL_M101
    )
  {
    bIs101 = true;
  }

  m_iDbasSectorAddress = GTWConfig::DbasSectorAddress.TMWParam_get_default_value();
  
  m_iM14EOIactionMask = GTWConfig::M14ApplEoiActionMask.TMWParam_get_default_value();
  m_iM14OnlineActionMask = GTWConfig::M14ApplOnlineActionMask.TMWParam_get_default_value();
  
  m_iM103EOIactionMask = GTWConfig::M103ApplEoiActionMask.TMWParam_get_default_value();
  m_iM103OnlineActionMask = GTWConfig::M103ApplOnlineActionMask.TMWParam_get_default_value();
  m_iM103BlockingActionMask = GTWConfig::M103ApplBlokngActionMask.TMWParam_get_default_value();
  m_bI14AuthEnable = GTWConfig::I14AuthEnable.TMWParam_bool_get_default_value();

  //101/104 Secure Authentication Configuration
  if (bIs101)
    m_iM14AuthHMACAlgorithm = (GTWTYPES_I14AUTH_MAC_TYPE)I14AUTH_MAC_SHA256_8OCTET;
  else
    m_iM14AuthHMACAlgorithm = (GTWTYPES_I14AUTH_MAC_TYPE)GTWConfig::I14AuthHMACAlgorithm.TMWParam_enum_get_default_value();
  m_iM14AuthReplyTimeout = GTWConfig::I14AuthReplyTimeout.TMWParam_get_default_value();
  m_iM14AuthKeyChangeInterval = GTWConfig::I14AuthKeyChangeInterval.TMWParam_get_default_value();
  m_iM14AuthMaxKeyChangeCount = GTWConfig::I14AuthMaxKeyChangeCount.TMWParam_get_default_value();
  if (bIsSlave)
  {
    m_iM14AuthKeyChangeInterval *= 2;
    m_iM14AuthMaxKeyChangeCount *= 2;
  }
  m_iM14AuthRandomChallengeDataLength = GTWConfig::I14AuthRandomChallengeDataLength.TMWParam_get_default_value();
  m_iM14AuthSecurityStatsIOA = GTWConfig::I14AuthSecurityStatsIOA.TMWParam_get_default_value();
  m_bM14AuthExtraDiags = GTWConfig::I14AuthExtraDiags.TMWParam_bool_get_default_value();
  SET_DEFAULT_STRING(m_sCritRequests, GTWConfig::I14AuthCriticalRequests.TMWParam_get_default_string());
  SET_DEFAULT_STRING(m_sCritResponses, GTWConfig::I14AuthCriticalResponses.TMWParam_get_default_string());
  //101/104 Secure Authentication Users
  m_sM14AuthUserKey = GTWConfig::I14AuthUserKey.TMWParam_get_default_string();
  m_iM14UserRole = GTWConfig::I14AuthUserRole.TMWParam_get_default_value();
  m_iM14AuthUserNumber = I14AUTH_USERNUMBER_DEFAULT;//GTWConfig::I14AuthUserNumber.TMWParam_get_default_value();
  m_sM14AuthUserName = GTWConfig::I14AuthUserName.TMWParam_get_default_string();
}

TMWTYPES_BOOL GTWSectorEditor::BuildSelPopupMenu(int *id,CMenuEntryArray *pMenuEntries)
{
  GTWSector *pSector = (GTWSector*)GetEditableObject();

  //if (pSector)
  //{
	 // GTWChannel *pChannel = pSector->GetChannel();
	 // if (pChannel && pChannel->IsInternalChannel())
	 // {
	 //   return TMWDEFS_FALSE;
	 // }
  //}


  GTWTYPES_PROTOCOL protocol = GetProtocol();
  CMenuEntry menuEntry1("Edit Sector","Edit a Gateway Sector","Edit Sector text",GetEditableObject(),++(*id),MENU_CMD_EDIT);
  pMenuEntries->push_back(menuEntry1);

  CMenuEntry menuEntry2("Delete Sector","Delete a Gateway Sector","Delete Sector text",GetEditableObject(),++(*id),MENU_CMD_DELETE);
  pMenuEntries->push_back(menuEntry2);

  CMenuEntry menuEntry3("Add Data Type","Add a Data Type","Add Data Type text",GetEditableObject(),++(*id),MENU_CMD_ADD_DATA_TYPE);
  pMenuEntries->push_back(menuEntry3);

#ifdef _DEBUG
  pMenuEntries->push_back(CMenuEntry("Create TH XML File (output to C:\\test_harness_points.xml)", "", "", GetEditableObject(), ++(*id), MENU_CMD_CREATE_THXML_POINT_FILE));
  pMenuEntries->push_back(CMenuEntry("Create DTM csv File (output to C:\\dtm_points.csv)", "", "", GetEditableObject(), ++(*id), MENU_CMD_CREATE_DTM_CSV_POINT_FILE));
#endif
  
  switch (protocol)
  {
    case GTWTYPES_PROTOCOL_M101:
    case GTWTYPES_PROTOCOL_M102:
    case GTWTYPES_PROTOCOL_M103:
    case GTWTYPES_PROTOCOL_M104:
    case GTWTYPES_PROTOCOL_MDNP:
      {
        CMenuEntry menuEntry5("Auto Create Tags", "Auto create tags for this sector", "Auto create tags for this sector text", GetEditableObject(),++ (*id), MENU_CMD_AUTO_CREATE_TAGS);
        pMenuEntries->push_back(menuEntry5);
      }
      break;
  }
  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTWSectorEditor::MiscCommand(MENUENTRY_EDIT_CMD cmd)
{
  GTWSector *pSec = (GTWSector*)GetEditableObject();
  switch (cmd)
  {
  case MENU_CMD_AUTO_CREATE_TAGS:
  {
    return (pSec->CreateTagsAuto(SendBroadcastRefreshUI, &m_dto.token));
    break;
  }
#ifdef _DEBUG
  case MENU_CMD_CREATE_THXML_POINT_FILE:
    pSec->CreateTHXMLFile("C:\\test_harness_points.xml");
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_870, GetDTOToken(),
                                     "TR_CREATE_HTML_FILE", "Created C:\\test_harness_points.xml");
    break;

  case MENU_CMD_CREATE_DTM_CSV_POINT_FILE:
    pSec->CreateDTMCSVFile("C:\\dtm_points.csv");
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_870, GetDTOToken(),
                                     "TR_CREATE_DTM_POINT", "Created C:\\dtm_points.csv");
    break;
#endif
  break;
  }
  return TMWDEFS_FALSE;
}


void GTWSectorEditor::SendBroadcastRefreshUI(void *pCreateTagsCBparam)
{
  std::string* token = (std::string*)pCreateTagsCBparam;

  GtwBroadcastMessage::SendRefreshUI(token->c_str(), nullptr, nullptr);

  GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Status_Bar, BroadcastTypeEnum::Broadcast_Success, GtwLogger::SDG_Category_Editor, (token ? token->c_str() : nullptr), "TR_SUCCESS", "Success");
}

void GTWSectorEditor::DeleteINIparms()
{
  GetDefaults();
  SaveObject();

  GTWConfig::DbasSectorAddress.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(),GetSectorIndex());

  GTWConfig::M14ApplEoiActionMask.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(),GetSectorIndex());
  GTWConfig::M14ApplOnlineActionMask.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(),GetSectorIndex());
  
  GTWConfig::M103ApplEoiActionMask.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(),GetSectorIndex());
  GTWConfig::M103ApplOnlineActionMask.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(),GetSectorIndex());
  GTWConfig::M103ApplBlokngActionMask.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(),GetSectorIndex());

  GTWConfig::I14AuthEnable.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthHMACAlgorithm.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthKeyWrapAlgorithm.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthReplyTimeout.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthKeyChangeInterval.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthMaxKeyChangeCount.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthExtraDiags.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthAssocId.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthRandomChallengeDataLength.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthSecurityStatsIOA.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthSessionKeyLength.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthMaxSessionKeyStatusCount.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthCriticalRequests.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());
  GTWConfig::I14AuthCriticalResponses.TMWParam_mark_element_as_not_being_specified(GetSessionIndex(), GetSectorIndex());

  for (int userIndex = 0; userIndex< getGTKTPARM_MAX_USERS_PER_SECTOR(); userIndex++)
  {
    GTWConfig::I14AuthUserKey.TMWParam_mark_element_as_not_being_specified(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), GetSectorIndex(), userIndex));
    GTWConfig::I14AuthUserNumber.TMWParam_mark_element_as_not_being_specified(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), GetSectorIndex(), userIndex));
    GTWConfig::I14AuthUserName.TMWParam_mark_element_as_not_being_specified(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), GetSectorIndex(), userIndex));
    GTWConfig::I14AuthUserRole.TMWParam_mark_element_as_not_being_specified(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), GetSectorIndex(), userIndex));
  }
}

TMWTYPES_BOOL GTWSectorEditor::DeleteObject(GTWCollectionMember **pRefreshMember, bool bAskIfOk /*= true*/)
{
  GTWSector* pSector = (GTWSector*)GetEditableObject();

  pSector->SCLcloseSector();

  GTWCollectionBase* pClctn = pSector->GetParentCollection();

  if (pClctn->RemoveCollectionMember(pSector))
  {
    DeleteINIparms();

    pSector->DeleteCollectionMember();

    return true;
  }
  return false;
}

TMWTYPES_BOOL GTWSectorEditor::AddSector(GTWSession *pSession, TMWTYPES_BOOL bCreateTags)
{
  GTWDEFS_STAT status;
  GTWCollectionBase *pClctn;
  
  pClctn = pSession->GetMemberCollection();
  CStdString sectorName = "";
  sectorName.Format("%c%lu", GTWCNFG_PHYS_ASDU_ADDR_SEP, GetDbasSectorAddress());

  GTWBaseDataObject     *ppBdo;
  status = pSession->CreateBdoUsingName(TMWDEFS_FALSE, sectorName, &ppBdo, TMWDEFS_TRUE);
  GTWSector *pSector = (GTWSector *)ppBdo;

  if (pSector != NULL && status == GTWDEFS_STAT_SUCCESS)
  {
    SetEditableObject(pSector);
    return TMWDEFS_TRUE;
  }
  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTWSectorEditor::AddObject(MENUENTRY_EDIT_CMD cmd)
{
  return TMWDEFS_FALSE;
}

CStdString GTWSectorEditor::GetDescription(void)
{
  GTWSector *pSector = (GTWSector *)GetEditableObject();
  bool bRedundant = pSector ? pSector->GetChannel()->IsRedundant() : false;

  CStdString str;
  GTWProtocol *pProtocol = GTWProtocol::GetProtocol(GetProtocol());
  CStdString protoName = pProtocol->GetShortName();
  if (bRedundant)
  {
    protoName += " (Redundant)";
  }
  str.Format("Protocol = %s,Addr = %d",protoName,m_iDbasSectorAddress);
  return str;
}

TMWTYPES_BOOL GTWSectorEditor::EditObject()
{
  //GTWTYPES_PROTOCOL protocol = GetProtocol();
  //switch (protocol)
  //{
  //  case GTWTYPES_PROTOCOL_S101:
  //  case GTWTYPES_PROTOCOL_S104:
  //    {
  //      GTWS14SectorEditorDlg dlg(this,getAddMode());

  //      dlg.setAddMode(getAddMode());
  //      dlg.m_iDbasSectorAddress = m_iDbasSectorAddress;
  //      dlg.m_bEnableSA = m_bI14AuthEnable;

  //      if (dlg.DoModal() == IDOK)
  //      {
  //        m_iDbasSectorAddress = dlg.m_iDbasSectorAddress;

  //        /* When turning off secure authentication, make sure there are no mapped security MDOs */
  //        if (m_bI14AuthEnable && !dlg.m_bEnableSA)
  //        {
  //          GTWS14Sector *pSector = (GTWS14Sector*)GetEditableObject();
  //          GTWCollectionMember *pMember = pSector->GetS14MappedSecurityMdo();
  //          if (pMember)
  //          {
  //            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, GetDTOToken(), "TR_??", "Error: Secure authentication SDO %s is mapped, can not disable secure authentication", (const char *)pMember->GetFullName());
  //            return TMWDEFS_FALSE;
  //          }
  //        }
  //        m_bI14AuthEnable = dlg.m_bEnableSA;

  //        if (getAddMode() == TMWDEFS_FALSE)
  //        {
  //          ModifyObject();
  //        }
  //        return TRUE;
  //      }
  //    }
  //    break;
  //  case GTWTYPES_PROTOCOL_M101:
  //  case GTWTYPES_PROTOCOL_M104:
  //    {
  //      GTWM14SectorEditorDlg dlg(this,getAddMode());

  //      dlg.setAddMode(getAddMode());
  //      dlg.m_iDbasSectorAddress = m_iDbasSectorAddress;
  //      dlg.m_iEOIactionMask = m_iM14EOIactionMask;
  //      dlg.m_iOnlineActionMask = m_iM14OnlineActionMask;
  //      dlg.m_bEnableSA = m_bI14AuthEnable;

  //      if (dlg.DoModal() == IDOK)
  //      {
  //        m_iDbasSectorAddress = dlg.m_iDbasSectorAddress;
  //        m_iM14EOIactionMask = dlg.m_iEOIactionMask;
  //        m_iM14OnlineActionMask = dlg.m_iOnlineActionMask;

  //        /* When turning off secure authentication, make sure there are no mapped security MDOs */
  //        if (m_bI14AuthEnable && !dlg.m_bEnableSA)
  //        {
  //          GTWM14Sector *pSector = (GTWM14Sector*)GetEditableObject();
  //          GTWCollectionMember *pMember = pSector->GetM14MappedSecurityMdo();
  //          if (pMember)
  //          {
  //            GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_General, GetDTOToken(), "TR_??", "Error: Secure authentication MDO %s is mapped, can not disable secure authentication", (const char *)pMember->GetFullName());
  //            return TMWDEFS_FALSE;
  //          }
  //        }

  //        m_bI14AuthEnable = dlg.m_bEnableSA;

  //        //setXmlFileName(dlg.m_sXmlFile);
  //        if (getAddMode() == TMWDEFS_FALSE)
  //        {
  //          ModifyObject();
  //        }
  //        return TRUE;
  //      }
  //    }
  //    break;
  //  case GTWTYPES_PROTOCOL_M103:
  //    {
  //      GTWM103SectorEditorDlg dlg(this,getAddMode());

  //      dlg.setAddMode(getAddMode());
  //      dlg.m_iDbasSectorAddress = m_iDbasSectorAddress;
  //      dlg.m_iEOIactionMask = m_iM103EOIactionMask;
  //      dlg.m_iOnlineActionMask = m_iM103OnlineActionMask;
  //      dlg.m_iBlockingActionMask = m_iM103BlockingActionMask;

  //      if (dlg.DoModal() == IDOK)
  //      {
  //        m_iDbasSectorAddress = dlg.m_iDbasSectorAddress;
  //        m_iM103EOIactionMask = dlg.m_iEOIactionMask;
  //        m_iM103OnlineActionMask = dlg.m_iOnlineActionMask;
  //        m_iM103BlockingActionMask = dlg.m_iBlockingActionMask;
  //        if (getAddMode() == TMWDEFS_FALSE)
  //        {
  //          ModifyObject();
  //        }
  //        return TRUE;
  //      }
  //    }
  //    break;
  //}
  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTWSectorEditor::ModifyObject(void)
{
  GTWSector *pSector = (GTWSector *)GetEditableObject();
  if (pSector && pSector->IsA("GTWSector"))
  {
    pSector->m_SectorFullName = "";
    return pSector->SCLmodifySector(
         m_iDbasSectorAddress,
         m_iM14EOIactionMask,
         m_iM14OnlineActionMask,
         m_iM103EOIactionMask,
         m_iM103OnlineActionMask,
         m_iM103BlockingActionMask
      );
  }
  return TMWDEFS_FALSE;
}

void GTWSectorEditor::SetObject(TMWTYPES_USHORT sectorIndex)
{
  GTWConfig::DbasSectorAddress[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(),sectorIndex)] = m_iDbasSectorAddress;

  GTWConfig::M14ApplEoiActionMask[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(),sectorIndex)] = m_iM14EOIactionMask;
  GTWConfig::M14ApplOnlineActionMask[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(),sectorIndex)] = m_iM14OnlineActionMask;

  GTWConfig::M103ApplEoiActionMask[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(),sectorIndex)] = m_iM103EOIactionMask;
  GTWConfig::M103ApplOnlineActionMask[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(),sectorIndex)] = m_iM103OnlineActionMask;
  GTWConfig::M103ApplBlokngActionMask[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(),sectorIndex)] = m_iM103BlockingActionMask;
  GTWConfig::I14AuthEnable[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_bI14AuthEnable == 0 ? false : true;

  //101/104 Secure Authentication Configuration
  GTWConfig::I14AuthHMACAlgorithm[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14AuthHMACAlgorithm;
  GTWConfig::I14AuthReplyTimeout[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14AuthReplyTimeout;
  GTWConfig::I14AuthKeyChangeInterval[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14AuthKeyChangeInterval;
  GTWConfig::I14AuthMaxKeyChangeCount[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14AuthMaxKeyChangeCount;
  GTWConfig::I14AuthRandomChallengeDataLength[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14AuthRandomChallengeDataLength;
  GTWConfig::I14AuthSecurityStatsIOA[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14AuthSecurityStatsIOA;
  GTWConfig::I14AuthExtraDiags[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_bM14AuthExtraDiags;
  GTWConfig::I14AuthCriticalRequests.SetAt((TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)), m_sCritRequests);
  GTWConfig::I14AuthCriticalResponses.SetAt((TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)), m_sCritResponses);

  //101/104 Secure Authentication Users
  //GTWConfig::I14AuthUserKey[(TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(m_iSessionIndex, m_iSectorIndex, 0))] = (const char *)m_sM14AuthUserKey;
  //GTWConfig::I14AuthUserRole[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14UserRole;
  //GTWConfig::I14AuthUserNumber[GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), sectorIndex)] = m_iM14AuthUserNumber;
  //GTWConfig::I14AuthUserName[(TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(m_iSessionIndex, m_iSectorIndex, 0))] = (const char *)m_sM14AuthUserName;

  GTWConfig::I14AuthUserKey.SetAt((TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), sectorIndex, 0)), m_sM14AuthUserKey);
  GTWConfig::I14AuthUserNumber[(TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), sectorIndex, 0))] = m_iM14AuthUserNumber;
  GTWConfig::I14AuthUserName.SetAt((TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), sectorIndex, 0)), m_sM14AuthUserName);
  GTWConfig::I14AuthUserRole[(TMWTYPES_USHORT)(GTKTPARM_SESSION_SECTOR_AUTH_USER_INDEX(GetSessionIndex(), sectorIndex, 0))] = m_iM14UserRole;
  
}

TMWTYPES_BOOL GTWSectorEditor::SaveObject()
{
  TMWTYPES_USHORT index = 0;
  if (getAddMode())
    index = GetNextSectorIndex();
  else
    index = GetSectorIndex();

  SetObject(index);

  return SaveAdvancedEditorFields(GTKTPARM_SESSION_SECTOR_INDEX(GetSessionIndex(), GetSectorIndex()));
}

TMWTYPES_BOOL GTWSectorEditor::LoadObject()
{
  m_iDbasSectorAddress = GTWConfig::DbasSectorAddress(GetSessionIndex(),GetSectorIndex());
  
  m_iM14EOIactionMask = GTWConfig::M14ApplEoiActionMask(GetSessionIndex(),GetSectorIndex());
  m_iM14OnlineActionMask = GTWConfig::M14ApplOnlineActionMask(GetSessionIndex(),GetSectorIndex());

  m_iM103EOIactionMask = GTWConfig::M103ApplEoiActionMask(GetSessionIndex(),GetSectorIndex());
  m_iM103OnlineActionMask = GTWConfig::M103ApplOnlineActionMask(GetSessionIndex(),GetSectorIndex());
  m_iM103BlockingActionMask = GTWConfig::M103ApplBlokngActionMask(GetSessionIndex(),GetSectorIndex());
  if (GTWSession::GetS14SALicensed() == true)
  {
    m_bI14AuthEnable = GTWConfig::I14AuthEnable(GetSessionIndex(), GetSectorIndex());
  }

  //101/104 Secure Authentication Configuration
  m_iM14AuthHMACAlgorithm = (GTWTYPES_I14AUTH_MAC_TYPE)GTWConfig::I14AuthHMACAlgorithm(GetSessionIndex(), GetSectorIndex());
  m_iM14AuthReplyTimeout = GTWConfig::I14AuthReplyTimeout(GetSessionIndex(), GetSectorIndex());
  m_iM14AuthKeyChangeInterval = GTWConfig::I14AuthKeyChangeInterval(GetSessionIndex(), GetSectorIndex());
  m_iM14AuthMaxKeyChangeCount = GTWConfig::I14AuthMaxKeyChangeCount(GetSessionIndex(), GetSectorIndex());
  m_iM14AuthRandomChallengeDataLength = GTWConfig::I14AuthRandomChallengeDataLength(GetSessionIndex(), GetSectorIndex());
  m_iM14AuthSecurityStatsIOA = GTWConfig::I14AuthSecurityStatsIOA(GetSessionIndex(), GetSectorIndex());
  m_bM14AuthExtraDiags = GTWConfig::I14AuthExtraDiags(GetSessionIndex(), GetSectorIndex());
  //101/104 Secure Authentication Users
  m_sM14AuthUserKey = GTWConfig::I14AuthUserKey(GetSessionIndex(), GetSectorIndex());
  m_iM14AuthUserNumber = GTWConfig::I14AuthUserNumber(GetSessionIndex(), GetSectorIndex());
  m_sM14AuthUserName = GTWConfig::I14AuthUserName(GetSessionIndex(), GetSectorIndex());

  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTWSectorEditor::WebReadObject(nlohmann::json &pt, bool bEditAtRuntime)
{
  schema.clear();
  children.clear();

  GTWBaseEditor::WebReadObject(pt, bEditAtRuntime);

  if (!m_bAddMode && LoadObject() == TMWDEFS_FALSE)
    return false;
  try
  {

    AddTMWParamEditorField(schema, children, &GTWConfig::DbasSectorAddress,
      std::to_string(m_iDbasSectorAddress),
      (EDITOR_CONTROL_TYPE::NUMBER),
      "",
      true
    );
    if (m_protocol == GTWTYPES_PROTOCOL_M101 || m_protocol == GTWTYPES_PROTOCOL_M104)
    {
      AddM14EditorFields();
      AddSecureAuthEditorFields();
      AddAdvancedEditorFields(GTKTPARM_SESSION_SECTOR_INDEX(this->GetSessionIndex(), this->GetSectorIndex()), schema, children, EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_SECTOR | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_CLIENT | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_101_PROTOCOL | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_104_PROTOCOL);
    }
    if (m_protocol == GTWTYPES_PROTOCOL_S101 || m_protocol == GTWTYPES_PROTOCOL_S104)
    {
      AddSecureAuthEditorFields();
      AddAdvancedEditorFields(GTKTPARM_SESSION_SECTOR_INDEX(this->GetSessionIndex(), this->GetSectorIndex()), schema, children, EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_SECTOR | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_SERVER | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_101_PROTOCOL | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_104_PROTOCOL);
    }
    if (m_protocol == GTWTYPES_PROTOCOL_M103)
    {
      AddM103EditorFields();
      AddAdvancedEditorFields(GTKTPARM_SESSION_SECTOR_INDEX(this->GetSessionIndex(), this->GetSectorIndex()), schema, children, EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_SECTOR | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_CLIENT | EDITOR_TYPE_MASK_ENUM::EDITOR_TYPE_MASK_103_PROTOCOL);
    }

    AddHiddenEditorField(schema, children,
      "objectName",
      m_dto.objectName
    );

    AddHiddenEditorField(schema, children,
      "protocol",
      GetGtwTypesProtocol(m_protocol)
    );

    std::string json = schema.dump();
    pt["objectDataJson"] = json;
    pt["editorType"] = "GTWSectorEditor";
    pt += nlohmann::json::object_t::value_type("children", children);
  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Editor, nullptr, "Exception: %s", e.what());
    return TMWDEFS_FALSE;
  }
  return TMWDEFS_TRUE;
}

void GTWSectorEditor::AddM103EditorFields()
{
  AddTMWParamEditorField(schema, children, &GTWConfig::M103ApplEoiActionMask,
    std::to_string(m_iM103EOIactionMask),
    (EDITOR_CONTROL_TYPE::MASKS_EDITOR),
    "",
    true
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::M103ApplOnlineActionMask,
    std::to_string(m_iM103OnlineActionMask),
    (EDITOR_CONTROL_TYPE::MASKS_EDITOR),
    "",
    true
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::M103ApplBlokngActionMask,
    std::to_string(m_iM103BlockingActionMask),
    (EDITOR_CONTROL_TYPE::MASKS_EDITOR),
    "",
    true
  );
}


void GTWSectorEditor::AddM14EditorFields()
{
  AddTMWParamEditorField(schema, children, &GTWConfig::M14ApplEoiActionMask,
    std::to_string(m_iM14EOIactionMask),
    (EDITOR_CONTROL_TYPE::MASKS_EDITOR),
    "",
    true
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::M14ApplOnlineActionMask,
    std::to_string(m_iM14OnlineActionMask),
    (EDITOR_CONTROL_TYPE::MASKS_EDITOR),
    "",
    true
  );
}

void GTWSectorEditor::AddSecureAuthEditorFields()
{
  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthEnable,
    std::to_string(m_bI14AuthEnable),
    (EDITOR_CONTROL_TYPE::CHECKBOX),
    "",
    false
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthHMACAlgorithm,
    GTWTYPES_I14AUTH_MAC_TYPE_to_string(m_iM14AuthHMACAlgorithm),
    (EDITOR_CONTROL_TYPE::COMBOBOX),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthReplyTimeout,
    std::to_string(m_iM14AuthReplyTimeout),
    (EDITOR_CONTROL_TYPE::NUMBER),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthKeyChangeInterval,
    std::to_string(m_iM14AuthKeyChangeInterval),
    (EDITOR_CONTROL_TYPE::NUMBER),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthMaxKeyChangeCount,
    std::to_string(m_iM14AuthMaxKeyChangeCount),
    (EDITOR_CONTROL_TYPE::NUMBER),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthRandomChallengeDataLength,
    std::to_string(m_iM14AuthRandomChallengeDataLength),
    (EDITOR_CONTROL_TYPE::NUMBER),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthSecurityStatsIOA,
    std::to_string(m_iM14AuthSecurityStatsIOA),
    (EDITOR_CONTROL_TYPE::NUMBER),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthExtraDiags,
    std::to_string(m_bM14AuthExtraDiags),
    (EDITOR_CONTROL_TYPE::CHECKBOX),
    "",
    false,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthUserKey,
    m_sM14AuthUserKey,
    (EDITOR_CONTROL_TYPE::TEXT),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthUserNumber,
    std::to_string(m_iM14AuthUserNumber),
    (EDITOR_CONTROL_TYPE::NUMBER),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );

  AddTMWParamEditorField(schema, children, &GTWConfig::I14AuthUserName,
    m_sM14AuthUserName,
    (EDITOR_CONTROL_TYPE::TEXT),
    "",
    true,
    "TR_AUTH_CONFIG",
    ""
  );
}

TMWTYPES_BOOL GTWSectorEditor::ValidateObject()
{
  GTWSession *pSession = m_pSession;
  if (pSession)
  {
    GTWCollectionMember *member;
    int iAddr = GetDbasSectorAddress();
    CStdString sectorName = "";
      sectorName.Format("%c%lu", GTWCNFG_PHYS_ASDU_ADDR_SEP, iAddr);

    if (FindBdoUsingName(sectorName, &member) == GTWDEFS_STAT_SUCCESS && m_bAddMode)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_Editor, GetDTOToken(),
        "TR_SECTOR_DUPLICATE", "Failed to add sector. Sector at address {{arg1}} already exists.", sectorName.c_str());
      return TMWDEFS_FALSE;
    }
  }
  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTWSectorEditor::UpdateObject()
{
  try
  {
    m_iDbasSectorAddress = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::DbasSectorAddress)].get<std::string>().c_str());
    if (m_protocol == GTWTYPES_PROTOCOL_M101 || m_protocol == GTWTYPES_PROTOCOL_M104)
    {
      m_iM14EOIactionMask = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::M14ApplEoiActionMask)].get<std::string>().c_str());
      m_iM14OnlineActionMask = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::M14ApplOnlineActionMask)].get<std::string>().c_str());
    }
    if (m_protocol == GTWTYPES_PROTOCOL_M101 || m_protocol == GTWTYPES_PROTOCOL_M104 || m_protocol == GTWTYPES_PROTOCOL_S101 || m_protocol == GTWTYPES_PROTOCOL_S104)
    {
      m_bI14AuthEnable = GetBoolValueFromString(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthEnable)].get<std::string>());

      //101/104 Secure Authentication Configuration
      m_iM14AuthHMACAlgorithm = GTWTYPES_I14AUTH_MAC_TYPE_to_enum(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthHMACAlgorithm)].get<std::string>().c_str());
      m_iM14AuthReplyTimeout = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthReplyTimeout)].get<std::string>().c_str());
      m_iM14AuthKeyChangeInterval = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthKeyChangeInterval)].get<std::string>().c_str());
      m_iM14AuthMaxKeyChangeCount = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthMaxKeyChangeCount)].get<std::string>().c_str());
      m_iM14AuthRandomChallengeDataLength = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthRandomChallengeDataLength)].get<std::string>().c_str());
      m_iM14AuthSecurityStatsIOA = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthSecurityStatsIOA)].get<std::string>().c_str());
      m_bM14AuthExtraDiags = GetBoolValueFromString(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthExtraDiags)].get<std::string>().c_str());
      //101/104 Secure Authentication Users
      m_sM14AuthUserKey = m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthUserKey)].get<std::string>().c_str();
      m_iM14AuthUserNumber = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthUserNumber)].get<std::string>().c_str());
      m_sM14AuthUserName = m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::I14AuthUserName)].get<std::string>().c_str();
    }
    if (m_protocol == GTWTYPES_PROTOCOL_M103)
    {
      m_iM103EOIactionMask = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::M103ApplEoiActionMask)].get<std::string>().c_str());
      m_iM103OnlineActionMask = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::M103ApplOnlineActionMask)].get<std::string>().c_str());
      m_iM103BlockingActionMask = ConvertStringToInt(m_dto.pt_objectDataJson[GTWConfig::GetParamName(GTWConfig::M103ApplBlokngActionMask)].get<std::string>().c_str());
    }

    UpdateAdvancedEditorFields();

    if (!ValidateObject())
    {
      return TMWDEFS_FALSE;
    }

  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_Editor, nullptr, "Exception: %s", e.what());
    return TMWDEFS_FALSE;
  }
  return TMWDEFS_TRUE;
}

CStdString GTWSectorEditor::IsEditableAtRuntime(TMWTYPES_UINT ui_id)
{
  // this is not thread safe so lock it
  tmw::CriticalSectionLock lock(IsEditableAtRuntimeLock);

  InitAtRuntime(ui_id);

  if (m_bEditAtRunTime)
  { // grey the controls that can not be edited at runtime
    GetDlgItem(IDC_ENABLE_SA_CHK)->EnableWindow(false);
    GetDlgItem(IDC_SECTOR_ADDRESS_EC)->EnableWindow(false);
  }
  else
  {
    GetDlgItem(IDC_ENABLE_SA_CHK)->EnableWindow(GTWSession::GetM14SALicensed());
    GetDlgItem(IDC_SECTOR_ADDRESS_EC)->EnableWindow(true);
  }

  return GTWBaseEditor::IsEditableAtRuntime(ui_id);
}

