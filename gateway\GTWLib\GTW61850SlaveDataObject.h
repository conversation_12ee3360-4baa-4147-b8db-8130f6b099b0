/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright(c) 1997 - 2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTW61850sdo.h                                                 */
/* DESCRIPTION:  Definitions of an OPC - specific SDO class                    */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com(919) 870 - 6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 22 ***_NOBODY_*** "                                                     */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#pragma once

#include "GTWConverter.h"

#include "GTWSlaveDataObjectTemplate.h"
#include "gateway/GTWOsUtils/GtwOsDateTime.h"

class GTW61850SlaveDataObject;  // pre-define
class GTW61850WriteConverter;
class GTW61850Server;

/***************************************************************************/

class GTW61850ReadConverter
{
public:
  GTW61850ReadConverter() 
  {
    pSrcMdo = nullptr;
  }
  virtual ~GTW61850ReadConverter() 
  {
  }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject *pOpcSdo, GTWDEFS_STD_QLTY *stdQuality) = 0;
  
  GTW61850DataAttributeMDO* pSrcMdo;
  
protected:

  virtual void Delete(void) = 0;
};

/***************************************************************************/

class GTW61850WriteConverter
{
public:
  GTW61850WriteConverter()
  {  }

  virtual ~GTW61850WriteConverter() {}
  

  virtual bool select(tmw61850::ServerControlPoint* controlPoint) = 0;
  virtual bool write(tmw61850::ServerControlPoint* controlPoint, GTWDEFS_CTRL_MODE ctrlMode) = 0;
  virtual GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) = 0;
protected:
  virtual void Delete(void) = 0;
};


// TODO: to support select need to replace GTW61850WriteConverter with GTW61850WriteConverterT and override select in all of the write converters
//       then need to test all datatypes and all protocols and all SBO/nonSBO combos


/***************************************************************************/
static GTWDEFS_PARSED_OPTION_TYPE s_GTW61850SlaveDataObject_allowedOptions[] = {
  "IS_COMMAND_SDO",PARSED_OPTION_BOOL_VALUE,NULL,NULL, "TR_OPT_HELP_IS_COMMAND_SDO", "Identifies this point as a command point.  If a control point control value (ctlVal) is used in the server, the IS_COMMAND_SDO option must be set to true "
};

class GTWLIB_API GTW61850SlaveDataObject : public GTWSlaveDataObjectTemplate<GTWBaseDataObject>
{
  friend class GTW61850ServerCallBack;
  template<class T> friend class GTW61850WriteConverterT;

public:
  GTW61850SlaveDataObject();
  virtual ~GTW61850SlaveDataObject();

  DeclareClassInfo();

  bool FormatQualityString(char* buffer, int bufferSize, GTWEXPND_EXPANSION* pExpansion) override;
  GTWBaseEditor *GetBaseEditor(const EditorCommandDTO &dto) override;

  char const *Get61850TypeName()
  {
    return "UNKNOWN";
  }
  
  virtual bool IsOpcBrowsable() override
  {
    return false;
  }
  bool IsDeletable() override { return false; }

  void CleanUserData();

  void SetInternalModelPointers(tmw61850::DataAttribute* p61850valueDA, tmw61850::DataAttribute* p61850timeDA, tmw61850::DataAttribute* p61850qualityDA);
  void Set61850VTQValue(tmw61850::DataAttribute* p61850ValueDA, tmw61850::DataAttribute* p61850TimeDA, tmw61850::DataAttribute* p61850QualityDA);

  /*
  void Set61850VQTDa(
    tmw61850::DataAttribute *p61850valueDA,
    tmw61850::DataAttribute *p61850timeDA,
    tmw61850::DataAttribute *p61850qualityDA
  )
  {
    m_p61850ValueDa = p61850valueDA;
    if (m_p61850ValueDa)
    {
      m_p61850ValueDa->SetUserData(this);
    }

    m_p61850TimeDa = p61850timeDA;
    if (m_p61850TimeDa)
    {
      m_p61850TimeDa->SetUserData(this);

      tmw61850::DateTime *pDT = (tmw61850::DateTime *)m_p61850TimeDa->GetDateTime();
      pDT->SetBitsofAccuracy(9); // millisecond accuracy is only supported at this time in the SDG
      pDT->SetLeapSecondKnown(true);
      pDT->SetClockNotSynchronized(true);
      pDT->SetClockFailure(false);
    }

    m_p61850QualityDa = p61850qualityDA;
    if (m_p61850QualityDa)
      m_p61850QualityDa->SetUserData(this);
  }
  */


  bool IsExtRef();

  void BeginOperate(tmw61850::ServerControlPoint *pControlPoint);
  bool IsOperating();
  void EndOperate(bool bWriteSucceeded);//GTWDEFS_UPDTRSN updateReason);
  void EndOperateTimeout();

  tmw61850::DataAttribute *Get61850ValueDa()
  {
    return m_p61850ValueDa;
  }
  tmw61850::DataAttribute *Get61850TimeDa()
  {
    return m_p61850TimeDa;
  }
  tmw61850::DataAttribute *Get61850QualityDa()
  {
    return m_p61850QualityDa;
  }
  uint32_t Get61850QualityValue()
  {
    if (m_p61850QualityDa)
    {
      return m_p61850QualityDa->GetIntValue();
    }
    return 0;
  }

  void Set61850Quality(I61850_QUALITY_TYPE quality)
  {
    m_native61850Quality = quality;
  }

  I61850_QUALITY_TYPE Get61850Quality()
  {
    return m_native61850Quality;
  }

  GTW61850Server *GetServer();

  virtual  GTWDEFS_STD_QLTY getStdQuality(void);
  virtual void getValueString(const GTWEXPND_FORMAT &pFormat, TMWTYPES_CHAR *msg, TMWTYPES_USHORT msgLen);

  virtual bool getDisplayTimeAndQuality(TMWDTIME *pDateTime, GTWDEFS_TIME_QLTY *pTimeQuality);

  void SetMemberName(const CStdString &tagName)
  {
    m_sMemberName = tagName;
  }
  virtual CStdString GetMemberName(void)
  {
    return m_sMemberName;
  }

  void SetDaName(const CStdString& valueDaName, const CStdString& timeDaName, const CStdString& qualityDaName);

  virtual CStdString GetValueDaName(void)
  {
    return m_sValueDaName;
  }
  virtual CStdString GetTimeDaName(void)
  {
    return m_sTimeDaName;
  }
  virtual CStdString GetQualityDaName(void)
  {
    return m_sQualityDaName;
  }

  bool setSdoTypeFromMdoType(GTWDEFS_TYPE mdoType)
  {
    return m_value.ChangeType(mdoType);
  }

  void setQuality(GTWDEFS_STD_QLTY stdQuality);
  const CStdString getDescription(void)
  {
    if (m_pMyMdo)
    {
      return (m_pMyMdo->getMdoDescription());
    }
    return "";
  }

  void UpdateExtRefChanges();
  bool updateServerValue();

  bool GTW61850_write(tmw61850::DataAttribute *pDA);
  bool GTW61850_write(tmw61850::ServerControlPoint* controlPoint);
  bool selectControl(tmw61850::ServerControlPoint* controlPoint);

  //void StartProcessWriteResult(GTWDEFS_CTRL_STAT startingCtrlStat, tmw61850::DataAttribute *pDA);

  /* virtual functions */
  virtual bool bindSdoWithMdo(GTWMasterDataObject *pMdo);
  CStdString gtw_getFullName(void) override;

  virtual void getDbasDataId(GTWDEFS_DBAS_DATA_ID *pDbasDataId);

  virtual void writeSdoCB(GTWDEFS_CTRL_STAT newCtrlStat, void *pExtraCBData);
  bool IsCommandSDO(void);
  bool isCommand() override { return IsCommandSDO(); }
  bool isWritable() override;
  virtual bool IsCommandDataType() { return isCommand(); }

  void doUpdate()
  {
    updateSDO(GTWDEFS_UPDTRSN_REFRESH, m_pMyMdo);
  }

  template<typename T> void setValue(const T& value)
  {
    m_value = value;
    if (m_p61850ValueDa)
    {
      if (GetGTWApp()->IsInitialized())
      {
        m_value.AssignTo61850DataAttribute(*m_p61850ValueDa);
      }
    }
  }

  void setValue(const TMWDTIME& time)
  {
    //m_value.AssignFromTMWDTIME(time);
  }

  GTW61850_TYPE get61850Type() { return m_61850Type; }


  virtual void processWriteComplete(GTWDEFS_CTRL_STAT status) override
  {
    m_ctrlStat = status;
  }
  bool m_bUseNativeQuality;

protected:
  //virtual bool bindSdoWithMdoNative(GTWMasterDataObject *pMdo);

private:
  GTWDEFS_CTRL_STAT m_ctrlStat;
  GTW61850WriteConverter* m_pWriteCnvtr;

  bool m_bOperating;
  bool m_bTimedOut;
  GtwVariant        m_value;
  GtwOsDateTime     m_timestamp;
  GTWDEFS_STD_QLTY  m_quality;
  GTW61850_TYPE     m_61850Type;
  I61850_QUALITY_TYPE m_native61850Quality; // Add native quality storage

  bool m_bSynchronizeDownstream;

  tmw61850::DataAttribute *m_p61850ValueDa;
  tmw61850::DataAttribute *m_p61850TimeDa;
  tmw61850::DataAttribute *m_p61850QualityDa;

  GTWMasterDataObject *m_pMyMdo;
  CStdString           m_sFullName;
  CStdString           m_sMemberName;
  CStdString           m_sValueDaName;
  CStdString           m_sTimeDaName;
  CStdString           m_sQualityDaName;
  GTW61850ReadConverter  *m_pReadCnvtr;
  GTWDEFS_UPDTRSN m_updateReason;
  bool m_bIsCommandSDO;

  void SetCurrentTime();
  //void updateSDO(GTWDEFS_UPDTRSN updateReason, GTW61850SlaveDataObject *pSource);
  bool bindSdoCommandWithMdo(GTWMasterDataObject* pMdo);
  void bindSdoWithMdoWithUnknownType(GTWMasterDataObject* pMdo);

  virtual void updateSDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo = TMWDEFS_NULL);
  void UpdateInternalControlValues(tmw61850::ServerControlPoint* pControlPoint);

  static unsigned int __stdcall OperThreadFunc( void*  pParam );
  tmw61850::ServerControlPoint *m_pControlPoint;

  /* get kind of SDO   */
  GTWDEFS_SDO_KIND getSdoKind(void)
  {
    return (GTWDEFS_SDO_KIND_61850);
  }

  GTWDEFS_TYPE getSdoType(void)
  {
    return m_value.GetType();
  }

  void DoEndOperate(bool bWriteSucceeded);

  virtual void SetDefaultOptions(void)
  {
    m_bIsCommandSDO = false;
    return GTWSlaveDataObjectTemplate<GTWBaseDataObject>::SetDefaultOptions();
  }

  /* private virtual functions */
  GTWDEFS_STAT ParseOptionsField(const char *connectionToken, const char **ppOptionString) override
  {
    if (ParseOptionsString(ppOptionString,s_GTW61850SlaveDataObject_allowedOptions[0].name))
    {
      m_bIsCommandSDO = true;
    }
    else
    {
      return(GTWSlaveDataObjectTemplate<GTWBaseDataObject>::ParseOptionsField(connectionToken, ppOptionString));
    }

    return(GTWDEFS_STAT_SUCCESS);
  }

  void GetAllowedOptions( GTWDEFS_PARSED_OPTION_ARRAY &optionsArray  ) override
  {
    int i;
    for (i=0;i<TMWDEFS_ARRAY_LENGTH(s_GTW61850SlaveDataObject_allowedOptions);i++)
    {
      optionsArray.push_back(s_GTW61850SlaveDataObject_allowedOptions[i]);
    }
    GTWSlaveDataObjectTemplate<GTWBaseDataObject>::GetAllowedOptions(optionsArray);
    return;
  }

   //friend class c61850EndOperateWorkItem;
  friend class OnServerOperateWI;
  friend class OnServerSelectWI;
  friend class GTW61850ServerDataChangeWorkItem; // give access to GetValues
  friend class GTW61850ServerCallBack;
  template<class T> friend class GTW61850WriteConverterT;
  friend class GTW61850Server;

  // Both Set Value should only be called from the owning Server WorkQueue
  void SetModelValues(tmw61850::DataAttribute* pValue, tmw61850::DataAttribute* pTime, tmw61850::DataAttribute* pQuality);
  void UpdateInternalControlValues();

  void GetInternalTime(tmw61850::DateTime* p61850Time); 
};


