﻿  stdafx.cpp
  Environment.cpp
  FixedPointTests.cpp
  LogBufferTests.cpp
  MdoToMdoMappingTest.cpp
  MdoToSdoMappingTest.cpp
  SimpleTimerTest.cpp
  SortPerformanceTest.cpp
  classRegistryTest.cpp
  configIniTest.cpp
  jsonSamples.cpp
  licenseTest.cpp
  rsaCryptoTest.cpp
  stringTest.cpp
  tagPropertyTest.cpp
  timerTest.cpp
  userDataBaseTest.cpp
  variantTest.cpp
  webClientTest.cpp
  winMain.cpp
  zbibTest.cpp
F:\work\SDG_5.2.3\gateway\GTWLib.Test\LogBufferTests.cpp(10): message : Fix log tests
F:\work\SDG_5.2.3\gateway\GTWWebLib\crypto.hpp(225,1): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data (compiling source file SortPerformanceTest.cpp)
F:\work\SDG_5.2.3\gateway\GTWWebLib\crypto.hpp(225,1): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data (compiling source file Environment.cpp)
F:\work\SDG_5.2.3\gateway\GTWWebLib\crypto.hpp(225,1): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data (compiling source file webClientTest.cpp)
F:\work\SDG_5.2.3\gateway\GTWWebLib\crypto.hpp(225,1): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data (compiling source file configIniTest.cpp)
F:\work\SDG_5.2.3\gateway\GTWLib.Test\webClientTest.cpp(15): message : Paths: ie W:\SDG-trunk\gateway\GTWEngine\TestINIandCSVfiles\ in here need to become relative so tests will work for diffrent locations
  webServerTest.cpp
F:\work\SDG_5.2.3\gateway\GTWWebLib\crypto.hpp(225,1): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  GTWLibTest.vcxproj -> F:\work\SDG_5.2.3\bind_x64\GTWLibTest.exe
