// GTW61850DataAttributeMDOEditor.cpp: implementation of the GTW61850DataAttributeMDOEditor class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "GTW61850DataAttributeMDOEditor.h"

#include "gateway/GTWLib/GTW61850Client.h"
#include "gateway/GTWLib/GTW61850ReportControlBlock.h"
#include "gateway/GTWLib/GTW61850GOOSEControlBlock.h"
#include "gateway/GTWLib/GTW61850DataAttributeMDO.h"
#include "GTWconfigres.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

GTW61850DataAttributeMDOEditor::GTW61850DataAttributeMDOEditor(const EditorCommandDTO &dto, GTWCollectionMember *pEditableObject, GTWCollectionMember *pParent, bool bAddMode)
:
GTWBaseEditor(dto, pEditableObject, bAddMode,-1,-1,-1)
{
 	m_sMDOvalueTagName = "";
	m_sMDOtimeTagName = "";
	m_sMDOqualityTagName = "";
	m_sMDOoptions = "";
	m_sMDOdescription = "";
}

GTW61850DataAttributeMDOEditor::~GTW61850DataAttributeMDOEditor()
{
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::BuildSelPopupMenu(int *id,CMenuEntryArray *pMenuEntries)
{
  pMenuEntries->push_back(CMenuEntry("Edit IEC 61850 MDO","Edit an IEC 61850 Client Item","Edit IEC 61850 Client Item text",GetEditableObject(),++(*id),MENU_CMD_EDIT));
  
  GTW61850DataAttributeMDO *pMDO = (GTW61850DataAttributeMDO *)GetEditableObject();
  if (pMDO && pMDO->m_pValueDataAttribute && pMDO->GetClientNode()->IsUp())
  {
    pMenuEntries->push_back(CMenuEntry("Read IEC 61850 MDO","Read an IEC 61850 Client Item","Read IEC 61850 Client Item text",GetEditableObject(),++(*id),MENU_CMD_READ_61850_MDO));
  }
  if (pMDO->isWriteable())
  {
    pMenuEntries->push_back(CMenuEntry("Change Value of MDO","Change Value of MDO","Change Value of MDO text",GetEditableObject(),++(*id),MENU_CMD_CHANGE_VALUE));
  }

  pMenuEntries->push_back(CMenuEntry("Delete IEC 61850 MDO","Delete an IEC 61850 Client Item","Delete IEC 61850 Client Item text",GetEditableObject(),++(*id),MENU_CMD_DELETE));
  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::DeleteMultipleObjects()
{
  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::DeleteObject(GTWCollectionMember **pRefreshMember, bool bAskIfOk /*= true*/)
{
  GTWCollectionMember *pMember = GetEditableObject();
  GTW61850DataAttributeMDO *p61850ClientItem = (GTW61850DataAttributeMDO *)pMember;
  if (p61850ClientItem->HasMappedSdoAndCannotDelete())
  {
    GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_POINT_IN_USE_CANT_DELETE", "TAG: '{{arg1}}' is mapped to slave or master points or is used in an equation, cannot delete", (const char*)pMember->GetFullName());
    return TMWDEFS_FALSE;
  }

  GTWCollectionBase *pClctn = pMember->GetParentCollection();
  if (pClctn == NULL)
  {
    p61850ClientItem->DeleteMe();
  }
  else
  {
    GTW61850Client *p61850Client = p61850ClientItem->GetClientNode();

    p61850ClientItem->DeleteMe();

    p61850Client->Remove61850ClientNodes(pClctn);
  }

  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::ValidateObject()
{
  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::EditObject()
{
  return TMWDEFS_FALSE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::SaveObject()
{
  GTWBaseDataObject *pBdo = NULL;
  GTW61850DataAttributeMDO *pMdo = NULL;
  if (GetEditableObject() == NULL)
  {
    return TMWDEFS_FALSE;
  }
  else
  {
    pBdo = (GTWBaseDataObject *)GetEditableObject();
    pMdo = (GTW61850DataAttributeMDO *)pBdo->GetMdo();
  }

  GTWDEFS_STAT status = pMdo->updateOptions(GetDTOToken(), m_sMDOoptions);
  if (status != GTWDEFS_STAT_SUCCESS)
  {
    if (status == GTWDEFS_STAT_NOT_VALID)
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_MDO_SET_OPTIONS", "Could not set MDO options {{arg1}}", m_sMDOoptions.c_str());
      return TMWDEFS_FALSE;
    }
  }
  pMdo->setMdoDescription(m_sMDOdescription);
  pMdo->m_sValueTagName = m_sMDOvalueTagName;
  pMdo->m_sQualityTagName = m_sMDOqualityTagName;
  pMdo->m_sTimeTagName = m_sMDOtimeTagName;

  GTW61850Client *pClient = pMdo->GetClientNode();
  if (pClient)
  {
    if (pMdo->GetControlBlock()->GetReportControl())
    {
      if (pClient->SetDataAttrMdo(
        CStdString(m_sMDOvalueTagName),
        CStdString(m_sMDOqualityTagName),
        CStdString(m_sMDOtimeTagName),
        pMdo,
            pMdo->GetControlBlock()->GetReportControl()->GetDataSet()) == false)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_SET_MDO_PROPERTIES_REPORT", "Failed to set MDO properties on Report Control");
      }
    }
    else if (pMdo->GetControlBlock()->GetGOOSEControl())
    {
      if (pClient->SetDataAttrMdo(
            CStdString(m_sMDOvalueTagName),
            CStdString(m_sMDOqualityTagName),
            CStdString(m_sMDOtimeTagName),
            pMdo,
            pMdo->GetControlBlock()->GetGOOSEControl()->GetDataSet()) == false)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_SET_MDO_PROPERTIES_GOOSE", "Failed to set MDO properties on GOOSE Control");
      }
    }
    else if (pMdo->GetControlBlock()->GetDataSetControl())
    {
      if (pClient->SetDataAttrMdo(
            CStdString(m_sMDOvalueTagName),
            CStdString(m_sMDOqualityTagName),
            CStdString(m_sMDOtimeTagName),
            pMdo,
            pMdo->GetControlBlock()->GetDataSetControl()) == false)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_SET_MDO_PROPERTIES_DATASET", "Failed to set MDO properties on DataSetControl");
      }
    }
    else if (pMdo->GetControlBlock()->GetPointSetControl())
    {
      if (pClient->SetDataAttrMdo(
            CStdString(m_sMDOvalueTagName),
            CStdString(m_sMDOqualityTagName),
            CStdString(m_sMDOtimeTagName),
            pMdo,
            pMdo->GetControlBlock()->GetPointSetControl()) == false)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_SET_MDO_PROPERTIES_POINTSET", "Failed to set MDO properties on PointSet Control");
      }
    }
    else 
    {
      if (pClient->SetDataAttrMdo(
            CStdString(m_sMDOvalueTagName),
            CStdString(m_sMDOqualityTagName),
            CStdString(m_sMDOtimeTagName),
            pMdo) == false)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_SET_MDO_PROPERTIES_Control", "Failed to set MDO properties on Control");
      }
    }
  }

  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::LoadObject()
{
  GTWBaseDataObject *pBdo = (GTWBaseDataObject *)GetEditableObject();
  GTW61850DataAttributeMDO *pMdo = (GTW61850DataAttributeMDO *)pBdo->GetMdo();

  m_sMDOoptions = GTWPointMap::getMoptions(pBdo);

    m_sMDOvalueTagName=   pMdo->m_sValueTagName;
	  m_sMDOtimeTagName=    pMdo->m_sTimeTagName;
	  m_sMDOqualityTagName= pMdo->m_sQualityTagName;
	  m_sMDOdescription=    pMdo->getMdoDescription();

  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::ChangeValueOfObject(MENUENTRY_EDIT_CMD cmd)
{
  return TMWDEFS_FALSE;
}

void GTW61850DataAttributeMDOEditor::ReadObject()
{
  GTW61850DataAttributeMDO *pMDO = (GTW61850DataAttributeMDO *)GetEditableObject();
  if (pMDO)
  {
    pMDO->ReadDataAttributes();
  }
}
TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::WebReadObject(nlohmann::json &pt, GTW61850ControlBlock *p61850ControlBlock, bool bAddMode, bool bEditAtRuntime)
{
  GTWBaseEditor::WebReadObject(pt, bEditAtRuntime);

  if (!m_bAddMode && LoadObject() == TMWDEFS_FALSE)
    return false;

  bool readOnly = true;

  try
  {
    nlohmann::json schema;
    nlohmann::json children;

    if (m_bAddMode)
    {
      AddEditorField(schema, children,
        "DAPointList",
        "TR_DA_POINT_LIST",
        "Data Attribute Points",
        "",
        "TR_DA_POINT_LIST_DESC",
        "Specifies the current data attribute point list.",
        (EDITOR_CONTROL_TYPE::GRID),
        ListDAPointsJson(p61850ControlBlock, true, "",""),
        false,
        "",
        "",
        GTW_RESOURCE_UI_ID,
        ""
      );

      AddEditorField(schema, children,
        "autoMapQualityTime",
        "TR_AUTO_MAP_QUALITY_TIME",
        "Auto Map Quality & Time",
        "true",
        "TR_AUTO_MAP_QUALITY_TIME_DESC",
        "Specifies if the quality and time auto mapping is active.",
        (EDITOR_CONTROL_TYPE::CHECKBOX),
        "",
        false,
        "",
        "",
        GTW_RESOURCE_UI_ID,
        ""
      );

      AddEditorField(schema, children,
        "functionalConstraint",
        "TR_FUNCTIONAL_CONSTRAINT",
        "Functional Constraint",
        "",
        "TR_FUNCTIONAL_CONSTRAINT_DESC",
        "Specifies the functional constraint",
        (EDITOR_CONTROL_TYPE::TEXT),
        "",
        false,
        "",
        "TR_SEARCH",
        GTW_RESOURCE_UI_ID,
        ""
      );

      AddEditorField(schema, children,
        "DASearch",
        "TR_DA_SEARCH",
        "Enter a partial name to search for (case sensitive)",
        "",
        "TR_DA_SEARCH_DESC",
        "Enter a partial name to search for (case sensitive)",
        (EDITOR_CONTROL_TYPE::TEXT),
        "",
        false,
        "",
        "TR_SEARCH",
        GTW_RESOURCE_UI_ID,
        ""
      );

      AddEditorField(schema, children,
        "BTNSearch",
        "TR_SEARCH",
        "Search",
        "",
        "TR_SEARCH_DESC",
        "Search",
        (EDITOR_CONTROL_TYPE::ACTION),
        "",
        false,
        "",
        "TR_SEARCH",
        IDC_SHOWALLBUTTON,
        ""
      );

      AddEditorField(schema, children,
        "Add61850Item",
        "TR_ADD_ITEM",
        "Add Item",
        "",
        "TR_ADD_ITEM_DESC",
        "Add OPC UA Item.",
        (EDITOR_CONTROL_TYPE::ACTION),
        "",
        false,
        "",
        "",
        GTW_RESOURCE_UI_ID,
        ""
      );

      AddHiddenEditorField(schema, children,
        "61850ControlBlock",
        p61850ControlBlock->GetFullName()
      );

      pt["editorKind"] = EDITOR_KIND_to_string(EDITOR_KIND::AddItem);
    }
    else
    {
      AddEditorField(schema, children,
        "tagName",
        "TR_DATAATTRIBUTE_NAME",
        "DataAttribute Name",
        m_sMDOvalueTagName,
        "TR_TAG_NAME_DESC",
        "Specify the tag name",
        (EDITOR_CONTROL_TYPE::TEXT),
        "",
        true,
        "",
        "",
        IDC_I61850_TAG_NAME_EC,
        "", 
        &readOnly
      );

      AddEditorField(schema, children,
        "tagTime",
        "TR_DATAATTRIBUTE_TIME",
        "DataAttribute Time",
        m_sMDOtimeTagName,
        "TR_TAG_TIME_DESC",
        "Define the time for the MDO",
        (EDITOR_CONTROL_TYPE::TEXT_ACTION),
        "",
        false,
        "",
        "",
        IDC_I61850_TIME_TAG_NAME_EC,
        ""
      );

      AddEditorField(schema, children,
        "tagQuality",
        "TR_DATAATTRIBUTE_QUALITY",
        "DataAttribute Quality",
        m_sMDOqualityTagName,
        "TR_TAG_QUALITY_DESC",
        "Define the quality for the MDO",
        (EDITOR_CONTROL_TYPE::TEXT_ACTION),
        "",
        false,
        "",
        "",
        IDC_I61850_QUALITY_TAG_NAME_EC,
        ""
      );


      CStdString sMDOcontrolBlock = "Undefined";
      GTW61850DataAttributeMDO *p61850ClientItem = (GTW61850DataAttributeMDO *)GetEditableObject();
      if (p61850ClientItem->GetControlBlock() != NULL)
        sMDOcontrolBlock = p61850ClientItem->GetControlBlock()->GetFullName();

      AddEditorField(schema, children,
        "controlBlock",
        "TR_CONTROL_BLOCK",
        "Control Block",
        sMDOcontrolBlock,
        "TR_CONTROL_BLOCK_DESC",
        "Show control block for the MDO",
        (EDITOR_CONTROL_TYPE::TEXT),
        "",
        false,
        "",
        "",
        IDC_I61850_CONTROL_BLOCK_EC,
        "",
        &readOnly
      );

      AddEditorField(schema, children,
        "tagOptions",
        "TR_TAG_OPTIONS",
        "Tag Options",
        m_sMDOoptions,
        "TR_TAG_OPTIONS_DESC",
        "Specify the options of tag",
        (EDITOR_CONTROL_TYPE::OPTIONS_EDITOR),
        GetEditableObject() ? GetAllowedOptions(GetEditableObject()) : GetAllowedOptions(m_dto.objectClassName),
        false,
        "",
        "",
        IDC_OPC_MDO_OPTIONS_EC,
        ""
      );

      AddEditorField(schema, children,
        "tagDescription",
        "TR_TAG_DESCRIPTION",
        "Tag Description",
        m_sMDOdescription,
        "TR_TAG_DESCRIPTION_DESC",
        "Specify the description of the Tag",
        (EDITOR_CONTROL_TYPE::TEXT),
        "",
        false,
        "",
        "",
        IDC_I61850_MDO_DESCRIPTION_EC,
        ""
      );
    }
   
    AddHiddenEditorField(schema, children,
      "objectName",
      m_dto.objectName
    );

    std::string json = schema.dump();
    pt["objectDataJson"] = json;
    pt["editorType"] = "GTW61850DataAttributeMDOEditor";
    pt += nlohmann::json::object_t::value_type("children", children);
  }
  catch (std::exception &c)
  {
    std::string message = c.what();
    return TMWDEFS_FALSE;
  }

  return TMWDEFS_TRUE;
}
TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::WebReadObjectQualityTime(const std::string &objectName, const std::string &objectClass, nlohmann::json &pt, GTW61850ControlBlock *p61850ControlBlock)
{
  try
  {
    nlohmann::json schema;
    nlohmann::json children;

    AddEditorField(schema, children,
      "DAPointList",
      "TR_DA_POINT_LIST",
      "Data Attribute Points",
      "",
      "TR_DA_POINT_LIST_DESC",
      "Specifies the current data attribute point list.",
      (EDITOR_CONTROL_TYPE::GRID),
      ListDAQualityTimePointsJson(objectName, p61850ControlBlock),
      false,
      "",
      "",
      GTW_RESOURCE_UI_ID,
      ""
    );


    AddHiddenEditorField(schema, children,
      "DAName",
      objectName
    );

    AddHiddenEditorField(schema, children,
      "objectName",
      objectName
    );

    std::string json = schema.dump();
    pt["objectDataJson"] = json;
    pt["editorType"] = "GTW61850SelectDataAttributeEditor";
    pt += nlohmann::json::object_t::value_type("children", children);
  }
  catch (std::exception &c)
  {
    std::string message = c.what();
    return TMWDEFS_FALSE;
  }

  return TMWDEFS_TRUE;
}
CStdString GTW61850DataAttributeMDOEditor::ListDAQualityTimePointsJson(const std::string &objectName, GTW61850ControlBlock *p61850ControlBlock)
{
  try
  {
    CStdString strMainItem;
    CStdString strItem;

    //Build reportListJsonSource
    nlohmann::json listDSPointsJsonSource;
    nlohmann::json columnsJson;
    nlohmann::json childColumns;
    nlohmann::json dataJson;
    nlohmann::json childData;

    childColumns["field"] = "itemName";
    childColumns["header"] = "TR_ITEM_NAME";
    columnsJson.push_back(childColumns);
    childColumns["field"] = "type";
    childColumns["header"] = "TYPE";
    columnsJson.push_back(childColumns);
    listDSPointsJsonSource += nlohmann::json::object_t::value_type("columns", columnsJson);

    tmw::Array<tmw61850::DataAttribute*> *dsPoints = NULL;
    if (p61850ControlBlock)
    {
      if (p61850ControlBlock->GetReportControl() != 0)
      {
        dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetDSPoints(p61850ControlBlock->GetReportControl());
      }
      else if (p61850ControlBlock->GetGOOSEControl() != 0)
      {
        dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetDSPoints(p61850ControlBlock->GetGOOSEControl());
      }
      else if (p61850ControlBlock->GetDataSetControl() != 0)
      {
        dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetDSPoints(p61850ControlBlock->GetDataSetControl());
      }
      else
      {
        dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetAllLeafNodesWithValues(p61850ControlBlock->Get61850Client()->GetClientConnection()->Model());
      }
    }

    if (dsPoints != NULL && dsPoints->size() > 0)
    {
      int index = 0;
      tmw::String fullName;

      bool timeOnly = objectName == "SelectDAT";
      for (unsigned int i = 0; i < dsPoints->size(); i++)
      {
        tmw61850::DataAttribute *pDA = dsPoints->getAt(i);

        if (timeOnly && !pDA->IsValueDateTime())
          continue;

        if (!timeOnly && !pDA->IsValueQuality())
          continue;        

        strMainItem.Format("%s", pDA->GetFullName(fullName));
        childData["itemName"] = strMainItem;
        strItem.Format("%s", pDA->GetTypeAsXMLString());
        childData["type"] = strItem;

        ++index;
        dataJson.push_back(childData);
      }
    }
    listDSPointsJsonSource += nlohmann::json::object_t::value_type("data", dataJson);
    return listDSPointsJsonSource.dump();
  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "Exception: %s", e.what());
    return "";
  }
  return "";
}

CStdString GTW61850DataAttributeMDOEditor::ListDAPointsJson(GTW61850ControlBlock *p61850ControlBlock, bool m_bDontShowQT, const CStdString &fcs, const CStdString &ssearchStr)
{
  try
  {
    CStdString strMainItem;
    CStdString strItem;

    //Build reportListJsonSource
    nlohmann::json listDSPointsJsonSource;
    nlohmann::json columnsJson;
    nlohmann::json childColumns;
    nlohmann::json dataJson;
    nlohmann::json childData;

    if (m_bDontShowQT == true)
    {
      childColumns["field"] = "checkbox";
      childColumns["header"] = "&#10004;";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "itemName";
      childColumns["header"] = "TR_ITEM_NAME";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "type";
      childColumns["header"] = "TYPE";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "FC";
      childColumns["header"] = "FC";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "time";
      childColumns["header"] = "TIME";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "quality";
      childColumns["header"] = "QUALITY";
      columnsJson.push_back(childColumns);
      listDSPointsJsonSource += nlohmann::json::object_t::value_type("columns", columnsJson);
    }
    else
    {
      childColumns["field"] = "checkbox";
      childColumns["header"] = "&#10004;";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "itemName";
      childColumns["header"] = "TR_ITEM_NAME";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "type";
      childColumns["header"] = "TYPE";
      columnsJson.push_back(childColumns);
      childColumns["field"] = "FC";
      childColumns["header"] = "FC";
      columnsJson.push_back(childColumns);
      listDSPointsJsonSource += nlohmann::json::object_t::value_type("columns", columnsJson);
    }

    tmw::Array<tmw61850::DataAttribute*> *dsPoints = NULL;
    if (p61850ControlBlock->GetReportControl() != 0)
    {
      dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetDSPoints(p61850ControlBlock->GetReportControl());
    }
    else if (p61850ControlBlock->GetGOOSEControl() != 0)
    {
      dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetDSPoints(p61850ControlBlock->GetGOOSEControl());
    }
    else if (p61850ControlBlock->GetDataSetControl() != 0)
    {
      dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetDSPoints(p61850ControlBlock->GetDataSetControl());
    }
    else if (p61850ControlBlock->GetPointSetControl() != 0)
    {
      //FVE
      //    dsPoints = m_p61850ControlBlock->Get61850Client()->GetClientConnection()->GetDSPoints(m_p61850ControlBlock->GetPointSetControl());
      dsPoints = p61850ControlBlock->Get61850Client()->GetClientConnection()->Model()->GetAllLeafNodesWithValues(p61850ControlBlock->Get61850Client()->GetClientConnection()->Model());
    }
    else
    {
      GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_LIST_POINTS_NOT_A_CONTROL", "Not a valid Control Block");
      return "";
    }

    int count = 0;
    for (unsigned int i = 0; dsPoints && i < dsPoints->size(); i++)
    {
      tmw61850::DataAttribute *pDA = dsPoints->getAt(i);

      if (m_bDontShowQT == true && (pDA->IsValueQuality() || pDA->IsValueDateTime()))
        continue;

      // Filter on FC
      if (fcs.length() > 0 && tmw::util::strstrNoCase(fcs.c_str(), pDA->GetFC()) == nullptr)
        continue;

      tmw::String fullName;
      strMainItem.Format("%s", pDA->GetFullName(fullName));
      if (ssearchStr.GetLength() > 0 && strMainItem.Find(ssearchStr) == -1)
        continue;

      childData["checkbox"] = true;
      childData["itemName"] = strMainItem;
      strItem.Format("%s", pDA->GetTypeAsXMLString());
      childData["type"] = strItem;
      strItem.Format("%s", pDA->GetFC());
      childData["FC"] = strItem;

      if (m_bDontShowQT == true)
      {
        childData["checkbox"] = true;
        childData["itemName"] = strMainItem;
        strItem.Format("%s", pDA->GetTypeAsXMLString());
        childData["type"] = strItem;
        strItem.Format("%s", pDA->GetFC());
        childData["FC"] = strItem;

        CStdString sTimeDA = "";
        CStdString sQualityDA = "";

        GetQualityTimestampDA(dsPoints, pDA, sTimeDA, sQualityDA, strMainItem);
        childData["time"] = sTimeDA;
        childData["quality"] = sQualityDA;
      }
      ++count;
      //if (count >= 1000)
      //{
      //  GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_61850_TOO_MANY_POINTS_FOUND", "Too many points in model for display, please select a search filter  The first 1000 items are displayed.");
      //  break;
      //}
      dataJson.push_back(childData);
    }
    
    listDSPointsJsonSource += nlohmann::json::object_t::value_type("data", dataJson);
    return listDSPointsJsonSource.dump();
  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "Exception: %s", e.what());
    return "";
  }
  return "";
}

void GTW61850DataAttributeMDOEditor::GetQualityTimestampDA(tmw::Array<tmw61850::DataAttribute *> *dsPoints, tmw61850::DataAttribute *pDA, CStdString &sTimeDA, CStdString &sQualityDA, CStdString sItemID)
{
  tmw61850::DataAttribute *pQualityDA = nullptr;
  tmw61850::DataAttribute *pTimestampDA = nullptr;
  pDA->GetQualityAndTimeStamp(pQualityDA, pTimestampDA);

  if (dsPoints && dsPoints->find(pQualityDA) == -1)
    pQualityDA = nullptr;

  if (dsPoints && dsPoints->find(pTimestampDA) == -1)
    pTimestampDA = nullptr;

  tmw::String fullName;

  // do time stamp mapping
  if (pTimestampDA == nullptr)
  {
    if (sItemID.Find(".mag.") != -1 || sItemID.Find(".stVal") != -1)
    {
      // Set subitem 3 (time)
      tmw61850::DataAttribute *pTimeDA = (tmw61850::DataAttribute *)tmw61850::DataAttribute::FindNodeInParent("t", pDA);
      if (pTimeDA && dsPoints && dsPoints->find(pTimeDA) != -1)
        sTimeDA = pTimeDA->GetFullName(fullName);
      else
        sTimeDA = "";
    }
  }
  else
  {
    // Set subitem 3 (time)
    if (pTimestampDA)
      sTimeDA = pTimestampDA->GetFullName(fullName);
    else
      sTimeDA = "";
  }

  // do quality mapping
  if (pQualityDA == nullptr)
  {
    if (sItemID.Find(".mag.") != -1 || sItemID.Find(".stVal") != -1)
    {
      // Set subitem 4 (quality)
      tmw61850::DataAttribute *pQualDA = (tmw61850::DataAttribute *)tmw61850::DataAttribute::FindNodeInParent("q", pDA);
      if (pQualDA && dsPoints && dsPoints->find(pQualDA) != -1)
        sQualityDA = pQualDA->GetFullName(fullName);
      else
        sQualityDA = "";
    }
  }
  else
  {
    // Set subitem 4 (quality)
    if (pQualityDA)
      sQualityDA = pQualityDA->GetFullName(fullName);
    else
      sQualityDA = "";
  }
}
TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::UpdateObject()
{
  try
  {
    m_sMDOvalueTagName = m_dto.pt_objectDataJson["tagName"].get<std::string>();
    m_sMDOtimeTagName = m_dto.pt_objectDataJson["tagTime"].get<std::string>();
    m_sMDOqualityTagName = m_dto.pt_objectDataJson["tagQuality"].get<std::string>();
    m_sMDOoptions = m_dto.pt_objectDataJson["tagOptions"].get<std::string>();
    m_sMDOdescription = m_dto.pt_objectDataJson["tagDescription"].get<std::string>();

    if (!ValidateObject())
    {
      return TMWDEFS_FALSE;
    }

  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "Exception: %s", e.what());
    return TMWDEFS_FALSE;
  }
  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::AddObject(GTW61850ControlBlock *p61850ControlBlock, std::string sDAPointListChecked, std::string sAutoMapQualityTime)
{
  try
  {
    bool bAutoMapQualityTime = GetBoolValueFromString(sAutoMapQualityTime);

    if (sDAPointListChecked != "")
    {
	    auto jDAPointListChecked = nlohmann::json::parse(sDAPointListChecked);
	
	    int i = 0;
	    for (nlohmann::json::iterator it = jDAPointListChecked.begin(); it != jDAPointListChecked.end(); ++it) 
	    {
	      CStdString sItemID = jDAPointListChecked[i];
	      if (sItemID != "")
	      {
	        tmw61850::DataAttribute *pDA = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(sItemID, p61850ControlBlock->Get61850Client()->GetClientConnection()->Model());
	        if (pDA != nullptr)
	          AddItem(sItemID, bAutoMapQualityTime,p61850ControlBlock, pDA);
	      }
	      i++;
	    }
    }
  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "Exception: %s", e.what());
    return TMWDEFS_FALSE;
  }
  return TMWDEFS_TRUE;
}

TMWTYPES_BOOL GTW61850DataAttributeMDOEditor::AddItem(CStdString sItemID, bool bAutoMapQualityTime, GTW61850ControlBlock *p61850ControlBlock, tmw61850::DataAttribute *pDA)
{
  try
  {
    tmw::Array<tmw61850::DataAttribute *> *dsPoints = nullptr;
    if (p61850ControlBlock->GetBaseDataSetControl())
    {
      dsPoints = p61850ControlBlock->GetBaseDataSetControl()->GetDSPoints();
    }
    else if (p61850ControlBlock->GetDataSetControl())
    {
      dsPoints = p61850ControlBlock->GetDataSetControl()->GetDSPoints();
    }

    CStdString sTimeDA = "";
    CStdString sQualityDA = "";
    if (bAutoMapQualityTime)
    {
      GetQualityTimestampDA(dsPoints, pDA, sTimeDA, sQualityDA, sItemID);
    }

    GTW61850DataAttributeMDO *pMdo = dynamic_cast<GTW61850DataAttributeMDO*>(p61850ControlBlock->Get61850Client()->FindMdo(sItemID.c_str()));
    GTWDEFS_STAT status = GTWDEFS_STAT_NOT_VALID;
    if (pMdo)
    {
      status = GTWDEFS_STAT_SUCCESS;
      if (sTimeDA == "")
      {
        pMdo->setTimeTagName("");
      }
      if (sQualityDA == "")
      {
        pMdo->setQualityTagName("");
      }
      pMdo->SetControlBlock(p61850ControlBlock);
    }
    else
    {
      GTW61850DataAttributeMDO *p61850Mdo = NULL;
      status = GetGTWApp()->GetPointMap()->Create61850Mdo(
        p61850ControlBlock->Get61850Client(),
        p61850ControlBlock,
        p61850ControlBlock->Get61850Client()->GetAliasName() + "." + sItemID,
        sQualityDA,
        sTimeDA,
        pDA->GetType(),
        &p61850Mdo);

      if (status != GTWDEFS_STAT_SUCCESS)
      {
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, GetDTOToken(), "TR_CREATE_61850_SERVER_NO_ADD_MDO", "Could not add {{arg1}} MDO on 61850 server: {{arg2}}. (duplicate ?)", sItemID.c_str(), p61850ControlBlock->Get61850Client()->GetAliasName().c_str());
        return TMWDEFS_FALSE;
      }
    }
  }
  catch (std::exception &e)
  {
    LOG(GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, nullptr, "Exception: %s", e.what());
    return TMWDEFS_FALSE;
  }
  return TMWDEFS_TRUE;
}

CStdString GTW61850DataAttributeMDOEditor::IsEditableAtRuntime(TMWTYPES_UINT ui_id)
{
  // this is not thread safe so lock it
  tmw::CriticalSectionLock lock(IsEditableAtRuntimeLock);

  InitAtRuntime(ui_id);

  if (m_bEditAtRunTime)
  {
    //GetDlgItem(??ID??)->EnableWindow(false);
  }
  else
  {
    //GetDlgItem(??ID??)->EnableWindow(true);
  }

  return GTWBaseEditor::IsEditableAtRuntime(ui_id);
}
