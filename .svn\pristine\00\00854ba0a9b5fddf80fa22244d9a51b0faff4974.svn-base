/******************************************************************************
*  CreateProcess function: a simple way for Linux/Unix to call programs
* without blocking and without the complications of fork()/exec() which
* normal people shouldn't need to understand :)
*
*  Usage example:

#include "CreateProcess.h"

int main(void)
{
// Call it like this:
CreateProcess("ls", "-a -l --sort=size");

// Or like this:
CreateProcess("date", NULL);

// Note that the second call does not wait for the first to finish
// as system() would

return 0;
}

* Enjoy!
* - <PERSON>
*/
#include "stdafx.h"

#include <stdio.h>
#ifndef _WIN32
#include <unistd.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>
#include <sys/wait.h>

pid_t CreateProcess(const char* command, const char* parametersIn, bool bSyncExecution)
{
  const int maxNumArgs = 1024;
  const char* args[maxNumArgs];
  char* parameters = NULL;

  memset(args, 0, (sizeof(char*) * maxNumArgs));
  args[0] = command;

  if (parametersIn != NULL)
  {
    parameters = strdup(parametersIn);
    int strLen = strlen(parameters);

    int numParameters = 1;
    bool expectNextParam = true;
    int i;
    for (i = 0; i < strLen; i++)
    {
      if (parameters[i] == ' ' || parameters[i] == '\t' ||
        parameters[i] == '\n')
      {
        expectNextParam = true;
        parameters[i] = '\0';
      }
      else if (expectNextParam)
      {
        args[numParameters] = &(parameters[i]);
        numParameters++;
        expectNextParam = false;
      }
    }
  }

  pid_t pid = fork();
  if (pid == 0)
  {
    execvp(command, (char**)args);
    _exit(1);
  }

  if (parameters != NULL)
    free(parameters);

  if (bSyncExecution == true)
  {
    int     status, timeout /* unused ifdef WAIT_FOR_COMPLETION */;
    timeout = 5000;

    while (0 == waitpid(pid, &status, WNOHANG)) {
      if (--timeout < 0) {
        perror("timeout");
        return false;
      }
      sleep(1);
    }

    printf("%s WEXITSTATUS %d WIFEXITED %d [status %d]\n",
      command,
      WEXITSTATUS(status),
      WIFEXITED(status),
      status);

    if (1 != WIFEXITED(status) || 0 != WEXITSTATUS(status)) {
      perror("%s failed, halt system");
      return false;
    }

  }
  return pid;
}

#endif