61850Client102.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
61850Client102.BayControllerQ/LLN0_MEAS_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
61850Client102.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
61850Client102.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,61850Client102,61850Client102.BayControllerQ/LLN0.MEAS,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PDS,,,,,,,,,,,,,,I61850_MDO
61850Client102.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,61850Client102,61850Client102.BayControllerQ/LLN0.MEAS,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PDS,,,,,,,,,,,,,,I61850_MDO
61850Client102.BayControllerQ/T1MMXU1.PhV.phsC.range,61850Client102,61850Client102.BayControllerQ/LLN0.MEAS,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PDS,,,,,,,,,,,,,,I61850_MDO
61850Client102.ResponseQsize,,,,8,,,,,,,,,,1,0,,,,,USER
S101_2505.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
S101_2505.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
S101_2505.L3.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
S101_2505.L3.A3.ClearEventBuffers,,,,1,,,,,,,,,,On,0,,,,,USER
,S101_2505,3,3,1,100,,,,,,,,,,,,,,
,S101_2505,3,3,3,200,,,,,,,,,,,,,,
,S101_2505,3,3,5,300,,,,,,,,,,,,,,
,S101_2505,3,3,7,400,,,,,,,,,,,,,,
,S101_2505,3,3,9,500,,,,,,,,,,,,,,
,S101_2505,3,3,11,600,,,,,,,,,,,,,,
,S101_2505,3,3,13,700,,,,,,,,,,,,,,
,S101_2505,3,3,15,800,,,,,,,,,,,,,,
,S101_2505,3,3,45,1,,,,,,,,,,,,,,
,S101_2505,3,3,46,2,,,,,,,,,,,,,,
,S101_2505,3,3,47,3,,,,,,,,,,,,,,
,S101_2505,3,3,48,4,,,,,,,,,,,,,,
,S101_2505,3,3,49,5,,,,,,,,,,,,,,
,S101_2505,3,3,50,6,,,,,,,,,,,,,,
,S101_2505,3,3,51,7,,,,,,,,,,,,,,
MDNP_20000.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
MDNP_20000.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
,MDNP_20000,4,,41,0,,,,,,,,,,,,,,
MDNP_20000.L4.ColdRestartNow,,,,1,,,,,,,,,,Off,0,,,,,USER
,MDNP_20000,4,,21,0,,,,,,,,,,,,,,
,MDNP_20000,4,,20,0,,,,,,,,,,,,,,
MDNP_20000.L4.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
MDNP_20000.L4.WarmRestartNow,,,,1,,,,,,,,,,Off,0,,,,,USER
,MDNP_20000,4,,12,0,,,,,,,,,,,,,,
,MDNP_20000,4,,1,0,,,,,,,,,,,,,,
,MDNP_20000,4,,3,0,,,,,,,,,,,,,,
,MDNP_20000,4,,85,0,,,,,,,,,,,,,,
,MDNP_20000,4,,86,0,,,,,,,,,,,,,,
,MDNP_20000,4,,87,0,,,,,,,,,,,,,,
,MDNP_20000,4,,110,0,,,,,,,,,,,,,,
MDNP_20000.L4.CreateTagsAuto,,,,1,,,,,,,,,,Off,0,,,,,USER
,MDNP_20000,4,,30,0,,,,,,,,,,,,,,
SMB_503.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
SMB_503.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
SMB_503.L1.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
DiscreteInputReg,SMB_503,1,,1,0,,,,,,,,,,,,,,
Coil,SMB_503,1,,0,0,,,,,,,,,,,,,,
InputRegister,SMB_503,1,,3,1,,,,,,,,,,,,,,
HoldingRegister,SMB_503,1,,4,0,,,,,,,,,,,,,,
S104_2404.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
S104_2404.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
S104_2404.L4.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
S104_2404.L4.A3.ClearEventBuffers,,,,1,,,,,,,,,,Off,0,,,,,USER
,S104_2404,4,3,1,100,,,,,,,,,,,,,,
,S104_2404,4,3,3,200,,,,,,,,,,,,,,
,S104_2404,4,3,5,300,,,,,,,,,,,,,,
,S104_2404,4,3,7,400,,,,,,,,,,,,,,
,S104_2404,4,3,9,500,,,,,,,,,,,,,,
,S104_2404,4,3,11,600,,,,,,,,,,,,,,
,S104_2404,4,3,13,700,,,,,,,,,,,,,,
,S104_2404,4,3,15,800,,,,,,,,,,,,,,
,S104_2404,4,3,45,1,,,,,,,,,,,,,,
,S104_2404,4,3,46,2,,,,,,,,,,,,,,
,S104_2404,4,3,47,3,,,,,,,,,,,,,,
,S104_2404,4,3,48,4,,,,,,,,,,,,,,
,S104_2404,4,3,49,5,,,,,,,,,,,,,,
,S104_2404,4,3,50,6,,,,,,,,,,,,,,
,S104_2404,4,3,51,7,,,,,,,,,,,,,,
M101_2504.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
M101_2504.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
M101_2504.L3.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
,M101_2504,3,3,1,100,,,,S101_2505,3,3,1,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,3,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,5,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,7,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,9,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,11,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,13,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,15,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,45,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,46,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,47,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,48,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,49,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,50,1,,,,,,
,M101_2504,3,3,1,100,,,,S101_2505,3,3,51,1,,,,,,
,M101_2504,3,3,3,200,,,,,,,,,,,,,,
,M101_2504,3,3,5,300,,,,,,,,,,,,,,
,M101_2504,3,3,7,400,,,,,,,,,,,,,,
,M101_2504,3,3,9,500,,,,,,,,,,,,,,
,M101_2504,3,3,11,600,,,,,,,,,,,,,,
,M101_2504,3,3,13,700,,,,,,,,,,,,,,
,M101_2504,3,3,15,800,,,,,,,,,,,,,,
,M101_2504,3,3,45,1,,,,,,,,,,,,,,
,M101_2504,3,3,46,2,,,,,,,,,,,,,,
,M101_2504,3,3,47,3,,,,,,,,,,,,,,
,M101_2504,3,3,48,4,,,,,,,,,,,,,,
,M101_2504,3,3,49,5,,,,,,,,,,,,,,
,M101_2504,3,3,50,6,,,,,,,,,,,,,,
,M101_2504,3,3,51,7,,,,,,,,,,,,,,
,M101_2504,3,3,70,8,,,,,,,,,,,,,,
M101_2504.L3.A3.CreateTagsAuto,,,,1,,,,,,,,,,Off,0,,,,,USER
M101_2504.L3.CreateTagsAuto,,,,1,,,,,,,,,,Off,0,,,,,USER
M104_2405.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
M104_2405.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
M104_2405.L3.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
,M104_2405,4,3,1,100,,,,,,,,,,,,,,
,M104_2405,4,3,3,200,,,,,,,,,,,,,,
,M104_2405,4,3,5,300,,,,,,,,,,,,,,
,M104_2405,4,3,7,400,,,,,,,,,,,,,,
,M104_2405,4,3,9,500,,,,,,,,,,,,,,
,M104_2405,4,3,11,600,,,,,,,,,,,,,,
,M104_2405,4,3,13,700,,,,,,,,,,,,,,
,M104_2405,4,3,15,800,,,,,,,,,,,,,,
,M104_2405,4,3,45,1,,,,,,,,,,,,,,
,M104_2405,4,3,46,2,,,,,,,,,,,,,,
,M104_2405,4,3,47,3,,,,,,,,,,,,,,
,M104_2405,4,3,48,4,,,,,,,,,,,,,,
,M104_2405,4,3,49,5,,,,,,,,,,,,,,
,M104_2405,4,3,50,6,,,,,,,,,,,,,,
,M104_2405,4,3,51,7,,,,,,,,,,,,,,
,M104_2405,4,3,70,8,,,,,,,,,,,,,,
M104_2405.L4.A3.CreateTagsAuto,,,,1,,,,,,,,,,Off,0,,,,,USER
M104_2405.L4.CreateTagsAuto,,,,1,,,,,,,,,,Off,0,,,,,USER
SDNP_20001.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
SDNP_20001.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
SDNP_20001.L3.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
,SDNP_20001,3,,41,0,,,,,,,,,,,,,,
SDNP_20001.L3.ColdRestartNow,,,,1,,,,,,,,,,Off,0,,,,,USER
,SDNP_20001,3,,21,0,,,,,,,,,,,,,,
,SDNP_20001,3,,20,0,,,,,,,,,,,,,,
SDNP_20001.L3.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
SDNP_20001.L3.WarmRestartNow,,,,1,,,,,,,,,,Off,0,,,,,USER
,SDNP_20001,3,,12,0,,,,,,,,,,,,,,
,SDNP_20001,3,,1,0,,,,,,,,,,,,,,
,SDNP_20001,3,,3,0,,,,,,,,,,,,,,
,SDNP_20001,3,,85,0,,,,,,,,,,,,,,
,SDNP_20001,3,,86,0,,,,,,,,,,,,,,
,SDNP_20001,3,,87,0,,,,,,,,,,,,,,
,SDNP_20001,3,,110,0,,,,,,,,,,,,,,
MMB_502.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
MMB_502.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
MMB_502.L1.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
DiscreteInputReg,MMB_502,1,,1,0,,,,,,,,,,,,,,
Coil,MMB_502,1,,0,0,,,,,,,,,,,,,,
InputRegister,MMB_502,1,,3,1,,,,,,,,,,,,,,
HoldingRegister,MMB_502,1,,4,0,,,,,,,,,,,,,,
TASE2App.VMDStateQ1,TASE2App,TASE2App.f,,15,,T2PPS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.VMDDiscreteQ1,TASE2App,TASE2App.f,,4,,T2PPS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/Discrete2,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,2,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/Discrete8,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,2,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/Discrete6,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,2,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/Discrete5,TASE2App,TASE2App.PolledDS_1_0ICC2/NewDS),,2,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/Discrete7,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,2,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/DiscreteQ5,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,4,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/DiscreteExtended8,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,10,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/DiscreteExtended3,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,10,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/DiscreteExtended2,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,10,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/DiscreteExtended4,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,10,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/DiscreteExtended7,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,10,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.ICC1/DiscreteExtended6,TASE2App,TASE2App.PolledDS_1_0(ICC2/NewDS),,10,,T2PDS,,,,,,,,,,,,,,TASE2_CLT_MDO
TASE2App.f_READ_POINTS,,,,1,,,,,,,,,,On,0,,,,,USER
TASE2App.Report Count,,,,8,,,,,,,,,,0,0,,,,,USER
TASE2App.Read Response Count,,,,8,,,,,,,,,,192615,0,,,,,USER
