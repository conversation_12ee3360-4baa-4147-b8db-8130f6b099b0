/* 
 * SDG Runtime
 *
 * SDG Runtime API
 *
 * OpenAPI spec version: 1.0.0
 * 
 * Generated by: https://github.com/swagger-api/swagger-codegen.git
 */

using System;
using System.Linq;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel.DataAnnotations;
using SwaggerDateConverter = Engine.IO.Swagger.Client.SwaggerDateConverter;

namespace Engine.IO.Swagger.Model
{
    /// <summary>
    /// Describes an editable object
    /// </summary>
    [DataContract]
    public partial class EditorSpecificationObjectDTO :  IEquatable<EditorSpecificationObjectDTO>, IValidatableObject
    {
        /// <summary>
        /// Defines EditorKind
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public enum EditorKindEnum
        {
            
            /// <summary>
            /// Enum CancelOk for value: CancelOk
            /// </summary>
            [EnumMember(Value = "CancelOk")]
            CancelOk = 1,
            
            /// <summary>
            /// Enum AddItem for value: AddItem
            /// </summary>
            [EnumMember(Value = "AddItem")]
            AddItem = 2
        }

        /// <summary>
        /// Gets or Sets EditorKind
        /// </summary>
        [DataMember(Name="editorKind", EmitDefaultValue=false)]
        public EditorKindEnum? EditorKind { get; set; }
        /// <summary>
        /// Initializes a new instance of the <see cref="EditorSpecificationObjectDTO" /> class.
        /// </summary>
        /// <param name="objectDataJson">objectDataJson.</param>
        /// <param name="editorType">editorType.</param>
        /// <param name="editorKind">editorKind.</param>
        /// <param name="children">children.</param>
        public EditorSpecificationObjectDTO(string objectDataJson = default(string), string editorType = default(string), EditorKindEnum? editorKind = default(EditorKindEnum?), List<EditorFieldObjectDTO> children = default(List<EditorFieldObjectDTO>))
        {
            this.ObjectDataJson = objectDataJson;
            this.EditorType = editorType;
            this.EditorKind = editorKind;
            this.Children = children;
        }
        
        /// <summary>
        /// Gets or Sets ObjectDataJson
        /// </summary>
        [DataMember(Name="objectDataJson", EmitDefaultValue=false)]
        public string ObjectDataJson { get; set; }

        /// <summary>
        /// Gets or Sets EditorType
        /// </summary>
        [DataMember(Name="editorType", EmitDefaultValue=false)]
        public string EditorType { get; set; }


        /// <summary>
        /// Gets or Sets Children
        /// </summary>
        [DataMember(Name="children", EmitDefaultValue=false)]
        public List<EditorFieldObjectDTO> Children { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            var sb = new StringBuilder();
            sb.Append("class EditorSpecificationObjectDTO {\n");
            sb.Append("  ObjectDataJson: ").Append(ObjectDataJson).Append("\n");
            sb.Append("  EditorType: ").Append(EditorType).Append("\n");
            sb.Append("  EditorKind: ").Append(EditorKind).Append("\n");
            sb.Append("  Children: ").Append(Children).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }
  
        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return JsonConvert.SerializeObject(this, Formatting.Indented);
        }

        /// <summary>
        /// Returns true if objects are equal
        /// </summary>
        /// <param name="input">Object to be compared</param>
        /// <returns>Boolean</returns>
        public override bool Equals(object input)
        {
            return this.Equals(input as EditorSpecificationObjectDTO);
        }

        /// <summary>
        /// Returns true if EditorSpecificationObjectDTO instances are equal
        /// </summary>
        /// <param name="input">Instance of EditorSpecificationObjectDTO to be compared</param>
        /// <returns>Boolean</returns>
        public bool Equals(EditorSpecificationObjectDTO input)
        {
            if (input == null)
                return false;

            return 
                (
                    this.ObjectDataJson == input.ObjectDataJson ||
                    (this.ObjectDataJson != null &&
                    this.ObjectDataJson.Equals(input.ObjectDataJson))
                ) && 
                (
                    this.EditorType == input.EditorType ||
                    (this.EditorType != null &&
                    this.EditorType.Equals(input.EditorType))
                ) && 
                (
                    this.EditorKind == input.EditorKind ||
                    (this.EditorKind != null &&
                    this.EditorKind.Equals(input.EditorKind))
                ) && 
                (
                    this.Children == input.Children ||
                    this.Children != null &&
                    this.Children.SequenceEqual(input.Children)
                );
        }

        /// <summary>
        /// Gets the hash code
        /// </summary>
        /// <returns>Hash code</returns>
        public override int GetHashCode()
        {
            unchecked // Overflow is fine, just wrap
            {
                int hashCode = 41;
                if (this.ObjectDataJson != null)
                    hashCode = hashCode * 59 + this.ObjectDataJson.GetHashCode();
                if (this.EditorType != null)
                    hashCode = hashCode * 59 + this.EditorType.GetHashCode();
                if (this.EditorKind != null)
                    hashCode = hashCode * 59 + this.EditorKind.GetHashCode();
                if (this.Children != null)
                    hashCode = hashCode * 59 + this.Children.GetHashCode();
                return hashCode;
            }
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<System.ComponentModel.DataAnnotations.ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}
