/*
	This file is only used by IntelliSense (VisualStudio code suggestion system)
	DO NOT INCLUDE THIS FILE FROM YOUR ACTUAL SOURCE FILES.
	This file lists the preprocessor macros extracted from your GCC.
	It is needed for IntelliSense to parse other header files correctly.
*/
#if defined(_MSC_VER) || defined (__SYSPROGS_CODESENSE__)
#ifndef __DBL_MIN_EXP__
#define __DBL_MIN_EXP__ (-1021)
#endif
#ifndef __UINT_LEAST16_MAX__
#define __UINT_LEAST16_MAX__ 65535
#endif
#ifndef __ATOMIC_ACQUIRE
#define __ATOMIC_ACQUIRE 2
#endif
#ifndef __FLT_MIN__
#define __FLT_MIN__ 1.17549435082228750797e-38F
#endif
#ifndef __UINT_LEAST8_TYPE__
#define __UINT_LEAST8_TYPE__ unsigned char
#endif
#ifndef __CHAR_BIT__
#define __CHAR_BIT__ 8
#endif
#ifndef __UINT8_MAX__
#define __UINT8_MAX__ 255
#endif
#ifndef __WINT_MAX__
#define __WINT_MAX__ 4294967295U
#endif
#ifndef __ORDER_LITTLE_ENDIAN__
#define __ORDER_LITTLE_ENDIAN__ 1234
#endif
#ifndef __SIZE_MAX__
#define __SIZE_MAX__ 18446744073709551615UL
#endif
#ifndef __WCHAR_MAX__
#define __WCHAR_MAX__ 2147483647
#endif
#ifndef __GCC_HAVE_SYNC_COMPARE_AND_SWAP_1
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_1 1
#endif
#ifndef __GCC_HAVE_SYNC_COMPARE_AND_SWAP_2
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_2 1
#endif
#ifndef __GCC_HAVE_SYNC_COMPARE_AND_SWAP_4
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_4 1
#endif
#ifndef __DBL_DENORM_MIN__
#define __DBL_DENORM_MIN__ double(4.94065645841246544177e-324L)
#endif
#ifndef __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8 1
#endif
#ifndef __GCC_ATOMIC_CHAR_LOCK_FREE
#define __GCC_ATOMIC_CHAR_LOCK_FREE 2
#endif
#ifndef __FLT_EVAL_METHOD__
#define __FLT_EVAL_METHOD__ 0
#endif
#ifndef __unix__
#define __unix__ 1
#endif
#ifndef __GCC_ATOMIC_CHAR32_T_LOCK_FREE
#define __GCC_ATOMIC_CHAR32_T_LOCK_FREE 2
#endif
#ifndef __x86_64
#define __x86_64 1
#endif
#ifndef __UINT_FAST64_MAX__
#define __UINT_FAST64_MAX__ 18446744073709551615UL
#endif
#ifndef __SIG_ATOMIC_TYPE__
#define __SIG_ATOMIC_TYPE__ int
#endif
#ifndef __DBL_MIN_10_EXP__
#define __DBL_MIN_10_EXP__ (-307)
#endif
#ifndef __FINITE_MATH_ONLY__
#define __FINITE_MATH_ONLY__ 0
#endif
#ifndef __GNUC_PATCHLEVEL__
#define __GNUC_PATCHLEVEL__ 4
#endif
#ifndef __UINT_FAST8_MAX__
#define __UINT_FAST8_MAX__ 255
#endif
#ifndef __DEC64_MAX_EXP__
#define __DEC64_MAX_EXP__ 385
#endif
#ifndef __UINT_LEAST64_MAX__
#define __UINT_LEAST64_MAX__ 18446744073709551615UL
#endif
#ifndef __SHRT_MAX__
#define __SHRT_MAX__ 32767
#endif
#ifndef __LDBL_MAX__
#define __LDBL_MAX__ 1.18973149535723176502e+4932L
#endif
#ifndef __UINT_LEAST8_MAX__
#define __UINT_LEAST8_MAX__ 255
#endif
#ifndef __GCC_ATOMIC_BOOL_LOCK_FREE
#define __GCC_ATOMIC_BOOL_LOCK_FREE 2
#endif
#ifndef __UINTMAX_TYPE__
#define __UINTMAX_TYPE__ long unsigned int
#endif
#ifndef __linux
#define __linux 1
#endif
#ifndef __DEC32_EPSILON__
#define __DEC32_EPSILON__ 1E-6DF
#endif
#ifndef __unix
#define __unix 1
#endif
#ifndef __UINT32_MAX__
#define __UINT32_MAX__ 4294967295U
#endif
#ifndef __LDBL_MAX_EXP__
#define __LDBL_MAX_EXP__ 16384
#endif
#ifndef __WINT_MIN__
#define __WINT_MIN__ 0U
#endif
#ifndef __linux__
#define __linux__ 1
#endif
#ifndef __SCHAR_MAX__
#define __SCHAR_MAX__ 127
#endif
#ifndef __WCHAR_MIN__
#define __WCHAR_MIN__ (-__WCHAR_MAX__ - 1)
#endif
#ifndef __DBL_DIG__
#define __DBL_DIG__ 15
#endif
#ifndef __GCC_ATOMIC_POINTER_LOCK_FREE
#define __GCC_ATOMIC_POINTER_LOCK_FREE 2
#endif
#ifndef __SIZEOF_INT__
#define __SIZEOF_INT__ 4
#endif
#ifndef __SIZEOF_POINTER__
#define __SIZEOF_POINTER__ 8
#endif
#ifndef __GCC_ATOMIC_CHAR16_T_LOCK_FREE
#define __GCC_ATOMIC_CHAR16_T_LOCK_FREE 2
#endif
#ifndef __USER_LABEL_PREFIX__
#define __USER_LABEL_PREFIX__ 
#endif
#ifndef __STDC_HOSTED__
#define __STDC_HOSTED__ 1
#endif
#ifndef __LDBL_HAS_INFINITY__
#define __LDBL_HAS_INFINITY__ 1
#endif
#ifndef __FLT_EPSILON__
#define __FLT_EPSILON__ 1.19209289550781250000e-7F
#endif
#ifndef __GXX_WEAK__
#define __GXX_WEAK__ 1
#endif
#ifndef __LDBL_MIN__
#define __LDBL_MIN__ 3.36210314311209350626e-4932L
#endif
#ifndef __DEC32_MAX__
#define __DEC32_MAX__ 9.999999E96DF
#endif
#ifndef __INT32_MAX__
#define __INT32_MAX__ 2147483647
#endif
#ifndef __SIZEOF_LONG__
#define __SIZEOF_LONG__ 8
#endif
#ifndef __STDC_IEC_559__
#define __STDC_IEC_559__ 1
#endif
#ifndef __STDC_ISO_10646__
#define __STDC_ISO_10646__ 201103L
#endif
#ifndef __DECIMAL_DIG__
#define __DECIMAL_DIG__ 21
#endif
#ifndef __gnu_linux__
#define __gnu_linux__ 1
#endif
#ifndef __LDBL_HAS_QUIET_NAN__
#define __LDBL_HAS_QUIET_NAN__ 1
#endif
#ifndef __GNUC__
#define __GNUC__ 4
#endif
#ifndef __GXX_RTTI
#define __GXX_RTTI 1
#endif
#ifndef __MMX__
#define __MMX__ 1
#endif
#ifndef __FLT_HAS_DENORM__
#define __FLT_HAS_DENORM__ 1
#endif
#ifndef __SIZEOF_LONG_DOUBLE__
#define __SIZEOF_LONG_DOUBLE__ 16
#endif
#ifndef __BIGGEST_ALIGNMENT__
#define __BIGGEST_ALIGNMENT__ 16
#endif
#ifndef __DBL_MAX__
#define __DBL_MAX__ double(1.79769313486231570815e+308L)
#endif
#ifndef __INT_FAST32_MAX__
#define __INT_FAST32_MAX__ 9223372036854775807L
#endif
#ifndef __DBL_HAS_INFINITY__
#define __DBL_HAS_INFINITY__ 1
#endif
#ifndef __INT64_MAX__
#define __INT64_MAX__ 9223372036854775807L
#endif
#ifndef __DEC32_MIN_EXP__
#define __DEC32_MIN_EXP__ (-94)
#endif
#ifndef __INT_FAST16_TYPE__
#define __INT_FAST16_TYPE__ long int
#endif
#ifndef __LDBL_HAS_DENORM__
#define __LDBL_HAS_DENORM__ 1
#endif
//VS2005-2012 treats all files as C++, while VS2013+ can treat C files correctly.
#if defined(_MSC_VER) && (_MSC_VER < 1800 || defined(__cplusplus))
#define __cplusplus 199711L
#endif
#ifndef __DEC128_MAX__
#define __DEC128_MAX__ 9.999999999999999999999999999999999E6144DL
#endif
#ifndef __INT_LEAST32_MAX__
#define __INT_LEAST32_MAX__ 2147483647
#endif
#ifndef __DEC32_MIN__
#define __DEC32_MIN__ 1E-95DF
#endif
#ifndef __DEPRECATED
#define __DEPRECATED 1
#endif
#ifndef __DBL_MAX_EXP__
#define __DBL_MAX_EXP__ 1024
#endif
#ifndef __DEC128_EPSILON__
#define __DEC128_EPSILON__ 1E-33DL
#endif
#ifndef __SSE2_MATH__
#define __SSE2_MATH__ 1
#endif
#ifndef __ATOMIC_HLE_RELEASE
#define __ATOMIC_HLE_RELEASE 131072
#endif
#ifndef __PTRDIFF_MAX__
#define __PTRDIFF_MAX__ 9223372036854775807L
#endif
#ifndef __amd64
#define __amd64 1
#endif
#ifndef __STDC_NO_THREADS__
#define __STDC_NO_THREADS__ 1
#endif
#ifndef __ATOMIC_HLE_ACQUIRE
#define __ATOMIC_HLE_ACQUIRE 65536
#endif
#ifndef __GNUG__
#define __GNUG__ 4
#endif
#ifndef __LONG_LONG_MAX__
#define __LONG_LONG_MAX__ 9223372036854775807LL
#endif
#ifndef __SIZEOF_SIZE_T__
#define __SIZEOF_SIZE_T__ 8
#endif
#ifndef __SIZEOF_WINT_T__
#define __SIZEOF_WINT_T__ 4
#endif
#ifndef __GCC_HAVE_DWARF2_CFI_ASM
#define __GCC_HAVE_DWARF2_CFI_ASM 1
#endif
#ifndef __GXX_ABI_VERSION
#define __GXX_ABI_VERSION 1002
#endif
#ifndef __FLT_MIN_EXP__
#define __FLT_MIN_EXP__ (-125)
#endif
#ifndef __INT_FAST64_TYPE__
#define __INT_FAST64_TYPE__ long int
#endif
#ifndef __DBL_MIN__
#define __DBL_MIN__ double(2.22507385850720138309e-308L)
#endif
#ifndef __LP64__
#define __LP64__ 1
#endif
#ifndef __DECIMAL_BID_FORMAT__
#define __DECIMAL_BID_FORMAT__ 1
#endif
#ifndef __DEC128_MIN__
#define __DEC128_MIN__ 1E-6143DL
#endif
#ifndef __REGISTER_PREFIX__
#define __REGISTER_PREFIX__ 
#endif
#ifndef __UINT16_MAX__
#define __UINT16_MAX__ 65535
#endif
#ifndef __DBL_HAS_DENORM__
#define __DBL_HAS_DENORM__ 1
#endif
#ifndef __UINT8_TYPE__
#define __UINT8_TYPE__ unsigned char
#endif
#ifndef __NO_INLINE__
#define __NO_INLINE__ 1
#endif
#ifndef __FLT_MANT_DIG__
#define __FLT_MANT_DIG__ 24
#endif
#ifndef __VERSION__
#define __VERSION__ "4.8.4"
#endif
#ifndef _STDC_PREDEF_H
#define _STDC_PREDEF_H 1
#endif
#ifndef __GCC_ATOMIC_INT_LOCK_FREE
#define __GCC_ATOMIC_INT_LOCK_FREE 2
#endif
#ifndef __FLOAT_WORD_ORDER__
#define __FLOAT_WORD_ORDER__ __ORDER_LITTLE_ENDIAN__
#endif
#ifndef __STDC_IEC_559_COMPLEX__
#define __STDC_IEC_559_COMPLEX__ 1
#endif
#ifndef __DEC64_EPSILON__
#define __DEC64_EPSILON__ 1E-15DD
#endif
#ifndef __ORDER_PDP_ENDIAN__
#define __ORDER_PDP_ENDIAN__ 3412
#endif
#ifndef __DEC128_MIN_EXP__
#define __DEC128_MIN_EXP__ (-6142)
#endif
#ifndef __INT_FAST32_TYPE__
#define __INT_FAST32_TYPE__ long int
#endif
#ifndef __UINT_LEAST16_TYPE__
#define __UINT_LEAST16_TYPE__ short unsigned int
#endif
#ifndef unix
#define unix 1
#endif
#ifndef __INT16_MAX__
#define __INT16_MAX__ 32767
#endif
#ifndef __SIZE_TYPE__
#define __SIZE_TYPE__ long unsigned int
#endif
#ifndef __UINT64_MAX__
#define __UINT64_MAX__ 18446744073709551615UL
#endif
#ifndef __INT8_TYPE__
#define __INT8_TYPE__ signed char
#endif
#ifndef __ELF__
#define __ELF__ 1
#endif
#ifndef __FLT_RADIX__
#define __FLT_RADIX__ 2
#endif
#ifndef __INT_LEAST16_TYPE__
#define __INT_LEAST16_TYPE__ short int
#endif
#ifndef __LDBL_EPSILON__
#define __LDBL_EPSILON__ 1.08420217248550443401e-19L
#endif
#ifndef __k8
#define __k8 1
#endif
#ifndef __SIG_ATOMIC_MAX__
#define __SIG_ATOMIC_MAX__ 2147483647
#endif
#ifndef __GCC_ATOMIC_WCHAR_T_LOCK_FREE
#define __GCC_ATOMIC_WCHAR_T_LOCK_FREE 2
#endif
#ifndef __SIZEOF_PTRDIFF_T__
#define __SIZEOF_PTRDIFF_T__ 8
#endif
#ifndef __x86_64__
#define __x86_64__ 1
#endif
#ifndef __DEC32_SUBNORMAL_MIN__
#define __DEC32_SUBNORMAL_MIN__ 0.000001E-95DF
#endif
#ifndef __INT_FAST16_MAX__
#define __INT_FAST16_MAX__ 9223372036854775807L
#endif
#ifndef __UINT_FAST32_MAX__
#define __UINT_FAST32_MAX__ 18446744073709551615UL
#endif
#ifndef __UINT_LEAST64_TYPE__
#define __UINT_LEAST64_TYPE__ long unsigned int
#endif
#ifndef __FLT_HAS_QUIET_NAN__
#define __FLT_HAS_QUIET_NAN__ 1
#endif
#ifndef __FLT_MAX_10_EXP__
#define __FLT_MAX_10_EXP__ 38
#endif
#ifndef __LONG_MAX__
#define __LONG_MAX__ 9223372036854775807L
#endif
#ifndef __DEC128_SUBNORMAL_MIN__
#define __DEC128_SUBNORMAL_MIN__ 0.000000000000000000000000000000001E-6143DL
#endif
#ifndef __FLT_HAS_INFINITY__
#define __FLT_HAS_INFINITY__ 1
#endif
#ifndef __UINT_FAST16_TYPE__
#define __UINT_FAST16_TYPE__ long unsigned int
#endif
#ifndef __DEC64_MAX__
#define __DEC64_MAX__ 9.999999999999999E384DD
#endif
#ifndef __CHAR16_TYPE__
#define __CHAR16_TYPE__ short unsigned int
#endif
#ifndef __PRAGMA_REDEFINE_EXTNAME
#define __PRAGMA_REDEFINE_EXTNAME 1
#endif
#ifndef __INT_LEAST16_MAX__
#define __INT_LEAST16_MAX__ 32767
#endif
#ifndef __DEC64_MANT_DIG__
#define __DEC64_MANT_DIG__ 16
#endif
#ifndef __UINT_LEAST32_MAX__
#define __UINT_LEAST32_MAX__ 4294967295U
#endif
#ifndef __GCC_ATOMIC_LONG_LOCK_FREE
#define __GCC_ATOMIC_LONG_LOCK_FREE 2
#endif
#ifndef __INT_LEAST64_TYPE__
#define __INT_LEAST64_TYPE__ long int
#endif
#ifndef __INT16_TYPE__
#define __INT16_TYPE__ short int
#endif
#ifndef __INT_LEAST8_TYPE__
#define __INT_LEAST8_TYPE__ signed char
#endif
#ifndef __DEC32_MAX_EXP__
#define __DEC32_MAX_EXP__ 97
#endif
#ifndef __INT_FAST8_MAX__
#define __INT_FAST8_MAX__ 127
#endif
#ifndef __INTPTR_MAX__
#define __INTPTR_MAX__ 9223372036854775807L
#endif
#ifndef linux
#define linux 1
#endif
#ifndef __SSE2__
#define __SSE2__ 1
#endif
#ifndef __EXCEPTIONS
#define __EXCEPTIONS 1
#endif
#ifndef __LDBL_MANT_DIG__
#define __LDBL_MANT_DIG__ 64
#endif
#ifndef __DBL_HAS_QUIET_NAN__
#define __DBL_HAS_QUIET_NAN__ 1
#endif
#ifndef __SIG_ATOMIC_MIN__
#define __SIG_ATOMIC_MIN__ (-__SIG_ATOMIC_MAX__ - 1)
#endif
#ifndef __code_model_small__
#define __code_model_small__ 1
#endif
#ifndef __k8__
#define __k8__ 1
#endif
#ifndef __INTPTR_TYPE__
#define __INTPTR_TYPE__ long int
#endif
#ifndef __UINT16_TYPE__
#define __UINT16_TYPE__ short unsigned int
#endif
#ifndef __WCHAR_TYPE__
#define __WCHAR_TYPE__ int
#endif
#ifndef __SIZEOF_FLOAT__
#define __SIZEOF_FLOAT__ 4
#endif
#ifndef __UINTPTR_MAX__
#define __UINTPTR_MAX__ 18446744073709551615UL
#endif
#ifndef __DEC64_MIN_EXP__
#define __DEC64_MIN_EXP__ (-382)
#endif
#ifndef __INT_FAST64_MAX__
#define __INT_FAST64_MAX__ 9223372036854775807L
#endif
#ifndef __GCC_ATOMIC_TEST_AND_SET_TRUEVAL
#define __GCC_ATOMIC_TEST_AND_SET_TRUEVAL 1
#endif
#ifndef __FLT_DIG__
#define __FLT_DIG__ 6
#endif
#ifndef __UINT_FAST64_TYPE__
#define __UINT_FAST64_TYPE__ long unsigned int
#endif
#ifndef __INT_MAX__
#define __INT_MAX__ 2147483647
#endif
#ifndef __amd64__
#define __amd64__ 1
#endif
#ifndef __INT64_TYPE__
#define __INT64_TYPE__ long int
#endif
#ifndef __FLT_MAX_EXP__
#define __FLT_MAX_EXP__ 128
#endif
#ifndef __ORDER_BIG_ENDIAN__
#define __ORDER_BIG_ENDIAN__ 4321
#endif
#ifndef __DBL_MANT_DIG__
#define __DBL_MANT_DIG__ 53
#endif
#ifndef __INT_LEAST64_MAX__
#define __INT_LEAST64_MAX__ 9223372036854775807L
#endif
#ifndef __DEC64_MIN__
#define __DEC64_MIN__ 1E-383DD
#endif
#ifndef __WINT_TYPE__
#define __WINT_TYPE__ unsigned int
#endif
#ifndef __UINT_LEAST32_TYPE__
#define __UINT_LEAST32_TYPE__ unsigned int
#endif
#ifndef __SIZEOF_SHORT__
#define __SIZEOF_SHORT__ 2
#endif
#ifndef __SSE__
#define __SSE__ 1
#endif
#ifndef __LDBL_MIN_EXP__
#define __LDBL_MIN_EXP__ (-16381)
#endif
#ifndef __INT_LEAST8_MAX__
#define __INT_LEAST8_MAX__ 127
#endif
#ifndef __SSP__
#define __SSP__ 1
#endif
#ifndef __SIZEOF_INT128__
#define __SIZEOF_INT128__ 16
#endif
#ifndef __LDBL_MAX_10_EXP__
#define __LDBL_MAX_10_EXP__ 4932
#endif
#ifndef __ATOMIC_RELAXED
#define __ATOMIC_RELAXED 0
#endif
#ifndef __DBL_EPSILON__
#define __DBL_EPSILON__ double(2.22044604925031308085e-16L)
#endif
#ifndef _LP64
#define _LP64 1
#endif
#ifndef __INT_LEAST32_TYPE__
#define __INT_LEAST32_TYPE__ int
#endif
#ifndef __SIZEOF_WCHAR_T__
#define __SIZEOF_WCHAR_T__ 4
#endif
#ifndef __UINT64_TYPE__
#define __UINT64_TYPE__ long unsigned int
#endif
#ifndef __INT_FAST8_TYPE__
#define __INT_FAST8_TYPE__ signed char
#endif
#ifndef __DBL_DECIMAL_DIG__
#define __DBL_DECIMAL_DIG__ 17
#endif
#ifndef __FXSR__
#define __FXSR__ 1
#endif
#ifndef __DEC_EVAL_METHOD__
#define __DEC_EVAL_METHOD__ 2
#endif
#ifndef __INTMAX_MAX__
#define __INTMAX_MAX__ 9223372036854775807L
#endif
#ifndef __BYTE_ORDER__
#define __BYTE_ORDER__ __ORDER_LITTLE_ENDIAN__
#endif
#ifndef __FLT_DENORM_MIN__
#define __FLT_DENORM_MIN__ 1.40129846432481707092e-45F
#endif
#ifndef __INT8_MAX__
#define __INT8_MAX__ 127
#endif
#ifndef __UINT_FAST32_TYPE__
#define __UINT_FAST32_TYPE__ long unsigned int
#endif
#ifndef __CHAR32_TYPE__
#define __CHAR32_TYPE__ unsigned int
#endif
#ifndef __FLT_MAX__
#define __FLT_MAX__ 3.40282346638528859812e+38F
#endif
#ifndef __INT32_TYPE__
#define __INT32_TYPE__ int
#endif
#ifndef __SIZEOF_DOUBLE__
#define __SIZEOF_DOUBLE__ 8
#endif
#ifndef __INTMAX_TYPE__
#define __INTMAX_TYPE__ long int
#endif
#ifndef __DEC128_MAX_EXP__
#define __DEC128_MAX_EXP__ 6145
#endif
#ifndef __ATOMIC_CONSUME
#define __ATOMIC_CONSUME 1
#endif
#ifndef __GNUC_MINOR__
#define __GNUC_MINOR__ 8
#endif
#ifndef __UINTMAX_MAX__
#define __UINTMAX_MAX__ 18446744073709551615UL
#endif
#ifndef __DEC32_MANT_DIG__
#define __DEC32_MANT_DIG__ 7
#endif
#ifndef __DBL_MAX_10_EXP__
#define __DBL_MAX_10_EXP__ 308
#endif
#ifndef __LDBL_DENORM_MIN__
#define __LDBL_DENORM_MIN__ 3.64519953188247460253e-4951L
#endif
#ifndef __STDC__
#define __STDC__ 1
#endif
#ifndef __PTRDIFF_TYPE__
#define __PTRDIFF_TYPE__ long int
#endif
#ifndef __ATOMIC_SEQ_CST
#define __ATOMIC_SEQ_CST 5
#endif
#ifndef __UINT32_TYPE__
#define __UINT32_TYPE__ unsigned int
#endif
#ifndef __UINTPTR_TYPE__
#define __UINTPTR_TYPE__ long unsigned int
#endif
#ifndef __DEC64_SUBNORMAL_MIN__
#define __DEC64_SUBNORMAL_MIN__ 0.000000000000001E-383DD
#endif
#ifndef __DEC128_MANT_DIG__
#define __DEC128_MANT_DIG__ 34
#endif
#ifndef __LDBL_MIN_10_EXP__
#define __LDBL_MIN_10_EXP__ (-4931)
#endif
#ifndef __SSE_MATH__
#define __SSE_MATH__ 1
#endif
#ifndef __SIZEOF_LONG_LONG__
#define __SIZEOF_LONG_LONG__ 8
#endif
#ifndef __GCC_ATOMIC_LLONG_LOCK_FREE
#define __GCC_ATOMIC_LLONG_LOCK_FREE 2
#endif
#ifndef __LDBL_DIG__
#define __LDBL_DIG__ 18
#endif
#ifndef __FLT_DECIMAL_DIG__
#define __FLT_DECIMAL_DIG__ 9
#endif
#ifndef __UINT_FAST16_MAX__
#define __UINT_FAST16_MAX__ 18446744073709551615UL
#endif
#ifndef __GNUC_GNU_INLINE__
#define __GNUC_GNU_INLINE__ 1
#endif
#ifndef __FLT_MIN_10_EXP__
#define __FLT_MIN_10_EXP__ (-37)
#endif
#ifndef __GCC_ATOMIC_SHORT_LOCK_FREE
#define __GCC_ATOMIC_SHORT_LOCK_FREE 2
#endif
#ifndef __UINT_FAST8_TYPE__
#define __UINT_FAST8_TYPE__ unsigned char
#endif
#ifndef _GNU_SOURCE
#define _GNU_SOURCE 1
#endif
#ifndef __ATOMIC_ACQ_REL
#define __ATOMIC_ACQ_REL 4
#endif
#ifndef __ATOMIC_RELEASE
#define __ATOMIC_RELEASE 3
#endif
#ifndef NDEBUG
#define NDEBUG 1
#endif
#ifndef RELEASE
#define RELEASE 1
#endif
#endif

// --- Include directories begin --- //
///usr/include/c++/4.8
///usr/include/x86_64-linux-gnu/c++/4.8
///usr/include/c++/4.8/backward
///usr/lib/gcc/x86_64-linux-gnu/4.8/include
///usr/local/include
///usr/lib/gcc/x86_64-linux-gnu/4.8/include-fixed
///usr/include/x86_64-linux-gnu
///usr/include
// --- Include directories end --- //


// --- Library directories begin --- //
///usr/lib/gcc/x86_64-linux-gnu/4.8/:/usr/lib/gcc/x86_64-linux-gnu/4.8/../../../x86_64-linux-gnu/:/usr/lib/gcc/x86_64-linux-gnu/4.8/../../../../lib/:/lib/x86_64-linux-gnu/:/lib/../lib/:/usr/lib/x86_64-linux-gnu/:/usr/lib/../lib/:/usr/lib/gcc/x86_64-linux-gnu/4.8/../../../:/lib/:/usr/lib/
// --- Library directories begin --- //

