/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2008 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTW61850DataAttributeMDO.h                                  */
/* DESCRIPTION:  Definitions of an IEC 61850-MDO                             */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 22 ***_NOBODY_*** "                                                     */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#pragma once

#include "GTWLibDll.h"

#include "GTWBaseDataObject.h"
#include "GTWMasterDataObjectTemplate.h"
#include "GTW61850QualityDefs.h"

static GTWDEFS_PARSED_OPTION_TYPE s_GTW61850MDO_allowedOptions[] =
{
  "ALIAS",PARSED_OPTION_STRING_VALUE,NULL,NULL, "TR_OPT_HELP_ALIAS", "Supply an alternate name for this point - Use double quotation marks for multiple words",
  "ALLOWWRITES",PARSED_OPTION_BOOL_VALUE,NULL,NULL, "TR_OPT_HELP_ALLOWWRITES", "deprecated",
  "ORCAT",PARSED_OPTION_CHOICE_VALUE,(void*)&GTWConfig::OrCatMasks[0],(void*)(size_t)GTWConfig::OrCatMasksLen, "TR_OPT_HELP_ORCAT", "origin category",
  "ORIDENT",PARSED_OPTION_STRING_VALUE,NULL,NULL, "TR_OPT_HELP_ORIDENT", "origin identifier",
  "CONTROL_TEST",PARSED_OPTION_BOOL_VALUE,NULL,NULL, "TR_OPT_HELP_CONTROL_TEST", "control test bit",
  "CONTROL_CHECK",PARSED_OPTION_CHOICE_VALUE,(void*)&GTWConfig::i61850CtrlCheck[0],(void*)(size_t)GTWConfig::i61850CtrlCheckLen, "TR_OPT_HELP_CONTROL_CHECK", "control check",
};

class GTW61850Client;

namespace tmw {
class DataSet;
class DataAttribute;
class ReportControl;
class Value;
};

class GTWLIB_API GTW61850DataAttributeMDO : public GTWMasterDataObjectTemplate<GTWBaseDataObject>
{
  friend class GTW61850Client;
  friend class GTW61850DataAttributeMDOEditor;
  friend class GTW61850CommandPointEditor;
  friend class OnDataChangeItem;

public:
  void updateMDOBool(bool b);
  void updateMDOLong(int b);
  void updateMDOULong(unsigned int b);
  void updateMDOFloat(float b);
  void updateMDODouble(double b);
  void updateMDOString(const CStdString& s);

  void SetVariantValue(double value);

  virtual bool FormatQualityString(char* buffer, int bufferSize, GTWEXPND_EXPANSION* pExpansion) override;

  void SetControlType()
  {
    m_bIsControl = true;
  }

public:
  DeclareClassInfo();
  GTW61850DataAttributeMDO();

  virtual ~GTW61850DataAttributeMDO();

  GTWBaseEditor* GetBaseEditor(const EditorCommandDTO& dto) override;
  static void Get61850QualityBitString(unsigned short q, CStdString& sBS);

  virtual CStdString GetFullName() override;
  const char* getQualityTagName()
  {
    return (const char*)m_sQualityTagName;
  }
  void setQualityTagName(const char* n)
  {
    m_sQualityTagName = n;
  }

  tmw61850::Value::Type getDesiredType()
  {
    return m_DesiredType;
  }

  tmw61850::Value::Type getValueType()
  {
    return m_pValueDataAttribute ? m_pValueDataAttribute->GetType() : tmw61850::Value::Type::UNKNOWN;
  }

  const char* getTimeTagName()
  {
    return m_sTimeTagName.c_str();
  }
  void setTimeTagName(const char* n)
  {
    m_sTimeTagName = n;
  }

  const char* getValueTagName()
  {
    return m_sValueTagName.c_str();
  }

  tmw61850::DataAttribute* getValueDataAttribute() { return m_pValueDataAttribute; }
  tmw61850::DataAttribute* getQualityDataAttribute() { return m_pQualityDataAttribute; }
  tmw61850::DataAttribute* getTimeDataAttribute() { return m_pTimeDataAttribute; }

  void setPointChangeReason(tmw61850::ClientPointChangeInfo::PointChangeReason changeReason)
  {
    m_changeReason = changeReason;
  }

  void setDirty(bool b)
  {
    m_bDirty = b;
  }

  void set61850Quality(I61850_QUALITY_TYPE q)
  {
    m_i61850Quality = q;
  }

  I61850_QUALITY_TYPE get61850Quality()
  {
    return m_i61850Quality;
  }

public:

  GTWDEFS_UPDTRSN GetGTWChangeReason(void);

  /*
  bool IsReadyToUpdate()
  {
    int cnt = 0;
    if (m_pValueDataAttribute != NULL)
      ++cnt;
    if (m_pQualityDataAttribute != NULL)
      ++cnt;
    if (m_pTimeDataAttribute != NULL)
      ++cnt;
    
    if (cnt <= m_iDirtyCount)
      return true;

    return false;
  }
  */


  virtual GTWDEFS_TYPE getMdoType(void)
  {
    return (m_vValue.GetType());
  }

  virtual bool checkMdoToMdoConverter(GTWCNVTR_TYPE cnvtrType)
  {
    if (
      cnvtrType == GTWCNVTR_TYPE_BOOL
      || cnvtrType == GTWCNVTR_TYPE_SHORT
      || cnvtrType == GTWCNVTR_TYPE_USHORT
      || cnvtrType == GTWCNVTR_TYPE_LONG
      || cnvtrType == GTWCNVTR_TYPE_ULONG
      || cnvtrType == GTWCNVTR_TYPE_SFLOAT
      || cnvtrType == GTWCNVTR_TYPE_DOUBLE
      )
    {
      return TMWDEFS_TRUE;
    }
    return TMWDEFS_FALSE;
  }

  virtual bool isControl() override;
  static bool isWriteableFC(const char* sFunctionalConstraint);
  virtual bool isWriteableNonControl() override;
  virtual bool isWriteable() override;
  virtual bool isCommandMDO() override;

  bool WriteMe(GTWDEFS_CTRL_MODE ctrlMode);
  void DeleteMe(void);
  void DoClientWrite();

  void SetClientNode(GTW61850Client *p) 
  {
    m_pClientNode = p;
  }
  
  GTW61850Client* GetClientNode(void)
  {
    return m_pClientNode;
  }
  
  //void UponInsert(GTWCollectionBase * pParent)
  //{
  //  GTWMasterDataObjectTemplate<GTWBaseDataObject>::UponInsert(pParent);

  //  int lll = 3;
  //}

  void SetControlBlock(GTW61850ControlBlock *p) 
  {
    if (m_pControlBlock)
    {
      m_pControlBlock->OnRemoveMDO(this);
    }

    m_pControlBlock = p;
    if (m_pControlBlock)
    {
      m_pControlBlock->OnAddMDO(this);
    }

    this->GetClientNode()->ResetDataAttrMdo(p, this);
  }
  
  GTW61850ControlBlock *GetControlBlock(void) 
  {
    return m_pControlBlock;
  }
  
  void InitializeValue(tmw61850::Value::Type type);

  void SetValue(tmw61850::DataAttribute *pDa, tmw61850::Value *pVal);
  void SetValue(tmw61850::Value *pVal);
  void SetQuality(tmw61850::Value *pVal);
  void SetTime(tmw61850::Value *pVal);

  void SetValue(CStdString v)    
  {
    m_vValue = v; 
    //updateInternalValue();
  }
  void SetValue(bool v)    
  { 
    m_vValue = v; 
    //updateInternalValue();
  }
  void SetValue(TMWTYPES_SHORT v)   
  { 
    m_vValue = v; 
    //updateInternalValue();
  }
  void SetValue(TMWTYPES_INT v)    
  { 
    m_vValue = v; 
    //updateInternalValue();
  }
  void SetValue(TMWTYPES_DOUBLE v)  
  { 
    m_vValue = v; 
    //updateInternalValue();
  }
  void SetValue(TMWTYPES_SFLOAT v)  
  { 
    m_vValue = v; 
    //updateInternalValue();
  }

  void setQuality(I61850_QUALITY_TYPE q) { m_i61850Quality = q; }
  //void SetLastReportedGMTTime(TMWDTIME t);
  I61850_QUALITY_TYPE getQuality(void) { return m_i61850Quality; }

  virtual GTWDEFS_STD_QLTY getMdoStdQuality(void);
  static I61850_QUALITY_TYPE getGTW61850Quality(const tmw::BitString* p61850Qual);
  static GTWDEFS_STD_QLTY getStdQuality(I61850_QUALITY_TYPE quality);
  static GTWDEFS_STD_QLTY getStdQuality(const tmw::BitString* p61850Qual);

  virtual void getMdoDbasDataId(GTWDEFS_DBAS_DATA_ID *pDbasDataId);

  virtual void getMdoValueAsString(const GTWEXPND_FORMAT &pFormat, CStdString &msg);
  virtual void getMdoValueAsVariant(GtwVariant &variant);

  GtwVariant getValueAsVarient();
  TMWTYPES_DOUBLE getValue();
  TMWTYPES_DOUBLE getLastValue();
  unsigned short getLastAlarmStatusSaved();
  //CStdString getValueAsString();
  void getValueAsString(CStdString &sValue);

  virtual void GetDescriptionColText( CStdString &itemStr );
  virtual bool GetValueColText(CStdString &itemStr );

  /* virtual functions */
  virtual void *bindMdoToSdoForReading(GTWSlaveDataObject *pUpdateSdo, GTWCNVTR_TYPE cnvtrType);

  virtual void *bindMdoToSdoForWriting(GTWSlaveDataObject *pWriteFromSdo,GTWCNVTR_TYPE cnvtrType);

  virtual bool SetCommandValue(GtwVariant &variantValue);

  //virtual const char *GetBaseName(void) { return (const char *)name; }
  virtual void updateMDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo);

  virtual CStdString GetMemberNameWithUserTagName(void)
  {
    return(m_sTagName);
  }

  virtual CStdString GetUtagOrMemberName(void)
  {
    return m_sAliasName.GetLength() > 0 ? m_sAliasName : GetMemberName();
  }

  virtual CStdString GetMemberName(void)
  {
    return (m_sTagName);
  }


  virtual CStdString GetItemName(void)
  {
    return(m_sTagName);
  }
  void getTime(TMWDTIME *pValue);

  CStdString GetControlBlockName();
  bool IsValueChanged();
  bool IsQualityChanged();
  bool IsTimeChanged();
  void ClearValueChanged();
  void ClearQualityChanged();
  void ClearTimeChanged();
  bool IsTime1970();

  //void SetAllowWrites(bool b) { m_bAllowWrites = b; }
  void ReadDataAttributes();

protected:
  virtual void updateMDO(GTWDEFS_UPDTRSN updateReason);

  virtual void SetDefaultOptions(void)
  {
    m_sAliasName = "";
    setMdoUserTagName(m_sAliasName);
    setControlParamDefaults();
    return(GTWMasterDataObjectTemplate<GTWBaseDataObject>::SetDefaultOptions());
  }
  
  GTWDEFS_STAT ParseOptionsField(const char *connectionToken, const char **ppOptionString) override
  {
    CStdString aliasName;
    CStdString aeInitValueMDO;
    CStdString aeResetValueMDO;
    CStdString aeResetValueMDOValue;
   
    TMWTYPES_UINT orCat = 0;
    TMWTYPES_UINT ctlCheck = 0;
    CStdString orIdent = "01";
    bool bTest = false;
    GTW61850Client* pClientNode = this->GetClientNode();

    if (ParseOptionsStringString(ppOptionString, s_GTW61850MDO_allowedOptions[0].name, aliasName))
    {
      m_sAliasName = aliasName;
      if (setMdoUserTagName(m_sAliasName) == false)
      {
        m_sAliasName = "";
        GtwBroadcastMessage::SendMessage(DestinationMaskEnum::Destination_Popup, BroadcastTypeEnum::Broadcast_Error, GtwLogger::SDG_Category_61850, connectionToken, "TR_FAILED_TO_APPLY_ALIAS_TO_61850_TAG", "Failed to apply alias '{{arg1}}' to 61850 tag (duplicate ?)", (const char *)aliasName);
        return GTWDEFS_STAT_NOT_VALID;
      }
    }
    else if (ParseOptionsString(ppOptionString, s_GTW61850MDO_allowedOptions[1].name))
    {

      LOG6(pClientNode->GetClientConnection(), GtwLogger::Severity_Warning, GtwLogger::SDG_Category_PointMap, "Point OPTION: %s is deprecated (it should be removed from the CSV file).", s_GTW61850MDO_allowedOptions[1].name);

      //m_bAllowWrites = TMWDEFS_TRUE;
    }
    else if (ParseOptionsHex(ppOptionString, s_GTW61850MDO_allowedOptions[2].name, &orCat))
    {
      if (orCat >= static_cast<int>(tmw61850::EnumDefs::OrCat::notsupported) && orCat <= static_cast<int>(tmw61850::EnumDefs::OrCat::process))
      {
        m_orCat = orCat;
      }
      else
      {
        return GTWDEFS_STAT_NOT_VALID;
      }
    }
    else if (ParseOptionsStringString(ppOptionString, s_GTW61850MDO_allowedOptions[3].name, orIdent))
    {
      m_sOrIdent = orIdent;
    }
    else if (ParseOptionsString(ppOptionString, s_GTW61850MDO_allowedOptions[4].name))
    {
      m_bControlTest = true;
    }
    else if (ParseOptionsHex(ppOptionString, s_GTW61850MDO_allowedOptions[5].name, &ctlCheck))
    {
      if (ctlCheck >= static_cast<int>(tmw61850::EnumDefs::Check::nocheck) && ctlCheck <= static_cast<int>(tmw61850::EnumDefs::Check::both))
      {
        m_controlCheck = ctlCheck;
      }
      else
      {
        return GTWDEFS_STAT_NOT_VALID;
      }
    }
    else
    {
      return(GTWMasterDataObjectTemplate<GTWBaseDataObject>::ParseOptionsField(connectionToken, ppOptionString));
    }

    return(GTWDEFS_STAT_SUCCESS);
  }

   void GetAllowedOptions( GTWDEFS_PARSED_OPTION_ARRAY &optionsArray  ) override
  {
    int maxOptions = isControl() ? TMWDEFS_ARRAY_LENGTH(s_GTW61850MDO_allowedOptions) : 2;
    int i;
    for (i = 0; i < maxOptions; i++)
    {
      optionsArray.push_back(s_GTW61850MDO_allowedOptions[i]);
    }
    GTWMasterDataObjectTemplate<GTWBaseDataObject>::GetAllowedOptions(optionsArray);
    return;
  }

private:
  bool                m_bInitialized;
  CStdString          m_sTagName;
  CStdString          m_sValueTagName;
  CStdString          m_sQualityTagName;
  CStdString          m_sTimeTagName;
  CStdString          m_sAliasName;
  I61850_QUALITY_TYPE m_i61850Quality;

  GtwVariant           m_vLastValue;
  I61850_QUALITY_TYPE  m_iLast61850Quality;
  TMWDTIME             m_LastReportedTime;

  bool m_bDirty;
  char m_cWritableNC;
  tmw61850::ClientPointChangeInfo::PointChangeReason m_changeReason;

  tmw61850::DataAttribute *m_pValueDataAttribute;
  tmw61850::DataAttribute *m_pQualityDataAttribute;
  tmw61850::DataAttribute *m_pTimeDataAttribute;
  tmw61850::ClientControlPoint *m_pControlInfo;
  tmw61850::Value::Type m_DesiredType;

  bool WriteCurrentValueToServer();
  void PrepareControl(const tmw61850::Value &value);
  void setControlParamDefaults();
  void _updateMdo(TMWTYPES_ULONG reason);
  void SetLastValue();
  void SetLastQuality();
  void SetLastReportedTime();
  void SetLastValueSaved(unsigned short v);

  void ReadDataAttribute(tmw61850::DataAttribute *pAttr);
  GTWDEFS_STAT CompareTagName( CStdString &tagName);
  GTW61850Client *m_pClientNode;
  GTW61850ControlBlock *m_pControlBlock;
  unsigned short  m_vAlarmStatusSaved;
  bool m_bValueChanged;
  bool m_bQualityChanged;
  bool m_bTimeChanged;
  //bool m_bAllowWrites;

  // Control settings
  TMWTYPES_UINT m_orCat;
  TMWTYPES_UINT m_controlCheck;
  std::string m_sOrIdent;
  bool m_bControlTest;

  //tmw::AutoCriticalSection m_InternalValueCriticalSection;
  //tmw61850::DataAttribute *m_pInternalValueDataAttribute; // stays in sync with m_vValue;

  //void updateInternalValue();
  bool m_bIsControl; // used for allowed options in add a control editor
};