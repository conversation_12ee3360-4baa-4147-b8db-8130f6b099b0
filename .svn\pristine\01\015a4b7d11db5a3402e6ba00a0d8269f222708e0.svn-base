/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWModbusCommandSdo.h                                       */
/* DESCRIPTION:  Interface definition for Modbus Command SDOs                */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/

#ifndef GTWModbusCommandSdo_DEFINITIONS
#define GTWModbusCommandSdo_DEFINITIONS

#include "GTWModbusSlaveDataObject.h"

void init();

class GTWModbusCommandSdo : public GTWModbusSlaveDataObject
{
protected:
  bool      synchronizeDownstream;
  
  TMWDEFS_COMMAND_STATUS cmd_status; 
  
  virtual void writeSdoCB(GTWDEFS_CTRL_STAT newCtrlStat, void *pExtraCBData);
  
public:
  DeclareClassInfo();
  GTWModbusCommandSdo(
    TMWTYPES_USHORT nPointNum, 
    TMWTYPES_UCHAR nDataType, 
    bool synchronizeDownstream);
  
  
  void setStatus(TMWDEFS_COMMAND_STATUS status) 
  { 
    cmd_status = status; 
  }
  TMWDEFS_COMMAND_STATUS getStatus(void) 
  { 
    return (cmd_status); 
  }

  bool getSynchronizeDownstream() 
  {
    return (synchronizeDownstream);
  }
};

#endif // GTWModbusCommandSdo_DEFINITIONS 


