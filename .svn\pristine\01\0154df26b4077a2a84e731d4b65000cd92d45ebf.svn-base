sdnp.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
sdnp.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
sdnp.L3.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c102.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c102.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c102.BayControllerQ/QB1XSWI2.Pos.stSeld,c102,c102.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QB1XSWI2.Pos.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QC1XSWI3.Pos.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QA1XCBR1.Pos.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c102.BayControllerQ/QB1CILO2.EnaOpn.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QB1CILO2.EnaCls.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QC1CILO3.EnaOpn.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QC1CILO3.EnaCls.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QA1CILO1.EnaOpn.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QA1CILO1.EnaCls.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QB1CSWI2.Pos.stSeld,c102,c102.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c102,c102.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c102,c102.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QB1CSWI2.Pos.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QC1CSWI3.Pos.stSeld,c102,c102.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c102,c102.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c102,c102.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QC1CSWI3.Pos.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QA1CSWI1.Pos.stSeld,c102,c102.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c102,c102.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c102,c102.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/QA1CSWI1.Pos.stVal,c102,c102.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c102.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c102.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c102,c102.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c102,c102.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c102,c102.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c102,c102.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c102,c102.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c102,c102.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c102.BayControllerQ/T1MMXU1.PhV.phsC.range,c102,c102.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c102.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c103.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c103.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c103.BayControllerQ/QB1XSWI2.Pos.stSeld,c103,c103.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QB1XSWI2.Pos.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QC1XSWI3.Pos.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QA1XCBR1.Pos.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c103.BayControllerQ/QB1CILO2.EnaOpn.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QB1CILO2.EnaCls.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QC1CILO3.EnaOpn.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QC1CILO3.EnaCls.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QA1CILO1.EnaOpn.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QA1CILO1.EnaCls.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QB1CSWI2.Pos.stSeld,c103,c103.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c103,c103.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c103,c103.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QB1CSWI2.Pos.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QC1CSWI3.Pos.stSeld,c103,c103.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c103,c103.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c103,c103.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QC1CSWI3.Pos.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QA1CSWI1.Pos.stSeld,c103,c103.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c103,c103.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c103,c103.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/QA1CSWI1.Pos.stVal,c103,c103.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c103.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c103.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c103,c103.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c103,c103.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c103,c103.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c103,c103.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c103,c103.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c103,c103.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c103.BayControllerQ/T1MMXU1.PhV.phsC.range,c103,c103.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c103.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c104.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c104.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c104.BayControllerQ/QB1XSWI2.Pos.stSeld,c104,c104.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QB1XSWI2.Pos.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QC1XSWI3.Pos.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QA1XCBR1.Pos.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c104.BayControllerQ/QB1CILO2.EnaOpn.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QB1CILO2.EnaCls.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QC1CILO3.EnaOpn.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QC1CILO3.EnaCls.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QA1CILO1.EnaOpn.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QA1CILO1.EnaCls.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QB1CSWI2.Pos.stSeld,c104,c104.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c104,c104.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c104,c104.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QB1CSWI2.Pos.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QC1CSWI3.Pos.stSeld,c104,c104.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c104,c104.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c104,c104.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QC1CSWI3.Pos.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QA1CSWI1.Pos.stSeld,c104,c104.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c104,c104.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c104,c104.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/QA1CSWI1.Pos.stVal,c104,c104.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c104.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c104.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c104,c104.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c104,c104.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c104,c104.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c104,c104.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c104,c104.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c104,c104.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c104.BayControllerQ/T1MMXU1.PhV.phsC.range,c104,c104.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c104.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c105.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c105.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c105.BayControllerQ/QB1XSWI2.Pos.stSeld,c105,c105.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QB1XSWI2.Pos.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QC1XSWI3.Pos.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QA1XCBR1.Pos.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c105.BayControllerQ/QB1CILO2.EnaOpn.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QB1CILO2.EnaCls.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QC1CILO3.EnaOpn.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QC1CILO3.EnaCls.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QA1CILO1.EnaOpn.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QA1CILO1.EnaCls.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QB1CSWI2.Pos.stSeld,c105,c105.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c105,c105.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c105,c105.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QB1CSWI2.Pos.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QC1CSWI3.Pos.stSeld,c105,c105.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c105,c105.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c105,c105.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QC1CSWI3.Pos.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QA1CSWI1.Pos.stSeld,c105,c105.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c105,c105.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c105,c105.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/QA1CSWI1.Pos.stVal,c105,c105.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c105.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c105.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c105,c105.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c105,c105.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c105,c105.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c105,c105.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c105,c105.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c105,c105.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c105.BayControllerQ/T1MMXU1.PhV.phsC.range,c105,c105.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c105.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c106.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c106.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c106.BayControllerQ/QB1XSWI2.Pos.stSeld,c106,c106.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QB1XSWI2.Pos.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QC1XSWI3.Pos.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QA1XCBR1.Pos.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c106.BayControllerQ/QB1CILO2.EnaOpn.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QB1CILO2.EnaCls.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QC1CILO3.EnaOpn.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QC1CILO3.EnaCls.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QA1CILO1.EnaOpn.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QA1CILO1.EnaCls.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QB1CSWI2.Pos.stSeld,c106,c106.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c106,c106.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c106,c106.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QB1CSWI2.Pos.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QC1CSWI3.Pos.stSeld,c106,c106.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c106,c106.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c106,c106.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QC1CSWI3.Pos.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QA1CSWI1.Pos.stSeld,c106,c106.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c106,c106.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c106,c106.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/QA1CSWI1.Pos.stVal,c106,c106.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c106.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c106.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c106,c106.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c106,c106.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c106,c106.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c106,c106.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c106,c106.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c106,c106.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c106.BayControllerQ/T1MMXU1.PhV.phsC.range,c106,c106.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c106.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c107.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c107.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c107.BayControllerQ/QB1XSWI2.Pos.stSeld,c107,c107.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QB1XSWI2.Pos.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QC1XSWI3.Pos.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QA1XCBR1.Pos.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c107.BayControllerQ/QB1CILO2.EnaOpn.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QB1CILO2.EnaCls.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QC1CILO3.EnaOpn.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QC1CILO3.EnaCls.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QA1CILO1.EnaOpn.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QA1CILO1.EnaCls.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QB1CSWI2.Pos.stSeld,c107,c107.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c107,c107.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c107,c107.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QB1CSWI2.Pos.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QC1CSWI3.Pos.stSeld,c107,c107.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c107,c107.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c107,c107.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QC1CSWI3.Pos.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QA1CSWI1.Pos.stSeld,c107,c107.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c107,c107.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c107,c107.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/QA1CSWI1.Pos.stVal,c107,c107.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c107.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c107.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c107,c107.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c107,c107.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c107,c107.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c107,c107.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c107,c107.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c107,c107.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c107.BayControllerQ/T1MMXU1.PhV.phsC.range,c107,c107.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c107.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c108.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c108.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c108.BayControllerQ/QB1XSWI2.Pos.stSeld,c108,c108.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QB1XSWI2.Pos.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QC1XSWI3.Pos.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QA1XCBR1.Pos.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c108.BayControllerQ/QB1CILO2.EnaOpn.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QB1CILO2.EnaCls.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QC1CILO3.EnaOpn.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QC1CILO3.EnaCls.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QA1CILO1.EnaOpn.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QA1CILO1.EnaCls.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QB1CSWI2.Pos.stSeld,c108,c108.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c108,c108.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c108,c108.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QB1CSWI2.Pos.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QC1CSWI3.Pos.stSeld,c108,c108.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c108,c108.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c108,c108.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QC1CSWI3.Pos.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QA1CSWI1.Pos.stSeld,c108,c108.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c108,c108.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c108,c108.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/QA1CSWI1.Pos.stVal,c108,c108.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c108.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c108.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c108,c108.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c108,c108.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c108,c108.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c108,c108.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c108,c108.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c108,c108.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c108.BayControllerQ/T1MMXU1.PhV.phsC.range,c108,c108.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c108.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c109.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c109.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c109.BayControllerQ/QB1XSWI2.Pos.stSeld,c109,c109.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QB1XSWI2.Pos.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QC1XSWI3.Pos.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QA1XCBR1.Pos.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c109.BayControllerQ/QB1CILO2.EnaOpn.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QB1CILO2.EnaCls.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QC1CILO3.EnaOpn.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QC1CILO3.EnaCls.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QA1CILO1.EnaOpn.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QA1CILO1.EnaCls.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QB1CSWI2.Pos.stSeld,c109,c109.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c109,c109.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c109,c109.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QB1CSWI2.Pos.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QC1CSWI3.Pos.stSeld,c109,c109.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c109,c109.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c109,c109.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QC1CSWI3.Pos.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QA1CSWI1.Pos.stSeld,c109,c109.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c109,c109.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c109,c109.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/QA1CSWI1.Pos.stVal,c109,c109.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c109.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c109.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c109,c109.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c109,c109.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c109,c109.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c109,c109.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c109,c109.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c109,c109.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c109.BayControllerQ/T1MMXU1.PhV.phsC.range,c109,c109.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c109.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c110.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c110.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c110.BayControllerQ/QB1XSWI2.Pos.stSeld,c110,c110.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QB1XSWI2.Pos.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QC1XSWI3.Pos.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QA1XCBR1.Pos.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c110.BayControllerQ/QB1CILO2.EnaOpn.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QB1CILO2.EnaCls.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QC1CILO3.EnaOpn.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QC1CILO3.EnaCls.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QA1CILO1.EnaOpn.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QA1CILO1.EnaCls.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QB1CSWI2.Pos.stSeld,c110,c110.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c110,c110.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c110,c110.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QB1CSWI2.Pos.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QC1CSWI3.Pos.stSeld,c110,c110.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c110,c110.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c110,c110.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QC1CSWI3.Pos.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QA1CSWI1.Pos.stSeld,c110,c110.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c110,c110.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c110,c110.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/QA1CSWI1.Pos.stVal,c110,c110.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c110.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c110.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c110,c110.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c110,c110.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c110,c110.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c110,c110.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c110,c110.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c110,c110.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c110.BayControllerQ/T1MMXU1.PhV.phsC.range,c110,c110.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c110.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c111.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c111.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c111.BayControllerQ/QB1XSWI2.Pos.stSeld,c111,c111.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QB1XSWI2.Pos.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QC1XSWI3.Pos.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QA1XCBR1.Pos.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c111.BayControllerQ/QB1CILO2.EnaOpn.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QB1CILO2.EnaCls.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QC1CILO3.EnaOpn.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QC1CILO3.EnaCls.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QA1CILO1.EnaOpn.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QA1CILO1.EnaCls.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QB1CSWI2.Pos.stSeld,c111,c111.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c111,c111.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c111,c111.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QB1CSWI2.Pos.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QC1CSWI3.Pos.stSeld,c111,c111.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c111,c111.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c111,c111.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QC1CSWI3.Pos.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QA1CSWI1.Pos.stSeld,c111,c111.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c111,c111.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c111,c111.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/QA1CSWI1.Pos.stVal,c111,c111.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c111.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c111.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c111,c111.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c111,c111.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c111,c111.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c111,c111.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c111,c111.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c111,c111.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c111.BayControllerQ/T1MMXU1.PhV.phsC.range,c111,c111.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c111.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c112.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c112.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c112.BayControllerQ/QB1XSWI2.Pos.stSeld,c112,c112.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QB1XSWI2.Pos.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QC1XSWI3.Pos.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QA1XCBR1.Pos.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c112.BayControllerQ/QB1CILO2.EnaOpn.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QB1CILO2.EnaCls.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QC1CILO3.EnaOpn.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QC1CILO3.EnaCls.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QA1CILO1.EnaOpn.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QA1CILO1.EnaCls.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QB1CSWI2.Pos.stSeld,c112,c112.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c112,c112.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c112,c112.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QB1CSWI2.Pos.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QC1CSWI3.Pos.stSeld,c112,c112.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c112,c112.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c112,c112.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QC1CSWI3.Pos.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QA1CSWI1.Pos.stSeld,c112,c112.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c112,c112.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c112,c112.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/QA1CSWI1.Pos.stVal,c112,c112.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c112.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c112.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c112,c112.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c112,c112.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c112,c112.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c112,c112.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c112,c112.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c112,c112.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c112.BayControllerQ/T1MMXU1.PhV.phsC.range,c112,c112.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c112.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c113.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c113.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c113.BayControllerQ/QB1XSWI2.Pos.stSeld,c113,c113.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QB1XSWI2.Pos.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QC1XSWI3.Pos.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QA1XCBR1.Pos.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c113.BayControllerQ/QB1CILO2.EnaOpn.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QB1CILO2.EnaCls.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QC1CILO3.EnaOpn.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QC1CILO3.EnaCls.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QA1CILO1.EnaOpn.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QA1CILO1.EnaCls.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QB1CSWI2.Pos.stSeld,c113,c113.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c113,c113.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c113,c113.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QB1CSWI2.Pos.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QC1CSWI3.Pos.stSeld,c113,c113.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c113,c113.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c113,c113.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QC1CSWI3.Pos.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QA1CSWI1.Pos.stSeld,c113,c113.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c113,c113.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c113,c113.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/QA1CSWI1.Pos.stVal,c113,c113.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c113.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c113.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c113,c113.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c113,c113.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c113,c113.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c113,c113.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c113,c113.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c113,c113.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c113.BayControllerQ/T1MMXU1.PhV.phsC.range,c113,c113.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c113.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c114.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c114.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c114.BayControllerQ/QB1XSWI2.Pos.stSeld,c114,c114.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QB1XSWI2.Pos.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QC1XSWI3.Pos.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QA1XCBR1.Pos.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c114.BayControllerQ/QB1CILO2.EnaOpn.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QB1CILO2.EnaCls.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QC1CILO3.EnaOpn.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QC1CILO3.EnaCls.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QA1CILO1.EnaOpn.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QA1CILO1.EnaCls.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QB1CSWI2.Pos.stSeld,c114,c114.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c114,c114.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c114,c114.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QB1CSWI2.Pos.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QC1CSWI3.Pos.stSeld,c114,c114.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c114,c114.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c114,c114.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QC1CSWI3.Pos.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QA1CSWI1.Pos.stSeld,c114,c114.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c114,c114.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c114,c114.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/QA1CSWI1.Pos.stVal,c114,c114.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c114.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c114.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c114,c114.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c114,c114.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c114,c114.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c114,c114.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c114,c114.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c114,c114.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c114.BayControllerQ/T1MMXU1.PhV.phsC.range,c114,c114.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c114.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c115.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c115.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c115.BayControllerQ/QB1XSWI2.Pos.stSeld,c115,c115.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QB1XSWI2.Pos.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QC1XSWI3.Pos.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QA1XCBR1.Pos.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c115.BayControllerQ/QB1CILO2.EnaOpn.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QB1CILO2.EnaCls.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QC1CILO3.EnaOpn.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QC1CILO3.EnaCls.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QA1CILO1.EnaOpn.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QA1CILO1.EnaCls.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QB1CSWI2.Pos.stSeld,c115,c115.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c115,c115.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c115,c115.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QB1CSWI2.Pos.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QC1CSWI3.Pos.stSeld,c115,c115.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c115,c115.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c115,c115.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QC1CSWI3.Pos.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QA1CSWI1.Pos.stSeld,c115,c115.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c115,c115.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c115,c115.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/QA1CSWI1.Pos.stVal,c115,c115.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c115.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c115.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c115,c115.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c115,c115.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c115,c115.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c115,c115.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c115,c115.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c115,c115.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c115.BayControllerQ/T1MMXU1.PhV.phsC.range,c115,c115.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c115.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c116.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c116.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c116.BayControllerQ/QB1XSWI2.Pos.stSeld,c116,c116.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QB1XSWI2.Pos.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QC1XSWI3.Pos.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QA1XCBR1.Pos.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c116.BayControllerQ/QB1CILO2.EnaOpn.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QB1CILO2.EnaCls.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QC1CILO3.EnaOpn.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QC1CILO3.EnaCls.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QA1CILO1.EnaOpn.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QA1CILO1.EnaCls.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QB1CSWI2.Pos.stSeld,c116,c116.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c116,c116.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c116,c116.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QB1CSWI2.Pos.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QC1CSWI3.Pos.stSeld,c116,c116.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c116,c116.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c116,c116.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QC1CSWI3.Pos.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QA1CSWI1.Pos.stSeld,c116,c116.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c116,c116.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c116,c116.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/QA1CSWI1.Pos.stVal,c116,c116.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c116.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c116.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c116,c116.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c116,c116.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c116,c116.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c116,c116.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c116,c116.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c116,c116.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c116.BayControllerQ/T1MMXU1.PhV.phsC.range,c116,c116.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c116.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c117.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c117.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c117.BayControllerQ/QB1XSWI2.Pos.stSeld,c117,c117.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QB1XSWI2.Pos.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QC1XSWI3.Pos.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QA1XCBR1.Pos.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c117.BayControllerQ/QB1CILO2.EnaOpn.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QB1CILO2.EnaCls.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QC1CILO3.EnaOpn.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QC1CILO3.EnaCls.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QA1CILO1.EnaOpn.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QA1CILO1.EnaCls.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QB1CSWI2.Pos.stSeld,c117,c117.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c117,c117.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c117,c117.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QB1CSWI2.Pos.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QC1CSWI3.Pos.stSeld,c117,c117.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c117,c117.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c117,c117.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QC1CSWI3.Pos.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QA1CSWI1.Pos.stSeld,c117,c117.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c117,c117.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c117,c117.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/QA1CSWI1.Pos.stVal,c117,c117.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c117.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c117.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c117,c117.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c117,c117.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c117,c117.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c117,c117.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c117,c117.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c117,c117.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c117.BayControllerQ/T1MMXU1.PhV.phsC.range,c117,c117.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c117.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c118.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c118.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c118.BayControllerQ/QB1XSWI2.Pos.stSeld,c118,c118.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QB1XSWI2.Pos.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QC1XSWI3.Pos.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QA1XCBR1.Pos.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c118.BayControllerQ/QB1CILO2.EnaOpn.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QB1CILO2.EnaCls.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QC1CILO3.EnaOpn.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QC1CILO3.EnaCls.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QA1CILO1.EnaOpn.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QA1CILO1.EnaCls.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QB1CSWI2.Pos.stSeld,c118,c118.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c118,c118.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c118,c118.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QB1CSWI2.Pos.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QC1CSWI3.Pos.stSeld,c118,c118.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c118,c118.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c118,c118.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QC1CSWI3.Pos.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QA1CSWI1.Pos.stSeld,c118,c118.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c118,c118.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c118,c118.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/QA1CSWI1.Pos.stVal,c118,c118.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c118.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c118.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c118,c118.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c118,c118.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c118,c118.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c118,c118.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c118,c118.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c118,c118.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c118.BayControllerQ/T1MMXU1.PhV.phsC.range,c118,c118.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c118.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c119.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c119.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c119.BayControllerQ/QB1XSWI2.Pos.stSeld,c119,c119.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QB1XSWI2.Pos.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QC1XSWI3.Pos.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QA1XCBR1.Pos.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c119.BayControllerQ/QB1CILO2.EnaOpn.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QB1CILO2.EnaCls.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QC1CILO3.EnaOpn.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QC1CILO3.EnaCls.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QA1CILO1.EnaOpn.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QA1CILO1.EnaCls.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QB1CSWI2.Pos.stSeld,c119,c119.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c119,c119.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c119,c119.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QB1CSWI2.Pos.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QC1CSWI3.Pos.stSeld,c119,c119.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c119,c119.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c119,c119.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QC1CSWI3.Pos.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QA1CSWI1.Pos.stSeld,c119,c119.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c119,c119.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c119,c119.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/QA1CSWI1.Pos.stVal,c119,c119.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c119.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c119.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c119,c119.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c119,c119.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c119,c119.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c119,c119.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c119,c119.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c119,c119.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c119.BayControllerQ/T1MMXU1.PhV.phsC.range,c119,c119.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c119.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c120.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c120.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c120.BayControllerQ/QB1XSWI2.Pos.stSeld,c120,c120.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QB1XSWI2.Pos.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QC1XSWI3.Pos.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QA1XCBR1.Pos.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c120.BayControllerQ/QB1CILO2.EnaOpn.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QB1CILO2.EnaCls.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QC1CILO3.EnaOpn.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QC1CILO3.EnaCls.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QA1CILO1.EnaOpn.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QA1CILO1.EnaCls.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QB1CSWI2.Pos.stSeld,c120,c120.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c120,c120.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c120,c120.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QB1CSWI2.Pos.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QC1CSWI3.Pos.stSeld,c120,c120.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c120,c120.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c120,c120.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QC1CSWI3.Pos.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QA1CSWI1.Pos.stSeld,c120,c120.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c120,c120.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c120,c120.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/QA1CSWI1.Pos.stVal,c120,c120.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c120.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c120.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c120,c120.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c120,c120.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c120,c120.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c120,c120.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c120,c120.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c120,c120.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c120.BayControllerQ/T1MMXU1.PhV.phsC.range,c120,c120.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c120.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c121.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
c121.BayControllerQ/LLN0_STAT_READ_DATASET,,,,1,,,,,,,,,,Off,0,,,,,USER
c121.BayControllerQ/QB1XSWI2.Pos.stSeld,c121,c121.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QB1XSWI2.Pos.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QB1XSWI2.Pos.q,132,BayControllerQ/QB1XSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QC1XSWI3.Pos.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QC1XSWI3.Pos.q,132,BayControllerQ/QC1XSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QA1XCBR1.Pos.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QA1XCBR1.Pos.q,132,BayControllerQ/QA1XCBR1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.RequestQsize,,,,8,,,,,,,,,,0,0,,,,,USER
c121.BayControllerQ/QB1CILO2.EnaOpn.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaOpn.q,131,BayControllerQ/QB1CILO2.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QB1CILO2.EnaCls.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CILO2.EnaCls.q,131,BayControllerQ/QB1CILO2.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QC1CILO3.EnaOpn.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaOpn.q,131,BayControllerQ/QC1CILO3.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QC1CILO3.EnaCls.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CILO3.EnaCls.q,131,BayControllerQ/QC1CILO3.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QA1CILO1.EnaOpn.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaOpn.q,131,BayControllerQ/QA1CILO1.EnaOpn.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QA1CILO1.EnaCls.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CILO1.EnaCls.q,131,BayControllerQ/QA1CILO1.EnaCls.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QB1CSWI2.Pos.stSeld,c121,c121.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QB1CSWI2.Pos.origin.orCat,c121,c121.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QB1CSWI2.Pos.origin.orIdent,c121,c121.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QB1CSWI2.Pos.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QB1CSWI2.Pos.q,132,BayControllerQ/QB1CSWI2.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QC1CSWI3.Pos.stSeld,c121,c121.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QC1CSWI3.Pos.origin.orCat,c121,c121.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QC1CSWI3.Pos.origin.orIdent,c121,c121.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QC1CSWI3.Pos.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QC1CSWI3.Pos.q,132,BayControllerQ/QC1CSWI3.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QA1CSWI1.Pos.stSeld,c121,c121.BayControllerQ/LLN0.STAT,,131,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QA1CSWI1.Pos.origin.orCat,c121,c121.BayControllerQ/LLN0.STAT,,133,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QA1CSWI1.Pos.origin.orIdent,c121,c121.BayControllerQ/LLN0.STAT,,137,,PDS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/QA1CSWI1.Pos.stVal,c121,c121.BayControllerQ/LLN0.STAT,BayControllerQ/QA1CSWI1.Pos.q,132,BayControllerQ/QA1CSWI1.Pos.t,PDS,,,,,,,,,,,,,,I61850_MDO
c121.pp_READ_POINTS,,,,1,,,,,,,,,,Off,0,,,,,USER
c121.BayControllerQ/T1MMXU1.A.phsA.cVal.mag.f,c121,c121.pp,BayControllerQ/T1MMXU1.A.phsA.q,135,BayControllerQ/T1MMXU1.A.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/T1MMXU1.A.phsB.cVal.mag.f,c121,c121.pp,BayControllerQ/T1MMXU1.A.phsB.q,135,BayControllerQ/T1MMXU1.A.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/T1MMXU1.A.phsC.cVal.mag.f,c121,c121.pp,BayControllerQ/T1MMXU1.A.phsC.q,135,BayControllerQ/T1MMXU1.A.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/T1MMXU1.PhV.phsA.cVal.mag.f,c121,c121.pp,BayControllerQ/T1MMXU1.PhV.phsA.q,135,BayControllerQ/T1MMXU1.PhV.phsA.t,PPS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/T1MMXU1.PhV.phsB.cVal.mag.f,c121,c121.pp,BayControllerQ/T1MMXU1.PhV.phsB.q,135,BayControllerQ/T1MMXU1.PhV.phsB.t,PPS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/T1MMXU1.PhV.phsC.cVal.mag.f,c121,c121.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,135,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c121.BayControllerQ/T1MMXU1.PhV.phsC.range,c121,c121.pp,BayControllerQ/T1MMXU1.PhV.phsC.q,133,BayControllerQ/T1MMXU1.PhV.phsC.t,PPS,,,,,,,,,,,,,,I61850_MDO
c121.ResponseQsize,,,,8,,,,,,,,,,0,0,,,,,USER
mdnp.ChannelActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
mdnp.ChannelPhysResetControl,,,,1,,,,,,,,,,Off,0,,,,,USER
mdnp.L4.ColdRestartNow,,,,1,,,,,,,,,,Off,0,,,,,USER
mdnp.L4.SessionActiveControl,,,,1,,,,,,,,,,On,0,,,,,USER
mdnp.L4.WarmRestartNow,,,,1,,,,,,,,,,Off,0,,,,,USER
,mdnp,4,,1,0,,,,,,,,,,,,,,
mdnp.L4.CreateTagsAuto,,,,1,,,,,,,,,,Off,0,,,,,USER
toggle,,,,,,,,,sdnp,3,,1,0,,,"square(0,1,1000)",,,,
