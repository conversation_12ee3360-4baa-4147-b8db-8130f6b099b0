/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWopc.cpp                                                  */
/* DESCRIPTION:  Definitions of an OPC-specific SDO class                    */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 37 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif

#if USE_OPC_44

#include "WinEventManager.h"
#include "OPCProps.h"
#include "GTWOpcServerCallBack.h"
#include "GTWOpcSlaveDataObject.h"
#include "../GTWOsUtils/GtwOsDateTime.h"


ImplementClassBaseInfo(GTWOpcSlaveDataObject, GTWBaseDataObject, pClassInfo1, new GTWOpcSlaveDataObject())

/***************************************************************************/

class GTWOpcReadConverter
{
public:
  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality) = 0;
  virtual void Delete(void) = 0;
};

/***************************************************************************/

class GTWOpcWriteConverter
{
public:
  GTWOpcWriteConverter()
  {
  }
  virtual ~GTWOpcWriteConverter()
  {
  }

  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo) = 0;
  virtual void Delete(void) = 0;

private:
};

class GTWOpcReadConverterBool : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<bool>* m_pOpcBoolReadCnvtr;

public:
  GTWOpcReadConverterBool(GTWReadConverterTemplate<bool>* pBoolReadCnvtr) :
    m_pOpcBoolReadCnvtr(pBoolReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    bool boolValue;

    m_pOpcBoolReadCnvtr->getValue(NULL, &boolValue, &stdQuality);
    pOpcSdo->m_OPCvalue.boolVal = boolValue != true ? 0 : -1;
    pOpcSdo->m_OPCvalue.vt = VT_BOOL;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcBoolReadCnvtr)
    {
      delete m_pOpcBoolReadCnvtr;
      m_pOpcBoolReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterChar : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_CHAR>* m_pOpcCharReadCnvtr;

public:
  GTWOpcReadConverterChar(GTWReadConverterTemplate<TMWTYPES_CHAR>* pCharReadCnvtr) :
    m_pOpcCharReadCnvtr(pCharReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcCharReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.cVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_I1;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcCharReadCnvtr)
    {
      delete m_pOpcCharReadCnvtr;
      m_pOpcCharReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterShort : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_SHORT>* m_pOpcShortReadCnvtr;

public:
  GTWOpcReadConverterShort(GTWReadConverterTemplate<TMWTYPES_SHORT>* pShortReadCnvtr) :
    m_pOpcShortReadCnvtr(pShortReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcShortReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.iVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_I2;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcShortReadCnvtr)
    {
      delete m_pOpcShortReadCnvtr;
      m_pOpcShortReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterDouble : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_DOUBLE>* m_pOpcDoubleReadCnvtr;

public:
  GTWOpcReadConverterDouble(GTWReadConverterTemplate<TMWTYPES_DOUBLE>* pDoubleReadCnvtr) :
    m_pOpcDoubleReadCnvtr(pDoubleReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcDoubleReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.dblVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_R8;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcDoubleReadCnvtr)
    {
      delete m_pOpcDoubleReadCnvtr;
      m_pOpcDoubleReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterSFloat : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_SFLOAT>* m_pOpcSfloatReadCnvtr;

public:
  GTWOpcReadConverterSFloat(GTWReadConverterTemplate<TMWTYPES_SFLOAT>* pSfloatReadCnvtr) :
    m_pOpcSfloatReadCnvtr(pSfloatReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcSfloatReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.fltVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_R4;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcSfloatReadCnvtr)
    {
      delete m_pOpcSfloatReadCnvtr;
      m_pOpcSfloatReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterLong : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_INT>* m_pOpcLongReadCnvtr;

public:
  GTWOpcReadConverterLong(GTWReadConverterTemplate<TMWTYPES_INT>* pLongReadCnvtr) :
    m_pOpcLongReadCnvtr(pLongReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcLongReadCnvtr->getValue(NULL, (TMWTYPES_INT*)&pOpcSdo->m_OPCvalue.lVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_I4;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcLongReadCnvtr)
    {
      delete m_pOpcLongReadCnvtr;
      m_pOpcLongReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterULong : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_UINT>* m_pOpcULongReadCnvtr;

public:
  GTWOpcReadConverterULong(GTWReadConverterTemplate<TMWTYPES_UINT>* pULongReadCnvtr) :
    m_pOpcULongReadCnvtr(pULongReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcULongReadCnvtr->getValue(NULL, (TMWTYPES_UINT*)&pOpcSdo->m_OPCvalue.ulVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_UI4;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcULongReadCnvtr)
    {
      delete m_pOpcULongReadCnvtr;
      m_pOpcULongReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterInt64 : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_INT64>* m_pOpcReadCnvtr;

public:
  GTWOpcReadConverterInt64(GTWReadConverterTemplate<TMWTYPES_INT64>* pULongReadCnvtr) :
    m_pOpcReadCnvtr(pULongReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.llVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_I8;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcReadCnvtr)
    {
      delete m_pOpcReadCnvtr;
      m_pOpcReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterUInt64 : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_UINT64>* m_pOpcReadCnvtr;

public:
  GTWOpcReadConverterUInt64(GTWReadConverterTemplate<TMWTYPES_UINT64>* pULongReadCnvtr) :
    m_pOpcReadCnvtr(pULongReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.ullVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_UI8;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcReadCnvtr)
    {
      delete m_pOpcReadCnvtr;
      m_pOpcReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterUShort : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_USHORT>* m_pOpcUShortReadCnvtr;

public:
  GTWOpcReadConverterUShort(GTWReadConverterTemplate<TMWTYPES_USHORT>* pUShortReadCnvtr) :
    m_pOpcUShortReadCnvtr(pUShortReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcUShortReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.uiVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_UI2;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcUShortReadCnvtr)
    {
      delete m_pOpcUShortReadCnvtr;
      m_pOpcUShortReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterUChar : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_UCHAR>* m_pOpcUCharReadCnvtr;

public:
  GTWOpcReadConverterUChar(GTWReadConverterTemplate<TMWTYPES_UCHAR>* pUCharReadCnvtr) :
    m_pOpcUCharReadCnvtr(pUCharReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    m_pOpcUCharReadCnvtr->getValue(NULL, &pOpcSdo->m_OPCvalue.bVal, &stdQuality);
    pOpcSdo->m_OPCvalue.vt = VT_UI1;
    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcUCharReadCnvtr)
    {
      delete m_pOpcUCharReadCnvtr;
      m_pOpcUCharReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterTMWDTIME : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<TMWDTIME>* m_pOpcTmwdtimeReadCnvtr;

public:
  GTWOpcReadConverterTMWDTIME(GTWReadConverterTemplate<TMWDTIME>* pTmwdtimeReadCnvtr) :
    m_pOpcTmwdtimeReadCnvtr(pTmwdtimeReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    TMWDTIME         tmwdtime;
    SYSTEMTIME       systemTime;

    m_pOpcTmwdtimeReadCnvtr->getValue(NULL, &tmwdtime, &stdQuality);

    systemTime.wYear = tmwdtime.year;
    systemTime.wMonth = tmwdtime.month;
    systemTime.wDayOfWeek = tmwdtime.dayOfWeek % 7;
    systemTime.wDay = tmwdtime.dayOfMonth;
    systemTime.wHour = tmwdtime.hour;
    systemTime.wMinute = tmwdtime.minutes;
    systemTime.wSecond = tmwdtime.mSecsAndSecs / 1000;
    systemTime.wMilliseconds = tmwdtime.mSecsAndSecs % 1000;

    pOpcSdo->m_OPCvalue.vt = VT_DATE;
    //SystemTimeToVariantTime(&systemTime, &pOpcSdo->m_OPCvalue.date);
    GtwOsDateTime::SystemTimeToVariantTimeWithMilliseconds(systemTime, &pOpcSdo->m_OPCvalue.date);

    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcTmwdtimeReadCnvtr)
    {
      delete m_pOpcTmwdtimeReadCnvtr;
      m_pOpcTmwdtimeReadCnvtr = NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcReadConverterString : public GTWOpcReadConverter
{
private:
  GTWReadConverterTemplate<CStdString>* m_pOpcStringReadCnvtr;

public:
  GTWOpcReadConverterString(GTWReadConverterTemplate<CStdString>* pStringReadCnvtr) :
    m_pOpcStringReadCnvtr(pStringReadCnvtr)
  {
  }

  virtual void gtwopc_getValue(GTWOpcSlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* sQ)
  {
    GTWDEFS_STD_QLTY stdQuality;
    CStdString          string;

    m_pOpcStringReadCnvtr->getValue(NULL, &string, &stdQuality);


    pOpcSdo->m_OPCvalue.vt = VT_BSTR;

    //  const char *str = string.LockBuffer();
    const char* str = string.GetBuffer();
    //LPWSTR x = A2W(str);
    int wchars_num = MultiByteToWideChar(CP_ACP, 0, str, -1, NULL, 0);
    wchar_t* wstr = new wchar_t[wchars_num];
    MultiByteToWideChar(CP_ACP, 0, str, -1, wstr, wchars_num);

    if (pOpcSdo->m_OPCvalue.vt == VT_BSTR && pOpcSdo->m_OPCvalue.bstrVal != NULL)
      SysFreeString(pOpcSdo->m_OPCvalue.bstrVal);

    pOpcSdo->m_OPCvalue.bstrVal = SysAllocString(wstr);
    delete[] wstr;

    pOpcSdo->setQuality(stdQuality);
    *sQ = stdQuality;
  }

  virtual void Delete(void)
  {
    if (m_pOpcStringReadCnvtr)
    {
      delete m_pOpcStringReadCnvtr;
      m_pOpcStringReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterString : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject>* m_pStringWriteCnvtr;

public:
  GTWOpcWriteConverterString(GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject>* pStringWriteCnvtr) :
    m_pStringWriteCnvtr(pStringWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo);
  virtual void Delete(void)
  {
    if (m_pStringWriteCnvtr)
    {
      delete m_pStringWriteCnvtr;
      m_pStringWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterBin : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<bool, GTWSlaveDataObject>* m_pBinWriteCnvtr;

public:
  GTWOpcWriteConverterBin(GTWWriteConverterTemplate<bool, GTWSlaveDataObject>* pBinWriteCnvtr) :
    m_pBinWriteCnvtr(pBinWriteCnvtr)
  {
  }

  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    Variant writeValue(pOpcReq->getValue()->getData());

    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    bool bValue;

    /* the pulse width is unknown */
    ctrlDesc.pulseOnTime = 0;
    ctrlDesc.pulseOffTime = 0;

    bValue = (writeValue.boolVal != 0);
    ctrlDesc.mode = GTWDEFS_BIN_CTRL_MODE_UNKNOWN;
    ctrlDesc.pExtraCallBackData = pOpcReq; //FVE add to rest of the writeValue methods
    GTWDEFS_CTRL_STAT ctrlStat = m_pBinWriteCnvtr->writeValue(pSdo, bValue, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pBinWriteCnvtr)
    {
      delete m_pBinWriteCnvtr;
      m_pBinWriteCnvtr = TMWDEFS_NULL;
    }
  }
};


/*****************************************************************************/

class GTWOpcWriteConverterShort : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject>* m_pShortWriteCnvtr;

public:
  GTWOpcWriteConverterShort(GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject>* pShortWriteCnvtr) :
    m_pShortWriteCnvtr(pShortWriteCnvtr)
  {
  }

  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pShortWriteCnvtr->writeValue(pSdo, writeValue.iVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pShortWriteCnvtr)
    {
      delete m_pShortWriteCnvtr;
      m_pShortWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterChar : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject>* m_pCharWriteCnvtr;

public:
  GTWOpcWriteConverterChar(GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject>* pCharWriteCnvtr) :
    m_pCharWriteCnvtr(pCharWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pCharWriteCnvtr->writeValue(pSdo, writeValue.cVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pCharWriteCnvtr)
    {
      delete m_pCharWriteCnvtr;
      m_pCharWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterDouble : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject>* m_pDoubleWriteCnvtr;

public:
  GTWOpcWriteConverterDouble(GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject>* pDoubleWriteCnvtr) :
    m_pDoubleWriteCnvtr(pDoubleWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pDoubleWriteCnvtr->writeValue(pSdo, writeValue.dblVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pDoubleWriteCnvtr)
    {
      delete m_pDoubleWriteCnvtr;
      m_pDoubleWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterSFloat : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject>* m_pSfloatWriteCnvtr;

public:
  GTWOpcWriteConverterSFloat(GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject>* pSfloatWriteCnvtr) :
    m_pSfloatWriteCnvtr(pSfloatWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pSfloatWriteCnvtr->writeValue(pSdo, writeValue.fltVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pSfloatWriteCnvtr)
    {
      delete m_pSfloatWriteCnvtr;
      m_pSfloatWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterInt64 : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject>* m_pWriteCnvtr;

public:
  GTWOpcWriteConverterInt64(GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject>* pLongWriteCnvtr) :
    m_pWriteCnvtr(pLongWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pWriteCnvtr->writeValue(pSdo, writeValue.llVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pWriteCnvtr)
    {
      delete m_pWriteCnvtr;
      m_pWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterUInt64 : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_UINT64, GTWSlaveDataObject>* m_pWriteCnvtr;

public:
  GTWOpcWriteConverterUInt64(GTWWriteConverterTemplate<TMWTYPES_UINT64, GTWSlaveDataObject>* pLongWriteCnvtr) :
    m_pWriteCnvtr(pLongWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pWriteCnvtr->writeValue(pSdo, writeValue.ullVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pWriteCnvtr)
    {
      delete m_pWriteCnvtr;
      m_pWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterLong : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject>* m_pLongWriteCnvtr;

public:
  GTWOpcWriteConverterLong(GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject>* pLongWriteCnvtr) :
    m_pLongWriteCnvtr(pLongWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pLongWriteCnvtr->writeValue(pSdo, writeValue.lVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pLongWriteCnvtr)
    {
      delete m_pLongWriteCnvtr;
      m_pLongWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterULong : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject>* m_pULongWriteCnvtr;

public:
  GTWOpcWriteConverterULong(GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject>* pLongWriteCnvtr) :
    m_pULongWriteCnvtr(pLongWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pULongWriteCnvtr->writeValue(pSdo, writeValue.ulVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pULongWriteCnvtr)
    {
      delete m_pULongWriteCnvtr;
      m_pULongWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterUShort : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject>* m_pUShortWriteCnvtr;

public:
  GTWOpcWriteConverterUShort(GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject>* pLongWriteCnvtr) :
    m_pUShortWriteCnvtr(pLongWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pUShortWriteCnvtr->writeValue(pSdo, writeValue.uiVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pUShortWriteCnvtr)
    {
      delete m_pUShortWriteCnvtr;
      m_pUShortWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTWOpcWriteConverterUChar : public GTWOpcWriteConverter
{
private:
  GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject>* m_pUCharWriteCnvtr;

public:
  GTWOpcWriteConverterUChar(GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject>* pUCharWriteCnvtr) :
    m_pUCharWriteCnvtr(pUCharWriteCnvtr)
  {
  }

  //OPC writeValue
  virtual void writeValue(DaRequest* pOpcReq, GTWOpcSlaveDataObject* pSdo)
  {
    GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
    ctrlDesc.pExtraCallBackData = pOpcReq;
    DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
    ValueQT aValue;
    element->getCacheValue(aValue);
    VARIANT writeValue = aValue.getData();

    GTWDEFS_CTRL_STAT ctrlStat = m_pUCharWriteCnvtr->writeValue(pSdo, writeValue.bVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
    pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
  }

  virtual void Delete(void)
  {
    if (m_pUCharWriteCnvtr)
    {
      delete m_pUCharWriteCnvtr;
      m_pUCharWriteCnvtr = TMWDEFS_NULL;
    }
  }
};

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::GTWOpcSlaveDataObject
Description : [none]	
Return :			constructor	-	
Parameters :
void	-	
Note : [none]
\**********************************************************************************/
GTWOpcSlaveDataObject::GTWOpcSlaveDataObject() :
m_pReadCnvtr(TMWDEFS_NULL),
m_pWriteCnvtr(TMWDEFS_NULL),
m_updateReason(GTWDEFS_UPDTRSN_UNKNOWN)
{
  m_pOpcDaAddrSpaceElem = NULL;
  m_OPCvalue.vt = VT_R8;
  m_OPCvalue.dblVal = 0.0;
//  VariantClear(&m_value);
  m_pMyMdo = TMWDEFS_NULL;
  m_quality = EnumQuality_BAD;
  CoFileTimeNow(&m_timestamp);
  m_accessRights = 0;
  m_type = VT_R8;
}

GTWOpcSlaveDataObject::~GTWOpcSlaveDataObject()
{
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_OPC, nullptr, "OPC Server removing item: %s",(const char *)m_sMemberName);

  this->unsubscribeOpcMdos();

  if (m_pReadCnvtr)
  {
    m_pReadCnvtr->Delete();
    delete m_pReadCnvtr;
  }
  if (m_pWriteCnvtr)
  {
    m_pWriteCnvtr->Delete();
    delete m_pWriteCnvtr;
  }

  if (m_pOpcDaAddrSpaceElem)
  {
    m_pOpcDaAddrSpaceElem->SetOpcSlaveDataObject(nullptr);
  }

  if (this->m_OPCvalue.vt == VT_BSTR && this->m_OPCvalue.bstrVal != NULL)
    SysFreeString(this->m_OPCvalue.bstrVal);
}

void GTWOpcSlaveDataObject::CleanUpDAAddressSpace()
{
  if (this->m_pOpcDaAddrSpaceElem != NULL && m_pOpcDaAddrSpaceElem->getParent() != NULL)
  {
    m_pOpcDaAddrSpaceElem->getParent()->removeChild(m_pOpcDaAddrSpaceElem);
    m_pOpcDaAddrSpaceElem = NULL;
  }
}

void GTWOpcSlaveDataObject::SetOPCDaAddrSpaceElement(GTWOpcDaAddressSpaceElement *pTag)
{
  if (GetGTWApp()->IsShuttingDown())
  {
    return;
  }

  tmw::CriticalSectionLock lock(m_OPCDaAddrSpaceElemCS);

  // If setting to null, remove first, this is also done in OnDelete
  if (!pTag && m_pOpcDaAddrSpaceElem && m_pOpcDaAddrSpaceElem->getParent())
  {
    // remove child calls GTWOpcDaAddressSpaceElement::removedFromAddressSpace on m_pOpcDaAddrSpaceElem
    // which then calls this method back to set m_pOpcDaAddrSpaceElem to null, which will be recursive if we dont set m_pOpcDaAddrSpaceElem to null here
    GTWOpcDaAddressSpaceElement *pTemp = m_pOpcDaAddrSpaceElem;
    m_pOpcDaAddrSpaceElem = nullptr;
    pTemp->getParent()->removeChild(pTemp);
  }

  m_pOpcDaAddrSpaceElem = pTag;
  if (m_pOpcDaAddrSpaceElem)
  {
    m_pOpcDaAddrSpaceElem->SetOpcSlaveDataObject(this);
  }
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::getGTWTypeFromNativeType
Description : [none]	
Return :			GTWCNVTR_TYPE	-	
Parameters :
void	-	
Note : [none]
\**********************************************************************************/
GTWCNVTR_TYPE GTWOpcSlaveDataObject::getGTWTypeFromNativeType(void)
{
  switch(m_type)
  {
  case VT_BOOL:
    return GTWCNVTR_TYPE_BOOL;
    break;
  case VT_I1:
    return GTWCNVTR_TYPE_CHAR;
    break;
  case VT_UI1:
    return GTWCNVTR_TYPE_UCHAR;
    break;
  case VT_I2:
    return GTWCNVTR_TYPE_SHORT;
    break;
  case VT_UI2:
    return GTWCNVTR_TYPE_USHORT;
    break;
  case VT_I4:
    return GTWCNVTR_TYPE_LONG;
    break;
  case VT_UI4:
    return GTWCNVTR_TYPE_ULONG;
    break;
  case VT_R4:
    return GTWCNVTR_TYPE_SFLOAT;
    break;
  case VT_R8:
    return GTWCNVTR_TYPE_DOUBLE;
    break;
  case VT_DATE:
    return GTWCNVTR_TYPE_TMWDTIME;
    break;
  case VT_BSTR:
    return GTWCNVTR_TYPE_STRING;
    break;
  }
  return GTWCNVTR_TYPE_UNKNOWN;
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::bindSdoWithMdoNative
Description : [none]	
Return :			bool	-	
Parameters :
GTWMasterDataObject *pMdo	-	
Note : [none]
\**********************************************************************************/
bool GTWOpcSlaveDataObject::bindSdoWithMdoNative(GTWMasterDataObject *pMdo)
{
  void *readCnvtr;
  void *writeCnvtr;

  /* Setup access rights */
  m_accessRights = 0;
  //SOPC m_accessRights    = 0;

  /* Initialize converters */
  m_pReadCnvtr  = TMWDEFS_NULL; /* only readable if set non null below */
  m_pWriteCnvtr = TMWDEFS_NULL; /* only writable if set non null below */

// NOTE : we will not allow a writeable MDO to setup an OPC Classic write converter if it is mapped to a control SDO because then the MDO could be written
//        via OPC rather than through the control mechanism which would not be expected
  bool bTryWriteCnvtr = pMdo->isWriteable() && !pMdo->HasMappedCommandSdo();

  switch(getGTWTypeFromNativeType())
  {
  case GTWCNVTR_TYPE_BOOL:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterBool((GTWReadConverterTemplate<bool> *) readCnvtr);
      if (bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterBin((GTWWriteConverterTemplate<bool, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_CHAR:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterChar((GTWReadConverterTemplate<TMWTYPES_CHAR> *) readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterChar((GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_UCHAR:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterUChar((GTWReadConverterTemplate<TMWTYPES_UCHAR> *) readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterUChar((GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_SHORT:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterShort((GTWReadConverterTemplate<TMWTYPES_SHORT> *) readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterShort((GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_USHORT:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterUShort((GTWReadConverterTemplate<TMWTYPES_USHORT> *) readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterUShort((GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_LONG:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterLong((GTWReadConverterTemplate<TMWTYPES_INT> *) readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterLong((GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_ULONG:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterULong((GTWReadConverterTemplate<TMWTYPES_UINT> *) readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterULong((GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_SFLOAT:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterSFloat((GTWReadConverterTemplate<TMWTYPES_SFLOAT> *) readCnvtr);
      if (bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterSFloat((GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_DOUBLE:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterDouble((GTWReadConverterTemplate<TMWTYPES_DOUBLE> *)readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_STRING:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterString((GTWReadConverterTemplate<CStdString> *)readCnvtr);
      if(bTryWriteCnvtr && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterString((GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;
  case GTWCNVTR_TYPE_TMWDTIME:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_TMWDTIME)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterTMWDTIME((GTWReadConverterTemplate<TMWDTIME> *) readCnvtr);
    }
    break;
  } // switch

  if (m_pReadCnvtr == TMWDEFS_NULL && m_pWriteCnvtr == TMWDEFS_NULL)
  {
    return TMWDEFS_FALSE;
  }

  if(m_pReadCnvtr != TMWDEFS_NULL)
  {
    m_accessRights |= EnumAccessRights_READABLE;

    // read it once to set up initial values
    retrieve();
    m_type = m_OPCvalue.vt;
  }

  if(m_pWriteCnvtr != TMWDEFS_NULL)
  {
    m_accessRights |= EnumAccessRights_WRITEABLE;
  }

  if(m_accessRights == 0) 
  {
    return(TMWDEFS_FALSE);
  }
  else
  {
    updateSDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);
  }

  CStdString userTagName = pMdo->getMdoUserTagName();
  if (userTagName != "")
  {
    SetMemberName(userTagName);
  }
  else
  {
    CStdString fullName = pMdo->getBdo()->GetFullName();
    SetMemberName(fullName);
  }

  m_pMyMdo = pMdo;
  return(TMWDEFS_TRUE);
}


GTWOpcReadConverter* GTWOpcSlaveDataObject::getReadConverter(GTWCNVTR_TYPE cnvtrType, void* readCnvtr)
{
  switch (cnvtrType)
  {
  case GTWCNVTR_TYPE_BOOL:
    return new GTWOpcReadConverterBool((GTWReadConverterTemplate<bool> *) readCnvtr);
  case GTWCNVTR_TYPE_CHAR:
    return new GTWOpcReadConverterChar((GTWReadConverterTemplate<TMWTYPES_CHAR> *) readCnvtr);
  case GTWCNVTR_TYPE_UCHAR:
    return new GTWOpcReadConverterUChar((GTWReadConverterTemplate<TMWTYPES_UCHAR> *) readCnvtr);
  case GTWCNVTR_TYPE_SHORT:
    return new GTWOpcReadConverterShort((GTWReadConverterTemplate<TMWTYPES_SHORT> *) readCnvtr);
  case GTWCNVTR_TYPE_USHORT:
    return new GTWOpcReadConverterUShort((GTWReadConverterTemplate<TMWTYPES_USHORT> *) readCnvtr);
  case GTWCNVTR_TYPE_LONG:
    return new GTWOpcReadConverterLong((GTWReadConverterTemplate<TMWTYPES_LONG> *) readCnvtr);
  case GTWCNVTR_TYPE_ULONG:
    return new GTWOpcReadConverterULong((GTWReadConverterTemplate<TMWTYPES_ULONG> *) readCnvtr);
  case GTWCNVTR_TYPE_SFLOAT:
    return new GTWOpcReadConverterSFloat((GTWReadConverterTemplate<TMWTYPES_SFLOAT> *) readCnvtr);
  case GTWCNVTR_TYPE_DOUBLE:
    return new GTWOpcReadConverterDouble((GTWReadConverterTemplate<TMWTYPES_DOUBLE> *) readCnvtr);
  case GTWCNVTR_TYPE_INT64:
    return new GTWOpcReadConverterInt64((GTWReadConverterTemplate<TMWTYPES_INT64> *) readCnvtr);
  case GTWCNVTR_TYPE_UINT64:
    return new GTWOpcReadConverterUInt64((GTWReadConverterTemplate<TMWTYPES_UINT64> *) readCnvtr);
  case GTWCNVTR_TYPE_STRING:
    return new GTWOpcReadConverterString((GTWReadConverterTemplate<CStdString> *) readCnvtr);
  case GTWCNVTR_TYPE_TMWDTIME:
    return new GTWOpcReadConverterTMWDTIME((GTWReadConverterTemplate<TMWDTIME> *) readCnvtr);
  }

  return nullptr;
}

GTWOpcWriteConverter* GTWOpcSlaveDataObject::getWriteConverter(GTWCNVTR_TYPE cnvtrType, void* readCnvtr)
{
  switch (cnvtrType)
  {
  case GTWCNVTR_TYPE_BOOL:
    return new GTWOpcWriteConverterBin((GTWWriteConverterTemplate<bool, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_CHAR:
    return new GTWOpcWriteConverterChar((GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_UCHAR:
    return new GTWOpcWriteConverterUChar((GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_SHORT:
    return new GTWOpcWriteConverterShort((GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_USHORT:
    return new GTWOpcWriteConverterUShort((GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_LONG:
    return new GTWOpcWriteConverterLong((GTWWriteConverterTemplate<TMWTYPES_LONG, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_ULONG:
    return new GTWOpcWriteConverterULong((GTWWriteConverterTemplate<TMWTYPES_ULONG, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_SFLOAT:
    return new GTWOpcWriteConverterSFloat((GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_DOUBLE:
    return new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_INT64:
    return new GTWOpcWriteConverterInt64((GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_UINT64:
    return new GTWOpcWriteConverterUInt64((GTWWriteConverterTemplate<TMWTYPES_UINT64, GTWSlaveDataObject> *) readCnvtr);
  case GTWCNVTR_TYPE_STRING:
    return new GTWOpcWriteConverterString((GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject> *) readCnvtr);
  }

  return nullptr;
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::bindSdoWithMdo
Description : Binds the SDO with an MDO	This function is called after 
the SDO has been created. This is not done by the constuctor 
because the MDO is not usually known in the scope in which 
the SDO is created.
Return :			bool	-	
Parameters :
GTWMasterDataObject *pMdo	-	
Note : [none]
\**********************************************************************************/
bool GTWOpcSlaveDataObject::bindSdoWithMdo(GTWMasterDataObject *pMdo)
{
  void *readCnvtr;
  void *writeCnvtr;
  m_pMyMdo = pMdo;

  // first try native type
  if (bindSdoWithMdoNative(pMdo))
  {
    return TMWDEFS_TRUE;
  }

  /* Setup access rights */
  m_accessRights    = 0;

  /* Initialize converters */
  m_pReadCnvtr  = TMWDEFS_NULL; /* only readable if set non null below */
  m_pWriteCnvtr = TMWDEFS_NULL; /* only writable if set non null below */
  GTWCNVTR_TYPE cnvtrType = pMdo->getCnvtrType();
  if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, cnvtrType)) != TMWDEFS_NULL)
  {
    m_pReadCnvtr = getReadConverter(cnvtrType, readCnvtr);
    if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, cnvtrType)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = getWriteConverter(cnvtrType, writeCnvtr);
    }
  }

  // NOTE : this may lead to readCnvtr being allocated twice
  if (m_pReadCnvtr == nullptr)
  {
    assert(readCnvtr == nullptr);
  }

  if (!m_pReadCnvtr)
  {
    if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterBool((GTWReadConverterTemplate<bool> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterBin((GTWWriteConverterTemplate<bool, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterChar((GTWReadConverterTemplate<TMWTYPES_CHAR> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterChar((GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterUChar((GTWReadConverterTemplate<TMWTYPES_UCHAR> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterUChar((GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterShort((GTWReadConverterTemplate<TMWTYPES_SHORT> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterShort((GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterUShort((GTWReadConverterTemplate<TMWTYPES_USHORT> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterUShort((GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterLong((GTWReadConverterTemplate<TMWTYPES_INT> *) readCnvtr);
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterLong((GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterULong((GTWReadConverterTemplate<TMWTYPES_UINT> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterULong((GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterInt64((GTWReadConverterTemplate<TMWTYPES_INT64> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterInt64((GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_UINT64)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterUInt64((GTWReadConverterTemplate<TMWTYPES_UINT64> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_UINT64)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterUInt64((GTWWriteConverterTemplate<TMWTYPES_UINT64, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterSFloat((GTWReadConverterTemplate<TMWTYPES_SFLOAT> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterSFloat((GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterDouble((GTWReadConverterTemplate<TMWTYPES_DOUBLE> *)readCnvtr);
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterDouble((GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterString((GTWReadConverterTemplate<CStdString> *) readCnvtr);
      if (pMdo->HasMappedCommandSdo() == false && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTWOpcWriteConverterString((GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_TMWDTIME)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTWOpcReadConverterTMWDTIME((GTWReadConverterTemplate<TMWDTIME> *) readCnvtr);
    }
  }

  if(m_pReadCnvtr != TMWDEFS_NULL)
  {
    m_accessRights |= EnumAccessRights_READABLE;

    // read it once to set up initial values
    retrieve();
    m_type = m_OPCvalue.vt;
  }

  if(m_pWriteCnvtr != TMWDEFS_NULL)
  {
    m_accessRights |= EnumAccessRights_WRITEABLE;
  }

  if(m_accessRights == 0) 
  {
    return(TMWDEFS_FALSE);
  }
  else
  {
    updateSDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);
  }

  CStdString userTagName = pMdo->getMdoUserTagName();
  if (userTagName != "")
  {
    SetMemberName(userTagName);
  }
  else
  {
    CStdString fullName = pMdo->getBdo()->GetFullName();
    SetMemberName(fullName);
  }

  return(TMWDEFS_TRUE);
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::getFullName
Description : [none]	
Return :			CStdString	-	
Parameters :
void	-	
Note : [none]
\**********************************************************************************/
CStdString GTWOpcSlaveDataObject::gtw_getFullName(void) 
{
  GTWBaseDataObject *pBdo = this->getBdo();
  if (pBdo != TMWDEFS_NULL)
  {
    GTWCollectionBase *pParentCollection = pBdo->GetParentCollection();
    if (pParentCollection != TMWDEFS_NULL)
    {
      GTWCollectionMember *pOwnerMember = pParentCollection->GetOwnerMember();
      CStdString collectionOwnerName = pOwnerMember->GetMemberName();

      m_sFullName = collectionOwnerName + CStdString(std::string(1, GTWCNFG_TAG_FIELD_SEPARATOR)) + GetMemberName();
      return (m_sFullName);
    }
  }
  return "";
}


/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::updateSDO
Description :   Called whenever an MDO update an SDO. Used to update 
the OPC SDO's time stamp. The actual data is updated by the
retrieve() function.	
Return :			void	-	
Parameters :
GTWDEFS_UPDTRSN updateReason	-	
GTWMasterDataObject *pMdo	-	
Note : [none]
\**********************************************************************************/
void GTWOpcSlaveDataObject::updateSDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo)
{
  GTWDEFS_TIME_QLTY timeQuality;
  TMWDTIME         timeStamp;
//  SYSTEMTIME       systemTime;

  m_updateReason = updateReason;

  GTWBaseDataObject *pBdo = pMdo->getBdo();
  if (pBdo == NULL)
  {
    return;
  }

  if (pMdo->getOPCTimeSource() == GTWDEFS_REPORTED_TIME)
  {
    pBdo->getMdoReportedTime(&timeStamp, &timeQuality, GetGTWApp()->gtwOpcTimeZone);
    if (timeQuality & GTWDEFS_TIME_QLTY_INVALID)
    {
      pBdo->getMdoUpdatedTime(&timeStamp, &timeQuality, GetGTWApp()->gtwOpcTimeZone);
    }
  }
  else
  {
    pBdo->getMdoUpdatedTime(&timeStamp, &timeQuality, GetGTWApp()->gtwOpcTimeZone);
  }

  GtwOsDateTime dateTime;
  dateTime.SetFromTMWDTime(&timeStamp);
  if (!dateTime.IsValid())
    dateTime.UTCNow();

  dateTime.GetAsFileTime(&m_timestamp);

  // convert timestamp to system time then to filetime
  //systemTime.wYear         = timeStamp.year;
  //systemTime.wMonth        = timeStamp.month;
  //systemTime.wDayOfWeek    = timeStamp.dayOfWeek%7;
  //systemTime.wDay          = timeStamp.dayOfMonth;
  //systemTime.wHour         = timeStamp.hour;
  //systemTime.wMinute       = timeStamp.minutes;
  //systemTime.wSecond       = timeStamp.mSecsAndSecs/1000;
  //systemTime.wMilliseconds = timeStamp.mSecsAndSecs%1000;

  // check for invalid system time 
  //COleDateTime::DateTimeStatus timeStatus = COleDateTime(systemTime).GetStatus();
  //if(timeStatus == COleDateTime::invalid )
  //{
  //  // ? use last reported timestamp instead of updated time
  //  CoFileTimeNow(&m_timestamp);
  //}
  //else
  //{	
  //  BOOL bOK = SystemTimeToFileTime(&systemTime, &m_timestamp);
  //  if (bOK == FALSE)
  //  {
  //      DWORD err = GetLastError();
  //  }
  //  //FVE todo ???? do we really want to do this?
  //  //LocalFileTimeToFileTime(&localFileTime, &m_timestamp);
  //}

  m_pMyMdo = pMdo;
  if (this->retrieve() == S_OK)
  {
    tmw::CriticalSectionLock lock(m_OPCDaAddrSpaceElemCS);
    if (m_pOpcDaAddrSpaceElem != NULL)
    {
#pragma message(__LOC__"This might be too slow to trace?")
        GTWEXPND_FORMAT format;
        format.setFormatPrefix("%");
        char valueBuf[80];
        this->getValueString(format, valueBuf, 80);

        //GtwOsDateTime curTime(&m_timestamp);
        //CStdString timeStr;
        //timeStr.Format("%d/%d/%d %d:%d:%d:%d", curTime.GetMonth(), curTime.GetDay(), curTime.GetYear(),
        //  curTime.GetHour(), curTime.GetMinute(), curTime.GetSecond(), curTime.GetMillSec());

      LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_OPC_DEEP, nullptr, "GTWOpcSlaveDataObject::updateSDO '%s', current value=%s, quality=%d", this->GetFullName().c_str(), valueBuf, m_quality);


      ValueQT updatedValue(m_OPCvalue, (EnumQuality)m_quality, DateTime(&m_timestamp));
      m_pOpcDaAddrSpaceElem->valueChanged(updatedValue);
    }
  }
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::retrieve
Description : called by a gateway client to retrieve the value of the point
Return :			HRESULT	-	
Parameters :
void	-	
Note : [none]
\**********************************************************************************/
EnumResultCode GTWOpcSlaveDataObject::retrieve()
{
  if (GetGTWApp()->IsShuttingDown() == true)
  {
    return EnumResultCode_E_FAIL;
  }

  try
  {
    GTWDEFS_STD_QLTY stdQuality;
    if (m_pReadCnvtr)
    {
      m_pReadCnvtr->gtwopc_getValue(this,&stdQuality);
    }
  }
  catch(...)
  {
    // need to get this SDOs collection and delete this item from it
    GTWOpcServerCollection *pCollection = (GTWOpcServerCollection *)GetParentCollection();
    if (pCollection)
    {
      pCollection->RemoveCollectionMember(this);
    }
    // since the MDO no longer exists set quality
    setQuality(GTWDEFS_STD_QLTY_NOT_TOPICAL);
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_OPC, nullptr, "%s", "GTWOpcSlaveDataObject::retrieve failed, exception caught");
    return(EnumResultCode_E_OPC_UNKNOWNITEMID);
  }
  return(EnumResultCode_S_OK);
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::gtwopc_write
Description : called by a gateway OPC server (i.e. FactorySoft) to write value to a point
Return :			HRESULT	-	
Parameters :
VARIANT & value	-	
Note : [none]
\**********************************************************************************/
EnumResultCode GTWOpcSlaveDataObject::gtwopc_write(DaRequest *pOpcReq, GTWOpcSlaveDataObject *pSdo)
{
  if ((m_accessRights & EnumAccessRights_WRITEABLE) == 0)
  {
    return(EnumResultCode_E_OPC_BADRIGHTS);
  }

  // If the write converted is called, it is expected to complete the request.
  m_pWriteCnvtr->writeValue(pOpcReq, pSdo);
  return EnumResultCode_S_OK;
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::setQuality
Description : [none]	
Return :			void	-	
Parameters :
GTWDEFS_STD_QLTY stdQuality	-	
Note : [none]
\**********************************************************************************/
void GTWOpcSlaveDataObject::setQuality(GTWDEFS_STD_QLTY stdQuality)
{
  if (stdQuality & GTWDEFS_STD_QLTY_UNINITIALIZED)
  {
    m_quality = EnumQuality_BAD_NOT_CONNECTED;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_BLOCKED)
  {
    m_quality = EnumQuality_BAD_OUT_OF_SERVICE;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_INVALID)
  {
    m_quality = EnumQuality_BAD;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_REF_ERROR)
  {
    m_quality = EnumQuality_UNCERTAIN_SENSOR_CAL;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_OVERFLOW)
  {
    m_quality = EnumQuality_UNCERTAIN_EGU_EXCEEDED;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_NOT_TOPICAL)
  {
    m_quality = EnumQuality_UNCERTAIN_LAST_USABLE;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_IN_TRANSIT)
  {
    m_quality = EnumQuality_UNCERTAIN;
  }
  else if (stdQuality & GTWDEFS_STD_QLTY_SUBSTITUTED)
  {
    m_quality = EnumQuality_GOOD_LOCAL_OVERRIDE;
  }
  else 
  {
    m_quality = EnumQuality_GOOD;
    if (m_updateReason & GTWDEFS_UPDTRSN_CTRL_BY_COMM || m_updateReason & GTWDEFS_UPDTRSN_CTRL_AT_DEVICE)
    {
      m_quality = EnumQuality_GOOD_LOCAL_OVERRIDE;
    }
  }
}


/**********************************************************************************\
Function :			GTWOpcMasterDataObject::getMdoDbasDataId
Description : [none]	
Return :			void	-	
Parameters :
GTWDEFS_DBAS_DATA_ID *pDbasDataId	-	
Note : [none]
\**********************************************************************************/
void GTWOpcSlaveDataObject::getDbasDataId(GTWDEFS_DBAS_DATA_ID *pDbasDataId)
{
  pDbasDataId->channelIndex = 0;
  pDbasDataId->dataType = (TMWTYPES_UCHAR)this->m_type;
  pDbasDataId->elementIndex = 0;
  pDbasDataId->functionType = 0;
  pDbasDataId->infoNumObjAddr = 0;
  pDbasDataId->internalDataType = true;
  pDbasDataId->originator = 0;
  pDbasDataId->recordAddress = 0;
  pDbasDataId->sectorIndex = 0;
  pDbasDataId->sessionIndex = 0;

}

void GTWOpcSlaveDataObject::writeSdoCB(GTWDEFS_CTRL_STAT newCtrlStat, void *pExtraCBData)
{
  m_ctrlStat = newCtrlStat;

  EnumResultCode writeResult = EnumResultCode_S_FALSE;
  switch(m_ctrlStat)
  {
  case(GTWDEFS_CTRL_STAT_SUCCESS):
    {
      writeResult = EnumResultCode_S_OK;
      break;
    }
  case(GTWDEFS_CTRL_STAT_PENDING):
  case(GTWDEFS_CTRL_STAT_CNFMD_PENDING):
    {
      /* continue to loop, waiting for success or failure */
      break;
    }
  case(GTWDEFS_CTRL_STAT_OUT_OF_RANGE):
    {
      writeResult = EnumResultCode_E_OPC_RANGE;
      break;
    }
  case(GTWDEFS_CTRL_STAT_FAILURE):
  default:
    {
      writeResult = EnumResultCode_E_FAIL;
      break;
    }
  }

  if (writeResult != EnumResultCode_S_FALSE)
  {
    DaRequest *pOpcReq = (DaRequest *)pExtraCBData;
    //pOpcReq->addRef();
    if (pOpcReq)
    {
      pOpcReq->setResult(writeResult);
      pOpcReq->complete();
    }
  }
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::StartProcessWriteResult
Description : [none]	
Return :			void	-	
Parameters :
GTWDEFS_CTRL_STAT startingCtrlStat	-	
Note : [none]
\**********************************************************************************/
void GTWOpcSlaveDataObject::StartProcessWriteResult(GTWDEFS_CTRL_STAT startingCtrlStat, DaRequest *pOpcReq)
{
  m_ctrlStat = startingCtrlStat;
  if (startingCtrlStat == GTWDEFS_CTRL_STAT_SUCCESS)
  {
    writeSdoCB(startingCtrlStat, pOpcReq);
  }
  if (startingCtrlStat == GTWDEFS_CTRL_STAT_FAILURE)
  {
    writeSdoCB(startingCtrlStat, pOpcReq);
  }
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::getValueString
Description : [none]	
Return :			void	-	
Parameters :
const GTWEXPND_FORMAT &pFormat	-	
TMWTYPES_CHAR *msg	-	
TMWTYPES_USHORT msgLen	-	
Note : [none]
\**********************************************************************************/
void GTWOpcSlaveDataObject::getValueString(const GTWEXPND_FORMAT &pFormat, TMWTYPES_CHAR *msg, TMWTYPES_USHORT msgLen)
{
  switch(m_OPCvalue.vt)
  {
  case(VT_BOOL):
    {
      tmwtarg_snprintf(msg, msgLen, pFormat.stringFormat, m_OPCvalue.boolVal==0?"Off":"On");
      break;
    }
  case	VT_I1	:
    {
      tmwtarg_snprintf(msg, msgLen, pFormat.signedFormat, TMWTYPES_INT(m_OPCvalue.cVal));
      break;
    }
  case	VT_UI1	:
    {
      tmwtarg_snprintf(msg, msgLen, pFormat.unsignedFormat, TMWTYPES_INT(m_OPCvalue.bVal));
      break;
    }
  case(VT_I2):
    {
      tmwtarg_snprintf(msg, msgLen, pFormat.signedFormat, TMWTYPES_INT(m_OPCvalue.iVal));
      break;
    }
  case(VT_UI2):
    {
      tmwtarg_snprintf(msg, msgLen, pFormat.unsignedFormat, TMWTYPES_UINT(m_OPCvalue.uiVal));
      break;
  case(VT_I4):
    {
      tmwtarg_snprintf(msg, msgLen, pFormat.signedFormat, TMWTYPES_INT(m_OPCvalue.lVal));
      break;
    }
    }
  case(VT_UI4):
    {
      tmwtarg_snprintf(msg, msgLen, pFormat.unsignedFormat, TMWTYPES_UINT(m_OPCvalue.ulVal));
      break;
    }
  case(VT_R4):
    {
	    tmwtarg_snprintf(msg, msgLen, pFormat.floatFormat, m_OPCvalue.fltVal);
      break;
    }
  case(VT_R8):
    {
	    tmwtarg_snprintf(msg, msgLen, pFormat.floatFormat, m_OPCvalue.dblVal);
      break;
    }
  case(VT_I8):
  {
    tmwtarg_snprintf(msg, msgLen, pFormat.signed64bitIntFormat, m_OPCvalue.llVal);
    break;
  }
  case(VT_UI8):
  {
    tmwtarg_snprintf(msg, msgLen, pFormat.unsigned64bitIntFormat, m_OPCvalue.ullVal);
    break;
  }
  case(VT_BSTR):
    {
      CStdString value;
      value = m_OPCvalue.bstrVal;
	    tmwtarg_snprintf(msg, msgLen, pFormat.stringFormat, value.c_str());
      break;
    }
  case(VT_DATE):
    {
      SYSTEMTIME  systemTime;
      INT err = GtwOsDateTime::VariantTimeToSystemTimeWithMilliseconds(m_OPCvalue.date, &systemTime);
      if (FAILED(err)) 
      {
	      tmwtarg_snprintf(msg, msgLen, pFormat.stringFormat, "bad variant time");
      }
      else
      {
	      tmwtarg_snprintf(msg,
		      msgLen,
		      "%04d-%02d-%02d %02d:%02d:%02d.%03d",
            systemTime.wYear, 
            systemTime.wMonth, 
            systemTime.wDay, 
            systemTime.wHour, 
            systemTime.wMinute, 
            systemTime.wSecond, 
            systemTime.wMilliseconds);
      }
      break;
    }
  case(VT_EMPTY):
    {
	    tmwtarg_snprintf(msg, msgLen, pFormat.stringFormat, "unknown (VT_EMPTY)");
      break;
    }
  default:
    {
	    tmwtarg_snprintf(msg, msgLen, "error-Unsupported type");
      break;
    }
  }
}

/**********************************************************************************\
Function :			GTWOpcSlaveDataObject::getStdQuality
Description : [none]	
Return :			GTWDEFS_STD_QLTY	-	
Parameters :
Note : [none]
\**********************************************************************************/
GTWDEFS_STD_QLTY GTWOpcSlaveDataObject::getStdQuality()
{
  GTWDEFS_STD_QLTY stdQuality;
  if (m_pReadCnvtr)
  {
    m_pReadCnvtr->gtwopc_getValue(this,&stdQuality);
    return stdQuality;
  }
  return GTWDEFS_STD_QLTY_NOT_USABLE;
}

 //OPC writeValue
void GTWOpcWriteConverterString::writeValue(DaRequest *pOpcReq, GTWOpcSlaveDataObject *pSdo)
{
  GTWDEFS_EXTRA_CTRL_INFO ctrlDesc;
  ctrlDesc.pExtraCallBackData = pOpcReq;
  DaAddressSpaceElement* element = pOpcReq->getAddressSpaceElement();
  ValueQT aValue;
  element->getCacheValue(aValue);
  Variant writeValue = aValue.getData();

  GTWDEFS_CTRL_STAT ctrlStat = m_pStringWriteCnvtr->writeValue(pSdo, writeValue.bstrVal, &ctrlDesc, GTWDEFS_CTRL_MODE_AUTO);
  pSdo->StartProcessWriteResult(ctrlStat, pOpcReq);
}

#endif
