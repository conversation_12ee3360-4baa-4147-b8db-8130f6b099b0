- product version info in #include "\code\gateway\GTWLib\SDGversion.ish"
- require admin rights
- maint plan warning dialog

- required components: 
.NET 2.0 
MSI 3.1 
mfc 
atl
c runtime 
visual basic 6 runtime 
nt services 
tmw license server 
crypkey drivers 
sdg service 
crash report
soe reader
simple opc client
c++ opc client
c++ opc test server
mshflxgd ocx ???
CI guides (101,103,104,dnp)
sdg impl guide
sdg quick start
dbghelp.dll
factory soft server dll ???
softing dlls
mbplus.dll
xerces dll
gdiplus.dll

service monitor setup
opc core compoents

soe reader sample example

ide21201.vxd (for win 98 license stuff) ???

xml schema files (from \code\tmwxml\schemas)


- custom registry keys (onFirstUiAfter): 
SOFTWARE\\Triangle MicroWorks\\SCADA Data Gateway
EnableFilePaths
AskToStartServiceOnGUIexit
IniFilePath
HelpFilePath
ServiceINIfileName
TargetFilePath
UserName
CompanyName

- view quick start at end
- run SDG at end

-require xp,2000,2003

- display license text file
- ask for user and company info
- installation directory

- warn of license loss if removed

- deal with uninstall of previous version #define PREV_PROD_GUID "{2FCF0AC9-B735-4EDA-9DAC-70A803772823}"

Files: look in C:\code\gateway\Install\File Groups\*.fgl


Shortcuts: look in C:\code\gateway\Install\Shell Objects\Default.shl
icons on desktop sdg, c++ opc client,
start menu
- run sdg
- run opc client
- start/stop sdg service
- event viewer
- simple vb client
- soe reader
- all docs



License file is in: C:\code\gateway\Install\Setup Files\Compressed Files\Language Independent\OS Independent\license.txt


Issues:
win2003 sp1
1. need custom prerequsite for MSI 3.1 (get 1603 error) 
from http://www.installsite.org/pages/en/bugs_isw115.htm
Error 1603 "Error installing Windows Installer engine" on Windows Server 2003 SP1
Description: 
Running a setup built with IS11.5 using Windows Installer 3.1 on Windows Server 2003 with Service Pack 1 shows the following error message:
1603: Error installing Windows Installer engine. A file which needs to be replaced may be held in use. Close all applications and try again.
When clicking OK on that message box the installation continues properly. On Windows Server 2003 without SP1 the error message isn't displayed. 
Cause: 
SP1 includes MSI 3.1 and conflicts with the standard MSI 3.1 redistributables. 
Workaround: 
Don't install the MSI 3.1 runtime files with your setup (install MSI 2.0 or none at all), or build a custom prerequisite to install MSI 3.1 instead of using the option in Release wizard. 
Status: 
This problem has been reported in the InstallShield Community Forum. Macrovision is aware of the problem and working on a fix in InstallShield 12.0. It's unclear whether a fix will also be released for version 11.5. 
Created: 2006-03-28   















function OnFirstUIBefore()
    NUMBER nResult, nSetupType, nvSize, nUser;
    STRING szTitle, szMsg, szQuestion, svName, svCompany, szFile;
    STRING szLicenseFile;
    LIST list, listStartCopy;
	BOOL bCustom;
begin	
    // TO DO: if you want to enable background, window title, and caption bar title                                                                   
    // SetTitle( @PRODUCT_NAME, 24, WHITE );                                        
    // SetTitle( @PRODUCT_NAME, 0, BACKGROUNDCAPTION ); 	                  
    // Enable( FULLWINDOWMODE );						   
    // Enable( BACKGROUND );							  
    // SetColor(BACKGROUND,RGB (0, 128, 128));					   

	SHELL_OBJECT_FOLDER = @PRODUCT_NAME;	   
    
	nSetupType = TYPICAL;	

Dlg_SdWelcome:
    szTitle = "";
    szMsg   = "";
    nResult = SdWelcome(szTitle, szMsg);
    if (nResult = BACK) goto Dlg_SdWelcome;
	
	szTitle   = "";
	svName    = "";
    svCompany = "";

Dlg_SdCustomerInformation:
	nResult = SdCustomerInformation(szTitle, svName, svCompany, nUser);
	if (nResult = BACK) goto Dlg_SdWelcome;

Dlg_SetupType:
    szTitle = "";
    szMsg   = "";
    nResult = SetupType(szTitle, szMsg, "", nSetupType, 0);
    if (nResult = BACK) then
        goto Dlg_SdCustomerInformation;
    else
	    nSetupType = nResult;
        if (nSetupType != CUSTOM) then
	        nvSize = 0;
	        FeatureCompareSizeRequired(MEDIA, INSTALLDIR, nvSize);
	        if (nvSize != 0) then      
            	MessageBox(szSdStr_NotEnoughSpace, WARNING);
	            goto Dlg_SetupType;
            endif;
			bCustom = FALSE;
			goto Dlg_SQL;
		else
			bCustom = TRUE;
        endif;
    endif;    

Dlg_SdAskDestPath:    	
    nResult = SdAskDestPath(szTitle, szMsg, INSTALLDIR, 0);
    if (nResult = BACK) goto Dlg_SetupType;

Dlg_SdFeatureTree: 
    szTitle    = "";
    szMsg      = "";
    if (nSetupType = CUSTOM) then
		nResult = SdFeatureTree(szTitle, szMsg, INSTALLDIR, "", 2);
		if (nResult = BACK) goto Dlg_SdAskDestPath;  
    endif;

Dlg_SQL:
    nResult = OnSQLLogin( nResult );
    if( nResult = BACK ) then
    	if (!bCustom) then
    		goto Dlg_SetupType;    
    	else
    		goto Dlg_SdFeatureTree;
    	endif;
    endif;

Dlg_SdStartCopy:
    szTitle = "";
    szMsg   = "";
    listStartCopy = ListCreate( STRINGLIST );
    //The following is an example of how to add a string(svName) to a list(listStartCopy).
    //eg. ListAddString(listStartCopy,svName,AFTER);
    nResult = SdStartCopy( szTitle, szMsg, listStartCopy );			
    ListDestroy(listStartCopy);
	
	if (nResult = BACK) then
    	goto Dlg_SQL;
    endif;

    // setup default status
    Enable(STATUSEX);
 
    return 0;
end;
