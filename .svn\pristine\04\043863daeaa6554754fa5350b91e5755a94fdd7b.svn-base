<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Paho Asynchronous MQTT C Client Library: MQTTReasonCodes.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Paho Asynchronous MQTT C Client Library
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">MQTTReasonCodes.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;MQTTExportDeclarations.h&quot;</code><br />
</div>
<p><a href="_m_q_t_t_reason_codes_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:aba6db0fccfa3f8972ea48117b8b2a279"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> { <br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969">MQTTREASONCODE_SUCCESS</a> = 0, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92">MQTTREASONCODE_NORMAL_DISCONNECTION</a> = 0, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff">MQTTREASONCODE_GRANTED_QOS_0</a> = 0, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876">MQTTREASONCODE_GRANTED_QOS_1</a> = 1, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93">MQTTREASONCODE_GRANTED_QOS_2</a> = 2, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3">MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE</a> = 4, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899">MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS</a> = 16, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9">MQTTREASONCODE_NO_SUBSCRIPTION_FOUND</a> = 17, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055">MQTTREASONCODE_CONTINUE_AUTHENTICATION</a> = 24, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7">MQTTREASONCODE_RE_AUTHENTICATE</a> = 25, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3">MQTTREASONCODE_UNSPECIFIED_ERROR</a> = 128, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5">MQTTREASONCODE_MALFORMED_PACKET</a> = 129, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756">MQTTREASONCODE_PROTOCOL_ERROR</a> = 130, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c">MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR</a> = 131, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d">MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION</a> = 132, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd">MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID</a> = 133, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff">MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD</a> = 134, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803">MQTTREASONCODE_NOT_AUTHORIZED</a> = 135, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb">MQTTREASONCODE_SERVER_UNAVAILABLE</a> = 136, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74">MQTTREASONCODE_SERVER_BUSY</a> = 137, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4">MQTTREASONCODE_BANNED</a> = 138, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113">MQTTREASONCODE_SERVER_SHUTTING_DOWN</a> = 139, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3">MQTTREASONCODE_BAD_AUTHENTICATION_METHOD</a> = 140, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409">MQTTREASONCODE_KEEP_ALIVE_TIMEOUT</a> = 141, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21">MQTTREASONCODE_SESSION_TAKEN_OVER</a> = 142, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b">MQTTREASONCODE_TOPIC_FILTER_INVALID</a> = 143, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac">MQTTREASONCODE_TOPIC_NAME_INVALID</a> = 144, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a">MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE</a> = 145, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932">MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND</a> = 146, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1">MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED</a> = 147, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec">MQTTREASONCODE_TOPIC_ALIAS_INVALID</a> = 148, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa">MQTTREASONCODE_PACKET_TOO_LARGE</a> = 149, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e">MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH</a> = 150, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9">MQTTREASONCODE_QUOTA_EXCEEDED</a> = 151, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc">MQTTREASONCODE_ADMINISTRATIVE_ACTION</a> = 152, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca">MQTTREASONCODE_PAYLOAD_FORMAT_INVALID</a> = 153, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f">MQTTREASONCODE_RETAIN_NOT_SUPPORTED</a> = 154, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb">MQTTREASONCODE_QOS_NOT_SUPPORTED</a> = 155, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c">MQTTREASONCODE_USE_ANOTHER_SERVER</a> = 156, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0">MQTTREASONCODE_SERVER_MOVED</a> = 157, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd">MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED</a> = 158, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1">MQTTREASONCODE_CONNECTION_RATE_EXCEEDED</a> = 159, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f">MQTTREASONCODE_MAXIMUM_CONNECT_TIME</a> = 160, 
<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842">MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED</a> = 161, 
<br />
&#160;&#160;<a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f">MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED</a> = 162
<br />
 }</td></tr>
<tr class="separator:aba6db0fccfa3f8972ea48117b8b2a279"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a91922a5e3c1e5ec7670b6e296854f1b7"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_m_q_t_t_reason_codes_8h.html#a91922a5e3c1e5ec7670b6e296854f1b7">MQTTReasonCode_toString</a> (enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a> value)</td></tr>
<tr class="separator:a91922a5e3c1e5ec7670b6e296854f1b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="aba6db0fccfa3f8972ea48117b8b2a279"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aba6db0fccfa3f8972ea48117b8b2a279">&#9670;&nbsp;</a></span>MQTTReasonCodes</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The MQTT V5 one byte reason code </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a63b379af5fba8c0512b381a4dbe26969"></a>MQTTREASONCODE_SUCCESS&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a3590f41d984646bc58c82734c1516c92"></a>MQTTREASONCODE_NORMAL_DISCONNECTION&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a3fd0d12c0e44b4df9f716aef89b61aff"></a>MQTTREASONCODE_GRANTED_QOS_0&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a07578b30b2d72af2eeea6be268475876"></a>MQTTREASONCODE_GRANTED_QOS_1&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a74ac34a39a849c9c369b18545a4b1f93"></a>MQTTREASONCODE_GRANTED_QOS_2&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a55f533a6cc98417d08dac8cc69da0ed3"></a>MQTTREASONCODE_DISCONNECT_WITH_WILL_MESSAGE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a1720d8b04af4c0d92e27b378d735e899"></a>MQTTREASONCODE_NO_MATCHING_SUBSCRIBERS&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a55208c34a26f67e112d53c54be37acb9"></a>MQTTREASONCODE_NO_SUBSCRIPTION_FOUND&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a0c0726c0e87eaddd636708497c69d055"></a>MQTTREASONCODE_CONTINUE_AUTHENTICATION&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a6cc1b342856c1d96d54c368148b536f7"></a>MQTTREASONCODE_RE_AUTHENTICATE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a1881ee597bfef9157f0034a1377328e3"></a>MQTTREASONCODE_UNSPECIFIED_ERROR&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a2cbee3502c00d304bf1091195457fcf5"></a>MQTTREASONCODE_MALFORMED_PACKET&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279ae0dad403f352e31449764e2ac94c7756"></a>MQTTREASONCODE_PROTOCOL_ERROR&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a41629fa453cdf14ef6a5370a16d5a19c"></a>MQTTREASONCODE_IMPLEMENTATION_SPECIFIC_ERROR&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a021ceca20e6d35279075a2b93ece973d"></a>MQTTREASONCODE_UNSUPPORTED_PROTOCOL_VERSION&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279ab58bb236e7dbd000a56c590c01bc73fd"></a>MQTTREASONCODE_CLIENT_IDENTIFIER_NOT_VALID&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279abfc617112d5856722108912c5c6633ff"></a>MQTTREASONCODE_BAD_USER_NAME_OR_PASSWORD&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a91a14fc763349cf4a7047d24f13d0803"></a>MQTTREASONCODE_NOT_AUTHORIZED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a0cfd4de78870b3fb0499b916d06d40bb"></a>MQTTREASONCODE_SERVER_UNAVAILABLE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279af507e75147b0b34f36955c9f62389a74"></a>MQTTREASONCODE_SERVER_BUSY&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279ab4cf7578f0078293fa66a4cd5e5d4aa4"></a>MQTTREASONCODE_BANNED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a085e1572ffce61838807b7429b691113"></a>MQTTREASONCODE_SERVER_SHUTTING_DOWN&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279af62e569703d7a7f0acffaa59522b9dc3"></a>MQTTREASONCODE_BAD_AUTHENTICATION_METHOD&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279af21a6c320e34993d7aa169330ab23409"></a>MQTTREASONCODE_KEEP_ALIVE_TIMEOUT&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279ad15ffa6884f97976e237afafcbccea21"></a>MQTTREASONCODE_SESSION_TAKEN_OVER&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a00319b171f469824dd6938cbd0212b5b"></a>MQTTREASONCODE_TOPIC_FILTER_INVALID&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a6268968177868576f6b9239aa9afd8ac"></a>MQTTREASONCODE_TOPIC_NAME_INVALID&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279adaee01dbc97a0773b5032a29c797613a"></a>MQTTREASONCODE_PACKET_IDENTIFIER_IN_USE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a4908a8293054f8ff8d6c47fe0cf31932"></a>MQTTREASONCODE_PACKET_IDENTIFIER_NOT_FOUND&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a45afaacbefd2d816fddf9fe9804b61d1"></a>MQTTREASONCODE_RECEIVE_MAXIMUM_EXCEEDED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a8e0fcdd051e154e319058600b58652ec"></a>MQTTREASONCODE_TOPIC_ALIAS_INVALID&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a11a587e15c468bf1c6ba9df7e8fd78aa"></a>MQTTREASONCODE_PACKET_TOO_LARGE&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279af76d0e32fb44fa94e407b1af5dc7aa4e"></a>MQTTREASONCODE_MESSAGE_RATE_TOO_HIGH&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a954fcabf6e88925b2a57bcd84032d9f9"></a>MQTTREASONCODE_QUOTA_EXCEEDED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279ae1e3b428072be26d2cbf6f88361f76cc"></a>MQTTREASONCODE_ADMINISTRATIVE_ACTION&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a2d629400116e1723c5e2e597bbfe29ca"></a>MQTTREASONCODE_PAYLOAD_FORMAT_INVALID&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279aa4378012148d98599398bc4a3480c38f"></a>MQTTREASONCODE_RETAIN_NOT_SUPPORTED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a83865a2440b512e5602152521e3810bb"></a>MQTTREASONCODE_QOS_NOT_SUPPORTED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279aabaee4062c4e4941b9eed59f09e9440c"></a>MQTTREASONCODE_USE_ANOTHER_SERVER&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a783254c7acf8de52ee345bc176f9d6c0"></a>MQTTREASONCODE_SERVER_MOVED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a1c694648e36a40162939a2785450b6bd"></a>MQTTREASONCODE_SHARED_SUBSCRIPTIONS_NOT_SUPPORTED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a879c56ed34fa2dd6492e7a34a9747bc1"></a>MQTTREASONCODE_CONNECTION_RATE_EXCEEDED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a6f07c3b42690afc7b117321dc4e2657f"></a>MQTTREASONCODE_MAXIMUM_CONNECT_TIME&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a7bcd0f9b21c398a217667aebb4107842"></a>MQTTREASONCODE_SUBSCRIPTION_IDENTIFIERS_NOT_SUPPORTED&#160;</td><td class="fielddoc"></td></tr>
<tr><td class="fieldname"><a id="aba6db0fccfa3f8972ea48117b8b2a279a81b5708f676f52594b680f085e444e1f"></a>MQTTREASONCODE_WILDCARD_SUBSCRIPTIONS_NOT_SUPPORTED&#160;</td><td class="fielddoc"></td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a id="a91922a5e3c1e5ec7670b6e296854f1b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91922a5e3c1e5ec7670b6e296854f1b7">&#9670;&nbsp;</a></span>MQTTReasonCode_toString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* MQTTReasonCode_toString </td>
          <td>(</td>
          <td class="paramtype">enum <a class="el" href="_m_q_t_t_reason_codes_8h.html#aba6db0fccfa3f8972ea48117b8b2a279">MQTTReasonCodes</a>&#160;</td>
          <td class="paramname"><em>value</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a printable string description of an MQTT V5 reason code. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td>an MQTT V5 reason code. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the printable string description of the input reason code. NULL if the code was not found. </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:45 for Paho Asynchronous MQTT C Client Library by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
