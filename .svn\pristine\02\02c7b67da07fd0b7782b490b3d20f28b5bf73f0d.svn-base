
#pragma once
#include "afxwin.h"
#include "afxmt.h"


// This class is responsible for starting and monitoring an external application,
// which is automatically restarted (reaminated) in case of its termination.
// A separate thread is used for monitoring and restarting the application.
// The reamination events are communicated to the main thread through a private window message.
//
class CGTWMonitorWorker
{
public:

	// The only constructor (a default one)
	CGTWMonitorWorker():
		  m_bStopRequested(false),
		  m_pWorkerThread(NULL),
		  m_pMainThread(NULL),
		  m_hProcess(NULL){}

	~CGTWMonitorWorker(void);

	// ID for the private window message, which is used for notification of the main thread
	// about reamination events:
	// wParam - a value of type BOOL, indicated if the reanimation was completed successfully
    // lParam - a pointer to a CString object containing the description of the reason of
    //  reanimation failure (NULL if the reanimation was successful, i.e. if wParam == TRUE)
	static const UINT GTWMonitored_MSG_ID = WM_USER + 1;

	// Starts the application, using the command line passed as the parameter,
	// and starts the monitoring thread.
	// In case of failure throws an exception of CString type with the object containing
	// the description of the problem.
	void Run(CString &csExe, CString csIni ) throw (...);

	// Stops monitoring the application (but doesn't terminate the application)
	void Stop(void);

private:

	static const DWORD threadTerminationTimeout = 5 * 1000; // milliseconds

	// Thread procedure for the worker thread.
	// Monitores the application by calling periodically CheckProcess
	// The parameter is the object of CReaminatorWorker class, which is the owner of the thread.
	// (Otherwise we cannot access the non-static class members, since this method is static)
	static UINT WorkerThreadProc(LPVOID pParam);

	// Checks the monitored application status and starts it if it's not running anymore
	void CheckProcess();

	// critical section for accessing m_bStopRequested, which can be accessed by both
	// the main thread and the worker (monitoring) thread
	CCriticalSection m_critSect;

	// Command line used for starting the application
	CString m_csExe;
	CString m_csIni;

	// Flag indicating that the worker (monitoring) thread needs to be stopped.
	// IMPORTANT: Must be accessed only using thread-safe helper methods
	// GetStopRequested and SetStopRequested
	bool m_bStopRequested;

	// Worker thread
	CWinThread* m_pWorkerThread;

	// Main thread. Used for sending notification window message to it from the worker thread
	CWinThread* m_pMainThread;

	// Handle of the process of the application, which is been monitored
	HANDLE m_hProcess;

	// Stops the worker (monitoring) thread. If the thread is not stopped after the timout, which is
	// indicated in milliseconds as a method parameter, then the thread is killed.
	void StopThread(DWORD timeoutMs);

	// Starts the application using the command line stored in m_csCmdLine, checks if the process
	// was started successfully and saves the handle of the process to m_hProcess.
	// In case of failure throws an exception of CString type with the object containing
	// the description of the problem.
	void StartProcess(void) throw(...);

	// Thread-safe access to m_bStopRequested
	bool GetStopRequested(void)
	{
		m_critSect.Lock();
		bool bStopRequested = m_bStopRequested;
		m_critSect.Unlock();

		return bStopRequested;
	}

	// Thread-safe access to m_bStopRequested
	void SetStopRequested(bool value)
	{
		m_critSect.Lock();
		m_bStopRequested = value;
		m_critSect.Unlock();
	}
};
