/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWEquationFunctionBitWiseAnd.cpp                                                */
/* DESCRIPTION:  Class definition for BitWise equations                      */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 6 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif
#include "GTWEquationFunctionConverter.h"
#include "GTWEquationFunctionBitWiseAnd.h"


GTWEQUAT_FUNC_DESCRIPTOR gtweqlgc_BitWiseAndFunction = {"bit_and", "bit_and(expr-list)", "Bit Wise 'and' of all exprs in expr-list", GTWEquationFunctionBitWiseAnd::gtweqlgc_allocBitWiseAnd};

void GTWEquationFunctionBitWiseAnd::getValue(GTWMasterDataObject *pMdo, TMWTYPES_UINT *pValue,TMWTYPES_USHORT *pStdQuality)
{
  *pValue      = 0xffffffff;
  *pStdQuality = GTWDEFS_STD_QLTY_GOOD;

  TMWTYPES_UINT     tempValue;
  GTWDEFS_STD_QLTY tempStdQuality;
  for (TMWTYPES_UINT i = 0;i < getNumArguments();i++)
  { 

    getConverter(i).getValue(pMdo,&tempValue,&tempStdQuality);

    *pValue      &= tempValue;
    *pStdQuality |= tempStdQuality;
  }  
}

