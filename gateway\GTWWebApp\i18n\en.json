{"TR_61850_CANT_DELETE_DATASET_DISCONNECTED": "Cannot delete Dataset '{{arg1}}' while disconnected. Please connect and try again.", "TR_61850_CANT_DELETE_DATASET_IN_USE": "Cannot delete Dataset '{{arg1}}' because it belongs to one or more control blocks. Delete control blocks and try again.", "TR_61850_CANT_DELETE_NON_DYNAMIC_DATASET": "Dataset '{{arg1}}' is not dynamic and cannot be deleted.", "TR_61850_CLIENT_CANT_CHANGE_DATASET": "Unable to change dataset.  The control block {{arg1}} has associated MDOs.", "TR_61850_CLIENT_DUP_LIC": "Failed to add IEC 61850 Client (could be duplicate, or no license)", "TR_61850_CLIENT_DUPLICATE": "Cannot add 61850 client : '{{arg1}}'. Duplicate name.", "TR_61850_CLIENT_FAILED_DUPLICATE": "Failed to create 61850 Client because it has a duplicate name : '{{arg1}}'", "TR_61850_CLIENT_SCL_FILE_INVALID": "61850 Client SCL file was invalid: {{arg1}}.", "TR_61850_CLIENT_SCL_FILE_NOT_FOUND": "61850 Client SCL file was not found at: {{arg1}}.", "TR_61850_Command_ADD": "Could not add {{arg1}} CommandPointSet on 61850 server: {{arg2}}.", "TR_61850_Command_DELETE_EXISTING": "Cannot delete '{{arg1}}' Command Point Set, it has existing items:{{arg2}}", "TR_61850_CREATE_SDO_FAILED_DUPLICATE": "Could not create {{arg1}} SDO. Duplicate tag name?", "TR_61850_CREATE_SDO_FAILED_INVALID_TAG": "Could not create {{arg1}} SDO. Invalid tag name?", "TR_61850_CREATE_SDO_FAILED_LIC_LIMIT": "Could not create {{arg1}} SDO. The license limit was reached", "TR_61850_INVALID_MODEL": "Error: Invalid 61850 model", "TR_61850_FAILED_TO_ENABLE_RCB": "Added RCB '{{arg1}}' successfully but failed to enable the report. See logs for details.", "TR_61850_LOAD_COMMUNICATION": "Failed to load communication section of the SCL file. Using defaults.", "TR_61850_MD_BIND_MISMATCH": "Could not bind SDO with MDO. Type mismatch?", "TR_61850_MDO_DELETE": "61850 MDOs cannot be deleted", "TR_61850_MDO_SLAVE_MAP_FAILED": "Cannot map a non MDO point to this slave point", "TR_61850_NO_Command": "No more IEC 61850 CommandPointSets available", "TR_61850_NO_CONTROLS": "No controls exist in the current model.", "TR_61850_NO_POINTS_FOUND": "No valid points", "TR_61850_NO_POLLED": "No more IEC 61850 PolledPointSets available", "TR_61850_NO_Writable": "No more IEC 61850 WritablePointSets available", "TR_61850_ONLY_STAUTS_CONTROLS": "No valid controls exist in the current model : all of the controls in the current model are status only controls. Please set the ctlModel to something other than status only if needed.", "TR_61850_POLLED_ADD": "Could not add {{arg1}} PolledPointSet on 61850 server: {{arg2}}.", "TR_61850_POLLED_DELETE": "Cannot delete '{{arg1}}' Polled Dataset, it has existing items: {{arg2}}", "TR_61850_POLLED_DELETE_EXISTING": "Cannot delete '{{arg1}}' Polled Point Set, it has existing items: {{arg2}}", "TR_61850_REPORT_DELETE_ERROR": "Cannot delete '{{arg1}}' Report Control Block, it currently has a retry enable thread running. Change the 'Retry Enable Count' to zero to stop the thread and try again.", "TR_61850_REPORT_DELETE_EXISTING": "Cannot delete '{{arg1}}' Report Control Block, it has existing items: {{arg2}}", "TR_61850_SERVER_DUP_LIC": "Failed to add IEC 61850 Server (could be duplicate, or no license)", "TR_61850_SERVER_DUPLICATE": "Cannot add 61850 server : '{{arg1}}'. Duplicate name.", "TR_61850_SERVER_FAILED_DUPLICATE": "Failed to create 61850 Server because it has a duplicate name: '{{arg1}}'", "TR_61850_SERVER_NEEDS_TO_BE_RESTARTED_TO_APPLY_CHANGES": "The 61850 server '{{arg1}}' needs to be restarted to apply the changes", "TR_61850_SERVER_NOT_FOUND_ICD_FILE": "Cannot find 61850 server ICD file : '{{arg1}}'.", "TR_61850_TOO_MANY_POINTS_FOUND": "Too many points in the model to display, please select first a Pre-Filter. Only the first 1000 items are displayed.", "TR_61850_Writable_ADD": "Could not add {{arg1}} WritablePointSet on 61850 server: {{arg2}}.", "TR_61850_Writable_DELETE_EXISTING": "Cannot delete '{{arg1}}' Writable Point Set, it has existing items: {{arg2}}", "TR_6T_CATEGORY_APP": "APP", "TR_6T_CATEGORY_C_APP": "C APP", "TR_6T_CATEGORY_C_CASM": "C CASM", "TR_6T_CATEGORY_C_CLIENT": "C CLIENT", "TR_6T_CATEGORY_C_CLIENTRPT": "C CLIENTRPT", "TR_6T_CATEGORY_C_CLIENTSTATE": "C CLIENTSTATE", "TR_6T_CATEGORY_C_DYNAMIC_DATASETS": "C DYNAMIC DATASETS", "TR_6T_CATEGORY_C_EXTREF": "C EXTREF", "TR_6T_CATEGORY_C_SCL": "C SCL", "TR_6T_CATEGORY_C_STACK": "C STACK", "TR_6T_CATEGORY_C_TARGET": "C TARGET", "TR_6T_CATEGORY_C_TEST": "C TEST", "TR_6T_CATEGORY_C_TIME": "C TIME", "TR_6T_CATEGORY_C_TRANSLOW": "C TRANSLOW", "TR_6T_CATEGORY_C_TRANSPORT": "C TRANSPORT", "TR_6T_CATEGORY_CLIENTPARSEVALUES": "CLIENT PARSE VALUES", "TR_6T_CATEGORY_CONTROL": "CONTROL", "TR_6T_CATEGORY_DISCOVERY": "DISCOVERY", "TR_6T_CATEGORY_EXTREF": "EXTREF", "TR_6T_CATEGORY_GENERAL": "GENERAL", "TR_6T_CATEGORY_GOOSE": "GOOSE", "TR_6T_CATEGORY_READ": "READ", "TR_6T_CATEGORY_READ_HANDLER": "READ HANDLER", "TR_6T_CATEGORY_REPORT": "REPORT", "TR_6T_CATEGORY_TIME_SYNCH": "TIME SYNCH", "TR_6T_CATEGORY_WRITE": "WRITE", "TR_6T_CATEGORY_XML": "XML", "TR_6T_FILTER": "MMS Filter", "TR_6T_LOW_LEVEL_STACK": "MMS Low Level Stack", "TR_6T_STANDARD_STACK": "MMS Standard Stack", "TR_ABOUT": "About", "TR_ACCEPT_CLOCK_SYNC": "Accept Clock Syncs", "TR_ACCEPT_CLOCK_SYNC_DESC": "If true and UseSystemTime is true then the Windows system clock will be adjusted by a time sync received from an external master device. If false time syncs will not adjust the Windows system clock.When using a simulated clock this setting has no effect and Clock syncs are always accepted and adjust the simulated clock", "TR_ACCESS_READ_AND_WRITE_USERS_OF_MANAGEMENT": "Access read and write of users management", "TR_ACCESS_RIGHT": "Access Right", "TR_ACCESS_RIGHT_DESC": "Definition of the access right", "TR_ACCESS_TO_CONFIG_UI_FOR_READ_AND_WRITE": "Access to configuration UI for read/write", "TR_ACCESS_TO_CONFIG_UI_FOR_READ_ONLY": "Access to configuration UI for read only", "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_AND_WRITE": "Access to runtime UI for read and write", "TR_ACCESS_TO_RUNTIME_UI_FOR_READ_ONLY": "Access to runtime UI for read only", "TR_ACTION": "Action", "TR_ACTION_NAME": "Action Name", "TR_ACTION_NAME_DESC": "Action Name", "TR_ACTION_PERIOD_SET_TO_0_NO_ACTION_WILL_BE_PERFORMED": "Action period is set to 0 ms. No action will be performed!", "TR_ACTION_VALUES": "Action Values", "TR_ACTION_VALUES_DESC": "Action Values", "TR_ACTION_VALUES_EXAMPLE": "Action Values example", "TR_ACTION_VALUES_EXAMPLE_DESC": "Action Values Example", "TR_ACTIVATE_ONLINE": "Activate On-line", "TR_ACTIVATE_PRODUCT_KEY_OFFLINE": "Activate product key off-line", "TR_ACTIVATE_PRODUCT_KEY_ONLINE": "Activate product key on-line", "TR_ACTIVE": "Active", "TR_ACTIVE_DESC": "Active", "TR_ADD": "Add ", "TR_ADD_DATA_ATTRIBUTE": "Add Data Attribute", "TR_ADD_DATA_ATTRIBUTE_DESC": "Add Data Attribute", "TR_ADD_DESC": "Add", "TR_ADD_DOMAIN": "Add Domain", "TR_ADD_DOMAIN_DESC": "Add Domain", "TR_ADD_GOOSE": "Could not add {{arg1}} GOOSE on 61850 server: {{arg2}}.", "TR_ADD_ITEM": "Add Item", "TR_ADD_ITEMS": "Add Item(s)", "TR_ADD_ITEM_DESC": "Add Item", "TR_ADD_ITEMS_DESC": "Add Item(s)", "TR_ADD_NEW_USER": "Add New User", "TR_ADD_NODE_TO_FILTER_LOG": "Don't Show log output for this device", "TR_ADD_POLLED_DS": "Could not add {{arg1}} Dataset on 61850 server: {{arg2}}.", "TR_ADD_PROPERTY": "Add Property", "TR_ADD_PROPERTY_DESC": "Add Property", "TR_ADD_SUBSCRIPTION": "Add Subscription", "TR_ADD_SUBSCRIPTION_DESC": "Add Subscription", "TR_ADD_TASE2_DA_TO_DS_FAILED": "Adding data attributes to the dataset failed. See logs for details.", "TR_ADD_TASE2_DA_TO_DS_FAILED_REMOVE_OK": "Adding data attributes to the dataset failed. See logs for details. Note: removal of members succeeded.", "TR_ADD_TASE2_DS_FAILED_TO_RECREATE_DATASET_WITHOUT_THE_ADDED_MEMBERS": "Failed to recreate dataset without the added members. See logs for details. It is recommended to close the SDG withOUT saving and start over.", "TR_ADD_TASE2_DS_HAS_BEEN_RECREATED_WITHOUT_THE_ADDED_MEMBERS": "Dataset has been recreated without the added members.", "TR_ADD_TASE2_RDS": "Add Dataset Transfer Sets", "TR_ADD_TASE2_RDS_DESC": "Add Dataset Transfer Sets", "TR_ADVANCE": "Advanced Parameters", "TR_ALARM_ARRAY": "Array of Alarm", "TR_ALARM_ARRAY_DESC": "Define the Array of Alarm", "TR_ALIAS_NAME": "Name", "TR_ALIAS_NAME_DESC": "Specifies the Name.", "TR_ALLOW_ANONYMOUS_USERS": "Allow Anonymous Users", "TR_ALLOW_ANONYMOUS_USERS_DESC": "Allow Anonymous Users", "TR_ALLOW_USER_CERTS": "User Certificate", "TR_ALLOW_USER_CERTS_DESC": "User Certificate", "TR_ALLOW_USER_NAMES": "Allow User Names", "TR_ALLOW_USER_NAMES_DESC": "Allow User Names", "TR_ALLOWABLE_CONNECTION_TYPES": "Allowable Connection Types", "TR_APP_CERT_DIRECTORY": "Application Certificate Directory", "TR_APP_CERT_DIRECTORY_DESC": "Specifies the Application Certificate Directory.", "TR_APP_ERROR": "Application Error", "TR_APP_ERROR_DESC": "Log application error messages", "TR_APP_START_STOP": "Application Start/Stop", "TR_APP_START_STOP_DESC": "Log application startup/shutdown messages", "TR_APP_STATUS": "Application Status", "TR_APP_STATUS_DESC": "Log periodic application status messages", "TR_APPL_AUTO_REQ_MODE": "Auto Request Mode", "TR_APPL_AUTO_REQ_MODE_DESC": "Each bit enables(1) or disables(0) an automatic requestThis parameter is only used for master or slave sessions using the DNP3 protocol.", "TR_APPL_DNPABS_RESP_TIMEOUT": "Response timeout (ms) - DNP", "TR_APPL_DNPABS_RESP_TIMEOUT_DESC": "Default DNP application response timeout. This value is the maximum amount of time (in milliseconds) that will be allowed before a command is canceled due to timeout. This time starts when the request is submitted and ends when the final application level response is received. This value can generally be overridden for specific data points by the 'TO' option in the point mapping file.", "TR_APPL_IECABS_RESP_TIMEOUT": "Response timeout (ms) - IEC", "TR_APPL_IECABS_RESP_TIMEOUT_DESC": "Default IEC application response timeout. This value is the maximum amount of time (in milliseconds) that will be allowed before a command is canceled due to timeout. This time starts when the request is submitted and ends when the final application level response is received. This value can generally be overridden for specific data points by the 'TO' option in the point mapping file.", "TR_APPL_INCR_RESP_TIMEOUT": "Incremental Response Timeout (ms)", "TR_APPL_INCR_RESP_TIMEOUT_DESC": "Maximum amount of time to allow between messages from a remote device when a request is pending to that device. The message need not be a direct response to the pending request. If no message is received from the remote device within this period it is assumed the device has terminated processing of the request and the request is canceled due to an application level timeout. This timer is restarted every time a message is received from the remote device.", "TR_APPL_MBABS_RESP_TIMEOUT": "Response timeout (ms) - Modbus", "TR_APPL_MBABS_RESP_TIMEOUT_DESC": "Default Modbus application response timeout. This value is the maximum amount of time (in milliseconds) that will be allowed before a command is canceled due to timeout. This time starts when the request is submitted and ends when the final application level response is received. This value can generally be overridden for specific data points by the 'TO' option in the point mapping file.", "TR_APPLICATION_FUNCTIONS": "Application Functions", "TR_APPLICATION_LAYER": "Application Layer", "TR_APPLICATION_SECURITY_CONFIGURATION": "Application Security Configuration", "TR_ARE_YOU_SURE_TO_CLEAR_MODEL": "Are you sure you want to clear the model?", "TR_ARE_YOU_SURE_TO_DELETE_DATA_ATTRIBUTE": "Are you sure you want to delete this Data Attribute?", "TR_ARE_YOU_SURE_TO_DELETE_DATASET": "Are you sure you want to delete this dataset?", "TR_ARE_YOU_SURE_TO_DELETE_DOMAIN": "Are you sure you want to delete this Domain?", "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_NODENAME": "Are you sure you want to delete {{NodeName}}?", "TR_ARE_YOU_SURE_TO_DELETE_OBJECT_WITH_CHILDREN_NODENAME": "Are you sure you want to delete {{NodeName}} and all children?", "TR_ARE_YOU_SURE_TO_DELETE_USER_": "Are you sure to delete user {{username}}?", "TR_ARE_YOU_SURE_TO_DELETE_USER_X": "Are you sure to delete user {{username}}?", "TR_ARE_YOU_SURE_TO_DELETE_WORKSPACE": "Are you sure to delete Workspace {{WORKSPACE}}?", "TR_ARE_YOU_SURE_TO_REMOVE_MAPPING": "Are you sure to remove the mapping {{mapping}}?", "TR_AREA_SPACE": "Area Space", "TR_AREA_SPACE_DESC": "Define the Area Space", "TR_ASDUORIGINATOR_ADDR": "Originator address", "TR_ASDUORIGINATOR_ADDR_DESC": "Originator address (for 2-octet COT).  This parameter is only used for master sessions using the IEC 60870-5-101 or IEC 60870-5-104 protocol profiles.", "TR_ASDUSIZE_CMN_ADDR": "Number of octets (bytes) in Common Address of ASDU", "TR_ASDUSIZE_CMN_ADDR_DESC": "Number of octets (bytes) in Common Address of ASDU (sector address) field.  This parameter is only used for master and slave sessions using the IEC 60870-5-101 or IEC 60870-5-104 protocol profiles.", "TR_ASDUSIZE_COT": "Number of octets (bytes) in Cause Of Transmission", "TR_ASDUSIZE_COT_DESC": "Number of octets (bytes) in Cause Of Transmission (COT) field of ASDU.  This parameter is only used for master and slave sessions using the IEC 60870-5-101 or IEC 60870-5-104 protocol profiles.", "TR_ASDUSIZE_IOA": "Number of octets (bytes) in Point Number", "TR_ASDUSIZE_IOA_DESC": "Number of octets (bytes) in Information Object Address (point number) field.  This parameter is only used for master and slave sessions using the IEC 60870-5-101 or IEC 60870-5-104 protocol profiles.", "TR_AUDIT": "Audit", "TR_AUDIT_LOG": "<PERSON>t Log", "TR_AUTH_CONFIG": "Authentication Configuration", "TR_AUTH_MODE": "Authentication Mode", "TR_AUTH_SEC_USERS_LIST": "SECAuth v5 Users", "TR_AUTH_SEC_USERS_LIST_DESC": "When DNP Secure Authentication Version 5 is configured, one user is created with the default name Common. See Manual for more details.", "TR_AUTHENTICATION_PASSWORD": "Authentication Password", "TR_AUTO_MAP_QUALITY_TIME": "Auto Map Quality & Time", "TR_AUTO_MAP_QUALITY_TIME_DESC": "If true 'TIME' and 'QUALITY' columns are added to the grid above. Time and quality are mapped according to IEC 61850-7-3 and IEC 61850-7-4. If false 't' and 'q' are not mapped and will be included in the list.", "TR_AUTO_SAVE": "Auto Save", "TR_AUTO_SAVE_ENABLE": "enable", "TR_AUTO_SAVE_ENABLE_DESC": "Enable auto save", "TR_AUTO_SAVE_PRD": "Auto Save Period", "TR_AUTO_SAVE_PRD_DESC": "Maximum amount of time between saving the .ini and .csv application configuration files in milliseconds. A value of 0 will disable saves. The lowest value for saving period is 60000 ms (1 minute)", "TR_AUTO_START_ENGINE_ON_GUI_EXIT": "Auto Start Engine on GUI Exit", "TR_AUTO_START_SERVICE_ON_GUI_EXIT": "Auto Service Engine on GUI Exit", "TR_AVAILABLE_SERVERS": "Available Servers", "TR_AVAILABLE_SERVERS_DESC": "Available Servers", "TR_BAD_NAMESPACE": "Namespace name can not have ..", "TR_BILATERAL_TABLE_ID": "Bilateral Table ID", "TR_BILATERAL_TABLE_ID_DESC": "Bilateral Table ID", "TR_BLOCKED": "BLOCKED", "TR_BLOCKED_DESC": "Blocked", "TR_BROWSER_WARNING_TITLE": "Incompatible Web Browser", "TR_BUFFER_OVERFLOW": "Buffer Overflow", "TR_BUFFER_OVERFLOW_DESC": "Specifies if the buffer overflow is active.", "TR_BUFFER_TIME": "Buffer Time (ms)", "TR_BUFFER_TIME_DESC": "Specifies the current Buffer Time.", "TR_BUFFERED": "Buffered", "TR_CAN_NOT_SET_SIGNED_AND_DUAL_REGISTER": "Cannot set SIGNED and DualRegister. The DualRegister option will be turned off. If Dual option is desired, turn off SIGNED.", "TR_CANCEL": "Cancel", "TR_CANT_CREATE_MDO": "Could not create {{arg1}} MDO.  Possible invalid tag name.", "TR_CANT_DELETE_POINT_IN_USE": "Equation: {{arg1}} is mapped to other points or is used in an equation, cannot edit/delete", "TR_CANT_DELETE_POINT_IS_MAPPED": "MDO: '{{arg1}}' is mapped to slave points or is used in an equation, can not delete", "TR_CANT_SEND_BROADCAST_EVENT_WS_MESSAGE": "Can't send Broadcast Event Web Socket message", "TR_CANT_SEND_HEALTH_ON_MESSAGE_WS_MESSAGE": "Can't send Health On Message Web Socket message", "TR_CANT_SEND_HEALTH_UPDATE_WS_MESSAGE": "Can't send Health Update Web Socket message", "TR_CANT_SEND_LOG_WS_MESSAGE": "Can't send Log Web Socket message", "TR_CANT_SEND_ON_MESSAGE_BROADCAST_EVENT_WS_MESSAGE": "Can't send on Message Broadcast Event Web Socket message", "TR_CANT_SEND_OPEN_BROADCAST_EVENT_WS_MESSAGE": "Can't send open Broadcast Event Web Socket message", "TR_CATEGORY": "Category", "TR_CENTRAL_AUTHORITY": "Certificate Authority", "TR_CERT_AUTH_CHAINING_VERIFICATION_DEPTH": "Certificate Authority Chaining Verification Depth", "TR_CERT_AUTH_DIR_PATH": "Directory to Certificate Authority", "TR_CERT_AUTH_DIR_PATH_DESC": "If it is not NULL, it points to a directory containing CA certificates in PEM format. The files each contain one CA certificate.  See the OpenSSL documentation for further requirements of this directory.", "TR_CERT_AUTH_FILE_PATH": "Certificate Authority File", "TR_CERT_AUTH_FILE_PATH_DESC": "if it is not NULL, it specifies a file containing the public master CA certificates.  The file (PEM format) may contain the certificates of multiple CAs", "TR_CERT_REVOCATION_FILE_PATH": "Certificate Authority Revocation List File", "TR_CERTIFICATE_REJECTED_LIST": "Auto Rejected Certificates (Can be Trusted if needed)", "TR_CERTIFICATE_REJECTED_LIST_DESC": "Auto Rejected Certificates (Can be Trusted if needed)", "TR_CERTIFICATE_TRUSTED_LIST": "Certificate Trusted List", "TR_CERTIFICATE_TRUSTED_LIST_DESC": "Certificate Trusted List", "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH": "CA Chaining Verification Depth", "TR_CHAN_TLSCERT_AUTH_CHAINING_VER_DEPTH_DESC": "Maximum length of 'chained' certificates that is allowed.  This must be greater than 0 to be valid.", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR": "Certificate Authority Directory Path", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_DIR_DESC": "Path to Directory of Certificate Authority Certificates.", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE": "Certificate Authority File", "TR_CHAN_TLSCERTIFICATE_AUTHORITY_FILE_DESC": "File containing Certificate Authority Certificates.", "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE": "Certificate Revocation File", "TR_CHAN_TLSCERTIFICATE_REVOCATION_FILE_DESC": "File containing Certificate Revocation List.", "TR_CHAN_TLSCOMMON_NAME": "TLS Common Name", "TR_CHAN_TLSCOMMON_NAME_DESC": "Common Name to require from peer certificates (or NULL string if no CommonName check is to be done)", "TR_CHAN_TLSDH_FILE": "<PERSON><PERSON><PERSON>", "TR_CHAN_TLSDH_FILE_DESC": "File containing <PERSON><PERSON><PERSON>.", "TR_CHAN_TLSDSACERTIFICATE_FILE": "DSA Public Certificate File", "TR_CHAN_TLSDSACERTIFICATE_FILE_DESC": "File containing the certificate for key for DSA TLS ciphers.", "TR_CHAN_TLSDSAPRIVATE_KEY_FILE": "DSA Private Key File", "TR_CHAN_TLSDSAPRIVATE_KEY_FILE_DESC": "File containing the private key for DSA TLS ciphers.", "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE": "DSA Private Key Pass Phrase", "TR_CHAN_TLSDSAPRIVATE_KEY_PASS_PHRASE_DESC": "PassPhrase for decrypting the private key for DSA TLS ciphers.", "TR_CHAN_TLSENABLE": "Enable TLS", "TR_CHAN_TLSENABLE_DESC": "If true, then the connection will be established using TLS for security.", "TR_CHAN_TLSHANDSHAKE_TIMEOUT": "TLS Handshake Timeout (sec)", "TR_CHAN_TLSHANDSHAKE_TIMEOUT_DESC": "Max time in milliseconds to wait for TLS connect handshake to complete.", "TR_CHAN_TLSRENEGOTIATION_COUNT": "TLS Max PDU before forcing cipher renegotiation", "TR_CHAN_TLSRENEGOTIATION_COUNT_DESC": "Maximum number of ISO PDUs (RFC1006) to allow between cipher renegotiations.  This must be a minimum of 5000 (see IEC 62351-4)", "TR_CHAN_TLSRENEGOTIATION_SECONDS": "TLS Renegotiation (sec)", "TR_CHAN_TLSRENEGOTIATION_SECONDS_DESC": "", "TR_CHAN_TLSRSACERTIFICATE_FILE": "RSA Public Certificate File", "TR_CHAN_TLSRSACERTIFICATE_FILE_DESC": "File containing the RSA certificate (PEM format) corresponding to the private key.  The certificate must be signed by a Certificate Authority recognized by the peer systems.", "TR_CHAN_TLSRSAPRIVATE_KEY_FILE": "RSA Private Key File", "TR_CHAN_TLSRSAPRIVATE_KEY_FILE_DESC": "File containing the private RSA key (PEM format).", "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE": "RSA Private Key Pass Phrase", "TR_CHAN_TLSRSAPRIVATE_KEY_PASS_PHRASE_DESC": "Pass phrase for decrypting the RSA private key.", "TR_CHANGE_PASSWORD": "Change Password", "TR_CHANGE_VALUE": "Change Value", "TR_CHANGE_VALUE_AND_QUALITY": "Change Value & Quality", "TR_CHANGE_VALUE_AND_QUALITY_OF": "Change Value & Quality of", "TR_CHANGE_VALUE_OF_SELECTED": "Change Value of Selected Tags", "TR_CHANGING_ADVANCE_PARAMATERS_REQUIRE_A_RESTART": "Changing Advanced Parameters Requires a Restart", "TR_CHANNEL_DELETE_ONLY": "Cannot delete last Redundant Channel: {{arg1}}. At least one redundant channel must exist for redundancy group.", "TR_CHANNEL_DUPLICATE": "Cannot add channel: '{{arg1}}'. Duplicate name.", "TR_CHANNEL_ENABLE_TLS": "Enable TLS", "TR_CHANNEL_ERROR_DELETE_CHAN": "An unknown error occurred when trying to delete channel {{arg1}}", "TR_CHANNEL_IP_PORT_NUMBER": "TCP/IP Port Number", "TR_CHANNEL_LOCAL_IP": "Local TCP/IP", "TR_CHANNEL_MBP_CARD_NUMBER": "Card Number", "TR_CHANNEL_MBP_RECEIVE_TIMEOUT": "Receive Timeout", "TR_CHANNEL_MBP_ROUTE_ADDRESS": "Route / Address", "TR_CHANNEL_MBP_SLAVE_PATH": "Slave Path", "TR_CHANNEL_NAME": "Channel Name", "TR_CHANNEL_NAME_DESC": "Alias name for communications channel. Must be specified and be unique.", "TR_CHANNEL_NO_MORE": "No more channels available", "TR_CHANNEL_PROTOCOL_TYPE": "Session Protocol", "TR_CHANNEL_PROTOCOL_TYPE_DESC": "Sets the protocol for the channel Applies to all Physical channel types", "TR_CHANNEL_START_DELAY": "Channel Start Delay (ms)", "TR_CHANNEL_START_DELAY_DESC": "Delay time in milliseconds to stagger starting all channels (slaves and masters). Only affects startup.", "TR_CHANNEL_TCP_MODE": "Mode", "TR_CHECK_BACK_ID": "Check Back ID", "TR_CHNG_INDICATED": "Change in data indicated", "TR_CHNG_INDICATED_DESC": "A change in the data is indicated by the source of data.", "TR_CLEAR": "Clear", "TR_CLEAR_DISPLAY": "Clear Display", "TR_CLEAR_LOG": "Clear Log", "TR_CLEAR_MODEL": "ClearModel", "TR_CLEAR_MODEL_DESC": "Clear Current Model.", "TR_CLEAR_SEARCH": "Clear Search", "TR_CLIENT": "Client", "TR_CLIENT_AE_INVOKE_ID": "AE Invoke ID", "TR_CLIENT_AE_QUALIFIER": "AE Qualifier", "TR_CLIENT_AP_INVOKE_ID": "AP Invoke ID", "TR_CLIENT_APP_ID": "AP Title", "TR_CLIENT_CALLING": "Local", "TR_CLIENT_CONNECTION_INFO": "Client/Connection Information", "TR_CLIENT_IP_ADDRESS": "Client TCP/IP Address", "TR_CLIENT_LN": "Assigned Client", "TR_CLIENT_PRESENTATION_ADDRESS": "Presentation Selector", "TR_CLIENT_SERVER_CONNECTION_SETTINGS": "Client/Server Connection Settings", "TR_CLIENT_SESSION_ADDRESS": "Session Selector", "TR_CLIENT_TRANSPORT_ADDRESS": "Transport Selector", "TR_CLOSE": "Close", "TR_CLOSE_PANEL": "Close Panel", "TR_COILS": "Coils", "TR_COLLAPSE": "Collapse", "TR_COLLAPSE_CHILDREN": "Collapse Children", "TR_COMMAND_KIND": "Command Kind", "TR_COMMAND_SENT_TO_SERVER": "Command sent to server", "TR_COMMAND_SUCCESS": "Success", "TR_CONFIG": "Configuration", "TR_CONFIG_ERROR": "Configuration error: {{arg1}}.", "TR_CONFIG_VIEW_1": "Configuration View #1", "TR_CONFIG_VIEW_2": "Configuration View #2", "TR_CONFIG_VIEW_3": "Configuration View #3", "TR_CONFIGURATION": "Configuration", "TR_CONFIGURATION_REVISION": "Configuration Revision", "TR_CONFIGURATION_REVISION_DESC": "Specifies if the configuration revision is active.", "TR_CONFIGURATOR_ROLE": "CONFIGURATOR", "TR_CONFIRM_NEW_PASSWORD": "Confirm New Password", "TR_CONNECT_RECONNECT": "Connect/Reconnect", "TR_CONNECT_TIMEOUT": "Connect Timeout (msecs)", "TR_CONTROL_BLOCK": "Control Block", "TR_CONTROL_BLOCK_DESC": "Show control block for the MDO", "TR_CONTROL_BLOCK_NAME": "Control Block Name", "TR_CONTROL_POINT": "Control Point", "TR_CONTROL_POINT_DESC": "Define the Control Point", "TR_CONTROL_POINT_LIST": "List of All Control Points", "TR_CONTROL_POINT_LIST_DESC": "Select a Control Point from the list of all Control Points below", "TR_CONTROLBLOCK_NAME": "Control Block name", "TR_CONTROLBLOCK_NAME_DESC": "Specify Control Block name", "TR_COULD_NOT_SET_MDO_OPTIONS": "Could not set MDO options {{arg1}}", "TR_CPU_ABBREVIATION": "CPU", "TR_CREATE_61400_ERROR": "No more alarms available", "TR_CREATE_61400MDO_ERROR": "Could not add {{arg1}} MDO on 61850 server: {{arg2}}. (duplicate ?)", "TR_CREATE_61850_CANT_DELETE": "Cannot delete client {{arg1}} while connected. Please disconnect and try again.", "TR_CREATE_61850_CHANGE_DATASET_NAME_FAILED": "Failed to change dataset name. Error: {{arg1}}", "TR_CREATE_61850_CREATE_MDO_INVALID_TAG": "Could not create {{arg1}} MDO. Invalid tag name?", "TR_CREATE_61850_DELETE": "MDO: '{{arg1}}' is mapped to slave points or is used in an equation, cannot delete", "TR_CREATE_61850_DISABLE_RPT_FAILED": "Failed to disable <PERSON><PERSON>. Error: {{arg1}}", "TR_CREATE_61850_FAILED_TO_READ_RCB": "Failed to read Report Control block. Error returned: {{arg1}}", "TR_CREATE_61850_FAILED_TO_REENABLE_RPT": "Failed to re-enable Control Block. Error: {{arg1}}", "TR_CREATE_61850_LIST_POINTS_NOT_A_CONTROL": "Not a valid Control Block", "TR_CREATE_61850_MDO": "MDO must have a name, cannot create", "TR_CREATE_61850_MDO_EXISTS": "MDO already defined, cannot create", "TR_CREATE_61850_MDO_SET_OPTIONS": "Could not set MDO options {{arg1}}", "TR_CREATE_61850_NO_MORE_CLIENTS": "No more IEC 61850 Clients available", "TR_CREATE_61850_SERVER_NO_ADD_MDO": "Could not add {{arg1}} MDO on 61850 server: {{arg2}}. (duplicate ?)", "TR_CREATE_61850_SET_DATASET_NAME_FAILED": "Failed to set dataset name. Unknown error.", "TR_CREATE_61850_SET_MDO_OPTIONS": "Could not set MDO options {{arg1}}", "TR_CREATE_61850_SET_MDO_PROPERTIES": "Failed to set MDO properties (invalid control)", "TR_CREATE_61850_SET_MDO_PROPERTIES_Control": "Failed to set MDO properties on Control", "TR_CREATE_61850_SET_MDO_PROPERTIES_DATASET": "Failed to set MDO properties on DataSetControl", "TR_CREATE_61850_SET_MDO_PROPERTIES_GOOSE": "Failed to set MDO properties on GOOSE Control", "TR_CREATE_61850_SET_MDO_PROPERTIES_POINTSET": "Failed to set MDO properties on PointSet Control", "TR_CREATE_61850_SET_MDO_PROPERTIES_REPORT": "Failed to set MDO properties on Report Control", "TR_CREATE_C2V": "Create C2V", "TR_CREATE_DATASET_FAILED_CONNECTED": "Cannot create a dataset while disconnected. Please connect and try again.", "TR_CREATE_DATASET_FAILED_EMPTY": "Error: Model is empty", "TR_CREATE_DATASET_FAILED_ERROR": "CreateDataset failed with error {{arg1}}", "TR_CREATE_DATASET_FAILED_EXISTS": "Dataset {{arg1}} already exists. Try delete first and then create again.", "TR_CREATE_DTM_POINT": "Created C:\\dtm_points.csv", "TR_CREATE_EQUATION_FAILED": "Could not create equation {{arg1}} for {{arg2}}", "TR_CREATE_HTML_FILE": "Created C:\\test_harness_points.xml", "TR_CREATE_NEW_DS": "Create New Dataset", "TR_CREATE_NEW_DS_DESC": "Create New Dataset", "TR_CREATE_NEW_POINT_BEFORE_MAPPING": "Create a new point before Mapping", "TR_CREATE_TRIAL_LICENSE": "CAN'T CREATE TRIAL LICENSE", "TR_CREATE_WORKSPACE_DIR": "CAN'T CREATE WORKSPACE FOLDER", "TR_CTRL_AT_DEVICE": "Change in data indicated at device", "TR_CTRL_AT_DEVICE_DESC": "A change in the data is indicated due to action at the device.", "TR_CTRL_BY_COMM": "Change in data indicated at by communications", "TR_CTRL_BY_COMM_DESC": "A change in the data is indicated due to a request through communications.", "TR_CTRL_CONFIRM": "Control has been confirmed", "TR_CTRL_CONFIRM_DESC": "A control request has been confirmed by a remote device, but is not yet complete.", "TR_CTRL_ERROR": "Error in control operation", "TR_CTRL_ERROR_DESC": "A remote device has responded to indicate an error in a control operation.", "TR_CTRL_PENDING": "Control pending", "TR_CTRL_PENDING_DESC": "A control request has been transmitted to a remote device.", "TR_CURRENT_ENTRY_ID": "Current Entry ID", "TR_CURRENT_ENTRY_ID_DESC": "Display the current entry ID.", "TR_CURRENT_INI_FILE": "Current INI file", "TR_CURRENT_PASSWORD": "Current Password", "TR_CURRENT_PASSWORD_DOESNT_MATCH": "Current password doesn't match", "TR_CURRENT_WORKSPACE": "Current Workspace", "TR_CUSTOM_DIPLAY": "Custom Display", "TR_CUSTOM_LOGS": "Custom logs", "TR_CUSTOM_LOGS_DESC": "Log trace messages for custom logs", "TR_DA_ITEM_PROPERTY_LIST": "DA Item property list", "TR_DA_ITEM_PROPERTY_LIST_DESC": "DA Item property list", "TR_DA_POINT_LIST": "Data Attribute Points", "TR_DA_POINT_LIST_CHECKED": "DA Point List Checked", "TR_DA_POINT_LIST_DESC": "Specifies the current data attribute point list.", "TR_DA_POINTS_TO_SEARCH": "Pre-Filter: Search 'All' points or only points from a selected DataSet.", "TR_DA_POINTS_TO_SEARCH_DESC": "The list above can get very large.  This can help cut down the items returned.", "TR_DA_QUALITY": "DA Quality", "TR_DA_QUALITY_DESC": "Define the Data Attribute quality", "TR_DA_SEARCH": "Pre-Filter on the 'Item Name' field (case sensitive)", "TR_DA_SEARCH_DESC": "The list above can get very large.  This can help cut down the items returned.", "TR_DA_TIME": "DA Time", "TR_DA_TIME_DESC": "Define the Data Attribute time", "TR_DA_VALUE": "DA Value", "TR_DA_VALUE_DESC": "Define the Data Attribute value", "TR_DASHBOARD": "Dashboard", "TR_DASHBOARD_MANAGER": "Dashboard Manager", "TR_DATA_ATTRIBUTE_NAME": "Data Attribute Name", "TR_DATA_ATTRIBUTE_NAME_DESC": "Specifies theData Attribute Name", "TR_DATA_ATTRIBUTE_SELECTION": "Data Attribute Selection", "TR_DATA_ATTRIBUTES": "Data Attributes", "TR_DATA_ATTRIBUTES_LIST": "Data Attributes List", "TR_DATA_ATTRIBUTES_LIST_DESC": "Data Attribute List", "TR_DATA_CHANGE": "Data Change", "TR_DATA_CHANGE_DESC": "Specifies if the data change is active.", "TR_DATA_CHNG_MON": "Data Change", "TR_DATA_DELETED": "Data Deleted", "TR_DATA_FILE_PATH": "SDG Configuration Root Path", "TR_DATA_POINTS_LIST": "Data Points list", "TR_DATA_POINTS_LIST_DESC": "Show the list of data points for the selected domain.", "TR_DATA_REFERENCE": "Data Reference", "TR_DATA_REFERENCE_DESC": "Specify the data reference.", "TR_DATA_SAVED": "Data saved", "TR_DATA_SET": "Dataset", "TR_DATA_SET_NAME": "Dataset Name", "TR_DATA_TYPE": "Data Type", "TR_DATA_TYPE_DESC": "Data Type", "TR_DATA_UPDATE_CHANGE": "Data Update Change", "TR_DATA_UPDATE_CHANGE_DESC": "Specifies if the data update change is active.", "TR_DATAATTRIBUTE_NAME": "Data Attribute Name", "TR_DATAATTRIBUTE_QUALITY": "Data Attribute Quality", "TR_DATAATTRIBUTE_TIME": "Data Attribute Time", "TR_DATABASE": "Database", "TR_DATASET_NAME": "Dataset Name", "TR_DATASET_NAME_DESC": "Specifies if the dataset name is active.", "TR_DATASET_READ_FAILED": "Error: Dataset read failed", "TR_DATASET_READ_SUCCEED": "Dataset read success", "TR_DATASET_VERIFICATION_FAILED": "Dataset definitions do not match", "TR_DATASET_VERIFICATION_SUCCEED": "Dataset verification succeed", "TR_DATASET_VERIFY_FAILED": "Dataset verification failed for '{{arg1}}'. Please make sure client and server dataset definitions match otherwise reports will not work properly.", "TR_DATASET_VERIFY_SUCCEED": "Dataset '{{arg1}}' successfully verified!", "TR_MENU_CMD_ADD_61850_DATASET": "Add IEC 61850 Dataset", "TR_DATASETS": "Datasets", "TR_DATATYPE_DELETE_IN_USE": "'{{arg1}}' cannot be deleted (try un-mapping its points)", "TR_DATE_TIME": "Date/Time", "TR_DBAS_SECTOR_ADDRESS": "ASDU address", "TR_DBAS_SECTOR_ADDRESS_DESC": "ASDU address of each sector", "TR_DEBUG": "Debug", "TR_DEEP_GRID_SEARCH": "The 'Deep Search' will search for the Tag Name, Alias, Type and Description in the selected node and any children of that node.", "TR_DEEP_SEARCH": "Deep Search", "TR_DEFAULT": "<PERSON><PERSON><PERSON>", "TR_DELETE": "Delete", "TR_DELETE_ALL_THE_OPTIONS": "Do you want to delete all the options?", "TR_DELETE_DATA_ATTRIBUTE": "Delete Data Attribute", "TR_DELETE_DATA_ATTRIBUTE_DESC": "Delete Data Attribute", "TR_DELETE_DATASET_FAILED": "Delete Dataset '{{arg1}}' failed on the server with unknown error. Check logs and make sure the Dataset is not in use on the server.", "TR_DELETE_DATASET_FAILED2": "Delete Dataset '{{arg1}}' failed on the server with unknown error. Check logs and make sure the Dataset exists on the server.", "TR_DELETE_DATASET_FAILED3": "Delete Dataset '{{arg1}}' failed with error {{arg2}}", "TR_DELETE_DATASET_NOT_FOUND": "Dataset {{arg1}} not found.", "TR_DELETE_DESC": "Delete", "TR_DELETE_DOMAIN": "Delete Domain", "TR_DELETE_DOMAIN_DESC": "Delete Domain", "TR_DELETE_GOOSE": "Cannot delete '{{arg1}}' Goose Control Block, it has existing items:{{arg2}}", "TR_DELETE_MAPPING": "Delete Mapping", "TR_DELETE_SELECTED": "Delete Selected Tags", "TR_DELETE_WORSPACE": "Delete Workspace", "TR_DESCRIPTION": "Description", "TR_DESTINATION_UDP_PORT": "Destination UDP Port", "TR_DEVICE_LOGGED": "Device filter - log output will not be shown for these devices", "TR_OBJECT_ALREADY_EXISTS": "Error: Object with name '{{arg1}}' already exists.", "TR_OPCUA_OBJECT_ALREADY_EXISTS": "Error: OPC UA Device with name '{{arg1}}' already exists. A restart may be needed if a device name has been modified.", "TR_DH_FILE_PATH": "DH File Path", "TR_DIAGNOSTICS_LOG_MASK": "Diagnostics Log Mask", "TR_DIAGNOSTICS_LOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to log diagnostic data, such as change to the number of transmitted frames, in the event log file.  If 0, no diagnostics data will be logged. ", "TR_DIAGNOSTICS_OPC_AELOG_MASK": "Diagnostics OPC Alarm and Event Mask", "TR_DIAGNOSTICS_OPC_AELOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to log diagnostic data, such as change to the number of transmitted frames, through the OPC alarm and Event Server. If 0, no diagnostics data will be reported. ", "TR_DISABLE_SAVE_ON_EXIT_CHK": "Disable Save on Exit", "TR_DISCRETE_INPUTS": "Discrete Inputs", "TR_DISPLAY_WARNING": "Show Warning", "TR_DNP": "DNP", "TR_DNPACTION_MASK0": "DNP Session Action Mask", "TR_DNPACTION_MASK0_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the DNPActionPrd. \nDNP Action mask Definitions: \nSee section 4.2 'Predefined Tag Names for Monitoring and Control' in the Manual. This parameter is only used for master sessions using the DNP3 protocol.", "TR_DNPACTION_NOW": "DNP Session Action Now Mask", "TR_DNPACTION_NOW_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the DNPActionPrd. \nDNP Action mask Definitions: \nSee section 4.2 'Predefined Tag Names for Monitoring and Control' in the Manual. This parameter is only used for master sessions using the DNP3 protocol.", "TR_DNPACTION_PRD0": "DNP Session Action Period (ms)", "TR_DNPACTION_PRD0_DESC": "Time between actions defined in the DNPActionMask. The period is disabled if set to zero.This parameter is only used for master sessions using the DNP3 protocol.", "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT": "Aggressive mode", "TR_DNPAUTH_AGGRESSIVE_MODE_SUPPORT_DESC": "Enable Aggressive mode.", "TR_DNPAUTH_EXTRA_DIAGS": "Extra diagnostics", "TR_DNPAUTH_EXTRA_DIAGS_DESC": "Output extra diagnostics to protocol analyzer.", "TR_DNPAUTH_HMACALGORITHM": "HMAC algorithm", "TR_DNPAUTH_HMACALGORITHM_DESC": "HMAC algorithm to be used in challenges. ", "TR_DNPAUTH_KEY_CHANGE_INTERVAL": "Key Change Interval (ms)", "TR_DNPAUTH_KEY_CHANGE_INTERVAL_DESC": "For Master: Session key interval.  When time since last key change reaches this value, session keys will be updated. For systems that communicate infrequently, this may be set to zero, using only the maxKeyChangeCount to determine when to update keys.\n\nFor Slave: Expected session key interval and count. When this amount of time elapses or this quantity of Authentication messages are sent or received, the session keys for this user will be invalidated. Interval and count should be 2 times the master key change interval and count. For systems that communicate infrequently, DNPAuthKeyChangeInterval may be set to zero, using only the DNPAuthMaxKeyChangeCount to determine when keys should be considered old and should be invalidated. ", "TR_DNPAUTH_MAX_ERROR_COUNT": "<PERSON> Error Count (only for Sav2)", "TR_DNPAUTH_MAX_ERROR_COUNT_DESC": "Number of error messages to be sent before disabling error message transmission ", "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT": "Max Key Change Count", "TR_DNPAUTH_MAX_KEY_CHANGE_COUNT_DESC": "Session Authentication ASDU count since last key change, When this number of authentication ASDUs is transmitted or received since the last key change, session keys will be updated.", "TR_DNPAUTH_OSNAME": "Out Station Name", "TR_DNPAUTH_OSNAME_DESC": "The Out Station Name of this DNP session. This must be configured to match on both the master and the outstation", "TR_DNPAUTH_REPLY_TIMEOUT": "Authentication reply timeout (ms)", "TR_DNPAUTH_REPLY_TIMEOUT_DESC": "How long to wait for any authentication reply. ", "TR_DNPAUTH_SAV5ENABLE": "DNP Secure Authentication Version 5", "TR_DNPAUTH_SAV5ENABLE_DESC": "TRUE if this is a DNP Secure Authentication Version 5 session.", "TR_DNPAUTH_USER_KEY": "User Key (must be 16, 24, or 32 hex values)", "TR_DNPAUTH_USER_KEY_CHANGE_METHOD": "Key change method. (place holder in INI file)", "TR_DNPAUTH_USER_KEY_CHANGE_METHOD_DESC": "Key change method. (place holder in INI file)", "TR_DNPAUTH_USER_KEY_DESC": "User Key (must be 16, 24, or 32 hex values).  For each key there must be unique user number DNPAuthUserNumber.", "TR_DNPAUTH_USER_NAME": "User name", "TR_DNPAUTH_USER_NAME_DESC": "The name of the user.", "TR_DNPAUTH_USER_NUMBER": "User Number", "TR_DNPAUTH_USER_NUMBER_DESC": "User Number: Configuration for each user.  The specification says default user number is 1 providing a user number for the device or 'any' user, configure it as first user in this array. Add any other user numbers. For each user number in the INI file there must be a DNPAuthUserKey.", "TR_DNPAUTO_ENABLE_UNSOL_CLASS1": "Class 1", "TR_DNPAUTO_ENABLE_UNSOL_CLASS1_DESC": "If enabled unsolicited messaging is set, this flag will indicate event class 1 should be enabled for unsolicited reporting. This parameter is only used for Master sessions", "TR_DNPAUTO_ENABLE_UNSOL_CLASS2": "Auto Enable Unsolicited Class 2", "TR_DNPAUTO_ENABLE_UNSOL_CLASS2_DESC": "If enabled unsolicited messaging is set, this flag will indicate event class 2 should be enabled for unsolicited reporting. This parameter is only used for Master sessions", "TR_DNPAUTO_ENABLE_UNSOL_CLASS3": "Auto Enable Unsolicited Class 3", "TR_DNPAUTO_ENABLE_UNSOL_CLASS3_DESC": "If enabled unsolicited messaging is set, this flag will indicate event class 3 should be enabled for unsolicited reporting. This parameter is only used for Master sessions", "TR_DNPCHANNEL_ACTION_MASK0": "DNP Channel Action Mask", "TR_DNPCHANNEL_ACTION_MASK0_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the DNPChannelActionPrd. \nDNP Action mask Definitions: \nSee section 4.2 'Predefined Tag Names for Monitoring and Control' in the Manual. This parameter is only used for master sessions using the DNP3 protocol.", "TR_DNPCHANNEL_ACTION_NOW": "DNP Channel Action Now Mask", "TR_DNPCHANNEL_ACTION_NOW_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the DNPActionPrd. \nDNP Action mask Definitions: \nSee section 4.2 'Predefined Tag Names for Monitoring and Control' in the Manual. This parameter is only used for master sessions using the DNP3 protocol.", "TR_DNPCHANNEL_ACTION_PRD0": "DNP Channel Action Period (ms)", "TR_DNPCHANNEL_ACTION_PRD0_DESC": "Time between actions defined in the DNPChannelActionMask. The period is disabled if set to zero.This parameter is only used for master sessions using the DNP3 protocol.", "TR_DNPCHANNEL_RESPONSE_TIMEOUT": "DNP Channel Response Timeout (ms)", "TR_DNPCHANNEL_RESPONSE_TIMEOUT_DESC": "For a DNP master how int to wait for a response to a request that has  actually been transmitted. This value can be made shorter than the session default Response Timeout to quickly determine if a particular device is not responding, without causing requests to other devices on the same channel to also time out. NOTE: This is not used by a DNP slave. ", "TR_DNPENABLE_SECURE_AUTHENTICATION": "Enables DNP Secure Authentication", "TR_DNPENABLE_SECURE_AUTHENTICATION_DESC": "Enables DNP Secure Authentication for this session ", "TR_DO_YOU_WANT_TO_SAVE_YOUR_CURRENT_WORKSPACE": "Do you want to save your current Workspace?", "TR_DOMAIN_DESTINATION": "Domain to Create Dataset on", "TR_DOMAIN_DESTINATION_DESC": "Choose the Domain to create Dataset on.", "TR_DOMAIN_DOES_NOT_EXIST_PLEASE_CREATE_DOMAIN_FIRST": "Domain does not exist. Please create domain first", "TR_DOMAIN_NAME": "Domain Name", "TR_DOMAIN_NAME_DESC": "Domain Name", "TR_DOMAINS": "Domains", "TR_DOMAINS_LIST": "Domains List", "TR_DOMAINS_LIST_DESC": "Show the domains list for the current model", "TR_DOWNLOAD_BUFFERED_LOG_ENTRIES": "Download Buffered Logs Entries", "TR_DOWNLOAD_CURRENT_CSV_FILE": "Download current CSV file", "TR_DOWNLOAD_CURRENT_INI_FILE": "Download current INI file", "TR_DOWNLOAD_DISPLAYED_LOG_ENTRIES": "Download Displayed Log Entries", "TR_DOWNLOAD_FILE": "Download File", "TR_DOWNLOAD_IMPORT_WORKSPACE": "Download/Import Workspaces", "TR_DOWNLOAD_RESULTS": "Download Results", "TR_DOWNLOAD_SELECTED_FILE": "Download Selected File", "TR_DOWNLOAD_SELECTED_LOG_FILE": "Download Selected Log File", "TR_DOWNLOAD_LICENSE_LOG_FILES": "Download License Log Files", "TR_DOWNLOAD_WORSPACE": "Download Workspace", "TR_DS_CHANGE": "Change Dataset of selected Control Block", "TR_DS_CHANGE_DESC": "Change Dataset of selected Control Block", "TR_DS_CONDITIONS_DETECTED": "Include DS Conditions Detected", "TR_DS_CONDITIONS_DETECTED_DESC": "Include DS Conditions Detected", "TR_DS_CREATE_NEW": "Create new Dataset and assign to selected Control Block", "TR_DS_CREATE_NEW_DESC": "Create new Dataset and assign to selected Control Block", "TR_DS_CREATE_NEW_DS": "Create new Dataset", "TR_DS_CREATE_NEW_DS_DESC": "Create new Dataset", "TR_DS_DELETE_DESC": "Delete Selected DS.", "TR_DS_LIST": "List of all Datasets in the Model", "TR_DS_LIST_DESC": "Select a Dataset from the list below.  Clicking on the name of a Dataset displays a preview of the its members.  Filters below can be used to further refine the list.", "TR_DS_MEMBER_LIST": "Dataset Members of selected item above", "TR_DS_MEMBER_LIST_DESC": "Contains all Dataset members of the selected item above. Filters below can be used to further refine the list", "TR_DS_NAME": "Dataset Name", "TR_DS_NAME_DESC": "Specifies the current dataset name.", "TR_DS_SELECTED_DELETE": "Delete Selected Dataset", "TR_DS_SELECTED_MANAGE": "Manage Selected Dataset", "TR_DS_SELECTED_MANAGE_DESC": "Manage Selected Dataset", "TR_DSA": "DSA", "TR_DSA_PRIVATE_KEY_FILE": "DSA Private Key File", "TR_DSA_PRIVATE_KEY_PASS_PHRASE": "DSA Private PassPhrase", "TR_DSA_PUBLIC_CERT_FILE": "DSA Public Certificate File", "TR_DSLCT_CONFIRM": "Cancel operation confirmed", "TR_DSLCT_CONFIRM_DESC": "A cancel operation has been confirmed by a remote device.", "TR_DSLCT_PENDING": "Cancel operation pending", "TR_DSLCT_PENDING_DESC": "A cancel operation has been transmitted to a remote device to abort a 2-pass control operation between the 1st and 2nd passes.", "TR_DSN_LIST": "DSN list", "TR_DSN_LIST_DESC": "DSN list", "TR_DUAL_END_POINT_IP_PORT": "Dual End Point TCP/IP Port", "TR_DUAL_REGISTER_KIND": "Dual Register Kind", "TR_DUAL_REGISTER_KIND_DESC": "Dual Register Kind", "TR_DUAL_REGISTER_TYPE": "Dual Register Type", "TR_DUAL_REGISTER_TYPE_DESC": "Dual Register Type", "TR_DUP_CHNG_MON": "Data Update Change", "TR_EDIT": "Edit ", "TR_EDIT_DATA_ATTRIBUTE": "Edit Data Attribute", "TR_EDIT_DOMAIN": "Edit Domain", "TR_EDIT_DOMAIN_DESC": "Edit Domain", "TR_EDIT_USER": "Edit User", "TR_EDIT_USER_SECURITY": "Edit User Security", "TR_EDIT_USER_SECURITY_DESC": "Edit User Security", "TR_ELEMENT_INDEX_M103": "Element Index (M103):", "TR_ELEMENT_INDEX_M103_DESC": "the element Index (M103)", "TR_EMAIL": "Email", "TR_ENABLE_EVENT_LOG_FILE": "Enable Sequence of Events Log", "TR_ENABLE_EVENT_LOG_FILE_DESC": "If true the Sequence of Events Log will be enabled. Note: enabling this log may degrade SDG performance.", "TR_ENABLE_IEC_FULL_STACK_ADDRESSING": "Enable IEC Full Stack Addressing (61850/ICCP protocols)", "TR_LIC_GRACE_PERIOD": "The number of minutes the license will stay valid after a loss of license", "TR_ENABLE_UNSOLICITED_EVENT_CLASS": "Enable Unsolicited Event Class", "TR_ENABLE_UNSOLICITED_EVENT_CLASS_INFO": "These options require the 'automatically enable unsolicited events upon remote or master device startup 0x0100' bit be set in the Auto Request Mode Mask", "TR_END_DATE": "End Date", "TR_ENGINE": "Engine", "TR_ENGINE_ABBREVIATION": "E:", "TR_ENGINE_INI_FILE_NAME": "Engine INI File Name", "TR_ENGINE_IS_RE_STARTING": "Engine is restarting", "TR_ENGINE_NOT_RESPONDING": "Gateway Engine is not running (check the SDG Engine logs or System logs for possible errors).", "TR_ENGINE_STARTED": "Gateway Engine is running.", "TR_ENGINE_STOPPED_LINUX": "Gateway Engine is not running (check the SDG Engine logs or issue 'systemctl status tmwsdg-engine' in a Linux shell for possible errors).", "TR_ENGINE_STOPPED_WINDOWS": "Gateway Engine is not running (check the SDG Engine logs or the Windows event log and services manager).", "TR_ENGINEERING_UNITS_RANGE": "Scaled/Output Range", "TR_ENTER_A_PARTIAL_OR_COMPLETE_NODE_NAME": "Enter a partial or complete node name", "TR_ENTER_FILTER": "Enter a filter", "TR_ENTER_PRODUCT_KEY": "Enter product key", "TR_ENTRY_ID": "Entry ID", "TR_ENTRY_ID_DESC": "Specifies if the entry ID is active.", "TR_EQUATION_ALLOC_MEMORY": "Could not allocate memory for '{{arg1}}'", "TR_EQUATION_BLANK": "Equation: the equation name cannot be blank", "TR_EQUATION_END_OF_COMMENT": "End of comment not found", "TR_EQUATION_FUNCTION_CONVERT_FAILED": "Could not convert '{{arg1}}' to '{{arg2}}'", "TR_EQUATION_FUNCTION_FIVE_ARGS": "The '{{arg1}}' function may only have 5 arguments", "TR_EQUATION_FUNCTION_FOUR_ARGS": "The '{{arg1}}' function may only have 4 arguments", "TR_EQUATION_FUNCTION_FOUT_ARGS": "The '{{arg1}}' function must have 4 arguments", "TR_EQUATION_FUNCTION_NO_ARGS": "The '{{arg1}}' function may only have one argument", "TR_EQUATION_FUNCTION_NO_MORE_THAN_32_ARGS": "The '{{arg1}}' function cannot have more than 32 arguments", "TR_EQUATION_FUNCTION_ONE_ARG": "The '{{arg1}}' function may only have 1 argument", "TR_EQUATION_FUNCTION_ONE_ARGS": "The '{{arg1}}' function must have one argument", "TR_EQUATION_FUNCTION_SIX_ARGS": "The '{{arg1}}' function may only have 6 arguments", "TR_EQUATION_FUNCTION_THREE_ARGS": "The '{{arg1}}' function may only have 3 arguments", "TR_EQUATION_FUNCTION_TO_MANY_ARGS": "Too many arguments to the '{{arg1}}' function", "TR_EQUATION_FUNCTION_TOO_MANY_ARGS": "Too many arguments to the '{{arg1}}' function", "TR_EQUATION_FUNCTION_TWO_ARG": "The '{{arg1}}' function must have at least two arguments", "TR_EQUATION_FUNCTION_TWO_ARGS": "The '{{arg1}}' function may only have 2 arguments", "TR_EQUATION_ILLEGAL_CHAR": "Illegal control character inside string.", "TR_EQUATION_ILLEGAL_CHAR_IGNORED": "Illegal character '{{arg1}}'; character is ignored", "TR_EQUATION_ILLEGAL_TAB": "Illegal TAB inside string.", "TR_EQUATION_MEMORY_ALLOCATION": "Could not allocate memory for '{{arg1}}'", "TR_EQUATION_MISSING_QUOTE": "Ending quote missing before end of line.", "TR_EQUATION_PRODUCES_BAD_TYPE": "Equation produces type that cannot be used", "TR_EQUATION_SYNTAX": "Could not modify equation {{arg1}} for {{arg2}}. Make sure equation syntax is correct.", "TR_EQUATION_TIME_SOURCE": "Equation Time Source", "TR_EQUATION_TIME_SOURCE_DESC": "Specifies the source of the time tag for data points which are generated as the result of an equation. The possible values are Update or Reported where Update means the time, relative to the SDG system clock, at which the equation was last calculated, Reported specifies the reported time of the most recent event that caused the equation's result to change. Reported time will be relative to the remote slave device's system clock except on initialization where the SDG's system clock is used until the first event with time is received. It is important to note that static data polling, or received events which do not specify a reported time, may cause a specific data point's value to change without its event time being modified. Based on system polling rates and other parameters this could result in discontinuous times being reported, especially in equations that have inputs from multiple slave devices. ", "TR_EQUATION_UNEXPCTED_TOKEN": "Unexpected token '{{arg1}}'", "TR_EQUATION_UNEXPECTED_EQUATION": "Unexpected end of equation", "TR_EQUATION_UNKONWN_IDENTIFIER": "Unknown identifier '{{arg1}}'", "TR_EQUATION_WRONG_TYPES": "Type mis-match between '{{arg1}}' and '{{arg2}}' ('{{arg3}}' expects a type {{arg4}} and {{arg5}} is of {{arg6}} type, try making the types the same by using a cast or a different MDO)", "TR_EQUATIONS_LOG_MASK": "Equations Log Mask", "TR_EQUATIONS_LOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to log results of equations. If 0, no equation results will be logged. ", "TR_EQUATIONS_OPC_AELOG_MASK": "Equations OPC Alarm and Event Mask", "TR_EQUATIONS_OPC_AELOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to log results of equations through the OPC alarm and Event Server. If 0, no equation results will be reported. ", "TR_ERR": "Error: {{arg1}}", "TR_ERROR": "Error", "TR_ERROR_": "Error: {{error}}", "TR_ERROR_61850_ADD_MULTIPLE_ITEMS_FAILED": "", "TR_ERROR_61850_DA_POINT_LIST_NOT_CHANGED": "Error: 61850 Items not added", "TR_ERROR_61850_FC_DATASET_NOT_CHANGED": "Error: 61850 functional constraint not changed", "TR_ERROR_61850_GOOSE_DATASET_MEMBER_UNAVAILABLE": "Error: 61850 GOOSE dataset member unavailable", "TR_ERROR_61850_GOOSE_DATASET_NOT_CHANGED": "Error: 61850 GOOSE dataset not changed", "TR_ERROR_61850_IED_LIST_UNAVAILABLE": "Error: 61850 IED list unavailable", "TR_ERROR_61850_MODEL_NOT_SAVED": "Error: 61850 model not Saved", "TR_ERROR_61850_REPORT_DATASET_MEMBER_UNAVAILABLE": "Error: 61850 report dataset member unavailable", "TR_ERROR_61850_REPORT_DATASET_NOT_CHANGED": "Error: 61850 report dataset not changed", "TR_ERROR_61850_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: 61850 server not disconnected/connected ", "TR_ERROR_61850_SERVER_NOT_RESTARTED": "Error: 61850 server not restarted", "TR_ERROR_61850_SERVER_READ": "Error: 61850 server read failed", "TR_ERROR_ACTIVATE_OPC_ITEM_FAILED": "Error: The activation of the OPC item Failed", "TR_ERROR_ARG1": "Error: {{arg1}}", "TR_ERROR_AUTO_CREATE_TAGS": "Error in Auto Create Tags", "TR_ERROR_CAN_T_LOGOFF_USER": "Error: Cannot log-off user", "TR_ERROR_CAN_T_READ_ABOUT_CONTENT": "Error: Cannot read About information", "TR_ERROR_CAN_T_READ_AUDIT_DATABASE": "Error: <PERSON><PERSON> read Audit Database", "TR_ERROR_CAN_T_READ_USERS_DATABASE": "Error: Cannot read users database.", "TR_ERROR_CAN_T_SAVE_LICENSE": "Error: License not saved", "TR_ERROR_CAN_T_SAVE_LICENSE_UNSPECIFIED_ERROR": "License error: Unspecified error", "TR_ERROR_CANNOT_ADD_ANOTHER_USER_THE_MAX_NUMBER_OF_SUPPORTED_USERS_HAS_BEEN_REACHED": "Error: Cannot Add Another User The Max Number Of Supported Users Has Been Reached", "TR_ERROR_COMMAND_FAILED": " Error: Command failed {{ERROR}}", "TR_ERROR_DATA_NOT_SAVED": "Error: Data not saved", "TR_ERROR_DROP_ON_FOLDER": "Error in Drop On Folder", "TR_ERROR_ENABLE_DISABLE_61850_REPORT_CONTROL_BLOCK": "Error:Cannot Enable or Disable 61850 Report Control Block", "TR_ERROR_ENABLING_AUTHENTICATION_IS_ONLY_AVAILABLE_IF_HTTPS_IS_ENABLED": "Error: Enabling authentication is only available if SSL/HTTPS is Enabled.", "TR_ERROR_FAILED_TO_CREATE_CERTIFICATE": "Error: Failed to create certificate, {{ERROR}}", "TR_ERROR_FAILED_TO_CREATE_CERTIFICATE_NOT_INIT": "Failed to create certificate. OPC UA Server is not initialized.", "TR_ERROR_FAILED_TO_START_AFTER_120_SEC": "Failed to start engine after 120 seconds", "TR_ERROR_FAILED_TO_STOP_AFTER_120_SEC": "Failed to stop engine after 120 seconds", "TR_ERROR_FILE_NOT_AVAILABLE": "Error: File not available", "TR_ERROR_FILE_NOT_DOWNLOADED": "Error: File not downloaded", "TR_ERROR_FILE_NOT_SAVED": "Error: File not saved.", "TR_ERROR_GOOSE_MONITOR_STREAMS_UNAVAILABLE": "Error: ", "TR_ERROR_ICCP_SERVER_READ": "Error: ICCP server read failed", "TR_ERROR_IN_CSV_FILE": "Error: CSV file can't be saved, it would over write the current file with possible errors", "TR_ERROR_IN_INI": "Error INI/CSV", "TR_ERROR_IN_INI_FILE": "Error: INI file can't be saved.", "TR_ERROR_IN_INI_FILE_PARSE": "Error parsing INI file: {{arg1}}", "TR_ERROR_IN_INI_FILE_MIGRATE": "Migration of INI file: {{arg1}} needs to be done", "TR_WARNING_IN_INI_FILE_PARSE_CLASSIC_OPC": "Parsing INI file found unsupported OPC classic parameter(s) in file: {{arg1}}, note these parameters will be removed when the workspace is saved", "TR_ERROR_IN_NEW_INI_FILE": "Error: INI file must have a .ini file extension", "TR_ERROR_INVALID_EQUATION": "Error: Invalid equation", "TR_ERROR_INVALID_FILENAME": "Error: Invalid file name", "TR_ERROR_MAPPING": "Error: Incorrect Mapping", "TR_ERROR_MDO_MAPPING_CONFLICT": "Failed to map MDO '{{arg1}}' to MDO '{{arg2}}' because it is already mapped to '{{arg3}}'.", "TR_ERROR_NO_OPC_ITEM_SELECTED": "Error: No OPC Item selected", "TR_ERROR_OBJECT_NOT_DELETED": "Error: Object not deleted.", "TR_ERROR_ODBC_TABLE_INFO_UNAVAILABLE": "Error: ODBC table information unavailable", "TR_ERROR_OPC_ADD_ITEM_FAILED": "Error: Cannot add OPC Item", "TR_ERROR_OPC_ADD_MULTIPLE_ITEMS_FAILED": "Error: Failed to add multiple OPC items", "TR_ERROR_OPC_LOAD_ITEM_FAILED": "Error: Cannot load OPC Item", "TR_ERROR_OPC_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: OPC Server can't be connected or disconnected", "TR_ERROR_OPCAE_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: OPC AE Server can't be connected or disconnected", "TR_ERROR_OPCUA_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: OPC UA Server can't be connected or disconnected", "TR_ERROR_OPCUA_SERVER_STATUS": "Error: OPC UA Server Status", "TR_ERROR_PASSWORD_IS_NOT_SPECIFIED": "Error: Password Is Not Specified", "TR_ERROR_PERFORM_WRITE_ACTION": "Error: preforming write action failed", "TR_ERROR_RE_STARTING_ENGINE": "Error restarting Engine", "TR_ERROR_RE_STARTING_MONITOR": "Error: Monitor encountered an error while re-starting", "TR_ERROR_READ_OPC_ITEM_FAILED": "Error: <PERSON><PERSON> read OPC Item", "TR_ERROR_REFRESH_OPC_PROPERTIES_FAILED": "Error: <PERSON>not refresh OPC Item properties", "TR_ERROR_RESET_61850_RETRY_CONNECT_COUNT": "Error: <PERSON><PERSON> reset retry count for 61850", "TR_ERROR_RESET_AVERAGE_MDO_UPDATE_RATE": "Error: <PERSON><PERSON> reset average update rate", "TR_ERROR_SCALE_INCORRECT": "Error: Scale Incorrect", "TR_ERROR_SCALE_OPTION_IS_INCORRECT": "Error: Scale Option Is Incorrect", "TR_ERROR_SUBSCRIBE_UNSUBSCRIBE_GOOSE_STREAM": "Error: Cannot unsubscribe or subscribe GOOSE Stream", "TR_ERROR_TASE2_CANNOT_ADD_DSTS": "Error: Cannot Add Dataset Transfer Sets", "TR_ERROR_TASE2_CANNOT_DELETE_DATA_ATTRIBUTE": "Error: <PERSON>not delete Data Attribute", "TR_ERROR_TASE2_CANNOT_DELETE_DOMAIN": "Error: <PERSON><PERSON> delete Domain", "TR_ERROR_TASE2_CONFIG_INFO_UNAVAILABLE": "Error: ICCP Domain information unavailable", "TR_ERROR_TASE2_DATA_TYPE": "Error: Cannot Enable/Disable SBO", "TR_ERROR_TASE2_EXPORTMODEL_FAILED": "Error: Unable to export model to CSV file", "TR_ERROR_TASE2_OPERATE_CONTROL": "Error: ICCP operate control failed", "TR_ERROR_TASE2_REPORT_DATASET_MEMBER_UNAVAILABLE": "Error: ICCP report dataset member unavailable", "TR_ERROR_TASE2_REPORT_DATASET_NOT_CHANGED": "Error: ICCP report dataset not changed", "TR_ERROR_TASE2_SERVER_NOT_DISCONNECTED_CONNECTED": "Error: ICCP server not disconnected/connected ", "TR_ERROR_TASE2_SERVER_NOT_RESTARTED": "Error: ICCP server not restarted", "TR_ERROR_TASE2_SERVER_NOT_SAVED": "Error: ICCP Server can't be saved", "TR_ERROR_THE_COMMUNICATION_WITH_THE_MONITOR_IS_LOST_PLEASE_REFRESH_YOUR_BROWSER": "The connection with the Gateway Monitor was lost.<br />Please refresh your browser when the monitor is running again (might take few minutes).", "TR_ERROR_THE_GATEWAY_IS_NOT_RUNNING": "Error: The Gateway is not running", "TR_ERROR_THE_REQUEST_IS_MISSING_ONE_OR_MORE_REQUIRED_FIELDS": "Error: The request is missing one or more required fields.", "TR_ERROR_USER_IS_NOT_LOGGED_IN": "User is not logged in", "TR_ERROR_USER_NOT_DELETED": "Error: User not deleted.", "TR_ERROR_USERNAME_ALREADY_PRESENT": "Error: <PERSON><PERSON><PERSON>", "TR_ERROR_USERNAME_IS_NOT_SPECIFIED": "Error: <PERSON><PERSON><PERSON> Is Not Specified", "TR_ERROR_USERNAME_NOT_PRESENT": "Error. User name doesn't exist.", "TR_ERROR_VERIFY_DATASET": "Error: Cannot verify Dataset", "TR_ERROR_WORK_SPACE_DIR_CREATE_FAILS": "Failed to create workspace directory {{arg1}}", "TR_ERROR_WORK_SPACE_DOESNT_EXISTS": "Error Work Space {{arg1}} doesn't exists", "TR_ERROR_WORK_SPACE_EXISTS": "Error: Work Space {{arg1}} already exists", "TR_ERROR_WORKSPACE_ALREADY_PRESENT": "Error: Workspace already present, please delete the workspace {{WORKSPACE}} first", "TR_ERROR_WORKSPACE_FILE_NOT_AVAILABLE": "Error: Workspace's file not available", "TR_ERROR_WORKSPACE_IS_CURRENTLY_RUNNING": "Error: Workspace is currently running", "TR_ERROR_WORKSPACE_NOT_DELETED": "Error: Workspace Not Deleted", "TR_ERROR_WORKSPACE_NOT_IMPORTED": "Error: Workspace Not Imported", "TR_ERRORS_LOG_MASK": "Errors <PERSON>g <PERSON>", "TR_ERRORS_LOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to log errors, such as changes in number of link layer checksum failures or in link session on-line/off-line status, in the event log file.  If 0, nothing will be logged. ", "TR_ERRORS_OPC_AELOG_MASK": "Errors OPC Alarm and Event", "TR_ERRORS_OPC_AELOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to report errors, such as changes in number of link layer checksum failures or in link session on-line/off-line status, through the OPC alarm and Event Server. If 0, no errors will be reported. ", "TR_EU_Type": "EU Type", "TR_EU_Type_DESC": "Define the EU Type", "TR_EVENT_CODE_DETECTED": "Include event Code Detected", "TR_EVENT_CODE_DETECTED_DESC": "Include event Code Detected", "TR_EVENT_LOG_FILE_NAME": "Sequence of Events Log File Name", "TR_EVENT_LOG_FILE_NAME_DESC": "Sequence of Events Log File Name and Path.  See manual for a description of the available %xxx property keywords.", "TR_EVENT_LOG_RECORD_FORMAT": "Sequence of Events Log Record Format", "TR_EVENT_LOG_RECORD_FORMAT_DESC": "Sequence of Events Log record format.  See manual for a description of the available %xxx property keywords.", "TR_EVENT_NAME": "Event Name", "TR_EVENT_NAME_DESC": "Define the Event Name", "TR_EVENT_SPACE": "Event Space", "TR_EVENT_SPACE_DESC": "Define the Event Space", "TR_EXCEPTION": "EXCEPTION AT STARTUP", "TR_EXCEPTION_STATUS": "Exception Status", "TR_EXECUTE_SQL": "Execute/Test SQL Query", "TR_EXECUTE_SQL_DESC": "Execute/Test SQL Query", "TR_EXPAND": "Expand", "TR_EXPAND_CHILDREN": "Expand Children", "TR_EXPIRES": "Expires", "TR_EXPORT": "Export", "TR_EXPORT_MAPPINGS": "Export Mappings", "TR_EXPORT_POINTS": "Export Points", "TR_EXPORT_RECURSIVELY": "Export Recursively (include children)", "TR_EXPRESSION": "Expression (drag or select items from lists above):", "TR_EXPRESSION_DESC": "the expression of the tag", "TR_EXTRA": "Extra", "TR_FAILED_CONTACT_UPDATE_SERVER": "Failed to contact the server to get latest version information.", "TR_FAILED_TO_ADD_MDO_OPC_SERVER": "Could not add {{arg1}} MDO on OPC server: {{arg2}}. (duplicate ?)", "TR_FAILED_TO_ADD_MDOS_OPC_SERVER": "Could not add MDOs on OPC server: {{arg1}}.", "TR_FAILED_TO_ADD_MDOS_OPC_UA_SERVER": "Could not add MDOs on OPC UA server: {{arg1}}.", "TR_FAILED_TO_APPLY_ALIAS_TO_61850_TAG": "Failed to apply alias '{{arg1}}' to 61850 tag (duplicate ?)", "TR_FAILED_TO_APPLY_ALIAS_TO_ICCP_TAG": "Failed to apply alias '{{arg1}}' to ICCP tag (duplicate ?)", "TR_FAILED_TO_APPLY_ALIAS_TO_OPC_TAG": "Failed to apply alias '{{arg1}}' to OPC tag (duplicate ?)", "TR_FAILED_TO_LOAD_POINTMAP": "Failed to load CSV file at: '{{arg1}}'", "TR_FC_CF": "CF - Configuration", "TR_FC_CF_DESC": "Enable the functional constraint CF - Configuration", "TR_FC_SP": "SP - Set Points", "TR_FC_SP_DESC": "Enable the functional constraint SP - Set Points", "TR_FC_SV": "SV - Substitution", "TR_FC_SV_DESC": "Enable the functional constraint SV - Substitution", "TR_FILE": "File", "TR_FILE_ALREADY_EXISTS_OVERWRITE_IT": "The selected file already exists in the destination folder, do you want to overwrite it?", "TR_FILE_DOWNLOADED": "File downloaded", "TR_FILE_SAVED": "File saved.", "TR_FILTER": "Filter", "TR_FOLDER_ALREADY_DEFINED": "Folder already defined, can not create", "TR_FOLDER_DUPLICATE": "Cannot add Folder: '{{arg1}}'. Duplicate name.", "TR_FOLDER_NAME": "Folder Name:", "TR_FOLDER_NAME_DESC": "Specify the folder name", "TR_FOLDER_ONLY_MDO_EQ": "The SDG currently only allows internal MDOs or equations to be added to user defined folders.", "TR_FORCE_DISCOVERY": "Force Build from Discovery at Startup (every time)", "TR_FORCE_DISCOVERY_DESC": "This forces a discovery of the model during startup. Warning: Depending on the server and the overall configuration this can slow down startup times", "TR_FORCE_LOGOFF_BY_USER": "You have been logged off by another user with equal or higher privileges.", "TR_FORCE_LOGOFF_TIME_OUT": "You have been logged off due to inactivity.", "TR_FORCE_SAVE_CURRENT_WORKSPACE": "Force Save Current Workspace", "TR_FUNCTION_M103": "Function (M103):", "TR_FUNCTION_M103_DESC": "the function (M103) of the tag", "TR_FUNCTIONAL_CONSTRAINT": "Pre-Filter: Show Functional Constraints", "TR_FUNCTIONAL_CONSTRAINT_DESC": "The list above can get very large.  This can help cut down the items returned.", "TR_GATEWAY_ALLOWED_IPS": "Allowed IPs", "TR_GATEWAY_ALLOWED_IPS_HELP": "(List of allowed IP addresses separated by comma)", "TR_GATEWAY_API_HOST_AND_HTTP_PORT": "Monitor API Host and HTTP Port", "TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_CONFIGURATOR_ROLE": "<i><u>CONFIGURATOR</i></u>: User Authentication Expiration Timeout (seconds)", "TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_OPERATOR_ROLE": "<i><u>OPERATOR</i></u>: User Authentication Expiration Timeout (seconds)", "TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_SU_ROLE": "<i><u>SUPER USER</i></u>: User Authentication Expiration Timeout (seconds)", "TR_GATEWAY_AUTHENTICATION_SECURITY_EXPIRATION_VIEWER_ROLE": "<i><u>VIEWER</i></u>: User Authentication Expiration Timeout (seconds)", "TR_GATEWAY_CONTROL_LOG_MASK": "Control Log Mask", "TR_GATEWAY_CONTROL_LOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to log control data, such as changes to pollEnabled or to GeneralInterrogationPeriod, in the event log file.  If 0, nothing will be logged. ", "TR_GATEWAY_CONTROL_OPC_AELOG_MASK": "Control OPC Alarm and Event Mask", "TR_GATEWAY_CONTROL_OPC_AELOG_MASK_DESC": "Each bit enables(1)/disables(0) a reason to log control data, such as changes to pollEnabled or to GeneralInterrogationPeriod, through the OPC alarm and Event Server. If 0, nothing will be reported. ", "TR_GATEWAY_ENABLE_AUDIT": "Enable <PERSON><PERSON>", "TR_GATEWAY_ENABLE_AUTHENTICATION": "Enable User Authentication", "TR_GATEWAY_ENABLE_ONLYLOCALIP": "Web interface can only be accessed from a local IP address when HTTPS is not enabled", "TR_GATEWAY_ENABLE_TRACE": "Gateway Enable Trace", "TR_GATEWAY_EXE_NAME": "Gateway Engine EXE Name", "TR_GATEWAY_FULL_LOG_ON_RESTART": "Generate Full Log on SDG re-start", "TR_GATEWAY_MIRROR_ALL_TO_LOG": "Add all log messages to the log file (errors and excepions are always logged)", "TR_GATEWAY_HOST_AND_HTTP_PORT": "Gateway Engine Host and HTTP(s) Port", "TR_GATEWAY_HTTP_PAGE_BLOCK_SIZE": "Number of Tags per page in the Dashboard Tags Grid (0 to disable)", "TR_GATEWAY_HTTPS_CERT_FILE": "HTTPS Web Server Certificate file name", "TR_GATEWAY_HTTPS_KEY_FILE": "HTTPS Web Server Private Key file name", "TR_GATEWAY_HTTPS_KEY_PASS_PHRASE": "HTTPS Web Server Private Key pass phrase", "TR_GATEWAY_INI_FILE_PATH": "Gateway Engine INI File Path", "TR_GATEWAY_IS_RUNNING": "Gateway Engine is running.", "TR_GATEWAY_IS_STARTING": "Gateway Engine is starting", "TR_GATEWAY_IS_STOPPED": "Gateway Engine is stopped.", "TR_GATEWAY_MANAGEMENT": "Gateway management", "TR_GATEWAY_MAX_LOG_FILES": "Maximum number of log files ", "TR_GATEWAY_TIME_ZONE_DB_PATH": "Gateway Time Zone Data Base Path", "TR_GATEWAY_WEB_DIRECTORY": "Gateway Web Directory", "TR_GATEWAY_WEBSITE_HOST_AND_HTTP_PORT": "Gateway Engine API Host and HTTP Port", "TR_GATEWAY_WEBSOCKET_UPDATE_BLOCK_SIZE": "Gateway WebSocket Update Block Size", "TR_GATEWAY_WEBSOCKET_UPDATE_RATE": "Gateway WebSocket Update Rate (seconds)", "TR_GCB_LIST": "List of all GOOSE Control Blocks in the Model", "TR_GCB_LIST_DESC": "Select a GOOSE Control Block from the list below.  Clicking on the name of a GOOSE Control Block displays a preview of the Dataset members.  Filters below can be used to further refine the list.", "TR_GCB_NAME": "GOOSE Control Block Name", "TR_GCB_NAME_DESC": "Displays the currently selected GOOSE Control Block from the list below.  Choosing OK will cause the currently selected GOOSE Control Block to be added.", "TR_GENERAL_INTERROGATION": "General interrogation", "TR_GENERAL_INTERROGATION_DESC": "Specifies if the general interrogation is active.", "TR_GENERAL_UA_SERVER_SETTINGS": "General OPC UA Server Settings", "TR_GENFILE_FROM_DISCOVERY": "Generate an SCL File from a Discovery (Once)", "TR_GLOBAL_CREATE_TAG_AUTOMATIC": "Global Create Tags Auto", "TR_GLOBAL_CREATE_TAG_AUTOMATIC_DESC": "if true, then tags (and storage space) will automatically be created upon reception of new data points within incoming response messages from remote devices.", "TR_GLOBAL_PARAMATER_APPLY_TO_THE_WORKSPACE": "Global Parameter apply to the Workspace", "TR_GO_TO_DASHBOARD": "Go To Dashboard", "TR_GO_TO_TMW_TO_DOWNLOAD_UPDATE": "Please go to <a href='https://www.trianglemicroworks.com/products/scada-data-gateway' target='_blank'>https://www.trianglemicroworks.com/products/scada-data-gateway</a> to download the latest version.", "TR_GOOD": "GOOD", "TR_GOOD_DESC": "GOOD status (no bits are set)", "TR_GOOSE_ADAPTER": "GOOSE Adapter", "TR_GOOSE_MONITOR_SCL_FILE_NOT_FOUND": "GOOSE Monitor SCL file not found at: {{arg1}}.", "TR_GOOSE_MONTIOR_ADAPTER": "Goose Monitor '{{arg1}}' : Could not find adapter ({{arg2}}, {{arg3}}). The Goose Monitor will not function correctly until this is fixed.", "TR_GOOSE_MONTIOR_LOAD_SCL": "Goose Monitor '{{arg1}}' : Could not load SCL file : {{arg2}}. Make sure file and path are correct.", "TR_GOOSE_MONTIOR_NO_MORE": "No more GooseMonitors available", "TR_GOOSE_NAME": "GOOSE Name", "TR_GOOSE_STREAM_MANAGEMENT_ADAPTOR": "Goose Stream Adapter management", "TR_GOOSEMONITOR_ADAPTER_DEVICE": "GOOSE adapter device system path", "TR_GOOSEMONITOR_ADAPTER_DEVICE_DESC": "Specifies a GOOSE adapter device system path", "TR_GOOSEMONITOR_NAME": "GOOSE Monitor device name", "TR_GOOSEMONITOR_NAME_DESC": "Specifies GOOSE Monitor device name", "TR_GOOSEMONITOR_SCLFILE": "GOOSE Monitor SCL file", "TR_GOOSEMONITOR_SCLFILE_DESC": "Specifies GOOSE Monitor SCL file", "TR_GOOSEMONITOR_STREAM": "Monitored GOOSE stream", "TR_GOOSEMONITOR_STREAM_DESC": "Specifies a monitored GOOSE stream for a GOOSE Monitor device", "TR_GOOSEMONITOR_STREAM_THRESHOLD": "Invalid threshold time (secs)", "TR_GOOSEMONITOR_STREAM_THRESHOLD_DESC": "Specifies a monitored GOOSE stream invalid threshold time in seconds", "TR_GRID_SEARCH": "The Search will only look for the Tag Name/Type in the current/selected node", "TR_GTWDEFS_UPDTRSN_CHNG_INDICATED_DESC": "The source of data indicates this update is a change.", "TR_GTWDEFS_UPDTRSN_CTRL_AT_DEVICE_DESC": "The data was changed as a result of a control operation executed locally at the device.", "TR_GTWDEFS_UPDTRSN_CTRL_BY_COMM_DESC": "The data was changed as a result of a control operation through communications.", "TR_GTWDEFS_UPDTRSN_CTRL_CONFIRM_DESC": "Used for control points: A control operation has been confirmed (e.g., the remote device sent confirmation of having received a control operation).  However, though confirmed, the control operation may not yet be finished. When it is finished, GTWDEFS_UPDTRSN_CTRL_BY_COMM will be used.", "TR_GTWDEFS_UPDTRSN_CTRL_ERROR_DESC": "Used for control points: A error occurred with the control operation.", "TR_GTWDEFS_UPDTRSN_CTRL_PENDING_DESC": "Used for control points: A control operation has been initiated (e.g., sent to a remote device).  This could occur for the second pass of a 2-pass operation, or it could occur for the only pass of a 1-pass operation.", "TR_GTWDEFS_UPDTRSN_DSLCT_CONFIRM_DESC": "Used for control points: A de-select operation (cancel of a 1st-pass select operation) has been confirmed (e.g., the remote device sent confirmation of having de-selected the control operation).", "TR_GTWDEFS_UPDTRSN_DSLCT_PENDING_DESC": "Used for control points: The de-select operation (cancel of a 1st-pass select operation) has been initiated (e.g., sent to a remote device).", "TR_GTWDEFS_UPDTRSN_NONE_DESC": "Can be used as a log /* mask to disable logging", "TR_GTWDEFS_UPDTRSN_REFRESH_DESC": "The data is being refreshed by the source  of data (without  request); no change event is necessarily indicated.", "TR_GTWDEFS_UPDTRSN_REQUESTED_DESC": "The data was requested; no change event is necessarily indicated.", "TR_GTWDEFS_UPDTRSN_SLCT_CONFIRM_DESC": "Used for control points: A first pass in a two-pass control operation has been confirmed (e.g., the remote device sent confirmation of having received the first-pass control operation).", "TR_GTWDEFS_UPDTRSN_SLCT_PENDING_DESC": "Used for control points: The first pass in a two-pass control operation has been initiated (e.g., sent to a remote device)", "TR_GTWDEFS_UPDTRSN_TEST_MODE_DESC": "Used by some protocols to indicate the point or device is operating in a  test mode.", "TR_GTWDEFS_UPDTRSN_UNKNOWN_DESC": "The data is being updated for an unknown", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_ALL": "ALL", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_DATA": "Data", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_HEALTH": "Health", "TR_GTWTYPES_TAG_PURPOSE_ABBREVIATION_PERFORMANCE": "Perf", "TR_GTWTYPES_TAG_PURPOSE_ALL": "All", "TR_GTWTYPES_TAG_PURPOSE_MASK_DATA": "Data filter", "TR_GTWTYPES_TAG_PURPOSE_MASK_HEALTH": "Health filter", "TR_GTWTYPES_TAG_PURPOSE_MASK_PERFORMANCE": "Performance filter", "TR_HEALTH_DESC": "Health", "TR_HEALTH_VIEW": "Health View", "TR_HELP": "Help", "TR_HI": "Hi", "TR_HIDE_HELP": "Hide Help", "TR_HIGH_ORDER_INDEX": "High Order Register Index", "TR_HIGH_ORDER_INDEX_DESC": "Specify the high order register index", "TR_HOLDING_REGISTERS": "Holding registers", "TR_HORIZONTAL_DISPLAY": "Horizontal Display", "TR_HOW_ADD_VIEWS_TO_DASHBOARD": "How to add views to Dashboard", "TR_HOW_SEARCH_TAGS": "How to search Tags", "TR_HOW_TO_ADD_A_DEVICE": "How to add a device", "TR_HOW_TO_MAP_POINTS": "How to add map points", "TR_HTTP_CREATE": "HTTP SERVER CAN'T BE CREATED", "TR_HTTP_START": "HTTP SERVER CAN'T START", "TR_HTTPS_BROWSER_DESCRIPTION": "This Web Browser is incompatible.<br> Please use the most recent version of Google Chrome, Mozilla Firefox or Microsoft Edge.", "TR_HTTPS_CREATE": "HTTPS SERVER CAN'T BE CREATED", "TR_HTTPS_START": "HTTPS SERVER CAN'T START", "TR_HTTPS_TMW_CERT_DESCRIPTION": "The HTTPS certificate used is from Triangle Microworks, it is strongly recommended that it be replaced with your own certificates.", "TR_HTTPS_TMW_CERT_TITLE": "HTTPS certificate is from Triangle Microworks", "TR_HTTPS_WARNING_AT_STARTUP": "Hide HTTPS warning at startup", "TR_HTTPS_WARNING_DESCRIPTION": "HTTPS is not enabled, so this connection to the Gateway server is not secure.<br>Please review this setting in the 'System Settings > Security Parameters'.", "TR_HTTPS_WARNING_TITLE": "HTTPS is not enabled", "TR_I14AUTH_ENABLE": "Enables Secure Authentication", "TR_I14AUTH_ENABLE_DESC": "Enables Secure Authentication for this sector ", "TR_I14AUTH_EXTRA_DIAGS": "Extra diagnostics", "TR_I14AUTH_EXTRA_DIAGS_DESC": "Output extra diagnostics to protocol analyzer.", "TR_I14AUTH_HMACALGORITHM": "HMAC algorithm", "TR_I14AUTH_HMACALGORITHM_DESC": "HMAC algorithm to be used in challenges. ", "TR_I14AUTH_KEY_CHANGE_INTERVAL": "Key interval", "TR_I14AUTH_KEY_CHANGE_INTERVAL_DESC": "For Master: Session key interval.  When time since last key change reaches this value, session keys will be updated. For systems that communicate infrequently, this may be set to zero, using only the maxKeyChangeCount to determine when to update keys.\n\nFor Slave: Expected session key interval and count. When this amount of time elapses or this quantity of Authentication messages are sent or received, the session keys for this user will be invalidated. Interval and count should be 2 times the master key change interval and count. For systems that communicate infrequently, I14AuthKeyChangeInterval may be set to zero, using only the I14AuthMaxKeyChangeCount to determine when keys should be considered old and should be invalidated. ", "TR_I14AUTH_MAX_KEY_CHANGE_COUNT": "Key Change Count", "TR_I14AUTH_MAX_KEY_CHANGE_COUNT_DESC": "Session Authentication ASDU count since last key change, When this number of authentication ASDUs is transmitted or received since the last key change, session keys will be updated.", "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH": "Length of random challenge data", "TR_I14AUTH_RANDOM_CHALLENGE_DATA_LENGTH_DESC": "Length of random challenge data to send in challenge request. ", "TR_I14AUTH_REPLY_TIMEOUT": "How long to wait for any authentication reply (ms)", "TR_I14AUTH_REPLY_TIMEOUT_DESC": "How long to wait for any authentication reply. ", "TR_I14AUTH_SECURITY_STATS_IOA": "Base point number for the security statistics", "TR_I14AUTH_SECURITY_STATS_IOA_DESC": "Base Information Object Address (IOA) for the security statistics. ", "TR_I14AUTH_USER_KEY": "User Key (must be 32 hex values)", "TR_I14AUTH_USER_KEY_DESC": "User Key (must be 32 hex values).  For each key there must be unique user number I14AuthUserNumber.", "TR_I14AUTH_USER_NAME": "Name of the user", "TR_I14AUTH_USER_NAME_DESC": "The name of the user.", "TR_I14AUTH_USER_NUMBER": "User Number", "TR_I14AUTH_USER_NUMBER_DESC": "User Number: Configuration for each user.  The specification says default user number is 1 providing a user number for the device or 'any' user, configure it as first user in this array. Add any other user numbers. For each user number in the ini file there must be a I14AuthUserKey.", "TR_I61400ALARMS_NAME": "Alarm Node Name", "TR_I61400ALARMS_NAME_DESC": "Specifies the name of the SDG Alarms node", "TR_I61400EVENT_ALARMS_ARRAY_NAME": "Event Alarms Array name", "TR_I61400EVENT_ALARMS_ARRAY_NAME_DESC": "Specifies the name of the IEC 61400-25 (WALM) Event Alarms Array", "TR_I61400STATUS_ALARMS_ARRAY_NAME": "Status Alarms Array name", "TR_I61400STATUS_ALARMS_ARRAY_NAME_DESC": "Specifies the name of the IEC 61400-25 (WALM) Status Alarms Array", "TR_I61850_AUTH_MECHANISM": "Mechanism", "TR_I61850AUTH_MECHANISM": "Authorization Mechanism", "TR_I61850AUTH_MECHANISM_DESC": "IEC 61850 Authorization mechanism", "TR_I61850AUTH_PASSWORD": "Password", "TR_I61850AUTH_PASSWORD_DESC": "IEC 61850 Authorization password (clear text)", "TR_I61850CLIENT_AEINVOKE_ID": "AE Invoke ID", "TR_I61850CLIENT_AEINVOKE_ID_DESC": "The local IEC 61850 Clients AE Invoke ID. Value from 0 to 65536.", "TR_I61850CLIENT_AEQUALIFIER": "AE Qualifier", "TR_I61850CLIENT_AEQUALIFIER_DESC": "The local IEC 61850 Clients AE Qualifier. Value from 0 to 65536.", "TR_I61850CLIENT_APINVOKE_ID": "AP Invoke ID", "TR_I61850CLIENT_APINVOKE_ID_DESC": "The local IEC 61850 Clients ACSE AP Invoke ID. Value from 0 to 65536.", "TR_I61850CLIENT_APP_ID": "AP Title", "TR_I61850CLIENT_APP_ID_DESC": "The local IEC 61850 Clients ACSE AP title value. Use OSI Object Identifier '1,3,9999,X' where 'X' is a user defined integer", "TR_I61850CLIENT_CERT_AUTH_CHAINING_VER_DEPTH": "Certificate Authority Chaining Verification Depth", "TR_I61850CLIENT_CERT_AUTH_CHAINING_VER_DEPTH_DESC": "Maximum length of 'chained' certificates that is allowed.  This must be greater than 0 to be valid", "TR_I61850CLIENT_CERTIFICATE_AUTHORITY_FILE": "Certificate Authority File", "TR_I61850CLIENT_CERTIFICATE_AUTHORITY_FILE_DESC": "If it is not NULL, it specifies a file containing the public master CA certificates.  The file (PEM format) may contain the certificates of multiple CAs", "TR_I61850CLIENT_CERTIFICATE_AUTHORITY_REVOKE_LIST_FILE": "Certificate Authority Revoke List File", "TR_I61850CLIENT_CERTIFICATE_AUTHORITY_REVOKE_LIST_FILE_DESC": "If it is not NULL, it specifies a file containing the public master CA revoked certificates.  The file (PEM format) may contain multiple revoked certificates.", "TR_I61850CLIENT_CONNECT_TIMEOUT": "MMS Connect Timeout (ms)", "TR_I61850CLIENT_CONNECT_TIMEOUT_DESC": "Specifies the MMS connect timeout for the IEC 61850 Client.  After starting a connection attempt this is how int to wait for success.  The length of this parameter will depend on your network topology.  This value is also used to specify the timeout for 61850 request messages", "TR_I61850CLIENT_IPADDRESS": "Client TCP/IP Address", "TR_I61850CLIENT_IPADDRESS_DESC": "Specifies the local TCP/IP address of the IEC 61850 Client. A TCP/IP address can be used to select a particular network adapter or the default of '0.0.0.0', which will cause the TCP layer to use the first locally configured IP interface on the system", "TR_I61850CLIENT_MMSCOMMON_NAME": "MMS Common Name", "TR_I61850CLIENT_MMSCOMMON_NAME_DESC": "Common Name to require from peer certificates (or NULL string if no CommonName check is to be done)", "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE": "Private Key File", "TR_I61850CLIENT_MMSPRIVATE_KEY_FILE_DESC": "Path name of the file containing the private RSA key (PEM format) to be used in generating the ACSE signatures as required by IEC 62351-4.", "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE": "Private Key PassPhrase", "TR_I61850CLIENT_MMSPRIVATE_KEY_PASS_PHRASE_DESC": "Pass phrase for decrypting the RSA private key.", "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE": "Public Certificate File", "TR_I61850CLIENT_MMSPUBLIC_CERTIFICATE_FILE_DESC": "File containing the RSA certificate (PEM format) corresponding to the private key.  The certificate must be signed by a Certificate Authority recognized by the peer systems", "TR_I61850CLIENT_NAME": "IEC 61850 Client Name", "TR_I61850CLIENT_NAME_DESC": "Name of the IEC 61850 Client in the Gateway.  Note: All changes take effect upon Client restart.", "TR_I61850CLIENT_PRESENTATION_ADDRESS": "Presentation Selector (OSI-PSEL)", "TR_I61850CLIENT_PRESENTATION_ADDRESS_DESC": "The local IEC 61850 Clients Presentation Selector. Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_I61850CLIENT_RECONNECT_RETRY_COUNT": "Reconnect Re-try Count", "TR_I61850CLIENT_RECONNECT_RETRY_COUNT_DESC": "Specifies the reconnect re-try count for the IEC 61850 Client (0 = attempt reconnects for ever). A successful connection will cause the internal limit counter to be re-set to 0 resulting in continued connection attempts to the IEC 61850 server.", "TR_I61850CLIENT_RECONNECT_TIME": "Reconnect Timeout (ms)", "TR_I61850CLIENT_RECONNECT_TIME_DESC": "Specifies the reconnect timeout for the IEC 61850 Client (0 = no reconnect), should be greater than 'MMS Connect Timeout'", "TR_I61850CLIENT_SESSION_ADDRESS": "Session Selector (OSI-SSEL)", "TR_I61850CLIENT_SESSION_ADDRESS_DESC": "The local IEC 61850 Clients Session Selector. Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_I61850CLIENT_TLSCOMMON_NAME": "TLS Common Name", "TR_I61850CLIENT_TLSCOMMON_NAME_DESC": "Common Name to require from peer certificates (or NULL string if no CommonName check is to be done)", "TR_I61850CLIENT_TLSMAX_PDUS": "Max PDUs Before Forcing Cipher Renegotiation", "TR_I61850CLIENT_TLSMAX_PDUS_DESC": "Maximum number of ISO PDUs (RFC1006) to allow between cipher renegotiations.  This must be a minimum of 5000 (see IEC 62351-4).", "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME": "Max Renegotiate Wait Time (ms)", "TR_I61850CLIENT_TLSMAX_RENEGOTIATION_WAIT_TIME_DESC": "Maximum interval (milliseconds) to allow between the server's request for cipher renegotiation and the client's renegotiation request.  If this timeout is exceeded, the connection is terminated.", "TR_I61850CLIENT_TLSRENEGOTIATION": "Renegotiation (sec)", "TR_I61850CLIENT_TLSRENEGOTIATION_DESC": "Time interval (in seconds) to allow between cipher renegotiations.  This must be a minimum of 10 minutes (see IEC 62351-4).", "TR_I61850CLIENT_TLSRSAPRIVATE_KEY_FILE": "RSA Private Key File", "TR_I61850CLIENT_TLSRSAPRIVATE_KEY_FILE_DESC": "File containing the private RSA key (PEM format).", "TR_I61850CLIENT_TLSRSAPRIVATE_KEY_PASS_PHRASE": "RSA Private Key Pass Phrase", "TR_I61850CLIENT_TLSRSAPRIVATE_KEY_PASS_PHRASE_DESC": "Pass phrase for decrypting the RSA private key.", "TR_I61850CLIENT_TLSRSAPUBLIC_CERT_FILE": "TLS RSA Public Certificate File", "TR_I61850CLIENT_TLSRSAPUBLIC_CERT_FILE_DESC": "File containing the RSA certificate (PEM format) corresponding to the private key.  The certificate must be signed by a Certificate Authority recognized by the peer systems.", "TR_I61850CLIENT_TRANSPORT_ADDRESS": "Transport Selector (OSI-TSEL)", "TR_I61850CLIENT_TRANSPORT_ADDRESS_DESC": "The local IEC 61850 Clients Transport Selector. Shall be limited to no more than 8 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_I61850CLIENT_USE_MMSONLY": "Use MMS Authentication", "TR_I61850CLIENT_USE_MMSONLY_DESC": "The MMS Authentication requires an RSA certificate file (which will be exchanged with the partner), a key file (which must be the private key associated with the certificate), and a pass-phrase (in case the key file is stored locally encrypted).  There is also a parameter specifying (text string) a certificate CommonName.  If the CommonName parameter is not NULL, then the authentication logic will check to make sure that the Subject CommonName in the peer certificate matches the CommonName.  Peer certificates will also be validated by OpenSSL, including the certificate signature using the Certificate Authority (see Common Run-Time Parameters, below). The 62351 protocol also involves the exchange of certificates (whose signatures are validated against the Certificate Authority public keys) and an additional signature (which is generated using the private key and authenticated by the peer using the public key from the certificate.  This is done automatically by the TW security implementation.", "TR_I61850CLIENT_USE_SISCO_COMPATABILITY": "Use ED1 Compatibility", "TR_I61850CLIENT_USE_SISCO_COMPATABILITY_DESC": "Specifies if the security settings use ED1 compatibility or SISCO Compatibility", "TR_I61850CLIENT_USE_TLSONLY": "Use TLS Security", "TR_I61850CLIENT_USE_TLSONLY_DESC": "The TLS protocol allows client (initiating) and server (responding) systems to be configured with a variety of possible cyphers and parameters at connect time.  The connection establishment procedure involves the selection of a cypher scheme from the subset in common between client and server.  TMW implementation by default includes all of the strong (non-export) TLS (SSLv3) ciphers supported by the OpenSSL library.  This requires a certificate file (which will be exchanged with the partner), a key file (which must be the private key associated with the certificate), and a passphrase (in case the key file is stored locally encrypted).", "TR_I61850GOOSE_ADAPTER_NAME": "GOOSE Adapter Name", "TR_I61850GOOSE_ADAPTER_NAME_DESC": "GOOSE adapter used to send/receive GOOSE and SampledValues.", "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED": "Load Model from an SCL File", "TR_I61850LOAD_MODEL_FROM_FILE_ENABLED_DESC": "Load model for IEC 61850 Client from SCL file instead of performing a discovery.", "TR_I61850POLLED_DATA_SET_NAME": "Polled Dataset Name", "TR_I61850POLLED_DATA_SET_NAME_DESC": "Displays the currently selected Dataset from the list below.  Choosing OK will cause the currently selected Dataset to be added.", "TR_I61850POLLED_DATA_SET_PERIOD": "Polled Dataset Period (ms)", "TR_I61850POLLED_DATA_SET_PERIOD_DESC": "Specifies the period to read the dataset. A value of zero means disable, i.e. no polling will occur.", "TR_I61850POLLED_POINT_SET_NAME": "Polled Point Set Name", "TR_I61850POLLED_POINT_SET_NAME_DESC": "Displays the currently selected polled point set from the list below.  Choosing OK will cause the currently selected polled point set to be added.", "TR_I61850POLLED_POINT_SET_PERIOD": "Polled Point Set Period (ms)", "TR_I61850POLLED_POINT_SET_PERIOD_DESC": "Specifies the period to read the polled point set. A value of zero means disable, i.e. no polling will occur.", "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT": "Purge RCB Before Enable On Reconnect", "TR_I61850RCBPURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Purge RCB Before Enable On Reconnect", "TR_I61850RCBPURGE_BEFORE1ST_ENABLE": "Purge RCB Before 1st Enable", "TR_I61850RCBPURGE_BEFORE1ST_ENABLE_DESC": "Purge RCB Before 1st Enable", "TR_I61850RCBRCBRETRY_ENABLE_COUNT": "Number of times the SDG should retry enabling", "TR_I61850RCBRCBRETRY_ENABLE_COUNT_DESC": "The number of times the SDG should retry enabling this report when it fails to enable on server up. -1=forever, 0=never", "TR_I61850RCBRETRY_ENABLE_PERIOD": "Retrying Enabling RCB Period (ms)", "TR_I61850RCBRETRY_ENABLE_PERIOD_DESC": "The period for retrying enabling RCBs in milliseconds.", "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW": "Buffer Overflow included", "TR_I61850REPORT_CONTROL_BLOCK_BUF_OVERFLOW_DESC": "Specifies the Buffer Overflow included property", "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME": "Buffer time (ms)", "TR_I61850REPORT_CONTROL_BLOCK_BUF_TIME_DESC": "Specifies the buffer time of a Report Control Block", "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV": "Configuration Revision included", "TR_I61850REPORT_CONTROL_BLOCK_CONFIG_REV_DESC": "Specifies the Configuration Revision included property", "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE": "Monitor Data Change", "TR_I61850REPORT_CONTROL_BLOCK_DATA_CHANGE_DESC": "Specifies the Report Control Block's Monitor Data Change property", "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF": "Data Reference included", "TR_I61850REPORT_CONTROL_BLOCK_DATA_REF_DESC": "Specifies the Data Reference included property", "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME": "Dataset Name included", "TR_I61850REPORT_CONTROL_BLOCK_DATA_SET_NAME_DESC": "Specifies the Dataset Name included property", "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME": "Dataset name", "TR_I61850REPORT_CONTROL_BLOCK_DATASET_NAME_DESC": "Specifies the name of a report control block Dataset on an IEC 61850 server", "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE": "Monitor Data Update Change", "TR_I61850REPORT_CONTROL_BLOCK_DUP_CHANGE_DESC": "Specifies the Report Control Block's Monitor Data Update Change property", "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID": "Entry ID included", "TR_I61850REPORT_CONTROL_BLOCK_ENTRY_ID_DESC": "Specifies the Entry ID included property", "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG": "General Interrogation supported", "TR_I61850REPORT_CONTROL_BLOCK_GEN_INTEG_DESC": "Specifies the Report Control Block's General Interrogation supported property", "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED": "Integrity period monitored", "TR_I61850REPORT_CONTROL_BLOCK_INTEG_PERIOD_MONITORED_DESC": "Specifies the Report Control Block's Integrity period monitored property", "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD": "Integrity period (ms)", "TR_I61850REPORT_CONTROL_BLOCK_INTEGRITY_PERIOD_DESC": "Specifies the period of integrity updates of a Report Control Block", "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE": "Monitor Quality Change", "TR_I61850REPORT_CONTROL_BLOCK_QUALITY_CHANGE_DESC": "Specifies the Report Control Block's Monitor Quality Change property", "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL": "Reason For Incl included", "TR_I61850REPORT_CONTROL_BLOCK_REASON_FOR_INCL_DESC": "Specifies the Reason For Incl included property", "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM": "Sequence Number included", "TR_I61850REPORT_CONTROL_BLOCK_SEQ_NUM_DESC": "Specifies the Sequence Number included property", "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP": "Time Stamp included", "TR_I61850REPORT_CONTROL_BLOCK_TIME_STAMP_DESC": "Specifies the Time Stamp included property", "TR_I61850SCLCLIENT_IEDNAME": "Client IED to Load", "TR_I61850SCLCLIENT_IEDNAME_DESC": "Optional Client IED to load from SCL file ", "TR_I61850SCLFILE_IEDNAME": "Server IED to Load", "TR_I61850SCLFILE_IEDNAME_DESC": "Server IED to load from SCL file ", "TR_I61850SCLFILE_NAME": "SCL File Name", "TR_I61850SCLFILE_NAME_DESC": "Optional SCL File Name to load model from.  If in same directory as the INI file the path is not required. ", "TR_I61850SERVER_AEINVOKE_ID": "AE Invoke ID", "TR_I61850SERVER_AEINVOKE_ID_DESC": "The remote IEC 61850 Server  AE Invoke ID. Value from 0 to 65536.", "TR_I61850SERVER_AEQUALIFIER": "AE Qualifier", "TR_I61850SERVER_AEQUALIFIER_DESC": "The remote IEC 61850 Server  AE Qualifier. Value from 0 to 65536.", "TR_I61850SERVER_APINVOKE_ID": "AP Qualifier", "TR_I61850SERVER_APINVOKE_ID_DESC": "The remote IEC 61850 Server  AP Qualifier. Value from 0 to 65536.", "TR_I61850SERVER_APP_ID": "AP Title", "TR_I61850SERVER_APP_ID_DESC": "The remote IEC 61850 Server ACSE AP Title value. Use OSI Object Identifier '1,3,9999,X' where 'X' is a user defined integer", "TR_I61850SERVER_GOOSE_ADAPTER_NAME": "GOOSE Adapter Name", "TR_I61850SERVER_GOOSE_ADAPTER_NAME_DESC": "GOOSE adapter used to send/receive GOOSE and SampledValues.", "TR_I61850SERVER_IPADDRESS": "Server TCP/IP Address", "TR_I61850SERVER_IPADDRESS_DESC": "Specifies the TCP/IP address of the IEC 61850 Server", "TR_I61850SERVER_IPPORT": "Server TCP/IP Port (MMS-Port in SCL File)", "TR_I61850SERVER_IPPORT_DESC": "Sets the TCP/IP port of the IEC 61850 Server", "TR_I61850SERVER_PRESENTATION_ADDRESS": "Presentation Selector (OSI-PSEL)", "TR_I61850SERVER_PRESENTATION_ADDRESS_DESC": "The remote IEC 61850 Server Presentation Selector. Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_I61850SERVER_SCLFILE_IEDNAME": "IED Name", "TR_I61850SERVER_SCLFILE_IEDNAME_DESC": "IED to load", "TR_I61850SERVER_SESSION_ADDRESS": "Session Selector (OSI-SSEL)", "TR_I61850SERVER_SESSION_ADDRESS_DESC": "The remote IEC 61850 Server Session Selector. Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_I61850SERVER_TRANSPORT_ADDRESS": "Transport Selector (OSI-TSEL)", "TR_I61850SERVER_TRANSPORT_ADDRESS_DESC": "The remote IEC 61850 Server Transport Selector. Shall be limited to no more than 8 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_I61850SERVER_USE_MMSONLY": "Use MMS Authentication", "TR_I61850SERVER_USE_MMSONLY_DESC": "The MMS Authentication requires an RSA certificate file (which will be exchanged with the partner), a key file (which must be the private key associated with the certificate), and a pass-phrase (in case the key file is stored locally encrypted).  There is also a parameter specifying (text string) a certificate CommonName.  If the CommonName parameter is not NULL, then the authentication logic will check to make sure that the Subject CommonName in the peer certificate matches the CommonName.  Peer certificates will also be validated by OpenSSL, including the certificate signature using the Certificate Authority (see Common Run-Time Parameters, below). The 62351 protocol also involves the exchange of certificates (whose signatures are validated against the Certificate Authority public keys) and an additional signature (which is generated using the private key and authenticated by the peer using the public key from the certificate.  This is done automatically by the TW security implementation.", "TR_I61850SERVER_USE_TLSONLY": "Use TLS Security", "TR_I61850SERVER_USE_TLSONLY_DESC": "The TLS protocol allows client (initiating) and server (responding) systems to be configured with a variety of possible cyphers and parameters at connect time.  The connection establishment procedure involves the selection of a cypher scheme from the subset in common between client and server.  TMW implementation by default includes all of the strong (non-export) TLS (SSLv3) ciphers supported by the OpenSSL library.  This requires a certificate file (which will be exchanged with the partner), a key file (which must be the private key associated with the certificate), and a passphrase (in case the key file is stored locally encrypted).", "TR_I61850TIME_ZONE_BIAS": "Time Zone Offset (minutes)", "TR_I61850TIME_ZONE_BIAS_DESC": "An offset in minutes to adjust for server that do not send time in UTC", "TR_ICCP_CONFIG": "ICCP Configuration", "TR_ID": "ID", "TR_IECACTION_MASK0": "IEC Action Mask", "TR_IECACTION_MASK0_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the IECActionPrd. \nIEC Action mask Definitions: \nSee section 4.2 'Predefined Tag Names for Monitoring and Control' in the Manual. This parameter is only used for master sessions using the IEC 60870-5 protocol profile.", "TR_IECACTION_NOW": "IEC Action Now Mask", "TR_IECACTION_NOW_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the IECActionPrd. \nIEC Action mask Definitions: \nSee section 4.2 'Predefined Tag Names for Monitoring and Control' in the Manual. This parameter is only used for master sessions using the IEC 60870-5 protocol profile.", "TR_IECACTION_PRD0": "IEC Action Period (ms)", "TR_IECACTION_PRD0_DESC": "Time between actions defined in the IECActionMask.The period is disabled if set to zero.This parameter is only used for master sessions using the IEC 60870-5 protocol profile.", "TR_IECACTION_RETRY_COUNT0": "IEC Action Retry Count", "TR_IECACTION_RETRY_COUNT0_DESC": "How many times to retry the action mask on failure.If the retry time is 0 this has no effect.This parameter is only used for master sessions using the IEC 60870-5 protocol profile.", "TR_IECACTION_RETRY_TIME0": "IEC Action Retry Time (ms)", "TR_IECACTION_RETRY_TIME0_DESC": "The time period between retries of this action mask.If the time is 0 this has no effect.This parameter is only used for master sessions using the IEC 60870-5 protocol profile.", "TR_IED_CLIENT": "Client IED", "TR_IED_SERVER": "Server IED", "TR_IGNORE_DST": "Ignore DST", "TR_IGNORE_DST_DESC": "If true and UseTimeZoneClock is true changes in Day Light Savings time are ignored for display of time", "TR_ILLEGAL_CHARACTER": "Illegal Character", "TR_IMPORT": "Import", "TR_IMPORT_EXPORT_POINTS": "Import/Export Points", "TR_IMPORT_MAPPINGS": "Import Mappings", "TR_IMPORT_POINTS": "Import Points", "TR_IMPORT_WORKSPACE": "Import Workspace", "TR_IN_TRANSIT": "IN_TRANSIT", "TR_IN_TRANSIT_DESC": "In Transit/chatter", "TR_INFORMATION": "Information", "TR_INFORMATION_OBJECT_ADDRESS": "Information Object Address", "TR_INFORMATION_OBJECT_ADDRESS_DESC": "the Information Object Address of the tag", "TR_INI_FILE_NOT_SAVED_BECAUSE_OF_ERRORS": "INI file will not be saved because of errors when reading the file in. Please fix the ini file problems and restart the SDG.", "TR_INI_FILE_PARAMETER_HELP": "INI File Parameter Help", "TR_INI_LOAD": "CAN'T LOAD INI FILE", "TR_INIT_BEFORE_CSV": "CAN'T INITIALIZE BEFORE POINTMAP/CSV", "TR_INPUT_REGISTERS": "Input Registers", "TR_INSTALL_V2C": "Install V2C", "TR_INTEG_PERIOD": "Integrity Period", "TR_INTEG_PRD_MON": "Integrity Period Monitored", "TR_INTEGRITY_PERIOD": "Integrity Period (ms)", "TR_INTEGRITY_PERIOD_DESC": "Specifies the current integrity period.", "TR_INTEGRITY_PERIOD_MONITORED": "Integrity Period Monitored", "TR_INTEGRITY_PERIOD_MONITORED_DESC": "Specifies if the integrity period monitored is active.", "TR_INTERNAL_MDO_ALREADY_DEFINED": "MDO already defined, cannot create", "TR_INTERNAL_MDO_DUPLICATE": "Cannot add internal MDO: '{{arg1}}'. Duplicate name.", "TR_INTERNAL_SAVE_BLANK_NAME": "Error: name cannot be blank", "TR_INTERNAL_SET_MDO_OPTIONS": "Could not set MDO options {{arg1}}", "TR_INTERNAL_SET_OPTIONS_MDO": "Could not set internal MDO options {{arg1}}", "TR_INTERROGATION_COMMAND_AND_TOTALS_COUNTERS": "Interrogation Command & Totals Counters", "TR_INTERVAL_DESC": "Specifies the current Interval.", "TR_INVALID": "INVALID", "TR_INVALID_CHARACTER_IN_NODE_NAME": "invalid character in node name {{arg1}}", "TR_INVALID_DESC": "Invalid", "TR_INVALID_MONITOR_LOCATION": "INVALID MONITOR LOCATION", "TR_INVALID_TIME": "INVALID_TIME", "TR_INVALID_TIME_DESC": "Elapsed Time/Invalid", "TR_INVOICE": "Invoice", "TR_IP_ADDRESS": "TCP/IP Address", "TR_IS_ACTIVE": "Active", "TR_IS_INCORRECT": " is incorrect", "TR_IS_LICENSED": "Licensed", "TR_IS_LOGGED_IN": "Is Logged In", "TR_IS_REDUNDANCY_GROUP": "Is Redundancy Group", "TR_IS_REDUNDANCY_GROUP_DESC": "Is this channel a redundancy group (104 only)", "TR_IS_REDUNDANT_CHANNEL": "Is Redundant Channel", "TR_IS_REDUNDANT_CHANNEL_DESC": "Is this channel Redundant", "TR_IS_REQUIRED": " is required.", "TR_IS_REQUIRED_NUMERICAL": " is required (numerical).", "TR_IS_REQUIRED_NUMERICAL_0_TO_36000": " is required (numerical between 0 and 36000).", "TR_IS_REQUIRED_NUMERICAL_10_TO_100": " is required (numerical between 10 and 100).", "TR_IS_REQUIRED_NUMERICAL_100": " is required (numerical minimum 100).", "TR_IS_REQUIRED_NUMERICAL_1": " is required (numerical minimum 1).", "TR_IS_REQUIRED_NUMERICAL_360_TO_36000": " is required (numerical between 360 and 36000).", "TR_IS_REQUIRED_NUMERICAL_60_TO_36000": " is required (numerical between 60 and 36000).", "TR_IS_XML_CLIENT": "Is XML Client", "TR_IS_XML_CLIENT_DESC": "If the OPC CLient is a XML client", "TR_ISRV61850SERVER_AEINVOKE_ID": "AE Invoke ID", "TR_ISRV61850SERVER_AEINVOKE_ID_DESC": "The local IEC 61850 Server AE Invoke ID. Value from 0 to 65536.", "TR_ISRV61850SERVER_AEQUALIFIER": "AE Qualifier", "TR_ISRV61850SERVER_AEQUALIFIER_DESC": "The local IEC 61850 Server AE Qualifier. Value from 0 to 65536.", "TR_ISRV61850SERVER_APINVOKE_ID": "AP Invoke ID", "TR_ISRV61850SERVER_APINVOKE_ID_DESC": "The local IEC 61850 Server AP Invoke ID. Value from 0 to 65536.", "TR_ISRV61850SERVER_APP_ID": "AP Title", "TR_ISRV61850SERVER_APP_ID_DESC": "The local IEC 61850 Server ACSE AP Title value. Use OSI Object Identifier '1,3,9999,X' where 'X' is a user defined integer", "TR_ISRV61850SERVER_AUTH_MECHANISM": "Authorization Mechanism", "TR_ISRV61850SERVER_AUTH_MECHANISM_DESC": "IEC 61850 Server Authorization mechanism", "TR_ISRV61850SERVER_AUTH_PASSWORD": "Password", "TR_ISRV61850SERVER_AUTH_PASSWORD_DESC": "IEC 61850 Server Authorization password", "TR_ISRV61850SERVER_CERT_AUTH_CHAINING_VER_DEPTH": "Certificate Authority Chaining Verification Depth", "TR_ISRV61850SERVER_CERT_AUTH_CHAINING_VER_DEPTH_DESC": "Maximum length of 'chained' certificates that is allowed.  This must be greater than 0 to be valid", "TR_ISRV61850SERVER_CERTIFICATE_AUTHORITY_FILE": "Certificate Authority File", "TR_ISRV61850SERVER_CERTIFICATE_AUTHORITY_FILE_DESC": "If it is not NULL, it specifies a file containing the public master CA certificates.  The file (PEM format) may contain the certificates of multiple CAs", "TR_ISRV61850SERVER_CERTIFICATE_AUTHORITY_REVOKE_LIST_FILE": "Certificate Authority Revoke List File", "TR_ISRV61850SERVER_CERTIFICATE_AUTHORITY_REVOKE_LIST_FILE_DESC": "If it is not NULL, it specifies a file containing the public master CA revoked certificates.  The file (PEM format) may contain multiple revoked certificates.", "TR_ISRV61850SERVER_ICDFILE": "SCL File Name", "TR_ISRV61850SERVER_ICDFILE_DESC": "SCL file name to load model from for the IEC 61850 Server.", "TR_ISRV61850SERVER_IPADDRESS": "TCP/IP Address", "TR_ISRV61850SERVER_IPADDRESS_DESC": "Specifies the local TCP/IP address of the IEC 61850 Server. A TCP/IP address can be used to select a particular network adapter or the default of '0.0.0.0', which will cause the TCP layer to use the first locally configured IP interface on the system", "TR_ISRV61850SERVER_IPPORT": "Listening TCP/IP Port (MMS-Port in SCL File)", "TR_ISRV61850SERVER_IPPORT_DESC": "Specifies the TCP/IP port to listen on for this IEC 61850 Server. The default for unsecured is 102, the secured default is 3782", "TR_ISRV61850SERVER_MMSCOMMON_NAME": "MMS Common Name", "TR_ISRV61850SERVER_MMSCOMMON_NAME_DESC": "Common Name to require from peer certificates (or NULL string if no CommonName check is to be done)", "TR_ISRV61850SERVER_MMSPRIVATE_KEY_FILE": "Private Key File", "TR_ISRV61850SERVER_MMSPRIVATE_KEY_FILE_DESC": "Path name of the file containing the private RSA key (PEM format) to be used in generating the ACSE signatures as required by IEC 62351-4.", "TR_ISRV61850SERVER_MMSPRIVATE_KEY_PASS_PHRASE": "Private Key PassPhrase", "TR_ISRV61850SERVER_MMSPRIVATE_KEY_PASS_PHRASE_DESC": "Pass phrase for decrypting the RSA private key.", "TR_ISRV61850SERVER_MMSPUBLIC_CERTIFICATE_FILE": "Public Certificate File", "TR_ISRV61850SERVER_MMSPUBLIC_CERTIFICATE_FILE_DESC": "File containing the RSA certificate (PEM format) corresponding to the private key.  The certificate must be signed by a Certificate Authority recognized by the peer systems", "TR_ISRV61850SERVER_NAME": "IEC 61850 Server Name", "TR_ISRV61850SERVER_NAME_DESC": "Name of the IEC 61850 Server in the Gateway.  Note: All changes take effect upon Server restart.", "TR_ISRV61850SERVER_PRESENTATION_ADDRESS": "Presentation Selector (OSI-PSEL)", "TR_ISRV61850SERVER_PRESENTATION_ADDRESS_DESC": "The local IEC 61850 Server Presentation Selector. Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_ISRV61850SERVER_SESSION_ADDRESS": "Session Selector (OSI-SSEL)", "TR_ISRV61850SERVER_SESSION_ADDRESS_DESC": "The local IEC 61850 Server Session Selector. Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_ISRV61850SERVER_TLSCOMMON_NAME": "TLS Common Name", "TR_ISRV61850SERVER_TLSCOMMON_NAME_DESC": "Common Name to require from peer certificates (or NULL string if no CommonName check is to be done)", "TR_ISRV61850SERVER_TLSMAX_PDUS": "Max PDUs Before Forcing Cipher Renegotiation", "TR_ISRV61850SERVER_TLSMAX_PDUS_DESC": "Maximum number of ISO PDUs (RFC1006) to allow between cipher renegotiations.  This must be a minimum of 5000 (see IEC 62351-4).", "TR_ISRV61850SERVER_TLSMAX_RENEGOTIATION_WAIT_TIME": "Max Renegotiate Wait Time (ms)", "TR_ISRV61850SERVER_TLSMAX_RENEGOTIATION_WAIT_TIME_DESC": "Maximum interval (milliseconds) to allow between the server's request for cipher renegotiation and the client's renegotiation request.  If this timeout is exceeded, the connection is terminated.", "TR_ISRV61850SERVER_TLSRENEGOTIATION": "Renegotiation (sec)", "TR_ISRV61850SERVER_TLSRENEGOTIATION_DESC": "Time interval (in seconds) to allow between cipher renegotiations.  This must be a minimum of 10 minutes (see IEC 62351-4).", "TR_ISRV61850SERVER_TLSRSAPRIVATE_KEY_FILE": "RSA Private Key File", "TR_ISRV61850SERVER_TLSRSAPRIVATE_KEY_FILE_DESC": "File containing the private RSA key (PEM format).", "TR_ISRV61850SERVER_TLSRSAPRIVATE_KEY_PASS_PHRASE": "RSA Private Key Pass Phrase", "TR_ISRV61850SERVER_TLSRSAPRIVATE_KEY_PASS_PHRASE_DESC": "Pass phrase for decrypting the RSA private key.", "TR_ISRV61850SERVER_TLSRSAPUBLIC_CERT_FILE": "TLS RSA Public Certificate File", "TR_ISRV61850SERVER_TLSRSAPUBLIC_CERT_FILE_DESC": "File containing the RSA certificate (PEM format) corresponding to the private key.  The certificate must be signed by a Certificate Authority recognized by the peer systems.", "TR_ISRV61850SERVER_TRANSPORT_ADDRESS": "Transport Selector (OSI-TSEL)", "TR_ISRV61850SERVER_TRANSPORT_ADDRESS_DESC": "The local IEC 61850 Server Transport Selector. Shall be limited to no more than 8 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_ITEM": "<PERSON><PERSON>", "TR_ITEM_ATTRIBUTES": "Item attributes", "TR_ITEM_DESCRIPTION": "Item description", "TR_ITEM_DESCRIPTION_DESC": "Define the Item description", "TR_ITEM_ID": "Item ID", "TR_ITEM_ID_DESC": "Define the Item ID", "TR_ITEM_NAME": "Item Name", "TR_ITEM_NAME_DESC": "Define the Item Name", "TR_ITEM_PARENT_BROWSER": "Item browser", "TR_ITEM_TYPE": "Item Type", "TR_ITEM_TYPE_DESC": "Item type", "TR_ITEM_VALUE_TYPE": "Item value type", "TR_ITEM_VALUE_TYPE_DESC": "Define the Item value type", "TR_ITEMS_PARENT_BROWSER_DESC": "Item browser", "TR_KEY_ID": "Key ID", "TR_KEY_REJECTED": "KEY REJECTED", "TR_LANGUAGE": "Language", "TR_LICENSE": "License", "TR_LICENSE_ACTIVATION_SUCCESS_RESTART": "License activated successfully - Gateway Monitor and Engine will restart automatically.", "TR_LICENSE_DEMO": "Session {{arg2}} is configured as protocol {{arg2}} which is not currently licensed. This session has been set inactive", "TR_LICENSE_DEMO_EXPIRES": "The SCADA Data Gateway demo license will expire on {{arg1}}", "TR_LICENSE_DESC": "Log trace messages for Licensing code", "TR_LICENSE_ERROR": "License Manager error: {{arg1}}.  Please contact Triangle Microworks customer support.", "TR_LICENSE_EXCEPTION": "License Manager error: {{arg1}} Please contact Triangle Microworks customer support.", "TR_LICENSE_FAILED_SEVERE": "License Manager unknown error occurred. Please contact Triangle Microworks customer support.", "TR_LICENSE_INFORMATION": "License information", "TR_LICENSE_LOGS": "License Logs", "TR_LICENSE_LOST": "License Grace period has ended.  The license for the application has been lost. All protocols are disabled (i.e. they will not send/receive any data).", "TR_LICENSE_MANAGER": "License Manager", "TR_LICENSE_OPTIONS": "License options", "TR_LICENSE_OUT_OF_MAINTENANCE": "Your license is out of maintenance for this version. <NAME_EMAIL> to renew the maintenance.", "TR_LICENSE_REACQUIRED": "The SDG license has been re-acquired. A restart of the engine is recommended (goto settings->system settings and click 'Re-Start Gateway Engine')", "TR_LICENSE_ACQUIRED": "The SDG license has been acquired. A restart of the engine is recommended (goto settings->system settings and click 'Re-Start Gateway Engine')", "TR_LICENSE_RUN_DEMO": "The SCADA Data Gateway is running with a demo license", "TR_LICENSE_TYPE": "License type", "TR_LINE": "Line #", "TR_LINK_CLASS_PENDING_CNT": "Class Pending Count", "TR_LINK_CLASS_PENDING_CNT_DESC": "For an unbalanced master communication link, the total number of consecutive class 1 and class 2 request frames that may be sent to one device when an application layer response message is pending from this device before moving on to the next device on a multi drop network.  This parameter has no effect if only one device is configured for a communication channel.  If this parameter is set to zero, the device is still polled as described for parameter M870CNFG_LINK_CLASS1_POLL_CNT.This parameter only applies to IEC 60870-5-101 and IEC 60870-5-103 Master sessions.", "TR_LINK_CLASS1PENDING_DLY": "Class 1 Pending Delay (ms)", "TR_LINK_CLASS1PENDING_DLY_DESC": "For an unbalanced master communication link, the minimum delay in milliseconds after sending request for class 1 data when an application layer response is pending for this session.  This parameter may be used to limit the bandwidth on a shared media like Ethernet or to prevent taxing the target device with unnecessary communication overhead.This parameter only applies to IEC 60870-5-101 and IEC 60870-5-103 Master sessions.", "TR_LINK_CLASS1POLL_CNT": "Class 1 Poll Count", "TR_LINK_CLASS1POLL_CNT_DESC": "For an unbalanced master communication link, the total number of consecutive class 1 request frames that may be sent to one device before moving on  to the next device on a multi drop network (class 2 is always limited to one request frame unless an application layer response is pending).  This parameter has no effect if only one device is configured for a communication channel.  In a multi drop network topology, this parameter is used to balance polling between all devices and prevent one device from capturing all of the polling messages. This parameter only applies to IEC 60870-5-101 and IEC 60870-5-103 Master sessions.", "TR_LINK_CLASS1POLL_DLY": "Class 1 Poll Delay (ms)", "TR_LINK_CLASS1POLL_DLY_DESC": "For an unbalanced master communication link, the minimum delay in milliseconds after sending request for class 1 data when an application layer response is not pending for this session.  This parameter may be used to limit the bandwidth on a shared media like Ethernet or to prevent taxing the target device with unnecessary communication overhead.This parameter only applies to IEC 60870-5-101 and IEC 60870-5-103 Master sessions.", "TR_LINK_CLASS2PENDING_DLY": "Class 2 Pending Delay (ms)", "TR_LINK_CLASS2PENDING_DLY_DESC": "For an unbalanced master communication link, the minimum delay in milliseconds after sending request for class 2 data when an application layer response is pending for this session.  This parameter may be used to limit the bandwidth on a shared media like Ethernet or to prevent taxing the target device with unnecessary communication overhead.This parameter only applies to IEC 60870-5-101 and IEC 60870-5-103 Master sessions.", "TR_LINK_CLASS2POLL_DLY": "Class 2 Poll Delay (ms)", "TR_LINK_CLASS2POLL_DLY_DESC": "For an unbalanced master communication link, the minimum delay in milliseconds after sending request for class 2 data when an application layer response is not pending for this session.  This parameter may be used to limit the bandwidth on a shared media like Ethernet or to prevent taxing the target device with unnecessary communication overhead.This parameter only applies to IEC 60870-5-101 and IEC 60870-5-103 Master sessions.", "TR_LINK_CNFM_TIMEOUT": "T1 - Link layer Confirm Timeout (ms)", "TR_LINK_CNFM_TIMEOUT_DESC": "Maximum time to wait for confirmation of frame. For an IEC 60870-5-104 session this is the T1 parameter.  This parameter does not apply for link layer connections (sessions) when the GATEWAY is acting as an unbalanced slave.", "TR_LINK_CONFIRM_MODE": "Link Confirm Mode", "TR_LINK_CONFIRM_MODE_DESC": "Request the remote device to send a data link layer confirm of the last frame sent.  Note that this setting is independent of whether the remote device will require this device to send a data link confirm to frames it receives. This parameter is only used for master or slave sessions using the DNP3 protocol.", "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES": "K - Maximum number of unacknowledged transmit frames", "TR_LINK_KTRANSMITTED_UN_ACK_FRAMES_DESC": "Maximum number of unacknowledged transmit frames. ", "TR_LINK_LAYER": "link Layer", "TR_LINK_MAX_RETRIES": "Maximum number of attempts to re-transmit data", "TR_LINK_MAX_RETRIES_DESC": "Maximum number of attempts to re-transmit data link-layer frames that were not confirmed.  This parameter does not apply for link layer connections (sessions) when the GATEWAY is acting as an unbalanced slave.", "TR_LINK_MODE": "Data link transmission mode", "TR_LINK_MODE_DESC": "Data link transmission mode. Required for each communications channel.", "TR_LINK_SEND_ACK_DELAY": "T2 - Maximum time to wait to send an Acknowledge frame (ms)", "TR_LINK_SEND_ACK_DELAY_DESC": "Maximum time to wait to send an Acknowledge frame.  For an IEC 60870-5-104 session this is parameter T2 of the IEC 60870-5-104 Clause 5.1. (The maximum amount of time after receiving the last I-FORMAT APDU before transmitting an S-FORMAT APDU.) This parameter does not apply for link layer connections (sessions) when the GATEWAY is acting as an unbalanced slave.", "TR_LINK_SIZE_ADDRESS": "Link address size (bytes)", "TR_LINK_SIZE_ADDRESS_0_NOT_ALLOW_IN_UNBALANCED_LINK": "Link size address 0 is not allow in an unbalance link", "TR_LINK_SIZE_ADDRESS_DESC": "Number of octets (bytes) in link Address field. Note that a value of 0 is only valid for sessions whose link mode is balanced. This parameter is only used for IEC60870-5-101 master and slave sessions", "TR_LINK_TEST_FRAME_INTERVAL": "T3 - Test Frame interval (ms)", "TR_LINK_TEST_FRAME_INTERVAL_DESC": "Time for the Test Frame interval. For an IEC 60870-5-104 session this is the T3 parameter. It is recommended that T3 is greater than T1. This parameter does not apply for link layer connections (sessions) when the GATEWAY is acting as an unbalanced slave.", "TR_LINK_WRECIEVED_UN_ACK_FRAMES": "W - Maximum number of unacknowledged receive frames", "TR_LINK_WRECIEVED_UN_ACK_FRAMES_DESC": "Maximum number of unacknowledged receive frames. ", "TR_LN_DESTINATION": "Logical Node to Create Dataset on", "TR_LN_DESTINATION_DESC": "Choose the Logical Node to Create Dataset on.", "TR_LOAD_CONFIG_FROM_SERVER": "Load Config From SCL", "TR_LOAD_CONFIG_FROM_SERVER_DESC": "Load Configuration From SCL.", "TR_LOAD_MODEL": "Load Model", "TR_LOAD_MODEL_DESC": "Load Model from selected file.", "TR_LOAD_POINTMAP": "CAN'T LOAD POINTMAP/CSV", "TR_LOCAL_SERVER_SETTINGS": "Local Settings", "TR_LOCAL_UDP_PORT": "Local UDP Port", "TR_LOCK_SCROLL": "Auto Scroll", "TR_LOG": "Log", "TR_LOG_IN_FAILED_OTHER_USERS_ARE_ALREADY_LOGGED_IN": "Log in Failed: Other users are already logged in with similar roles. To successfully login, you need to log off the other users. Do you want to proceed?", "TR_LOG_MASKS_EVENT_LOG": "Log Masks Event Log", "TR_LOG_PARAMETERS": "Log Parameters", "TR_LOGIN": "<PERSON><PERSON>", "TR_LOGIN_BUTTON": "<PERSON><PERSON>", "TR_LOGOFF": "Log-off", "TR_LOGOFF_REMOTE_USER": "Log-off User", "TR_LOGS": "Logs", "TR_LOW_ORDER_INDEX": "Low Order Register Index", "TR_LOW_ORDER_INDEX_DESC": "Specify the low order register index", "TR_M103APPL_BLOCKING_ACTION_MASK": "Blocking Action Mask", "TR_M103APPL_BLOCKING_ACTION_MASK_DESC": "Each bit enables(1) or disables(0) an automatic request to be sent as a result of a slave device leaving blocking mode. This parameter is only used for   IEC60870-5-103 master sessions.", "TR_M103APPL_EOI_ACTION_MASK": "EOI Action Mask", "TR_M103APPL_EOI_ACTION_MASK_DESC": "Each bit enables(1) or disables(0) an automatic request to be sent as a result of receiving an initialization message from a slave device. This parameter is only used for   IEC60870-5-103 master sessions.", "TR_M103APPL_ONLINE_ACTION_MASK": "On-line Action Mask", "TR_M103APPL_ONLINE_ACTION_MASK_DESC": "Each bit enables(1) or disables(0) an automatic request to be sent as a result of a slave device coming on-line. This parameter is only used for   IEC60870-5-103 master sessions.", "TR_M14APPL_EOI_ACTION_MASK": "EOI Action Mask", "TR_M14APPL_EOI_ACTION_MASK_DESC": "Each bit enables(1) or disables(0) an automatic request to be sent as a result of receiving an initialization message from a slave device. This parameter is only used for   IEC60870-5 101,104 master sessions.", "TR_M14APPL_ONLINE_ACTION_MASK": "On-line Action Mask", "TR_M14APPL_ONLINE_ACTION_MASK_DESC": "Each bit enables(1) or disables(0) an automatic request to be sent as a result of a slave device coming on-line. This parameter is only used for   IEC60870-5 101,104 master sessions.", "TR_MAIN_PARAMETERS": "Main Parameters", "TR_MAINTENANCE_PLAN_EXPIRES": "Maintenance plan expires", "TR_MANAGE_WORKSPACE_FILES": "Workspace Files", "TR_MANDATORY_FIELDS_ARE_EMPTY_OR_INCORRECT": "Mandatory fields are empty or incorrect.", "TR_MAP_FAILED": "There were problems loading the point map (.csv) file.  Please correct the problem(s) manually by opening the CSV file with a text editor and fixing the problems. The error information is found in the protocol analyzer display.", "TR_MAP_TO_EXISTING_POINT": "Map to an existing point", "TR_MAP_TO_NEW_DATA_POINT": "Map to a new point", "TR_MAPPING": "Mapping", "TR_MAPPING_DETAILS": "Mapping Details", "TR_MASTER_DATA_OBJECT": "Master Data Object", "TR_MAX": "Max", "TR_MAX_NUM_THREADS": "Max Number of Threads", "TR_MAX_NUM_UNACK_FRAMES": "Maximum number of unacknowledged frames", "TR_MAX_SOEQSIZE": "SOE Queue Max Count", "TR_MAX_SOEQSIZE_DESC": "The maximum number of items allowed in the SOE Queue (and File). If this limit is exceeded oldest items are removed. Any specified value less than 1000 will default to 1000", "TR_MBACTION_PRD0": "Modbus Session Action Period (ms)", "TR_MBACTION_PRD0_DESC": "Time between actions defined in the MBActionMask. The period is disabled if set to zero.This parameter is only used for master sessions using the Modbus protocol.", "TR_MBCHANNEL_ACTION_MASK0": "Modbus Channel Action Mask", "TR_MBCHANNEL_ACTION_MASK0_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the MBChannelActionPrd. \nMB Action mask Definitions: \nThis parameter is only used for master sessions using the Modbus protocol.", "TR_MBCHANNEL_ACTION_NOW": "Modbus Channel Action Now Mask", "TR_MBCHANNEL_ACTION_NOW_DESC": "Use this mask to force one time event(s) or periodic events in conjunction with the MBChannelActionPrd. \nMB Action mask Definitions: \nSee section 4.2 'Predefined Tag Names for Monitoring and Control' in the Manual. This parameter is only used for master sessions using the Modbus protocol.", "TR_MBCHANNEL_ACTION_PRD0": "Modbus Channel Action period (ms)", "TR_MBCHANNEL_ACTION_PRD0_DESC": "Time between actions defined in the MBChannelActionMask. The period is disabled if set to zero.This parameter is only used for master sessions using the Modbus protocol.", "TR_MDO": "MDO:", "TR_MDO_ALREADY_EXITS": "MDO already defined, cannot create", "TR_MDO_CAN_NOT_CREATE": "MDO not Found, cannot create", "TR_MDO_CREATE_ALREADY": "MDO already defined, cannot create {{arg1}} MDO.", "TR_MDO_CREATED": "MDO Created", "TR_MDO_DESC": "MDO", "TR_MDO_DESTINATION": "MDO Destination", "TR_MDO_DESTINATION_DESC": "MDO Destination", "TR_MDO_DUPLICATED": "MDO Duplicated", "TR_MDO_EDITOR_ADD_MUTILPE_SUMMARY": "Add Multiple MDO Summary", "TR_MDO_EDITOR_CANNOT_ADD_MORE_THAN_1000": "Cannot add more than 1000 tags at once", "TR_MDO_EDITOR_CREATE_INVALID_TAG": "Could not create MDO:{{arg1}}. May be an invalid tag name or an invalid type.", "TR_MDO_EDITOR_CREATE_MDO_FOUND": "MDO not Found, cannot create", "TR_MDO_EDITOR_CREATE_MDO_USER_TAG": "Could not set MDO user tag name {{arg1}} (possibly a duplicate, see logs)", "TR_MDO_EDITOR_CREATE_SDO": "Could not create SDO.", "TR_MDO_EDITOR_CREATE_SUCCESSFULLY": "MDO {{arg1}} created successfully.", "TR_MDO_EDITOR_CREATE_TAG": "Failed to create tag '{{arg1}}' (possibly a duplicate)", "TR_MDO_EDITOR_CREATE_TAG_BAD": "Could not create {{arg1}} MDO. May be an invalid tag name or an invalid type.", "TR_MDO_EDITOR_CREATE_TAG_NAME": "Failed to set user tag name to '{{arg1}}' (possibly a duplicate)", "TR_MDO_EDITOR_MDO_SET_OPTIONS": "Could not set MDO options {{arg1}}", "TR_MDO_EDITOR_NOT_CREATE": "Could not create MDO(s):  '{{arg1}}'", "TR_MDO_EDITOR_OPTIONS": "Could not set SDO options {{arg1}}", "TR_MDO_EDITOR_OPTIONS_SDO": "Could not set SDO options {{arg1}}", "TR_MDO_EDITOR_SDO_BIND": "Could not bind SDO.", "TR_MDO_EDITOR_SDO_DEFINED": "SDO already defined, cannot create", "TR_MDO_EDITOR_SET_OPTIONS": "Could not set MDO options '{{arg1}}'", "TR_MDO_EDITOR_START_GREATER_THAN_END": "Start range is greater than end range", "TR_MDO_FAILED": "MDO Failed", "TR_MDO_LIST": "MDO:", "TR_MDO_LIST_DESC": "List of MDO", "TR_MDO_NO_TYPE": "Please select a valid type.", "TR_MDO_OPTIONS": "Could not set MDO options {{arg1}}", "TR_MDO_SOURCE": "MDO Source", "TR_MDO_SOURCE_DESC": "MDO Source", "TR_MEMORY_ABBREVIATION": "Mem.", "TR_MENU": "<PERSON><PERSON>", "TR_MENU_CMD_ADD_61400_ALARM_MDO": "Add 61400 Alarm MDO", "TR_MENU_CMD_ADD_61400_ALARMS_NODE": "Add IEC 61400 Alarm Node", "TR_MENU_CMD_ADD_61850_CLIENT": "Add IEC 61850 Client", "TR_MENU_CMD_ADD_61850_COMMAND_POINT": "Add IEC 61850 Control Point", "TR_MENU_CMD_ADD_61850_COMMAND_POINT_SET": "Add IEC 61850 Control Point Set", "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING": "Add 61850 Control to OPC Mapping", "TR_MENU_CMD_ADD_61850_CONTROL_TO_OPC_MAPPING_ITEM": "Add 61850 Control to OPC Mapping Item", "TR_MENU_CMD_ADD_61850_GOOSE": "Add IEC 61850 Goose Control Block", "TR_MENU_CMD_ADD_61850_ITEM": "Add 61850 Item", "TR_MENU_CMD_ADD_61850_POLLED_DATA_SET": "Add IEC 61850 Polled Dataset", "TR_MENU_CMD_ADD_61850_POLLED_POINT_SET": "Add IEC 61850 Polled Point Set", "TR_MENU_CMD_ADD_61850_REPORT": "Add IEC 61850 Report Control Block", "TR_MENU_CMD_ADD_61850_SERVER": "Add IEC 61850 Server", "TR_MENU_CMD_ADD_61850_WRITABLE_POINT": "Add IEC 61850 Writable Point", "TR_MENU_CMD_ADD_61850_WRITABLE_POINT_SET": "Add IEC 61850 Writable Point Set", "TR_MENU_CMD_ADD_DATA_TYPE": "Add Data Type", "TR_MENU_CMD_ADD_DATASET_ELEMENT": "Add Dataset Element", "TR_MENU_CMD_ADD_DNP_DESCP": "Add DNP Descriptor", "TR_MENU_CMD_ADD_DNP_PROTO": "Add DNP Prototype", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL": "Add DNP3 UDP/TCP Channel", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_MASTER": "Add DNP3 UDP+TCP Controlling Station", "TR_MENU_CMD_ADD_DNP3_UDP_TCP_CHANNEL_OUTSTATION_SLAVE": "Add DNP3 UDP+TCP Outstation", "TR_MENU_CMD_ADD_DNP3_XML_DEVICE": "Add DNP3 XML Device", "TR_MENU_CMD_ADD_EQ_MDO": "Add Equation MDO", "TR_MENU_CMD_ADD_GOOSE_MONITOR": "Add Goose Monitor", "TR_MENU_CMD_ADD_INTERNAL_MDO": "Add Internal MDO", "TR_MENU_CMD_ADD_MAPPING_MDO": "Add MDO Mapping", "TR_MENU_CMD_ADD_MAPPING_MDOS": "Add MDOs Mapping", "TR_MENU_CMD_ADD_MAPPING_SDO": "Add SDO Mapping", "TR_MENU_CMD_ADD_MAPPING_SDOS": "Add SDOs Mapping", "TR_MENU_CMD_ADD_MBP_CHANNEL": "Add ModbusPlus Channel", "TR_MENU_CMD_ADD_MDO": "Add MDO", "TR_MENU_CMD_ADD_MODEM": "Add Modem", "TR_MENU_CMD_ADD_MODEM_POOL": "Add modem pool", "TR_MENU_CMD_ADD_MODEM_POOL_CHANNEL": "Add Modem Pool Channel", "TR_MENU_CMD_ADD_MULTI_POINT": "Add Dual Register", "TR_MENU_CMD_ADD_MULTIPLE_MDO": "Add Multiple MDO", "TR_MENU_CMD_ADD_MULTIPLE_OPC_ITEM": "Add Multiple OPC Item", "TR_MENU_CMD_ADD_MULTIPLE_OPC_UA_ITEM": "Add Multiple OPC UA Items", "TR_MENU_CMD_ADD_ODBC_CLIENT": "Add ODBC Client", "TR_MENU_CMD_ADD_ODBC_ITEM": "Add ODBC Item", "TR_MENU_CMD_ADD_OPC_AE_ATTR": "Add OPC AE Attribute", "TR_MENU_CMD_ADD_OPC_AE_CLIENT": "Add OPC AE Client", "TR_MENU_CMD_ADD_OPC_AE_ITEM": "Add OPC AE Item", "TR_MENU_CMD_ADD_OPC_CLIENT": "Add OPC Client", "TR_MENU_CMD_ADD_OPC_ITEM": "Add OPC Item", "TR_MENU_CMD_ADD_OPC_UA_CERTIFICATE": "Create Self-Signed Certificate", "TR_MENU_CMD_ADD_OPC_UA_CLIENT": "Add OPC UA Client", "TR_MENU_CMD_ADD_OPC_UA_ITEM": "Add OPC UA Item", "TR_MENU_CMD_ADD_REDUNDANT_MASTER_CHANNEL": "Add Redundant Channel", "TR_MENU_CMD_ADD_REDUNDANT_SLAVE_CHANNEL": "Add Redundant Channel", "TR_MENU_CMD_ADD_SECTOR": "Add Sector", "TR_MENU_CMD_ADD_SERIAL_CHANNEL": "Add Serial Channel", "TR_MENU_CMD_ADD_SERIAL_CHANNEL_MASTER": "Add Serial Controlling Station/Client", "TR_MENU_CMD_ADD_SERIAL_CHANNEL_OUTSTATION_SLAVE": "Add Serial Outstation/Controlled Station/Server", "TR_MENU_CMD_ADD_SESSION": "Add Session", "TR_MENU_CMD_ADD_TASE2_CLIENT": "Add ICCP Client", "TR_MENU_CMD_ADD_TASE2_CLIENT_SERVER": "Add ICCP Bidirectional", "TR_MENU_CMD_ADD_TASE2_COMMAND_POINT": "Add ICCP Command Point", "TR_MENU_CMD_ADD_TASE2_COMMAND_POINT_SET": "Add ICCP Command Point Set", "TR_MENU_CMD_ADD_TASE2_DATA_ATTRIBUTE": "Add Data Attribute", "TR_MENU_CMD_ADD_TASE2_DATASET": "Add ICCP Dataset", "TR_MENU_CMD_ADD_TASE2_DOMAIN": "Add ICCP Domain", "TR_MENU_CMD_ADD_TASE2_DSTS": "Add/Manage ICCP Dataset Transfer Sets", "TR_MENU_CMD_ADD_TASE2_ITEM": "Add ICCP Item", "TR_MENU_CMD_ADD_TASE2_LOGICAL_DEVICE": "Add ICCP Domain", "TR_MENU_CMD_ADD_TASE2_POLLED_DATA_SET": "Add/Manage ICCP Polled Dataset", "TR_MENU_CMD_ADD_TASE2_POLLED_POINT_SET": "Add ICCP Polled Point Set", "TR_MENU_CMD_ADD_TASE2_SERVER": "Add ICCP Server", "TR_MENU_CMD_ADD_TCP_CHANNEL": "Add TCP Channel", "TR_MENU_CMD_ADD_TCP_CHANNEL_MASTER": "Add TCP Controlling Station/Client", "TR_MENU_CMD_ADD_TCP_CHANNEL_OUTSTATION_SLAVE": "Add TCP Outstation/Controlled Station/Server", "TR_MENU_CMD_ADD_USER_DEFINED_FOLDER": "Add User Defined Folder", "TR_MENU_CMD_ADD_WRITE_ACTION": "Add Write Action", "TR_MENU_CMD_AUTO_CREATE_TAGS": "Auto Create Tags", "TR_MENU_CMD_CHANGE_VALUE": "Change Value", "TR_MENU_CMD_CONNECT_OPC_AE_SERVER": "Connect OPC AE Server", "TR_MENU_CMD_CONNECT_OPC_SERVER": "Connect OPC Server", "TR_MENU_CMD_CONNECT_OPC_UA_SERVER": "Connect OPC UA Server", "TR_MENU_CMD_CONNECT_TO_61850_SERVER": "Connect to Server", "TR_MENU_CMD_CONNECT_TO_TASE2_SERVER": "Connect ICCP server", "TR_MENU_CMD_CONNECT_TO_DISCOVER_TASE2_SERVER": "Connect AND Discover ICCP Server", "TR_MENU_CMD_CREATE_DTM_CSV_POINT_FILE": "Create DTM CSV point file", "TR_MENU_CMD_CREATE_THXML_POINT_FILE": "Create Test Harness XML point file", "TR_MENU_CMD_DELETE": "Delete", "TR_MENU_CMD_DELETE_REDUNDANT_CHANNEL": "Delete Redundant Channel", "TR_MENU_CMD_DISABLE_DSTS": "Disable Dataset Transfer Set", "TR_MENU_CMD_DISABLE_RCB": "Disable Report Control Block", "TR_MENU_CMD_DISCONNECT_FROM_61850_SERVER": "Disconnect from 61850 Server", "TR_MENU_CMD_DISCONNECT_FROM_TASE2_SERVER": "Disconnect from Server", "TR_MENU_CMD_DISCONNECT_OPC_AE_SERVER": "Disconnect OPC AE Server", "TR_MENU_CMD_DISCONNECT_OPC_SERVER": "Disconnect OPC Server", "TR_MENU_CMD_DISCONNECT_OPC_UA_SERVER": "Disconnect OPC UA Server", "TR_MENU_CMD_DISCONNECT_TASE2_SERVER": "Stop ICCP Server", "TR_MENU_CMD_DROP_ON_FOLDER": "Drop On Folder", "TR_MENU_CMD_EDIT": "Edit", "TR_MENU_CMD_EDIT_OPC_UA_SERVER": "Edit OPC UA Server", "TR_MENU_CMD_EDIT_TASE2_EDIT_MODEL": "Edit ICCP Client Model", "TR_MENU_CMD_EDIT_WORKSPACE_PARAMETERS": "Edit Workspace Parameters", "TR_MENU_CMD_ENABLE_DSTS": "Enable Dataset Transfer Set", "TR_MENU_CMD_ENABLE_RCB": "Enable Report Control Block", "TR_MENU_CMD_MANAGE_TASE2_DATASET": "Manage ICCP Dataset", "TR_MENU_CMD_MANAGE_TASE2_DATASET_FULL_EDIT": "Manage ICCP Dataset", "TR_MENU_CMD_NONE": "N/A", "TR_MENU_CMD_OPC_UA_GET_SERVER_STATUS": "Show OPC UA Server Status", "TR_MENU_CMD_OPC_UA_SERVER_STATUS": "OPC UA - Server Status", "TR_MENU_CMD_OPERATE_TASE2_CONTROL": "Operate Control", "TR_MENU_CMD_PERFORM_WRITE_ACTION": "Perform Write Action", "TR_MENU_CMD_READ_61850_MDO": "Read", "TR_MENU_CMD_READ_DATASET": "Read Dataset", "TR_MENU_CMD_READ_ICCP_MDO": "Read", "TR_MENU_CMD_READ_OPC_DA_MDO": "Read", "TR_MENU_CMD_READ_OPC_UA_MDO": "Read MDO", "TR_MENU_CMD_RESET_61850_RETRY_CONNECT_COUNT": "Reset Connect Retry Count", "TR_MENU_CMD_RESET_AVERAGE_MDO_UPDATE_RATE": "Reset", "TR_MENU_CMD_RESTART_61850_SERVER": "Restart 61850 server", "TR_MENU_CMD_RESTART_TASE2_SERVER": "Start/Restart ICCP Server", "TR_MENU_CMD_SAVE_MODEL_TO_FILE": "Save Model to ICD file", "TR_MENU_CMD_SAVE_TASE2_SERVER": "Export ICCP Server Model", "TR_MENU_CMD_SELECT_DATA_ATTRIBUTE": "Select Data Attribute", "TR_MENU_CMD_SET_VALUE_AND_QUALITY": "Test Set Value and Quality", "TR_MENU_CMD_SHOW_CONFIG_TASE2_CLIENT": "ICCP Show Config", "TR_MENU_CMD_SHOW_CONFIG_TASE2_SERVER": "ICCP Show Config", "TR_MENU_CMD_SUBSCRIBE_GOOSE_STREAM": "Subscribe GOOSE Stream", "TR_MENU_CMD_SWITCH_TO_RCHANNEL": "Switch To This Channel", "TR_MENU_CMD_UNSUBSCRIBE_GOOSE_STREAM": "Unsubscribe GOOSE Stream", "TR_MENU_CMD_VERIFY_DATASET": "Verify Dataset", "TR_MESSAGE": "Message", "TR_MESSAGE_DETAIL": "Detail", "TR_MIN": "Min", "TR_MINIMUM_MAXIMUM_RANGE": "Min. and Max. Range:", "TR_MMS": "MMS", "TR_MMS_COMMON_NAME": "Common Name", "TR_MMS_PRIVATE_KEY_FILE": "Private Key File", "TR_MMS_PRIVATE_KEY_PASS": "Private Key PassPhrase", "TR_MMS_PUB_CERT_FILE": "Public Certificate File", "TR_MODBUS_ACTION_COILS": "Read Coils", "TR_MODBUS_ACTION_COILS_DESC": "Read Coils", "TR_MODBUS_ACTION_DISCRETE_INPUTS": "Discrete Inputs", "TR_MODBUS_ACTION_DISCRETE_INPUTS_DESC": "Discrete Inputs", "TR_MODBUS_ACTION_EXCEPTION_STATUS": "Exception_Status", "TR_MODBUS_ACTION_EXCEPTION_STATUS_DESC": "Exception_Status", "TR_MODBUS_ACTION_HOLDING_REGISTERS": "Holding Registers", "TR_MODBUS_ACTION_HOLDING_REGISTERS_DESC": "Holding Registers", "TR_MODBUS_ACTION_INPUT_REGISTERS": "Input Registers", "TR_MODBUS_ACTION_INPUT_REGISTERS_DESC": "Input Registers", "TR_MODBUS_ACTION_PRD": "Period (ms)", "TR_MODBUS_WRITE_ACTION_PRD": "Period (ms) (NOTE: period > 0 means periodic)", "TR_MODBUS_ACTION_PRD_DESC": "Period (ms)", "TR_MODBUS_HOLDING_HIGH_INDEX": "Error: the specified high order index does not exist. The Holding Register MDO must exist", "TR_MODBUS_HOLDING_LOW_INDEX": "Error: the specified low order index does not exist. The Holding Register MDO must exist.", "TR_MODBUS_INPUT_HIGH_INDEX": "Error: the specified high order index does not exist. The Input Register MDO must exist", "TR_MODBUS_INPUT_LOW_INDEX": "Error: the specified low order index does not exist. The Input Register MDO must exist.", "TR_MODBUS_MAP_EQUATION": "MDO: '{{arg1}}' is mapped to slave points or is used in an equation, it cannot be deleted. You can remove the dependency and try again.", "TR_MODBUS_REG_EXISTS": "Error: A Dual Register with that name already exists. Please provide a unique name.", "TR_MODBUS_SAME_INDEX": "Error: The high and low order indices cannot be the same", "TR_MODEL": "Model", "TR_MODEL_DISCOVERY": "Force Build from Discovery", "TR_MODEL_FILE": "Build From File", "TR_MODEL_FILE_NOT_FOUND": "Model file not found: '{{arg1}}'", "TR_MODEL_GEN": "Generate SCL File from Discovery", "TR_MODEM_BAUD": "Baud <PERSON>", "TR_MODEM_BAUD_DESC": "Sets the baud rate for corresponding Modem", "TR_MODEM_COM_PORT": "Modem com port", "TR_MODEM_COM_PORT_DESC": "Defines a modem com port", "TR_MODEM_DATA_BITS": "Data bits", "TR_MODEM_DATA_BITS_DESC": "Sets the number of data bits for corresponding Modem", "TR_MODEM_DIALING_MODE": "Dialing mode", "TR_MODEM_DIALING_MODE_DESC": "Sets the dialing mode of the Modem: 'pulse' - use pulse dialing. 'tone' - use tone dialing. ", "TR_MODEM_ENABLE": "Enable Modem", "TR_MODEM_ENABLE_DESC": "If true, then the modem will be Enabled and  Available for use. ", "TR_MODEM_HANGUP_STRING": "Hangup string", "TR_MODEM_HANGUP_STRING_DESC": "Sets the hangup string for the Modem", "TR_MODEM_INIT_STRING": "Initialization string", "TR_MODEM_INIT_STRING_DESC": "Sets the initialization string for the Modem", "TR_MODEM_NAME": "<PERSON>m alias name", "TR_MODEM_NAME_DESC": "Defines a modem alias name", "TR_MODEM_NO_DIAL_OUT": "Receiving telephone calls only", "TR_MODEM_NO_DIAL_OUT_DESC": "If true, then the modem will be configured for receiving telephone calls only. ", "TR_MODEM_PARITY": "Parity", "TR_MODEM_PARITY_DESC": "Sets the parity for corresponding Modem ModemChannel", "TR_MODEM_POOL_NAME": "Modem pool name", "TR_MODEM_POOL_NAME_DESC": "Defines a modem pool which contains modems for a channel", "TR_MODEM_READ_CMD_TIMEOUT": "Command read timeout (secs)", "TR_MODEM_READ_CMD_TIMEOUT_DESC": "Specifies the timeout in seconds that the SDG waits for a modem to respond to a command.  ", "TR_MODEM_RESP_TERMINATOR_CHAR": "Response termination character", "TR_MODEM_RESP_TERMINATOR_CHAR_DESC": "Sets the character used to terminate a response from the Modem: 'none' - don't use a character to terminate the response (assumes no command responses). 'line feed' - use a 'line feed' character to terminate the response. 'carriage return' - use a 'carriage return' character to terminate the response. ", "TR_MODEM_SERIAL_MODE": "Serial Mode", "TR_MODEM_SERIAL_MODE_DESC": "Sets the serial mode of the Modem: 'hardware' - use hardware flow control ('none' and 'windows' do not work for modems). ", "TR_MODEM_SETUP": "Modem Setup", "TR_MODEM_STOP_BITS": "Stop bits", "TR_MODEM_STOP_BITS_DESC": "Sets the number of stop bits for corresponding Modem", "TR_MODEM_WRITE_CMD_TIMEOUT": "Command write timeout (secs)", "TR_MODEM_WRITE_CMD_TIMEOUT_DESC": "Specifies the timeout in seconds that the SDG waits to send a command to a modem. ", "TR_MODIFY_DATASET_REMOVE_MAPPING": "One or more of the data attributes that are being removed are mapped. The corresponding mappings will be deleted.", "TR_MODIFY_DATASET_REMOVE_MDOS": "One or more of the data attributes that are being removed are associated with MDOs. The corresponding MDOs will be removed", "TR_MONITOR": "Monitor", "TR_MONITOR_ABBREVIATION": "M:", "TR_MONITOR_ENABLE_TRACE": "Monitor Enable Trace", "TR_MONITOR_HOST_AND_HTTP_PORT": "Monitor Host and HTTP(s) Port", "TR_MONITOR_IS_RE_STARTING": "Monitor is re-starting.", "TR_MONITOR_LOG": "Monitor Log", "TR_MONITOR_MANAGEMENT": "Monitor management", "TR_MONITOR_VIEW_1": "Monitor View #1", "TR_MONITOR_VIEW_2": "Monitor View #2", "TR_MONITOR_VIEW_3": "Monitor View #3", "TR_NAME": "Name", "TR_NAME_DESC": "Specifies the current name.", "TR_NATIVE_DATA_TYPE": "Native data type", "TR_NATIVE_DATA_TYPE_DESC": "Define the Native data type", "TR_NEW_GATEWAY": "New Gateway Configuration", "TR_NEW_PASSWORD": "New Password", "TR_NEW_VALUE": "New Value", "TR_NEW_VERSION_AVAILABLE": "Your version of SCADA Data Gateway is {{currentVersion}}. An update is available: {{newVersion}}.", "TR_NO": "No", "TR_NO_EDITOR": "No Editor available to change value", "TR_NO_ENDPOINS_EXIST": "No Endpoints Exist", "TR_NO_EXPIRATION": "No Expiration", "TR_NO_FILE_TO_DOWNLOAD": "No file to download", "TR_NO_GOOSE": "No more IEC 61850 GOOSEs available", "TR_NO_INI_FILE": "NO INI FILE FOUND", "TR_NO_LICENSE": "NO LICENSE FOUND", "TR_NO_LICENSE_INFORMATION_SAVED": "No license information saved", "TR_NO_LICENSE_UPDATE": "This computer is not licensed for SCADA Data Gateway.", "TR_NO_POLLED": "No more IEC 61850 PolledDataSets available", "TR_NO_RESULTS": "No Results", "TR_NO_SECTORS": "No more sectors available", "TR_NO_SESSIONS": "No more sessions available", "TR_NODE_ADDED": "Device added to the log's filters", "TR_NODE_LIST": "Node list", "TR_NODE_LIST_DESC": "Choose nodes to add to the new Dataset.", "TR_NODE_LIST_SDO_DESC": "Choose nodes to add to the new SDO.", "TR_NODE_NAME": "Node name", "TR_NODE_NAME_DESC": "Define the Node name", "TR_NODE_REMOVED": "<PERSON>ce removed", "TR_NODE_SDO_LIST": "Node list", "TR_NODES_REMOVED": "Devices removed", "TR_NONE": "NONE", "TR_NONE_DESC": "Remove selection", "TR_NOT_RUNNING": "Engine not running", "TR_NOT_TOPICAL": "NOT_TOPICAL", "TR_NOT_TOPICAL_DESC": "Not Topical (off-line/not dated)", "TR_NUM_THREADS": "Number of Threads", "TR_OBJECT_NODENAME_DELETED": "Object {{NodeName}} deleted", "TR_OBJECT_TAGNAME_ALREADY_EXISTS": "Object {{tagName}} already exists.", "TR_OBJECT_TAGNAME_CAN_T_BE_DELETED": "Object {{tagName}} can't be deleted.", "TR_OBJECT_TAGNAME_CAN_T_BE_EDITED": "Object {{tagName}} can't be edited.", "TR_OBJECT_TAGNAME_CAN_T_BE_OPERATED_ON": "Object {{tagName}} can't be operated on.", "TR_OBJECT_TAGNAME_DELETED": "Object {{tagName}} deleted.", "TR_ODBC_CLIENT_CONNECT_FAIL": "Could not connect to ODBC server : \n  ODBCaliasName[{{arg1}}]='{{arg2}}'\n  ODBCConnectionString[{{arg3}}]='{{arg4}}'", "TR_ODBC_CLIENT_CONNECT_RAW_FAIL": "Could not connect to using connection string= {{arg1}}.  Error message {{arg2}}", "TR_ODBC_CLIENT_DATABASE_CONNECTIVITY_PROBLEMS_DETECTED": "Warning: database connectivity problems detected for ODBC client '{{arg1}}'. This will affect all related MDOs and mappings. See errors in log. It is recommended to fix the database connectivity problems and restart the SDG.", "TR_ODBC_CLIENT_DUPLICATE": "Cannot add ODBC client : '{{arg1}}'. Duplicate name.", "TR_ODBC_DELETE_EQUATION": "MDO: '{{}}' is mapped to other points or is used in an equation, cannot delete", "TR_ODBC_EXECUTE_EXCEPTION_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, ExecuteSql::Exception caught: {{arg3}}", "TR_ODBC_EXECUTE_SEVERE_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, Severe ExecuteSql::Exception caught", "TR_ODBC_FAILED_INSERT_NOT_SUPPORTED": "Insert not supported for test", "TR_ODBC_FAILED_TO_FIND_QUERY": "Failed to find <PERSON><PERSON>", "TR_ODBC_LIST_DSNS_EXCEPTION_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, ListDataSourceNames::Exception caught: {{arg3}}", "TR_ODBC_LIST_DSNS_SEVERE_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, Severe ListDataSourceNames::Exception caught", "TR_ODBC_LIST_TABLES_EXCEPTION_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, ListTables::Exception caught: {{arg3}}", "TR_ODBC_LIST_TABLES_SEVERE_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, Severe ListTables::Exception caught", "TR_ODBC_CREATE_QUERY_EXCEPTION_FAILED": "ODBC: DB:{{arg1}}, Exception caught: {{arg2}}", "TR_ODBC_MAP": "MDO: '{{arg1}}' is mapped to other points or is used in an equation, cannot delete", "TR_ODBC_MDO_DELETE": "ODBC MDOs cannot be deleted", "TR_ODBC_NO_MORE": "No more ODBC clients available", "TR_ODBC_NO_QUERIES": "No more queries available", "TR_ODBC_OPENDB_FAILED": "Database is not open", "TR_ODBC_OPENDB_FAILED_PASSWORD": "Database is not open: {{arg1}}", "TR_ODBC_OPENDB_FAILED1": "Database {{arg1}} is not open: {{arg2}}", "TR_ODBC_QUERY_ADD_DUPLICATE_ERROR": "The SQL query could not be added. (duplicate?)", "TR_ODBC_QUERY_CHANGE": "The SQL query has changed; as a result query tags (MDOs) may be re-created and any mappings may be lost", "TR_ODBC_RECORD_SET_IS_EMPTY": "ODBC: DB:{{arg1}}, Q:{{arg2}}, Record set is empty: {{arg3}}", "TR_ODBC_SHOW_TABLES_EXCEPTION_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, ShowTable::Exception caught: {{arg3}}", "TR_ODBC_SHOW_TABLES_SEVERE_FAILED": "ODBC: DB:{{arg1}}, Q:{{arg2}}, Severe ShowTable::Exception caught", "TR_ODBCALIAS_NAME": "Name", "TR_ODBCALIAS_NAME_DESC": "Alias Name for this ODBC connection ", "TR_ODBCCONNECTION_STRING": "ODBC connection string", "TR_ODBCCONNECTION_STRING_DESC": "Specifies the connection string for this ODBC connection", "TR_ODBCQUERY": "SQL query", "TR_ODBCQUERY_ALIAS_NAME": "Query Name", "TR_ODBCQUERY_ALIAS_NAME_DESC": "The alias name of the query on this ODBC connection.  ", "TR_ODBCQUERY_ALWAYS_REFRESH_RS": "Always refresh record set", "TR_ODBCQUERY_ALWAYS_REFRESH_RS_DESC": "The query will always read the data from the database when GetNextRecord/MoveToRecord MDO is changed on this ODBC connection when ODBCQueryAlwaysRefreshRS is true. If false the data is read when ExecuteQuery MDO is changed but not when GetNextRecord/MoveToRecord MDO is changed. Note that if the table changes in the database an ExecuteQuery must be issued to refresh the local SDG cache of the table/record set", "TR_ODBCQUERY_DESC": "The SQL query to execute on this ODBC connection.  ", "TR_OFFLINE_ACTIVATION": "Off-line activation", "TR_OFFLINE_ACTIVATION_NEW_LICENSE": "Off-line activation", "TR_OFFLINE_ACTIVATION_STEP1_TEXT": "Step 1:  If this computer does NOT have Internet access, then click this button to create a C2V file that can be used to activate the key. Transfer the C2V file to a computer with Internet access and browse to:", "TR_OFFLINE_ACTIVATION_STEP2_TEXT": "Step 2: On the customer portal, log in with the product key then select off-line activation. Upload the C2V file to the portal. Click the Generate button and then download the V2C file. A V2C file will be saved on the Internet connected computer.  Transfer that file to this computer. Click the Install V2C button and browse to the V2C file. See the Licensing Guide for more details.", "TR_OK": "OK", "TR_OLD_VALUE": "Old Value", "TR_ONE_DOMAIN_HOST_IP_MUST_BE_SPECIFIED": "One of Domain, Host, or IP must be specified", "TR_ONLINE_ACTIVATION": "On-line Activation (requires Internet access)", "TR_ONLINE_ACTIVATION_TEXT": "If this computer has Internet access, then click this button to activate the product key on-line.", "TR_ONLY_EXT_REF": "Pre-Filter: Check to only show ExtRefs", "TR_ONLY_EXT_REF_DESC": "The list above can get very large.  This can help cut down the items returned.", "TR_OPC": "OPC", "TR_OPC_61850_SERVER_CONTROL_IS_IN_MIDDLE_OPERATION_AND_CANNOT_BE_DELETED_NOW": "The SDO control '{{arg1}}' is in the middle of an operation and cannot be currently be deleted.", "TR_OPC_ADD_CAUSE_POINT": "Add Cause Point", "TR_OPC_ADD_CAUSE_POINT_DESC": "Add Cause Point", "TR_OPC_AE_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Failed to load the OPC AE Address Space Element. Unknown error.", "TR_OPC_AE_ITEM_LIST_UNAVAILABLE": "Item List unavailable", "TR_OPC_AE_SERVER_LIST_UNAVAILABLE": "Server List unavailable", "TR_OPC_AESUBSCRIPTION_MASK": "OPC Data Access AE Subscription Mask", "TR_OPC_AESUBSCRIPTION_MASK_DESC": "Each bit enables(1)/disables(0) a reason that an item  subscription through the SDG OPC Data Access Server should enable OPC Alarm and Event notifications for the item. Note that this mask overrides all other masks if enabled. If 0, no OPC Alarm and Event notifications will occur as a result of an item subscription. ", "TR_OPC_CANCEL_REQUEST_POINT": "Cancel Request point", "TR_OPC_CANCEL_REQUEST_POINT_DESC": "Define the Cancel Request point", "TR_OPC_CANCEL_RESPONSE_POINT": "Cancel Response point", "TR_OPC_CANCEL_RESPONSE_POINT_DESC": "Define the Cancel Response point", "TR_OPC_CANT_ADD_MOD_SERVER_DUPLICATE": "Could not add {{arg1}} MDO on OPC server: {{arg2}}. (duplicate ?)", "TR_OPC_CANT_ADD_SERVER_DUP": "Could not add {{arg1}} MDO on OPC server: {{arg2}}. (duplicate ?)", "TR_OPC_CLASSIC_START": "OPC CLASSIC CAN'T START", "TR_OPC_CLIENT_CONNECT_FAIL": "Could not connect to OPC server : \n  OPCServerName[{{arg1}}]='{{arg2}}'\n  OPCServerProgID[{{arg3}}]='{{arg4}}'\n  OPCServerNode[{{arg5}}]='{{arg6}}'", "TR_OPC_CLIENT_IS_EMPTY": "Client is empty", "TR_OPC_CLIENT_IS_NOT_CONNECTED_CANNOT_SELECT_POINTS_UNTIL_CONNECTED.": "OPC Client {{arg1}} is not connected - cannot select points until connected.", "TR_OPC_CLIENT_NAME": "Client name", "TR_OPC_COMMAND_TERM_POINT": "Command Term point", "TR_OPC_COMMAND_TERM_POINT_DESC": "Define the Command Term point", "TR_OPC_DESC": "Log trace messages for OPC", "TR_OPC_ERROR_LOADING_ADDRESS_SPACE_ELEMENT": "Failed to load the OPC Address Space Element. Unknown error.", "TR_OPC_EXCEPTION_BROWSE_EVENT": "Severe Exception occurred in BrowseEventSpace", "TR_OPC_INVALID_ITEM_ID": "Invalid Item ID", "TR_OPC_INVALID_ITEM_NAME": "Invalid Item Name", "TR_OPC_INVALID_ITEM_TYPE": "Invalid Item Type", "TR_OPC_ITEM_LIST_UNAVAILABLE": "Item list unavailable", "TR_OPC_MDO_OPTIONS": "MDO Options", "TR_OPC_MDO_OPTIONS_DESC": "Define the MDO Options", "TR_OPC_MDO_OPTIONS_SET": "Could not set MDO options {{arg1}}", "TR_OPC_NO_MORE_CLIENTS": "No more OPC clients available", "TR_OPC_OPERATE_REQUEST_POINT": "Operate Request point", "TR_OPC_OPERATE_REQUEST_POINT_DESC": "Define the Operate Request point", "TR_OPC_OPERATE_RESPONSE_POINT": "Operate Response point", "TR_OPC_OPERATE_RESPONSE_POINT_DESC": "Define the Operate Response point", "TR_OPC_RESTART_REQUIRED_NAME_CHANGE": "The SDG needs to be saved and restarted for name changes to take effect in OPC", "TR_OPC_SELECT_REQUEST_POINT": "Select Request point", "TR_OPC_SELECT_REQUEST_POINT_DESC": "Define the Select Request point", "TR_OPC_SELECT_RESPONSE_POINT": "Select Response point", "TR_OPC_SELECT_RESPONSE_POINT_DESC": "Define Select Response point", "TR_OPC_SERVER_LIST_UNAVAILABLE": "Server List unavailable", "TR_OPC_SERVER_PROG_ID": "Server URL", "TR_OPC_SERVER_PROG_ID_DESC": "For OPC DA specifies the server URL of the OPC Server to connect to, if this connection is to an OPC XML DA server it's the port and path i.e port/OPC/DA", "TR_OPC_SET_MDO_OPTIONS": "Could not set MDO options {{arg1}}", "TR_OPC_STARTUP": "OPC Startup", "TR_OPC_STARTUP_DESC": "Log trace messages for OPC startup", "TR_OPC_STD_EXCEPTION_BROWSE_EVENT": "Exception occurred in BrowseEventSpace: {{arg1}}", "TR_OPC_UA_ADD_USER_FAILED": "OPC UA Add User Failed", "TR_OPC_UA_CLIENT_CERTIFICATE_FILE": "Certificate File", "TR_OPC_UA_CLIENT_CERTIFICATE_FILE_DESC": "Specifies the Client's OPC UA Certificate File", "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES": "Discard Oldest Changes", "TR_OPC_UA_CLIENT_DISCARD_OLDEST_CHANGES_DESC": "When more data changes occur than this item can store, the oldest value will be discarded.", "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH": "Max Notifications Per Publish", "TR_OPC_UA_CLIENT_MAX_NOTIFICATIONS_PER_PUBLISH_DESC": "Max number of reported items number, if 0, the server will report as many as possible.", "TR_OPC_UA_CLIENT_NAME": "Name of this OPC UA client", "TR_OPC_UA_CLIENT_NAME_DESC": "Specifies the name of this OPC UA client. Names must be unique. If not specified, it will default to OpcUaClient-'index'", "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE": "Private Key File", "TR_OPC_UA_CLIENT_PRIVATE_KEY_FILE_DESC": "Specifies the Client's OPC UA Private Key File", "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE": "Private Key Pass Phrase", "TR_OPC_UA_CLIENT_PRIVATE_KEY_PASS_PHRASE_DESC": "Specifies the Client's OPC UA Private Key Pass Phrase", "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE": "Publish Queue Size", "TR_OPC_UA_CLIENT_PUBLISH_QUEUE_SIZE_DESC": "The number of changes that can be stored for that item on the server until the next publish cycle.", "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL": "Publishing Interval (ms)", "TR_OPC_UA_CLIENT_PUBLISHING_INTERVAL_DESC": "Time the server shall send modifications.", "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT": "Re-try count", "TR_OPC_UA_CLIENT_RECONNECT_RETRY_COUNT_DESC": "Specifies the reconnect re-try count for the OPC UA Client (0 = attempt reconnects for ever)Setting the client refresh MDO will cause the internal limit counter to be re-set to 0resulting in continued connection attempts to the OPC UA server.", "TR_OPC_UA_CLIENT_RECONNECT_TIME": "Reconnect Timeout (ms)", "TR_OPC_UA_CLIENT_RECONNECT_TIME_DESC": "Specifies the reconnect timeout for the OPC UA Client (0 = no reconnect)", "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL": "Sampling Interval (ms)", "TR_OPC_UA_CLIENT_SAMPLING_INTERVAL_DESC": "Interval that item stores changes, default is 5 per second.", "TR_OPC_UA_CLIENT_SECURITY_MODE": "Security Mode", "TR_OPC_UA_CLIENT_SECURITY_MODE_DESC": "Selects the security mode for an OPC UA Client connection.", "TR_OPC_UA_CLIENT_SECURITY_POLICY": "Security Policy", "TR_OPC_UA_CLIENT_SECURITY_POLICY_DESC": "Selects the security policy for an OPC UA Client connection.", "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE": "Token Type", "TR_OPC_UA_CLIENT_SECURITY_TOKEN_TYPE_DESC": "Selects the security token type for an OPC UA Client connection.", "TR_OPC_UA_CLIENT_SERVER_URL": "URL of the OPC UA Server to connect to", "TR_OPC_UA_CLIENT_SERVER_URL_DESC": "Specifies the URL of the OPC UA Server to connect to.", "TR_OPC_UA_CLIENT_SESSION_TIMEOUT": "Session Timeout (ms)", "TR_OPC_UA_CLIENT_SESSION_TIMEOUT_DESC": "Time until the session expires if there is no communication.", "TR_OPC_UA_CONNECTION_SETTINGS": "OPC UA Connection Settings", "TR_OPC_UA_DELETE_USER_FAILED": "OPC UA Delete User Failed", "TR_OPC_UA_MODIFY_USER_FAILED": "Error. User wasn't modify.", "TR_OPC_UA_SECURITY_SETTINGS": "OPC UA Security Settings", "TR_OPC_UA_SERVER_LIST_UNAVAILABLE": "Server List unavailable", "TR_OPC_UA_TRUST_CERTIFICATE_FAILED": "Error: Trusting Selected Certificate Failed", "TR_OPC_UA_USER_AUTHENTICATION_SETTINGS": "OPC UA User Authentication Settings", "TR_OPC_UA_USER_CERTIFICATE_SETTINGS": "OPC UA User Certificate Settings", "TR_OPC_UA_USER_CONFIGURATION": "OPC UA User Configuration", "TR_OPC_USER_PASSWORD": "User Password", "TR_OPC_USER_PASSWORD_DESC": "Specifies the User Password", "TR_OPCAE_CLIENT_CONNECT": "Could not connect to OPC AE server : \\n  OPCServerName[{{arg1}}]='{{arg2}}'\\n  OPCServerProgID[{{arg3}}]='{{arg4}}'\\n  OPCServerNode[{{arg5}}]='{{arg6}}'", "TR_OPCAE_CLIENT_CONNECT_FAIL": "Could not connect to OPC AE server : \n  OPCServerName[{{arg1}}]='{{arg2}}'\n  OPCServerProgID[{{arg3}}]='{{arg4}}'\n  OPCServerNode[{{arg5}}]='{{arg6}}'", "TR_OPCAE_CLIENT_DUPLICATE": "Cannot add OPC AE client : '{{arg1}}'. Duplicate name.", "TR_OPCAE_NO_MORE": "No more OPC AE clients available", "TR_OPCAESERVER_BUFFER_TIME": "Buffer time", "TR_OPCAESERVER_BUFFER_TIME_DESC": "The buffer time, specified in milliseconds, indicates the number of times the event notifications may be sent to the subscription object. This parameter is the minimum time spread between two successive event notifications. The value 0 means that all event notifications shall be sent immediately from the server. If the parameter MaxSize is greater than 0, it instructs the server to send event notifications quicker in order to keep the buffer size within MaxSizeSpecifies the buffer time for the OPC AE Server subscription", "TR_OPCAESERVER_MAX_SIZE": "<PERSON>", "TR_OPCAESERVER_MAX_SIZE_DESC": "This parameter is the maximum number of events that may be specified in a call. The value 0 means no restriction for the number of events. Please note that if the value MaxSize is greater than 0, then events can be sent faster from the server than through the BufferTime parameter. Specifies the max size for the OPC AE Server subscription", "TR_OPCAESERVER_NAME": "Server Name", "TR_OPCAESERVER_NAME_DESC": "Optional Name for the OPC AE Server to connect to, if not specified use the value of OPCAEserverProgID. It is strongly recommended that this parameter be defined as an external OPC AE client may not be able to browse for tags in SDG OPC AE server if the OPC AE server name contains one or more period ('.') characters. To work around this issue, define this alias name (with no periods for the external OPC AE server, and reference the server by its alias.", "TR_OPCAESERVER_NODE": "Node name of the OPC AE Server", "TR_OPCAESERVER_NODE_DESC": "Specifies the Node name of the OPC AE Server to connect to", "TR_OPCAESERVER_PROG_ID": "Prog ID", "TR_OPCAESERVER_PROG_ID_DESC": "Specifies the PROG ID of the OPC AE Server to connect to", "TR_OPCAESERVER_RECONNECT_RETRY_COUNT": "Reconnect re-try count", "TR_OPCAESERVER_RECONNECT_RETRY_COUNT_DESC": "Specifies the reconnect re-try count for the External OPC AE Server (0 = attempt reconnects for ever)Setting the client refresh MDO will cause the internal limit counter to be re-set to 0resulting in continued connection attempts to the OPC AE server.", "TR_OPCAESERVER_RECONNECT_TIME": "Reconnect timeout (ms)", "TR_OPCAESERVER_RECONNECT_TIME_DESC": "Specifies the reconnect timeout for the OPC AE Server (0 = no reconnect)", "TR_OPCAETIME_SOURCE": "OPC Event Time Source", "TR_OPCAETIME_SOURCE_DESC": "Specifies the source of the time tag for OPC Alarm and Event data points. The possible values are Update or Reported where Update means the time, relative to the SDG system clock, at which the data point was last updated. Reported specifies the reported time of the event that caused the data to change. Reported time will be relative to the remote slave device's system clock except on initialization where the SDG's system clock is used until the first event with time is received. It is important to note that static data polling, or received events which do not specify a reported time, may cause a specific data point's value to change without an event being generated, hence the event time will not change..  Note: this parameter specifies how time will be provided by the SDG OPC AE Server ", "TR_OPCAEUSE_SIMPLE_EVENTS": "Use OPC Alarm and Simple Events", "TR_OPCAEUSE_SIMPLE_EVENTS_DESC": "If true, all OPC AE events are reported as simple events. ", "TR_OPCCLIENT_FORCE_SPECIFICATION": "Force OPC Server Specification", "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE": "OPC Client Status Update Rate (ms)", "TR_OPCCLIENT_SERVER_STATUS_UPDATE_RATE_DESC": "The interval at which an SDG OPC client requests status information from it's server.  If the client does not wish to request status updates set this to 0. ", "TR_OPCDA_CLIENT_CHANGING_TYPE_REQUIRES_RECONNECT": "NOTE: changing server version requires disconnecting and reconnecting to take effect.", "TR_OPCDA_CLIENT_DUPLICATE": "Cannot add OPC DA client : '{{arg1}}'. Duplicate name.", "TR_OPCSERVER_NAME": "<PERSON>as Name", "TR_OPCSERVER_NAME_DESC": "Alias Name for the OPC Server to connect to, if not specified use the value of OPCserverProgID. It is strongly recommended that this parameter be defined as an external OPC client may not be able to browse for tags in SDG OPC server if the OPC server name contains one or more period ('.') characters. To work around this issue, define this alias name (with no periods for the external OPC server, and reference the server by its alias.", "TR_OPCSERVER_NODE": "Node Name or Address TCP/IP", "TR_OPCSERVER_NODE_DESC": "Specifies the Node name  or address TCP/IP of the OPC Server to connect to", "TR_OPCSERVER_READ_PROPERTIES_TIME": "Read Properties Time (ms)", "TR_OPCSERVER_READ_PROPERTIES_TIME_DESC": "Specifies the period at which properties are read (0 = don't read)", "TR_OPCSERVER_RECONNECT_DELAY": "Reconnect Delay (ms)", "TR_OPCSERVER_RECONNECT_DELAY_DESC": "If nonzero, this specifies the time (in ms) to wait to reset the retry count and continue to try to reconnect. If zero, it is ignored and the retries will not continue past the retry count value.", "TR_OPCSERVER_RECONNECT_RETRY_COUNT": "Reconnect Re-try Count", "TR_OPCSERVER_RECONNECT_RETRY_COUNT_DESC": "Specifies the reconnect re-try count for the External OPC Server (0 = attempt reconnects for ever)Setting the client refresh MDO will cause the internal limit counter to be re-set to 0resulting in continued connection attempts to the OPC server.", "TR_OPCSERVER_RECONNECT_TIME": "Reconnect Time (ms)", "TR_OPCSERVER_RECONNECT_TIME_DESC": "Specifies the reconnect retry time for the OPC client to try to reconnect to the server (0 = no reconnect)", "TR_OPCSERVER_TYPE": "Server OPC Version", "TR_OPCSERVER_UPDATE_RATE": "Refresh Rate (ms)", "TR_OPCSERVER_UPDATE_RATE_DESC": "Specifies the Refresh rate for the OPC Server", "TR_OPCTIME_SOURCE": "OPC Time Source", "TR_OPCTIME_SOURCE_DESC": "Specifies the source of the time tag for OPC data points. The possible values are Update or Reported where Update means the time, relative to the SDG system clock, at which the data point was last updated. Reported specifies the reported time of the most recent event that caused the data to change. Reported time will be relative to the remote slave device's system clock except on initialization where the SDG's system clock is used until the first event with time is received. It is important to note that static data polling, or received events which do not specify a reported time, may cause a specific data point's value to change without an event being generated, hence the event time will not change.  Note: this parameter specifies how time will be provided by the SDG OPC DA Server ", "TR_OPCUA_APP_CERT_CREATED_LICENSED": "The SDG created a default certificate and private key file (and password) for the OPC UA Server (see logs for details). This is required even when not using security.<br/><br/>The certificate will expire in one year from today.<br/><br/>To keep the certificate and private key, please save the configuration at your earliest convenience.<br/><br/>Note: the values for the certificate paths and password are in the ini file in the variables: OpcUaSecurityCertificateFile, OpcUaSecurityPrivateKeyFile, and OpcUaSecurityPrivateKeyPassPhrase (encrypted).<br/>These settings can be viewed in the OPC UA Server configuration dialog (Application Certificate, Private Key, Passphrase).", "TR_OPCUA_APP_CERT_CREATED_NOT_LICENSED": "The SDG created a default OPC UA application certificate and private key file (and password) - see logs for details.<br/>This is required for OPC UA clients to work correctly but will not have any effect on the clients themselves.<br/><br/>To keep the certificate and private key, please save the configuration at your earliest convenience.<br/><br/>Note: the values for the certificate paths and password are in the ini file in the variables: OpcUaSecurityCertificateFile, OpcUaSecurityPrivateKeyFile, and OpcUaSecurityPrivateKeyPassPhrase (encrypted)", "TR_OPCUA_APP_CERT_CREATED2": "OPC UA Server certificates were auto created.<br/>Details:<br/>Auto created certificate : {{arg1}}<br/>  Auto created private key : {{arg2}}<br/>Password for private key : (encrypted-see configuration dialog)", "TR_OPCUA_CLIENT_CONNECT_FAIL": "Could not connect to OPC UA server : \n  OpcUaClientName[{{arg1}}]='{{arg2}}'\n OpcUaClientServerUrl[{{arg3}}]='{{arg4}}'", "TR_OPCUA_CLIENT_DUPLICATE": "Cannot add OPC UA client : '{{arg1}}'. Duplicate name.", "TR_OPCUA_NO_MORE": "No more OPC UA clients available", "TR_OPCXML_DA_SERVER_NAME": "OPC XML DA Server Name", "TR_OPCXML_DA_SERVER_NAME_DESC": "Specifies the name used by an OPC XML DA client to connect to the SDG.  With the INI file defaults a local OPC XML DA client can connect to the SDG as follows: http://localhost:8081/SDG", "TR_OPCXML_DA_SERVER_PORT": "OPC XML DA Server Port", "TR_OPCXML_DA_SERVER_PORT_DESC": "The TCP/IP port for the OPC XML DA server.  With the INI file defaults a local OPC XML DA client can connect to the SDG as follows: http://localhost:8081/SDG ", "TR_OPEN_FILE": "{{arg1}}({{arg2}}) Record too int (over {{arg3}} bytes)", "TR_OPERATION_DESCRIPTION": "Specify the operation", "TR_OPERATION_HELP": "Operation Help", "TR_OPERATION_LIST": "Operations:", "TR_OPERATION_LIST_DESC": "List of possible operations", "TR_OPERATOR_ROLE": "OPERATOR", "TR_OPT_HELP_OPC_AE_MDO_EVENT_USE_OCCURENCE_TIME": "use the event occurrence time", "TR_OPT_HELP_OPC_AE_MDO_PSUEDO_POINTS": "specify list of semi-colon separated pseudo point names which are driven by changes in message", "TR_OPT_HELP_OPCAE_ACT_ACK_NOT_REQUIRED": "Active acknowledgment not required", "TR_OPT_HELP_OPCAE_ACT_ACK_REQUIRED": "Active acknowledgment required", "TR_OPT_HELP_OPCAE_ACT_MASK": "Active Mask", "TR_OPT_HELP_OPCAE_ACT_STATE": "Active State", "TR_OPT_HELP_OPCAE_FIRE_COND_ON_ATTR_CHANGE": "Fire condition on attribute change", "TR_OPT_HELP_OPCAE_INACT_ACK_NOT_REQUIRED": "Inactive acknowledgment not required", "TR_OPT_HELP_OPCAE_INACT_ACK_REQUIRED": "Inactive acknowledgment required", "TR_OPT_HELP_OPCAE_INACT_MASK": "Inactive Mask", "TR_OPT_HELP_OPCAE_INACT_STATE": "Inactive State", "TR_OPT_HELP_OPCUA_PUBLISH_OFF": "Publish OPC changes", "TR_OPT_HELP_OPCUAMESSAGE": "override the default message for this event with this string", "TR_OPTION": "Option", "TR_OPTIONAL_ITEMS_TO_INCUDE_IN_REPORT": "Operation Items To Include In Report", "TR_OPTIONS": "Options", "TR_OPTIONS_EDITOR": "Options Editor", "TR_OVERALL": "Overall", "TR_OVERFLOW": "OVERFLOW", "TR_OVERFLOW_DESC": "Overflow/Roll-over", "TR_PARAMETERS": "Parameters", "TR_PARSE_OPTION_VALIDATION_FAILED": "Failed to validate category/condition/sub condition name '{{arg1}}'", "TR_PARSE_OPTION_VALIDATION_FAILED2": "Failed to validate category/condition/sub condition name '{{arg1}}'", "TR_PASSWORD": "Password", "TR_PASSWORD_COMPLEXITY_REQUIREMENTS": "Password complexity requirements:\n . Must contains at least one lowercase characters.\n . Must contains at least one uppercase characters.\n . Must contains at least one digit from 0-9.\n . Must contains at least one special symbols in this list: '@, $, #, !, -, +, *, _, %, .'\n . Length must be at least 8 characters and a maximum of 24.", "TR_PASSWORD_DESC": "Password", "TR_PASSWORD_IS_REQUIRED": "Password is required.", "TR_PASSWORD_IS_REQUIRED_CHARACTERS_MINIMUM": "Password is required (6 characters minimum).", "TR_PAUSE": "Pause", "TR_PERFORMANCE_DESC": "Performance", "TR_PERFORMANCE_VIEW": "Performance View", "TR_PHYS_COM_BAUD": "Baud <PERSON>", "TR_PHYS_COM_BAUD_DESC": "Sets the baud rate for corresponding serial PhysComChannel", "TR_PHYS_COM_CHANNEL": "Physical channel i.e. COM port, Remote TCP/IP or Node Name", "TR_PHYS_COM_CHANNEL_DESC": "Sets the Communication Channel.  \nExamples:  \"COM1\" , \"************\" or \"ROCK<PERSON><PERSON><PERSON><PERSON>UBSTATION\" (for a TCP/IP channel)\n\nOn Serial port - \n     this is the com port name to use \nOn TCP client - \n     this is the host name or TCP/IP address to set up TCP connection to \nOn TCP server - \n     this is the host name or IP address to accept TCP connection from \n     May be *.*.*.* indicating accept connection from any client \n     May also be a list of ';' separated host names or TCP/IP addresses \n       to allow connections from. \nOn TCP Dual End Point Device - \n     this is the host names or IP address to accept TCP connection from or to connect to. \n     May also be a list of ';' or ',' separated host names or TCP/IP addresses \n       to allow connections from, will only try to connect to the first on the list. \n", "TR_PHYS_COM_CHNL_NAME": "Channel Name", "TR_PHYS_COM_CHNL_NAME_DESC": "Alias name for communications channel. Must be specified and be unique.", "TR_PHYS_COM_DATA_BITS": "Data Bits", "TR_PHYS_COM_DATA_BITS_DESC": "Sets the number of data bits for corresponding serial PhysComChannel", "TR_PHYS_COM_DEST_UDPPORT": "Destination UDP Port", "TR_PHYS_COM_DEST_UDPPORT_DESC": "On Master - if TCP and UDP is configured this specifies the destination UDP/IP \n             port to send broadcast requests in UDP datagrams to.\n            if UDP ONLY is configured this specifies the destination UDP/IP \n             port to send all requests in UDP datagrams to.\n            This must match the \"localUDPPort\" on the slave.\nOn Slave  - if TCP and UDP this is not used.\n            if UDP ONLY is configured this specifies the destination UDP/IP\n             port to send responses to. \n            Can be WINTCP_UDP_PORT_SRC = 2 indicating use the source port from a \n             UDP request received from master.", "TR_PHYS_COM_DIAL_OUT": "Enable <PERSON><PERSON> out", "TR_PHYS_COM_DIAL_OUT_DESC": "If true, then the modem will be configured for dialing and answering telephone calls. If false, then the modem will be configured for only answering telephone calls. ", "TR_PHYS_COM_DUAL_END_POINT_IP_PORT": "Dual End Point TCP/IP Port Number", "TR_PHYS_COM_DUAL_END_POINT_IP_PORT_DESC": "If Dual End Point is supported a listen will be done on the PhysComIpPort\n and a connection request will be sent to this port number when needed.\nThis should match ipPort on remote device.\nNormal state is listen, connection will be made when there is data to send.", "TR_PHYS_COM_INIT_UNSOL_UDPPORT": "Unsolicited UDP Port", "TR_PHYS_COM_INIT_UNSOL_UDPPORT_DESC": "On master - Not used.\nOn Slave  - if TCP and UDP not used.\n            if UDP ONLY is configured this specifies the destination UDP/IP \n             port to send the initial Unsolicited Null response to.\n             After receiving a UDP request from master, destUDPPort (which)\n             may indicate use source port) will be used for all responses.\n             This must not be WINTCP_UDP_PORT_NONE(0), WINTCP_UDP_PORT_ANY(1), or\n             WINTCP_UDP_PORT_SRC(2) for a slave that supports UDP.", "TR_PHYS_COM_IP_CONNECT_TIMEOUT": "Connect timeout (ms)", "TR_PHYS_COM_IP_CONNECT_TIMEOUT_DESC": "Sets the TCP/IP connect timeout to use if PhysComChannel specifies an TCP/IP address. For IEC 60870-5-104 this is the T0 parameter. Note that this parameter should be set to the  lowest value that works reliably.  In cases  where a connection can not be established the process will block for the period specified.", "TR_PHYS_COM_IP_MODE": "TCP/IP Mode", "TR_PHYS_COM_IP_MODE_DESC": "Sets the connection mode to use if PhysComChannel specifies an TCP/IP address", "TR_PHYS_COM_IP_PORT": "TCP/IP Port Number", "TR_PHYS_COM_IP_PORT_DESC": "Sets the TCP/IP port number to use if PhysComChannel specifies an TCP/IP address", "TR_PHYS_COM_LOCAL_IP_ADDRESS": "Local TCP/IP address", "TR_PHYS_COM_LOCAL_IP_ADDRESS_DESC": " On Client - \n      Address to bind socket to. This allows you to specify which TCP/IP address \n      to send as source address in TCP messages if there are multiple TCP/IP Addresses, \n      for example when there are multiple Network Interface Cards (NICs). \n      if \"0.0.0.0\" is used the TCP stack will choose which TCP/IP Address to use. \n      If an address that is not present is specified, the bind will fail and \n      the TCP stack will choose which address. \n      This address is also used for DNP Master when sending UDP datagrams. \n      Binding this address does not guarantee sending on a particular NIC. \n      This is determined by the TCP/IP Routing Table depending on the destination \n      TCP/IP Address. You can display this table by entering \"route print\" in \n      a command window. It is possible to add manual routes to cause a particular \n      NIC to be used. \"route add destIPAddress gateway\". \n      Enter \"route ?\" for more details. \n On Server - \n      not currently used for listeners. \n      (Note: this address IS used for DNP Outstation if configured for UDP ONLY) \n", "TR_PHYS_COM_LOCAL_UDPPORT": "Local UDP Port", "TR_PHYS_COM_LOCAL_UDPPORT_DESC": "Local port for sending and receiving UDP datagrams on.\nIf this is set to WINTCP_UDP_PORT_NONE = 0, UDP will not be enabled. \nFor DNP networking UDP should be supported. \nIt is not needed for any of the current IEC or modbus protocols.\nOn Master - If this is set to WINTCP_UDP_PORT_ANY = 1, an unspecified available \n            port will be used. \nOn Slave  - This should be chosen to match the UDP port that the master uses\n            to send Datagram messages to. \n            This must not be WINTCP_UDP_PORT_ANY = 1 or WINTCP_UDP_PORT_SRC = 2.", "TR_PHYS_COM_MBPCARD_NUMBER": "Channel MBP Card Number", "TR_PHYS_COM_MBPCARD_NUMBER_DESC": "Sets the Modbus Plus card number to use if PhysComType specifies a Modbus Plus Channel. ", "TR_PHYS_COM_MBPRECIEVE_TIMEOUT": "Modbus Plus Receive timeout", "TR_PHYS_COM_MBPRECIEVE_TIMEOUT_DESC": "Sets the Modbus Plus Receive timeout to use if PhysComType specifies a Modbus Plus Channel. ", "TR_PHYS_COM_MBPROUTE_PATH": "Modbus Plus Route Path", "TR_PHYS_COM_MBPROUTE_PATH_DESC": "Specifies the Modbus Plus Route Path to use if PhysComType specifies a Modbus Plus Channel. This parameter only applies to Modbus plus masters.", "TR_PHYS_COM_MBPSLAVE_PATH": "Modbus Plus Slave Path", "TR_PHYS_COM_MBPSLAVE_PATH_DESC": "Sets the Modbus Plus slave path to use if PhysComType specifies a Modbus Plus Channel. This parameter only applies to Modbus plus slaves.", "TR_PHYS_COM_MODBUS_RTU": "Enable Modbus RTU", "TR_PHYS_COM_MODBUS_RTU_DESC": "If true, then the channel will be configured for Modbus RTU serial communications.", "TR_PHYS_COM_MODEM_ANSWER_TIME": "Answer wait time (secs)", "TR_PHYS_COM_MODEM_ANSWER_TIME_DESC": "Sets the amount of time the modem waits for an answer after dialing (in seconds)", "TR_PHYS_COM_MODEM_IDLE_TIME": "Wait time to hang up after idle (secs)", "TR_PHYS_COM_MODEM_IDLE_TIME_DESC": "Sets the amount of time the modem waits to hang up after the channel is idle (in seconds)", "TR_PHYS_COM_MODEM_POOL": "Modem pool", "TR_PHYS_COM_MODEM_POOL_DESC": "The modem pool this channel uses ", "TR_PHYS_COM_MODEM_REDIAL_LIMIT": "Re-dial times", "TR_PHYS_COM_MODEM_REDIAL_LIMIT_DESC": "Sets the number of times the modem will dial before concluding that communication has failed. If dialing has failed (i.e. the limit is reached) the modem channel MDO ChannelRedialLimitControl will be TRUE, set this MDO (ChannelRedialLimitControl) to false to start dialing again. A value of 0 will cause the modem to attempt dialing forever. Note that setting this value to 0 will tie up the modem forever should a connection never be made. ", "TR_PHYS_COM_PARITY": "Parity", "TR_PHYS_COM_PARITY_DESC": "Sets the parity for corresponding serial PhysComChannel", "TR_PHYS_COM_PHONE_NUMBER": "Telephone number", "TR_PHYS_COM_PHONE_NUMBER_DESC": "Telephone number to dial out on a modem ", "TR_PHYS_COM_PROTOCOL": "Channel Protocol Type", "TR_PHYS_COM_PROTOCOL_DESC": "Sets the protocol for the channel Applies to all Physical channel types ", "TR_PHYS_COM_SERIAL_MODE": "Flow Control", "TR_PHYS_COM_SERIAL_MODE_DESC": "Sets the mode of the serial Channel: 'none' - use no flow control. 'hardware' - use hardware flow control. 'windows' - use flow control and serial parameters (baud rate, parity, etc.) as specified with the MODE command. ", "TR_PHYS_COM_STOP_BITS": "Stop Bits", "TR_PHYS_COM_STOP_BITS_DESC": "Sets the number of stop bits for corresponding serial PhysComChannel", "TR_PHYS_COM_VALIDATE_UDPADDRESS": "Validate UDP Address", "TR_PHYS_COM_VALIDATE_UDPADDRESS_DESC": "Whether or not to validate source address of received UDP datagram.", "TR_PHYS_OFFLINE_POLL_PERIOD": "Off-line poll period (ms)", "TR_PHYS_OFFLINE_POLL_PERIOD_DESC": "The period at which the sessions on the channel are polled if they are off-line.  Note that this parameter only applies to serial DNP and Modbus master sessions on this channel. A value of zero (0)will disable this feature.", "TR_PLEASE_ENTER_A_NEW_WORKSPACE_NAME": "Please enter a new Workspace name", "TR_PLEASE_SELECT_A_FILE": "Please select a file", "TR_PLEASE_SELECT_A_PROPERTY": "Please select a Property", "TR_PLEASE_SELECT_A_WORKSPACE": "Please select a Workspace", "TR_PLEASE_SELECT_AT_LEAST_ONE_ROLE": "Please select at least one role.", "TR_PLEASE_WAIT_UPDATE_SDG": "Please wait while we check if SCADA Data Gateway is up to date...", "TR_POINT_IN_USE_CANT_DELETE": "Cannot delete '{{arg1}}' because it is mapped to another point, used in an equation, or subscribed to in OPC/OPCUA server", "TR_POINT_MAP": "Point Map", "TR_POINT_MAP_FILE": "Point Map File", "TR_POINT_MAP_FILE_DESC": "Specifies Point Data Mapping file.  This value can have the full path to the file or just the file name.  If just the file name is specified it must be located in the same directory as the INI file.", "TR_POINT_MAP_NOT_FOUND": "Point map file was not found at: {{arg1}}.", "TR_POINT_MAP_UNDEFINED": "Point Map undefined", "TR_POINT_NUMBER_CAN_NOT_BE_ZERO": "Point number can't be zero.", "TR_POINT_PATH": "Full Name", "TR_POINT_TYPE": "Point Type", "TR_POINT_TYPE_DESC": "Point Type", "TR_POINTS": "Points", "TR_POLLED_DATA_SET_NAME": "Polled Dataset Name", "TR_POLLED_DS_DS_ID": "Dataset Name", "TR_POLLED_DS_DS_ID_DESC": "Specify Dataset Name", "TR_POLLED_DS_ID": "Polled Dataset Name", "TR_POLLED_DS_ID_DESC": "Specify Polled Dataset Name", "TR_POLLED_DS_PERIOD": "Polled Period", "TR_POLLED_DS_PERIOD_DESC": "Specifies the Polled Period.", "TR_POLLED_DS_SET_PERIOD": "DS Transfer Set Count", "TR_POLLED_DS_SET_PERIOD_DESC": "Specify the DS Transfer Set Count", "TR_POLLED_POINT_SET_DESC": "Specify the Poll Period", "TR_POLLED_POINT_SET_ID": "Polled Point Set Name", "TR_POLLED_POINT_SET_ID_DESC": "Specify the polled point Set Name", "TR_POLLED_POINT_SET_PERIOD": "Poll Period", "TR_POLLED_POINT_SET_PERIOD_DESC": "Specify the Point Set Period", "TR_PRIMARY_ADAPTOR": "{{arg1}}: Could not get primary adapter TCP/IP address, using localhost as primary TCP/IP address (not connected to Internet, specify the TCP/IP Address for monHost and gtwHost in the gtw_config.json file manually)", "TR_PRIVATE_KEY_PASS_PHRASE": "Private Key Pass Phrase", "TR_PRIVATE_KEY_PASS_PHRASE_DESC": "Private Key Pass Phrase", "TR_PROCESS_LOADED": "Engine process loaded", "TR_RUNNING_NO_LICENSE": "No License", "TR_RUNNING_IN_GRACE_PERIOD": "No License/Grace Period", "TR_RUNNING_NO_LICENSE_DETAIL": "No License.<br><a href='{{URL_ENGINE}}/#/license' target='_self'>License validation failed on host {{HOST}}, searching for a license.</a>", "TR_RUNNING_GRACE_PERIOD_LICENSE_DETAIL": "No License.<br><a href='{{URL_ENGINE}}/#/license' target='_self'>License not found on host {{HOST}}, running on grace period.</a>", "TR_PRODUCT_KEY_EXHAUSTED": "Product key exhausted", "TR_PRODUCT_KEY_FOR_NEW_LICENSE": "This product key is for a new license", "TR_PRODUCT_KEY_FOR_UPDATE_LICENSE": "This product key will update the current license (i.e. maintenance plan update)", "TR_PROG_ID": "PROG ID", "TR_PROPERTIES": "Properties", "TR_PROPERTY_DESC": "Define the property", "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT": "Purge Before Enable On Reconnect", "TR_PURGE_BEFORE_ENABLE_ON_RECONNECT_DESC": "Specifies if the purge before enable on reconnect is active.", "TR_QUAL_CHNG_MON": "Quality Change", "TR_QUALITY": "Quality", "TR_QUALITY_CHANGE": "Quality Change", "TR_QUALITY_CHANGE_DESC": "Specifies if the quality change is active.", "TR_QUALITY_ENUM_BLOCKED_DESC": "Block", "TR_QUALITY_ENUM_GOOD_DESC": "Good", "TR_QUALITY_ENUM_IN_TRANSIT_DESC": "in Transit", "TR_QUALITY_ENUM_INVALID_DESC": "Invalid", "TR_QUALITY_ENUM_INVALID_TIME_DESC": "Invalid time", "TR_QUALITY_ENUM_NOT_TOPICAL_DESC": "Not topical", "TR_QUALITY_ENUM_OVERFLOW_DESC": "Overflow", "TR_QUALITY_ENUM_REF_ERROR_DESC": "Reference error", "TR_QUALITY_ENUM_SUBSTITUTED_DESC": "Substituted", "TR_QUALITY_ENUM_TEST_DESC": "Test", "TR_QUALITY_ENUM_UNINITIALIZED_DESC": "Uninitialized", "TR_QUERY_RESULT_DESC": "Specifies the Query Results", "TR_QUERY_RESULTS": "Query Results", "TR_QUICK_START": "Quick Start", "TR_QUICK_START_DASHBOARD_MANAGEMENT": "Dashboard Management", "TR_QUICK_START_DEFAULT_DASHBOARD": "Default Dashboard", "TR_QUICK_START_DEVICE_TREE_VIEW": "Device Tree View", "TR_QUICK_START_EDITOR": "Editor", "TR_QUICK_START_LICENSES_MANAGEMENT": "Licenses Management", "TR_QUICK_START_MAPPING": "Mapping", "TR_QUICK_START_POPUP": "Quick Start Pop-up", "TR_QUICK_START_SYSTEM_SETTINGS": "System Settings", "TR_QUICK_START_TAGS_GRIDS": "Tags Grid", "TR_QUICK_START_TOPICS": "Quick Start Topics", "TR_QUICK_START_USERS_MANAGEMENT": "Users Management", "TR_QUICK_START_WORKSPACES": "Workspaces", "TR_RAW_UNITS_RANGE": "Input Range", "TR_RCB_DATASET_NAME": "Dataset Name", "TR_RCB_DATASET_NAME_DESC": "Specify Dataset Name", "TR_RCB_LIST": "List of all Report Control Blocks in the Model", "TR_RCB_LIST_DESC": "Select a Report Control Block from the list below.  Clicking on the name of a Report Control Block displays a preview of the Dataset members.  Filters below can be used to further refine the list.", "TR_RCB_NAME": "Report Control Block Name", "TR_RCB_NAME_DESC": "Displays the currently selected Report Control Block from the list below.  Choosing OK will cause the currently selected Report Control Block to be added.", "TR_RE_START_BOTH": "Re-Start Monitor and Engine", "TR_RE_START_GATEWAY": "Re-Start Gateway Engine", "TR_RE_START_MONITOR": "Re-Start Monitor", "TR_READ": "Read", "TR_READ_CONFIG_FROM_SERVER": "Read Configuration From Server", "TR_READ_CONFIG_FROM_SERVER_DESC": "Read Configuration From Server.", "TR_READ_DESC": "Read", "TR_READ_FAILED": "Error: <PERSON> failed", "TR_READ_GOOSE": "Warning: Could not read goose report '{{arg1}}', error = {{arg2}}", "TR_READ_POINT_MAP_AT_STARTUP": "Read Point Map At Startup", "TR_READ_POINT_MAP_AT_STARTUP_DESC": "if true, then the point mapping file will be read at startup ", "TR_READ_SUCCEED": "Read success", "TR_REASON_FOR_INCLUSION": "Reason For Inclusion", "TR_REASON_FOR_INCLUSION_DESC": "Specifies if the reason for inclusion is active.", "TR_RECONNECT_RETRY_COUNT": "Reconnect Re-try Count", "TR_RECONNECT_TIME": "Reconnect Time (msecs)", "TR_RECONNECTION_SETTINGS": "Reconnection Settings", "TR_REF_ERROR": "Reference Error", "TR_REF_ERROR_DESC": "Reference Error (ADC)", "TR_REFRESH": "REFRESH", "TR_REFRESH_AREA_SPACE": "Refresh Area space", "TR_REFRESH_AREA_SPACE_DESC": "Refresh Area space", "TR_REFRESH_DESC": "The data is being updated by the source of data without direct request.  No change is necessarily indicated.", "TR_REFRESH_ITEM_PARENT": "Refresh list of Items", "TR_REFRESH_ITEM_PARENT_DESC": "Refresh list of Items", "TR_REFRESH_LIST": "Refresh List", "TR_REFRESH_LIST_DESC": "Refresh List", "TR_REFRESH_PROPERTIES": "Refresh Properties", "TR_REFRESH_PROPERTIES_DESC": "Refresh Properties", "TR_REFRESH_SERVER_LIST": "Refresh Server list", "TR_REGISTER": "Register", "TR_REMEMBER_ME": "Remember Me", "TR_REMOTE_IP_NODE_NAME": "Remote TCP/IP / Node Name", "TR_REMOVE": "Remove", "TR_REMOVE_ALL": "Remove all", "TR_REMOVE_SUBSCRIPTION": "Remove Subscription", "TR_REMOVE_SUBSCRIPTION_DESC": "Remove Subscription", "TR_REMOVE_TASE2_DS_FAILED": "Removing data attributes from the dataset failed. See logs for details.\n\nThe dataset has been restored to the original but the MDOs will be permanently deleted if the configuration is saved.\nIt is recommended to close the SDG withOUT saving and start over.", "TR_REPORT_BY_EXCEPTION": "Report By Exception", "TR_REPORT_BY_EXCEPTION_DESC": "Specifies the current report by exception.", "TR_REPORT_NAME": "Report Control Block Name", "TR_REPORT_NONE_LEFT": "No more IEC 61850 Reports available", "TR_REQUEST_DATA_TYPE": "Request data type", "TR_REQUEST_DATA_TYPE_DESC": "Define the Request data type", "TR_REQUEST_QUOTE": "Request Quote", "TR_REQUESTED": "REQUESTED", "TR_REQUESTED_DESC": "The data is being updated because it was requested.", "TR_REQUIRES_RESTART": "Requires <PERSON><PERSON>", "TR_RESET": "Reset", "TR_RESET_COUNTER": "Reset Counter", "TR_RESET_PASSWORD": "Reset Password", "TR_RESET_USER_PASSWORD_OF": "Reset password of", "TR_RESULT": "Result", "TR_RESULTS": "Results:", "TR_RESUMED": "Resumed", "TR_RETRY_ENABLE_COUNT": "Retry Enable Count (-1=forever,0=never)", "TR_RETRY_ENABLE_COUNT_DESC": "Specifies the current retry enable count.", "TR_RETRY_ENABLE_PERIOD": "Retry Enable Period (ms)", "TR_RETRY_ENABLE_PERIOD_DESC": "Specifies the current retry enable period.", "TR_RETRY_FAILED_TRANSACTION": "Retry Failed Transaction", "TR_ROLE": "Role", "TR_RR_COILS_END_INDEX": "End Index", "TR_RR_COILS_END_INDEX_DESC": "End Index", "TR_RR_COILS_START_INDEX": "Start Index", "TR_RR_COILS_START_INDEX_DESC": "Start Index", "TR_RR_DISCRETE_INPUTS_END_INDEX": "End Index", "TR_RR_DISCRETE_INPUTS_END_INDEX_DESC": "End Index", "TR_RR_DISCRETE_INPUTS_START_INDEX": "Start Index", "TR_RR_DISCRETE_INPUTS_START_INDEX_DESC": "Start Index", "TR_RR_HOLDING_REGISTERS_END_INDEX": "End Index", "TR_RR_HOLDING_REGISTERS_END_INDEX_DESC": "End Index", "TR_RR_HOLDING_REGISTERS_START_INDEX": "Start Index", "TR_RR_HOLDING_REGISTERS_START_INDEX_DESC": "Start Index", "TR_RR_INPUT_REGISTERS_END_INDEX": "End Index", "TR_RR_INPUT_REGISTERS_END_INDEX_DESC": "End Index", "TR_RR_INPUT_REGISTERS_START_INDEX": "Start Index", "TR_RR_INPUT_REGISTERS_START_INDEX_DESC": "Start Index", "TR_RSA": "TLS RSA", "TR_RSA_PRIVATE_KEY_FILE": "RSA Private Key File", "TR_RSA_PRIVATE_KEY_PASS_PHRASE": "RSA Private PassPhrase", "TR_RSA_PUBLIC_CERT_FILE": "RSA Public Certificate File", "TR_RUN": "Run", "TR_RUN_NEW_WORKSPACE": "Create & Run New Workspace", "TR_RUN_SELECTED_WORKSPACE": "Run Workspace", "TR_RUNNING": "Engine running", "TR_RUNTIME_PARAMETERS": "Runtime Parameters", "TR_SAVE": "Save", "TR_SAVE_AS_CURRENT_WORKSPACE": "Save A Copy Of Current Workspace", "TR_SAVE_CURRENT_WORKSPACE": "Save Current Workspace", "TR_SAVE_GATEWAY": "Save Workspace", "TR_SAVE_POINTMAP": "CAN'T SAVE POINTMAP", "TR_SAVE_UNMAPPED_POINTS": "Save Unmapped Points", "TR_SAVE_UNMAPPED_POINTS_DESC": "if true, then unmapped tags will be saved to the point mapping file", "TR_SAVE_WORKSPACE": "Save Workspace", "TR_SAVING_INI_CSV": "Saving INI/CSV", "TR_SBO": "SBO", "TR_SBO_DESC": "Specifies if the Data Attribute is Selected Before Operate", "TR_SCL_CATEGORY_...PHYSICAL": "...PHYSICAL", "TR_SCL_CATEGORY_~~~TRANSPORT": "~~~TRANSPORT", "TR_SCL_CATEGORY_+++USER": "+++USER", "TR_SCL_CATEGORY_===APPLICATION": "===APPLICATION", "TR_SCL_CATEGORY_CYCLIC_DATA": "CYCLIC DATA", "TR_SCL_CATEGORY_CYCLIC_HDRS": "CYCLIC HDRS", "TR_SCL_CATEGORY_---DATA_LINK": "---DATA_LINK", "TR_SCL_CATEGORY_EVENT_DATA": "EVENT DATA", "TR_SCL_CATEGORY_EVENT_HDRS": "EVENT HDRS", "TR_SCL_CATEGORY_MMI": "MMI", "TR_SCL_CATEGORY_SECURITY_DATA": "SECURITY DATA", "TR_SCL_CATEGORY_SECURITY_HDRS": "SECURITY HDRS", "TR_SCL_CATEGORY_STATIC_DATA": "STATIC DATA", "TR_SCL_CATEGORY_STATIC_HDRS": "STATIC HDRS", "TR_SCL_CATEGORY_TARGET": "TARGET", "TR_SCL_DATABASE": "Database", "TR_SCL_FILE_NAME": "SCL File", "TR_SCL_FILTER": "SCL Filter", "TR_SCL_PROTOCOL_LAYER": "Protocol Layer", "TR_SDG_CATEGORY_61850": "61850", "TR_SDG_CATEGORY_870": "870", "TR_SDG_CATEGORY_DNP": "DNP", "TR_SDG_CATEGORY_EQUATION": "EQUATION", "TR_SDG_CATEGORY_MODBUS": "MODBUS", "TR_SDG_CATEGORY_ODBC": "ODBC", "TR_SDG_CATEGORY_OPC": "OPC", "TR_SDG_CATEGORY_OPC_DEEP": "OPC DEEP", "TR_SDG_CATEGORY_OPC_SU": "OPC SU", "TR_SDG_CATEGORY_OPC_UA": "OPC UA", "TR_SDG_CATEGORY_POINTMAP": "POINT MAP", "TR_SDG_CATEGORY_TASE2": "ICCP", "TR_SDG_FILTER": "SDG Filter", "TR_SDG_MMS": "MMS", "TR_SDG_OPC": "OPC", "TR_SDG_OTHER": "Other", "TR_SDG_SCL": "SCL", "TR_SDO_BIND_FAIL": "Could not bind SDO.", "TR_SDO_CAN_NOT_SET_OPTIONS": "Could not set SDO options {{arg1}}", "TR_SDO_CREATE_DEFINED_ALREADY": "SDO already defined, cannot create", "TR_SDO_CREATE_FAIL": "Could not create SDO.", "TR_SDO_CREATED": "SDO Created", "TR_SDO_DESC": "Name of the SDO", "TR_SDO_DUPLICATED": "SDO Duplicated", "TR_SDO_FAILED": "SDO Failed", "TR_SDO_NAME": "SDO Name", "TR_SDO_NAME_DESC": "The name of the SDO", "TR_SDO_NOT_EDITABLE": "The Selected SDO is not editable. To change its properties, it will have to be deleted and re-created.", "TR_SDO_OPTIONS": "SDO Options", "TR_SDO_OPTIONS_DESC": "The options of the SDO", "TR_SEARCH": "Search", "TR_SEARCH_CRITERIA": "Search Criteria", "TR_SEC_DIR_NOT_FOUND": "Security directory was not found at: {{arg1}}.", "TR_SEC_FILE_NOT_FOUND": "Security file was not found at: {{arg1}}.", "TR_SEC_CA_CERT_UNIQUE": "Cannot specify the certificate authority file and the certificate authority directory. Only one can be specified.", "TR_SECTOR_DUPLICATE": "Failed to add sector. Sector at address {{arg1}} already exists.", "TR_SECURITY": "Security", "TR_SECURITY_MODE_NONE": "Security Mode None", "TR_SECURITY_MODE_NONE_DESC": "Security Mode None", "TR_SECURITY_MODE_SIGN": "Security Mode Sign", "TR_SECURITY_MODE_SIGN_AND_ENCRYPT": "Security Mode Sign And Encrypt", "TR_SECURITY_MODE_SIGN_AND_ENCRYPT_DESC": "Security Mode Sign And Encrypt", "TR_SECURITY_MODE_SIGN_DESC": "Security Mode Sign", "TR_SECURITY_PARAMETERS": "Security Parameters", "TR_SECURITY_SETUP": "Security", "TR_SELECT": "Select...", "TR_SELECT_ALL": "Select All", "TR_SELECT_FILE": "Select File", "TR_SELECT_LANGUAGE": "Change language", "TR_SELECT_ONE": "Select one:", "TR_SELECT_SERVER": "Select Server", "TR_SELECT_SERVER_DESC": "Select Server", "TR_SEQUENCE_NUMBER": "Sequence Number", "TR_SEQUENCE_NUMBER_DESC": "Specifies if the sequence number is active.", "TR_SERIAL_PORT_SETUP": "Serial Port Setup", "TR_SERVER": "Server", "TR_SERVER_AE_INVOKE_ID": "AE Invoke ID", "TR_SERVER_AE_QUALIFIER": "AE Qualifier", "TR_SERVER_AP_INVOKE_ID": "AP Invoke ID", "TR_SERVER_APP_ID": "AP Title", "TR_SERVER_CALLED": "Remote", "TR_SERVER_INFORMATION": "Server Information", "TR_SERVER_IP_ADDRESS": "Server TCP/IP Address", "TR_SERVER_IP_PORT": "Server TCP/IP Port", "TR_SERVER_LIST": "Server list", "TR_SERVER_LIST_DESC": "Define the Server list", "TR_SERVER_NAME": "Server name", "TR_SERVER_NAME_DESC": "Define the Server name", "TR_SERVER_NODE": "Server Node", "TR_SERVER_NODE_DESC": "Define the Server Node", "TR_SERVER_PRESENTATION_ADDRESS": "Presentation Selector", "TR_SERVER_SESSION_ADDRESS": "Session Selector", "TR_SERVER_SUPPORTED_FEATURES": "Supported Features", "TR_SERVER_TRANSPORT_ADDRESS": "Transport Selector", "TR_SERVICE_INI_FILE_NAME": "Service INI File Name", "TR_SERVICE_PARAMETERS": "Service Parameters", "TR_SESSION_CONFIG": "Session Configuration", "TR_SESSION_DUPLICATE": "Failed to add session. Session {{arg1}} already exists.", "TR_NO_PERIOD_IN_CHAN_NAME_ALLOWED": "Channel Name can not have a period in it: {{arg1}}.", "TR_NO_PERIOD_IN_SESN_NAME_ALLOWED": "Session Name can not have a period in it: {{arg1}}.", "TR_SESSION_LINK_ADDRESS": "Remote Device Link Address", "TR_SESSION_LINK_ADDRESS_DESC": "Data link address of slave component or remote device. Each index identifies a unique session, which is a link layer connection between a Master and a Slave device. Set to 0xffff (65535) for the session to be a \"broadcast session.\". ", "TR_SESSION_LOCAL_ADDRESS": "SDG Local Link Address", "TR_SESSION_LOCAL_ADDRESS_DESC": "Data link address of the local device. This parameter is only used for master or slave sessions using the DNP3 protocol.", "TR_SESSION_NAME": "Session Name", "TR_SESSION_NAME_DESC": "Name of the session", "TR_SET_MDO_OPTIONS_FAILED": "Could not set MDO options {{arg1}}", "TR_SET_TAGNAME_FAILED": "Failed to set user tag name to {{arg1}} (could be a duplicate)", "TR_SETTINGS": "Settings", "TR_SEVEN_LAYER_ADDR": "Seven Layer Stack Settings", "TR_SEVERITY": "Severity", "TR_SHOW_ALL_FCS": "Show all functional constraints", "TR_SHOW_ALL_FCS_DESC": "Change functional constraints display.", "TR_SHOW_HELP": "Show Help", "TR_SHOW_ONLY_ST_MX": "Show only ST/MX", "TR_SHOW_QUICK_START_AT_STARTUP": "Show this Quick Start at Startup", "TR_SHUTTING_DOWN": "Engine shutting down", "TR_SISCO_COMPATABILITY": "SISCO Compatibility", "TR_SLAVE_MASTER_DATA_OBJECT": "Slave/Master Data Object", "TR_SLCT_CONFIRM": "1st of 2 pass confirmed", "TR_SLCT_CONFIRM_DESC": "A 1st pass in a 2-pass control operation has been confirmed by a remote device.", "TR_SLCT_PENDING": "1st of 2 pass pending", "TR_SLCT_PENDING_DESC": "A 1st pass in a 2-pass control operation has been transmitted to a remote device.", "TR_SOE_LOG": "Engine Sequence of Events Log", "TR_SOEQ": "SOE Queue", "TR_SOEQFILE_NAME": "SOE Queue File Name", "TR_SOEQFILE_NAME_DESC": "Sequence Of Events Queue File Name and Path.", "TR_SOURCE": "Source", "TR_SOURCE_NAME": "Source Name", "TR_SOURCE_NAME_DESC": "The name of the source", "TR_START_DATE": "Start date", "TR_START_GATEWAY": "Start Gateway Engine", "TR_STARTUP_CSV_LOADED": "Engine starting CSV loaded", "TR_STARTUP_DONE": "Engine starting done", "TR_STARTUP_ERROR": "Engine Startup error: {{arg1}}.  Please contact Triangle Microworks customer support.", "TR_STARTUP_ERROR_TIMEOUT": "Engine Startup Error: {{arg1}} (retry-cnt = {{arg2}}/30)", "TR_STARTUP_INI_LOADED": "Engine starting INI loaded", "TR_STARTUP_STARTING": "Engine starting", "TR_STARTUP_UNKNOWN": "Engine starting", "TR_STATUS": "STATUS", "TR_STATUS_CODE": "Status Code", "TR_STATUS_CODE_DESC": "Define Status Code", "TR_STOP_GATEWAY": "Stop Gateway Engine", "TR_SU_ROLE": "SUPER USER", "TR_SUBMIT_SUPPORT_REQUEST": "Submit Support Request", "TR_SUBSCRIBED_STREAM": "Subscribed Stream", "TR_SUBSCRIBED_STREAM_DESC": "Subscribed Stream", "TR_SUBSTITUTED": "SUBSTITUTED", "TR_SUBSTITUTED_DESC": "Substituted (over-ride or forced)", "TR_SUCCES_ARG1": "Success: {{arg1}}", "TR_SUCCESS": "Success", "TR_SUPER_USERS": "Super Users", "TR_SUPPORTED_ENDPOINTS": "Supported Endpoints", "TR_SUPPORTED_MESSAGE_SECURITY_POLICIES": "Supported Message Security Policies", "TR_SYSTEM_LOGS": "System Logs", "TR_SYSTEM_MANAGEMENT": "System Management", "TR_SYSTEM_SETTINGS": "System Settings", "TR_SYSTEM_TRACE": "System Trace", "TR_SYSTEMS_LOGS": "Systems logs", "TR_SYSTEMS_LOGS_DESC": "Systems logs", "TR_TABLE_INFO": "Table Info", "TR_TABLE_INFO_DESC": "Specifies the table information", "TR_TABLE_LIST": "Table List", "TR_TABLE_LIST_DESC": "Specifies the list of tables", "TR_TAG_DESCRIPTION": "Description", "TR_TAG_DESCRIPTION_DESC": "Specify the description of the Tag", "TR_TAG_EDIT_": "Edit ", "TR_TAG_NAME": "<PERSON>as Name", "TR_TAG_NAME_DESC": "The Gateway 'Tag Name'", "TR_TAG_NAME_SEARCH_HELP": "$ and * can be used as wild-card character.\n$ is used to represent a single character and * represents any number of characters.", "TR_TAG_OPTIONS": "Options", "TR_TAG_OPTIONS_DESC": "Specify the options of tag", "TR_TAG_QUALITY": "Quality", "TR_TAG_QUALITY_DESC": "Define the quality for the MDO", "TR_TAG_TIME": "Tag Time", "TR_TAG_TIME_DESC": "Define the time for the MDO", "TR_TAG_USER_NAME": "<PERSON>as Name", "TR_TAG_USER_NAME_DESC": "Specifies <PERSON><PERSON> Name", "TR_TAG_VALUE_TYPE": "Value Type:", "TR_TAG_VALUE_TYPE_DESC": "The type of the tag value", "TR_TAGS_DELETED": "Tag(s) Deleted", "TR_TARGET_LAYER": "Target layer", "TR_TARGET_LAYER_DESC": "Log trace messages for target layer", "TR_TASE2_ADD_DS_TRANSFERSET": "Could not add {{arg1}} DS Transfer Set on ICCP server at {{arg2}}. Make sure selected Dataset exists on the server and more transfer sets are available.", "TR_TASE2_ADD_MDO": "Could not add {{arg1}} MDO on 61850 server: {{arg2}}. (duplicate ?)", "TR_TASE2_ADD_MDO_DUPLICATE": "Could not add {{arg1}} MDO on ICCP server: {{arg2}}. (duplicate ?)", "TR_TASE2_ADDING_DATA_ATTR": "Adding data attributes to the dataset failed. See logs for details.  The original dataset has been recreated without the added members. Make sure client model is consistent with server model and try again.", "TR_TASE2_CANNOT_ADD_DA_CLIENT_NOT_CONNECTED": "Cannot add new data attribute because the client is not connected and the SDG cannot validate new data attribute.", "TR_TASE2_CANNOT_ADD_DATA_ATTRIBUTE_TO_VCC_DOMAIN": "Cannot add Data Attribute to VCC Domain", "TR_TASE2_CANNOT_CONNECT_BIDIRECTIONAL_CLIENT_SERVER_IS_NOT_RUNNING": "Cannot connect bidirectional client when its peer-server is not running. Please restart server and try again.", "TR_TASE2_CANNOT_DELETE_DATA_ATTRIBUTE_TO_VCC_DOMAIN": "Cannot delete Data Attribute to VCC Domain", "TR_TASE2_CANNOT_DELETE_DS_BECAUSE_ASSOCIATED_CONTROL_BLOCK": "The dataset '{{arg1}}' cannot be deleted because it is associated with the control block(s): {{arg2}}. Please delete the control block(s) first and then delete the dataset.", "TR_TASE2_CANNOT_DELETE_THE_VCC_DOMAIN": "Error: Cannot Delete the VCC Domain", "TR_TASE2_CANNOT_DISCOVER_MODEL_NOT_EMPTY": "Cannot discover because the client model is not empty. Edit and clear the model and try again.", "TR_TASE2_CANNOT_EDIT_MODEL": "Cannot edit client model while reconnect timer is running. Please set the channel active control to false and then re-try edit.", "TR_TASE2_CANNOT_START_BIDIRECTIONAL": "Cannot start bidirectional client while server is disconnected. Restart Server and try again.", "TR_TASE2_CLIENT_CAN_NOT_LOAD_FILE_NON_EMPTY_MODEL": "Cannot load file '{{arg1}}' because the client has a non empty model. Please clear the model first and try again.", "TR_TASE2_CLIENT_CLIENT_MODEL_IS_EMPTY_AFTER_LOAD": "Client '{{arg1}}' model is empty after loading file. Please make sure file is formatted correctly.", "TR_TASE2_CLIENT_COULD_NOT_LOAD_BAD_FORMAT": "Could not load '{{arg1}}'. Please make sure file is formatted correctly.", "TR_TASE2_CLIENT_DELETE_CLEAR_MODEL_CB": "Cannot delete model when control blocks exist. Please delete the control blocks first and try again.", "TR_TASE2_CLIENT_CANNOT_DISCOVER": "The client '{{arg1}}' has children DataAttribute MDOs or control blocks that must be deleted before a discovery can occur.", "TR_TASE2_CLIENT_DUP_LIC": "Failed to add ICCP Client (could be duplicate, or no license)", "TR_TASE2_CLIENT_DUPLICATE": "Cannot add ICCP client : '{{arg1}}'. Duplicate name.", "TR_TASE2_CLIENT_HAS_CHILD_MDO_MUST_BE_DEL": "The client '{{arg1}}' has children MDOs that must be deleted before the model can be loaded.", "TR_TASE2_CLIENT_NO_MORE": "No more ICCP Clients available", "TR_TASE2_Command_ADD": "Could not add {{arg1}} CommandPointSet on ICCP server: {{arg2}}.", "TR_TASE2_CONNOT_REMOVE_DA_DEPENDENT_ON_DS": "Cannot remove the data attribute because one of its dependent datasets {{arg1}} only has one element and is also contained in at least one control block, so it cannot be deleted. You will have to delete the dependent control blocks first.", "TR_TASE2_CREAT_MDO_NAME": "MDO must have a name, cannot create", "TR_TASE2_CREATE_DS": "Failed to Create Dataset: {{arg1}} on the server. The protocol analyzer may contain more information.", "TR_TASE2_CREATE_MDO_INVALID_TAG": "Could not create {{arg1}} MDO.  Invalid tag name?", "TR_TASE2_CSV_XML_FILE_NAME": ".csv/.xml File", "TR_TASE2_CSV_XML_FILE_NAME_DESC": ".csv/.xml File", "TR_TASE2_DATA_ATTRIBUTE_IS_NOT_SELECTED": "Data Attribute is not selected", "TR_TASE2_DATASET_IS_NOT_SELECTED": "Dataset is not selected", "TR_TASE2_DELETE_DATA_ATTR_MAPPED": "One or more of the data attributes that are being removed are mapped.  Do you want to continue and delete both the data attributes from the dataset and their mappings?", "TR_TASE2_DELETE_DATA_ATTR_MDO": "One or more of the data attributes that are being removed are associated with MDOs. Do you want to continue and delete both the data attributes from the dataset and the corresponding MDOs?", "TR_TASE2_DELETE_DS_NOT_FOUND": "Dataset {{arg1}} not found.", "TR_TASE2_DELETE_IN_USE": "MDO: '{{arg1}}' is mapped to slave points or is used in an equation, cannot delete", "TR_TASE2_DELETE_LAST_POINT_ON_DS": "Deleting the last point in the dataset will delete the dataset {{arg1}}", "TR_TASE2_DELETED_POINT_DEPENDENT_ON_CONTROL_BLOCK": "Error: the point {{arg1}} is dependent on the control block {{arg1}}. Please remove the corresponding MDO or delete the control block before deleting the data attribute.", "TR_TASE2_DISCOVER_WARNING": "Warning discovery is not fully supported in ICCP and the server model may not be correctly discovered for certain types. Discovery will also delete and replace the current model.", "TR_TASE2_DOMAIN_IS_NOT_SELECTED": "Domain is not selected", "TR_TASE2_DOMAIN_VCC_CANT_BE_EDITED": "VCC Domain cannot be edited", "TR_TASE2_DUPLICATE_DOMAIN": "Cannot have duplicate domain names. Please enter a unique name.", "TR_TASE2_ERROR_CANNOT_ADD_DS_NAME_ALREADY_EXISTS": "Error: Cannot add Dataset '{{arg1}}' already exists.", "TR_TASE2_ERROR_WHEN_ADDING_MDO_TO_CONTROL_BLOCK": "Error: occurred when adding MDO '{{arg1}}' to the control block '{{arg2}}'. See logs for errors. NOTE: The MDO and model point will both still be created.", "TR_TASE2_EXPORT_MODEL": "Export Model", "TR_TASE2_EXPORT_MODEL_DESC": "Export current model to CSV", "TR_TASE2_EXPORTED_MODEL_FILENAME": "Exported Model File Name (No extension)", "TR_TASE2_EXPORTED_MODEL_FILENAME_DESC": "Exported Model File Name no extension. An extension of .csv is added", "TR_TASE2_FAILED_TO_CREATE_MDO": "ICCP Client '{{arg1}}' Failed to create MDO '{{arg2}}'.See logs for details.", "TR_TASE2_INVALID_DATASET": "Dataset is invalid: {{arg1}}.", "TR_GENERAL_INVALID_DATASET": "Dataset is invalid.", "TR_TASE2_MAPPING_NEW_DA_DOES_NOT_EXIST": "It appears the new data attribute '{{arg1}}' does not exist on the server (it could not be read). The mapping might fail.", "TR_TASE2_MDO_ALREADY_DEFINED": "MDO already defined, cannot create", "TR_TASE2_MDO_DELETE": "ICCP MDOs cannot be deleted", "TR_TASE2_MDO_DUP_TAG": "Could not set MDO user tag name {{arg1}} (duplicate ?)", "TR_TASE2_NO_Command": "No more ICCP CommandPointSets available", "TR_TASE2_NO_CONTROL_BLOCK": "Not a valid Control Block", "TR_TASE2_NO_CONTROL_BLOCK_CONTROL_BLOCK_MUST_EXIST_TO_PUT_NEW_MDO": "Error: no control blocks exist. A control block must exist to put the new MDO.", "TR_TASE2_NO_EDIT_CONN": "Cannot edit server while connection is up", "TR_TASE2_NO_EDIT_WITH_CLIENTS_CON": "Cannot edit server while clients are connected", "TR_TASE2_NO_MORE": "No more ICCP Clients available", "TR_TASE2_NO_MORE_DS_TRANSFER": "No more DS Transfer Sets available", "TR_TASE2_NO_MORE_LD": "No more Logical Devices available", "TR_TASE2_NO_MORE_POLLED": "No more ICCP PolledPointSets available", "TR_TASE2_NO_MORE_POLLED_DS": "Could not add {{arg1}} PolledPointSet on ICCP server: {{arg2}}.", "TR_TASE2_NO_POINTS": "No ICCP points available", "TR_TASE2_NO_POLLED": "No polled dataset items available", "TR_TASE2_NONE_LEFT": "No more ICCP Clients available", "TR_TASE2_OPT_MDO": "Could not set MDO options {{arg1}}", "TR_TASE2_REMOVING_DATA_ATTR": "Removing data attributes from the dataset failed. See logs for details. The dataset has been restored to the original but the MDOs will be permanently deleted if the configuration is saved. It is recommended to close the SDG withOUT saving and start over.", "TR_TASE2_SELECT": "Failed to select control - nothing done.", "TR_TASE2_SELECT_INTEGER": "Write Value is not valid - must be an integer", "TR_TASE2_SELECT_WRONG_VALUE": "Write Value is not valid - must be a decimal number", "TR_TASE2_SERVER_ADD_POLLED_DS": "Could not add {{arg1}} PolledDataSet on ICCP server: {{arg2}}.", "TR_TASE2_SERVER_DUP_LIC": "Failed to add ICCP Server (could be duplicate, or no license)", "TR_TASE2_SET_MDO": "Could not set MDO options {{arg1}}", "TR_GENERAL_VALID_DATASET": "Dataset is valid - no errors detected.", "TR_TASE2CERT_AUTH_CHAINING_VER_DEPTH": "Certificate Authority Chaining Verification Depth", "TR_TASE2CERT_AUTH_CHAINING_VER_DEPTH_DESC": "Maximum length of 'chained' certificates that is allowed.  This must be greater than 0 to be valid", "TR_TASE2CERTIFICATE_AUTHORITY_FILE": "Certificate Authority File", "TR_TASE2CERTIFICATE_AUTHORITY_FILE_DESC": "If it is not NULL, it specifies a file containing the public master CA certificates.  The file (PEM format) may contain the certificates of multiple CAs", "TR_TASE2CERTIFICATE_AUTHORITY_REVOKE_LIST_FILE": "Certificate Authority Revoke List File", "TR_TASE2CERTIFICATE_AUTHORITY_REVOKE_LIST_FILE_DESC": "If it is not NULL, it specifies a file containing the public master CA revoked certificates.  The file (PEM format) may contain multiple revoked certificates.", "TR_TASE2CLIENT_AEINVOKE_ID": "AE Invoke ID", "TR_TASE2CLIENT_AEINVOKE_ID_DESC": "Specifies the AE Invoke ID. Value from 0 to 65536.", "TR_TASE2CLIENT_AEQUALIFIER": "AE Qualifier", "TR_TASE2CLIENT_AEQUALIFIER_DESC": "Specifies the AE Qualifier. Value from 0 to 65536.", "TR_TASE2CLIENT_APINVOKE_ID": "AP Invoke ID", "TR_TASE2CLIENT_APINVOKE_ID_DESC": "Specifies the AP Invoke ID. Value from 0 to 65536.", "TR_TASE2CLIENT_APP_ID": "AP Title", "TR_TASE2CLIENT_APP_ID_DESC": "ACSE AP title value. Use OSI Object Identifier '1,3,9999,X' where 'X' is a user defined integer", "TR_TASE2CLIENT_CONNECT_TIMEOUT": "MMS Timeout (ms)", "TR_TASE2CLIENT_CONNECT_TIMEOUT_DESC": "Specifies the MMS connect timeout for the ICCP Client.  After starting a connection attempt this is how int to wait for success.", "TR_TASE2CLIENT_CONNECTING_PORT": "TCP/IP Port", "TR_TASE2CLIENT_CONNECTING_PORT_DESC": "Specifies the port to which the client will try to connect on the specified Remote TCP/IP address. The default is 102.", "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME": "Dataset Transfer Set buffer time (secs)", "TR_TASE2CLIENT_DSTRANSFER_SET_BUFFER_TIME_DESC": "Specifies the Buffer Time attribute for a Dataset Transfer Set", "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL": "Dataset Transfer Set interval (secs)", "TR_TASE2CLIENT_DSTRANSFER_SET_INTERVAL_DESC": "Specifies the Interval attribute of a Dataset Transfer Set", "TR_TASE2CLIENT_DSTRANSFER_SET_RBE": "Dataset Transfer Set Report By Exception", "TR_TASE2CLIENT_DSTRANSFER_SET_RBE_DESC": "Specifies the Report By Exception attribute of a Dataset Transfer Set", "TR_TASE2CLIENT_INITIATE": "Initiate a connection", "TR_TASE2CLIENT_INITIATE_DESC": "Applies to Client and Client/Server only. Specifies if the client should initiate a connection upon startup of the SDG application.", "TR_TASE2CLIENT_PRESENTATION_ADDRESS": "Presentation Selector (OSI-PSEL)", "TR_TASE2CLIENT_PRESENTATION_ADDRESS_DESC": "Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT": "Reconnect re-try count", "TR_TASE2CLIENT_RECONNECT_RETRY_COUNT_DESC": "Specifies the reconnect re-try count for the ICCP Client (0 = attempt reconnects for ever)A successful connection will cause the internal limit counter to be re-set to 0resulting in continued connection attempts to the ICCP server.", "TR_TASE2CLIENT_RECONNECT_TIME": "Reconnect timeout (ms)", "TR_TASE2CLIENT_RECONNECT_TIME_DESC": "Specifies the reconnect timeout for the ICCP Client (0 = no reconnect)", "TR_TASE2CLIENT_RFC_IP_ADDR": "ICCP Client RFC TCP/IP Address", "TR_TASE2CLIENT_RFC_IP_ADDR_DESC": "Specifies the TCP/IP address (NIC) that the client will use for connecting to the server.", "TR_TASE2CLIENT_SESSION_ADDRESS": "Session Selector (OSI-SSEL)", "TR_TASE2CLIENT_SESSION_ADDRESS_DESC": "Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_TASE2CLIENT_TRANSPORT_ADDRESS": "Transport Selector (OSI-TSEL)", "TR_TASE2CLIENT_TRANSPORT_ADDRESS_DESC": "The local IEC 61850 Clients Transport Selector. Shall be limited to no more than 8 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_TASE2CLT_POLLED_POINT_SET_NAME": "Polled point set name", "TR_TASE2CLT_POLLED_POINT_SET_NAME_DESC": "Specifies the name of the polled point set", "TR_TASE2CLT_POLLED_POINT_SET_PERIOD": "Polled Point Set Period (ms)", "TR_TASE2CLT_POLLED_POINT_SET_PERIOD_DESC": "Specifies the period to read the polled point set", "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME": "ICCP domain of Reported Dataset", "TR_TASE2CLT_REPORTED_DSDOMAIN_NAME_DESC": "Specifies the domain of a Reported Dataset on an ICCP server", "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD": "Dataset Transfer Set period (secs)", "TR_TASE2CLT_REPORTED_DSINTEGRITY_PERIOD_DESC": "Specifies the period of integrity updates of a Dataset Transfer Set", "TR_TASE2CLT_REPORTED_DSNAME": "Reported Dataset name", "TR_TASE2CLT_REPORTED_DSNAME_DESC": "Specifies the name of a Reported Dataset on an ICCP server", "TR_TASE2MMSCOMMON_NAME": "MMS Common Name", "TR_TASE2MMSCOMMON_NAME_DESC": "Common Name to require from peer certificates (or NULL string if no CommonName check is to be done)", "TR_TASE2MMSPRIVATE_KEY_FILE": "Private Key File", "TR_TASE2MMSPRIVATE_KEY_FILE_DESC": "Path name of the file containing the private RSA key (PEM format) to be used in generating the ACSE signatures as required by IEC 62351-4.", "TR_TASE2MMSPRIVATE_KEY_PASS_PHRASE": "Private Key PassPhrase", "TR_TASE2MMSPRIVATE_KEY_PASS_PHRASE_DESC": "Pass phrase for decrypting the RSA private key.", "TR_TASE2MMSPUBLIC_CERTIFICATE_FILE": "Public Certificate File", "TR_TASE2MMSPUBLIC_CERTIFICATE_FILE_DESC": "File containing the RSA certificate (PEM format) corresponding to the private key.  The certificate must be signed by a Certificate Authority recognized by the peer systems", "TR_TASE2SECURITY_ON": "Enable security", "TR_TASE2SECURITY_ON_DESC": "Tells the application to connect using the security parameters. If security is turned on, all certificate paths have to be valid except for the revocation list file.", "TR_TASE2SERVER_AEINVOKE_ID": "AE Invoke ID", "TR_TASE2SERVER_AEINVOKE_ID_DESC": "Specifies the ASCE AE Invoke ID. Value from 0 to 65536.", "TR_TASE2SERVER_AEQUALIFIER": "AE Qualifier", "TR_TASE2SERVER_AEQUALIFIER_DESC": "Specifies the AE Qualifier. Value from 0 to 65536.", "TR_TASE2SERVER_APINVOKE_ID": "AP Invoke ID", "TR_TASE2SERVER_APINVOKE_ID_DESC": "Specifies the AP Invoke ID. Value from 0 to 65536.", "TR_TASE2SERVER_APP_ID": "AP Title", "TR_TASE2SERVER_APP_ID_DESC": "ACSE AP title value. Use OSI Object Identifier '1,3,9999,X' where 'X' is a user defined integer", "TR_TASE2SERVER_IPADDRESS": "ICCP Server TCP/IP Address", "TR_TASE2SERVER_IPADDRESS_DESC": "Specifies the TCP/IP address that the client will connect to. The address 127.0.0.1 specifies the local machine that the SDG is running on.", "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID": "Bilateral Table ID", "TR_TASE2SERVER_LOGICAL_DEVICE_BI_LATERAL_TABLE_ID_DESC": "Specifies the Bilateral Table ID", "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT": "Number of DS Transfer sets", "TR_TASE2SERVER_LOGICAL_DEVICE_DS_TRANSFER_SET_COUNT_DESC": "Specifies the number of DS Transfer sets", "TR_TASE2SERVER_LOGICAL_DEVICE_NAME": "Logical device name", "TR_TASE2SERVER_LOGICAL_DEVICE_NAME_DESC": "Specifies the name of a logical device on a ICCP Server", "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED": "Maximum number of clients", "TR_TASE2SERVER_MAX_CONNECTIONS_ALLOWED_DESC": "If specified, this setting controls how many clients can connect to the server. 0 means no client can connect. Empty means there is no limit.", "TR_TASE2SERVER_PRESENTATION_ADDRESS": "Presentation Selector (OSI-PSEL)", "TR_TASE2SERVER_PRESENTATION_ADDRESS_DESC": "Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_TASE2SERVER_RFC_IP_ADDR": "ICCP Server RFC TCP/IP Address", "TR_TASE2SERVER_RFC_IP_ADDR_DESC": "Specifies the TCP/IP address that the Server will listen on for connecting clients. 0.0.0.0 means use local default TCP/IP", "TR_TASE2SERVER_SESSION_ADDRESS": "Session Selector (OSI-SSEL)", "TR_TASE2SERVER_SESSION_ADDRESS_DESC": "Shall be limited to no more than 16 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_TASE2SERVER_SUPPORTED_FEATURES": "ICCP Server Supported Features", "TR_TASE2SERVER_SUPPORTED_FEATURES_DESC": "Specifies the ICCP blocks that are reported to connecting clients as Supported_Features", "TR_TASE2SERVER_TRANSPORT_ADDRESS": "Transport Selector (OSI-TSEL)", "TR_TASE2SERVER_TRANSPORT_ADDRESS_DESC": "Shall be limited to no more than 8 characters. The value shall contain an even number of visible characters. Characters shall be limited to 0 to 9 and A to F.", "TR_TASE2SERVICE_ROLE": "Service Role", "TR_TASE2SERVICE_ROLE_DESC": "Specifies the ICCP Service Role", "TR_TASE2SYNC_DATA_SETS": "Sync datasets", "TR_TASE2SYNC_DATA_SETS_DESC": "Applies to Client and only. If true, the client will attempt to adjust its local datasets to match servers model.", "TR_TASE2TLSCOMMON_NAME": "TLS Common Name", "TR_TASE2TLSCOMMON_NAME_DESC": "Common Name to require from peer certificates (or NULL string if no CommonName check is to be done)", "TR_TASE2TLSMAX_PDUS": "Max PDUs Before Forcing Cipher Renegotiation", "TR_TASE2TLSMAX_PDUS_DESC": "Maximum number of ISO PDUs (RFC1006) to allow between cipher renegotiations.  This must be a minimum of 5000 (see IEC 62351-4).", "TR_TASE2TLSMAX_RENEGOTIATION_WAIT_TIME": "Max Renegotiate Wait Time (ms)", "TR_TASE2TLSMAX_RENEGOTIATION_WAIT_TIME_DESC": "Maximum interval (milliseconds) to allow between the server's request for cipher renegotiation and the client's renegotiation request.  If this timeout is exceeded, the connection is terminated.", "TR_TASE2TLSRENEGOTIATION": "Renegotiation (sec)", "TR_TASE2TLSRENEGOTIATION_DESC": "Time interval (in seconds) to allow between cipher renegotiations.  This must be a minimum of 10 minutes (see IEC 62351-4).", "TR_TASE2TLSRSAPRIVATE_KEY_FILE": "RSA Private Key File", "TR_TASE2TLSRSAPRIVATE_KEY_FILE_DESC": "File containing the private RSA key (PEM format).", "TR_TASE2TLSRSAPRIVATE_KEY_PASS_PHRASE": "RSA Private Key Pass Phrase", "TR_TASE2TLSRSAPRIVATE_KEY_PASS_PHRASE_DESC": "Pass phrase for decrypting the RSA private key.", "TR_TASE2TLSRSAPUBLIC_CERT_FILE": "TLS RSA Public Certificate File", "TR_TASE2TLSRSAPUBLIC_CERT_FILE_DESC": "File containing the RSA certificate (PEM format) corresponding to the private key.  The certificate must be signed by a Certificate Authority recognized by the peer systems.", "TR_TASE2USE_SISCO_COMPATABILITY": "Use ED1 Compatibility", "TR_TASE2USE_SISCO_COMPATABILITY_DESC": "Specifies if the security settings use ED1 compatibility or SISCO Compatibility", "TR_TASE2VERSION": "ICCP Application Version", "TR_TASE2VERSION_DESC": "Specifies the ICCP Application Version", "TR_TEMPORARY_PASSWORD": "Temporary Password", "TR_TEST": "TEST", "TR_TEST_DESC": "Test", "TR_TEST_MODE": "Test mode", "TR_TEST_MODE_DESC": "Either the data point or the remote device is operating in a test mode.", "TR_TEST_ODBC_CONNECTION": "Test ODBC connection string", "TR_TEST_ODBC_CONNECTION_DESC": "Test ODBC connection string", "TR_THE_CURRENT_PASSWORD_IS_REQUIRED": "The Current Password is required.", "TR_THE_MONITOR_AND_GATWAY_NEED_TO_RESTART_DO_YOU_WANT_TO_SAVE_YOUR_CURRENT_WORKSPACE": "The Monitor and Engine need to restart.<br>Do you want to save your current Workspace?", "TR_THE_NEW_PASSWORD_MUST_BE_AT_LEAST_CHARACTERS_LONG": "The New Password must be at least 6 characters long.", "TR_THE_TWO_PASSWORD_FIELDS_DIDN_T_MATCH": "The two password fields didn't match.", "TR_THE_USER_ADMIN_CAN_NOT_BE_DELETED": "The user admin cannot be deleted.", "TR_THE_USERNAME_X_IS_ALREADY_USED": "The username {{username}} is already used.", "TR_THERE_WERE_PROBLEMS_SAVING_THE_INI_CSV_FILES": "There were problems saving the INI/CSV files. Please correct the problem(s) manually or use the 'Settings > System Settings  >Force Save Current Workspace' which may result in loss of data.", "TR_THIS_SYSTEM": "This SDG System", "TR_THIS_USER_IS_NOT_ACTIVE": "This user is not active", "TR_THRESHOLD": "<PERSON><PERSON><PERSON><PERSON>", "TR_TIME": "Time", "TR_TIME_AND_TIMING": "Time and Timing", "TR_TIME_FLAG_TOOLTIP_HELP": "'A' All fields of the date and time were assumed based on the date and time the event was received.\n'D'  The date was assumed based on the date and time the event was received. The time is as reported from the remote device.\n'-D' The event was assumed to have occurred in the day previous to the one in which the event was received because the reported hours were significantly greater than the current hours.\n'+D' The event was assumed to have occurred in the day after the one in  which the event was received because the reported hour was near 0 and the current hour was near 23.\n'H'  The hour and date were assumed based on the date and time the event was received. The minutes, seconds and milliseconds are as reported from the remote device.  \n'-H' The event was assumed to have occurred in the hour previous to the one in which the event was received because the reported minutes were significantly greater than the current minutes.\n'+H' The event was assumed to have occurred in the hour after the one in which the event was received because the reported minutes were near 0 and the current.\n'I'  The date/time is being reported by the remote device as being invalid.\n'N'  need Time IIN is asserted by remote DNP3 device, but local device is unable to synchronize.\n'R'  As reported from remote device.\n'U'  As reported in DNP3 object 2, variation 3.", "TR_TIME_STAMP": "Time Stamp", "TR_TIME_STAMP_DESC": "Specifies if the time stamp is active.", "TR_TIME_ZONE_BIAS": "Time Zone Bias", "TR_LOG_TIME_ZONE_CHANGED": "The GLOBAL log timezone has been changed. A full SDG restart is required for all logs to use the new timezone.", "TR_TIME_ZONE_NAME": "Time Zone (Display Only)", "TR_TIME_ZONE_NAME_DESC": "The name of the time zone to use for the SDG display time (empty string/default will set to UTC).  Note: SDG uses UTC for internal times.  UseTimeZoneClock must be true.", "TR_TLS": "TLS", "TR_TLS_COMMON_NAME": "Common Name", "TR_TLS_CONFIG": "62351-3 Security Configuration", "TR_TLS_HANDSHAKE_TIMEOUT": "TLS Handshake Timeout", "TR_TLS_MAX_PDUS": "Max PDUs Before Forcing Cipher Renegotiation", "TR_TLS_MAX_RENEG_WAIT_TIME": "Max Renegotiation Wait Time (ms)", "TR_TLS_RENEGOTIATION": "Renegotiation (sec)", "TR_TLS_RENEGOTIATION_COUNT": "Renegotiation Count", "TR_TLS_RENEGOTIATION_SECONDS": "Renegotiation (sec)", "TR_TRIGGER_OPTIONS": "Trigger Options", "TR_TRUST_REJECTED_CERTIFICATE": "Trust Selected Certificate", "TR_TS_CONDITIONS_DETECTED": "Include TS Conditions Detected", "TR_TS_CONDITIONS_DETECTED_DESC": "Include TS Conditions Detected", "TR_TS_SET_NAME": "Include Transfer Set Name", "TR_TS_SET_NAME_DESC": "Include Transfer Set Name", "TR_TS_SET_TIMESTAMP": "Include Transfer Set Time-stamp", "TR_TS_SET_TIMESTAMP_DESC": "Include Transfer Set Time-stamp", "TR_TYPE": "Type", "TR_UNABLE_EDIT_WHILE_TASE2_SERVER_RUNNING": "Unable to edit while ICCP Server running.", "TR_UNABLE_TO_CONNECT_TO_LICENSING_SERVER_PLEASE_CHECK_YOUR_INTERNET_CONNECTION": "Error: Unable to connect to licensing server. Please check your Internet connection.", "TR_UNAUTHORIZED": "Unauthorized - Login/Password incorrect", "TR_UNAUTHORIZED_MAX_FAILED_LOGIN_ATTEMPT_REACHED": "Too many failed login attempts. Wait for up to 1 minute to try again", "TR_UNAUTHORIZED_SU_ALREADY_LOGIN": "A Superuser is already logged in.", "TR_UNAUTHORIZED_WS": "Unauthorized - local IP only is enabled and HTTPS/WSS is disabled", "TR_UNDEFINED": "UNDEFINED ERROR", "TR_UNINITIALIZED": "Uninitialized", "TR_UNINITIALIZED_DESC": "Not set since startup", "TR_UNKNOWN": "Unknown", "TR_UNKNOWN_DESC": "The data is being updated, but the reason for the update is unknown.", "TR_UNLOCK_SCROLL": "Free Scroll", "TR_UNSELECT_ALL": "Unselect All", "TR_UNSOLICITED_UDP_PORT": "Unsolicited UDP Port", "TR_UNSPECIFIED_ERROR": "Unspecified error", "TR_UPDATE": "Check for update", "TR_UPLOAD_NEW_CONFIGURATION_FILE": "Upload New Configuration file", "TR_UPLOAD_NEW_CSV_FILE": "Upload new CSV file", "TR_UPLOAD_NEW_FILE": "Upload New File", "TR_UPLOAD_NEW_INI_FILE": "Upload new INI file", "TR_USE": "Use", "TR_USE_DEFLATE": "Use gzip compression", "TR_USE_LOCALHOST_FOR_COMMS": "Use localhost for communications between Engine and Monitor", "TR_USE_REPORTED_TIME": "Use Reported Time For MDOs", "TR_USE_REPORTED_TIME_DESC": "if true, do not update time on static updates this applies to MDOs that can be reported as events ", "TR_USE_SCL_FILE": "Use SCL File", "TR_USE_SYSTEM_TIME": "Use System Clock", "TR_USE_SYSTEM_TIME_DESC": "If true always read the date and time from the Windows system clock as opposed to an internally maintained clock. The internal clock is initialized to the Windows system clock at startup but will be adjusted whenever a clock synchronization is received from an external master. You would generally set UseSystemTime to true if you have an external clock synchronization mechanism that synchronizes the Windows system clock outside the SCADA Data Gateway, in this case it is advised that AcceptClockSync be set to false. ", "TR_USE_TIME_ZONE_CLOCK": "Use Time Zone Clock (Display Only)", "TR_USE_TIME_ZONE_CLOCK_DESC": "If true display the date and time for the SDG in the specified TimeZoneName ", "TR_USE_WEB_SSL": "Use Web SSL/HTTPS", "TR_USER_LIST": "User List", "TR_USER_LIST_DESC": "User List", "TR_USER_LOGGED_OUT_SUCCEFULLY": "User logged out successfully", "TR_USER_TAG_NAME": "User Tag Name", "TR_USER_TAG_NAME_DESC": "The user tag name of the MDO", "TR_USER_X_DELETED": "User {{username}} deleted.", "TR_USERNAME": "Username", "TR_USERNAME_DESC": "Username", "TR_USERNAME_IS_REQUIRED": "Username is required.", "TR_USERNAME_IS_REQUIRED_CHARACTERS_MINIMUM": "Username is required (4 characters minimum).", "TR_USERS": "Users", "TR_USERS_MANAGEMENT": "Users Management", "TR_VALID_EQUATION_SUCCESS": "Success: Valid equation", "TR_VALIDATE": "ERROR VALIDATING INI FILE", "TR_VALIDATE_CONFIG": "Validate Configuration", "TR_VALIDATE_EQUATION": "Validate Equation", "TR_VALIDATE_UDP_ADDRESS": "Validate UDP Address", "TR_VALUE": "Value", "TR_VERBOSE": "Verbose", "TR_VERIFY_CERTIFICATE": "Verify HTTPS Certificate", "TR_VERSION": "Version", "TR_VERSION_UP_TO_DATE": "Your version of SCADA Data Gateway {{currentVersion}} is up to date.", "TR_VERTICAL_DISPLAY": "Vertical Display", "TR_VIEW": "View", "TR_VIEWER_ROLE": "VIEWER", "TR_VIEWS": "Special Views", "TR_WARNING": "Warning", "TR_WARNING_DESC": "Warning", "TR_WARNING_VIEW": "Warning View", "TR_WEB_BROWSER_CANNOT_CONNECT_GATEWAY_ENGINE_ACCEPT_SELFSIGNED_CERTIFICATE_FIREFOX": "Web browser cannot connect to the Gateway Engine.<br>If you use a self-signed certificate please add a certificate exception for the Gateway Engine by opening: <a href='{{URL_ENGINE}}/redirect.html' target='_self'>{{URL_ENGINE}}/redirect.html</a>.<br>If not check the Gateway Engine status or the Gateway Engine server firewall settings.", "TR_WEB_BROWSER_CANNOT_CONNECT_GATEWAY_ENGINE_CHECK_ENGINE_SERVER_FIREWALL": "Web browser cannot connect to the Gateway Engine. Please check the Gateway Engine status or the Gateway Engine server firewall settings.", "TR_WEB_SERVER": "Web Server", "TR_WEB_SERVER_DESC": "Log trace messages for web server", "TR_WEBSOCKET_CLOSE": "WebSocket {{websocketName}} close: {{reason}}", "TR_WEBSOCKET_OPEN": "WebSocket {{websocketName}} open", "TR_WEBSOCKET_UPDATE_PARAMETERS": "WebSocket Update Parameters", "TR_WHAT_NAME_DO_YOU_TO_USE_FOR_YOUR_NEW_WORKSPACE": "What name do you want to use for your new Workspace", "TR_WIN_TIMER_FAILED": "A fatal error has occurred: {{arg1}}. The SDG will now try to exit. Please see logs for more details.", "TR_WORKSPACE": "Workspace", "TR_WORKSPACE_DELETED": "Workspace Deleted", "TR_WORKSPACE_IMPORTED": "Workspace Imported", "TR_WORKSPACE_MANAGEMENT": "Workspaces", "TR_YES": "Yes", "TR_YOUR_BROWSER_WILL_REFRESH_IN_X_SECONDS": "Your browser will refresh in {{value}} seconds.", "TR_YOUR_PASSWORD_DOESNT_MEET_COMPLEXITY_REQUIREMENTS": "Your password doesn't meet complexity requirements:<ul><li>Must contains at least one lowercase characters.</li><li>Must contains at least one uppercase characters.</li><li>Must contains at least one digit from 0-9.</li><li>Must contains at least one special symbols in this list: '@, $, #, !, -, +, *, _, %, .'</li><li>Length must be at least 8 characters and a maximum of 24.</li></ul>", "TR_ADMIN_USER_IS_NOT_CONFIGURED": "The admin user is not configured. Please use the GTWSettings tool to configure the admin user.", "TR_61850_CLIENT_SECURITY_NEED_PASSWORD": "61850 Client security need a password defined.", "TR_OPC_UA_ADD_MULTIPLE_ITEM_COMPLETE": "Add multiple items complete : {{arg1}} items added.", "TR_OPC_UA_ADD_MULTIPLE_ITEM_FAIL": "Failed to add multiple items. See logs for details.", "TR_OPC_UA_ADD_MULTIPLE_ITEM_COMPLETE_FAIL": "Add multiple items complete : {{arg1}} items added, {{arg2}} items failed to add (unsupported types - see logs for details).", "TR_MENU_CMD_CHANGE_61850_DATASET": "Change IEC 61850 Dataset", "TR_OPTIONAL_ITEMS_BE_INCLUDED": "Optional Items to Included In the Report", "TR_LICENSING_HELP": "Guide to Triangle MicroWorks Licensing", "TR_ERROR_TASE2_LOAD_MODEL": "Error: Fail to load model file.", "TR_61850_CHANGE_PORT_FOR_SECURITY": "IEC 61850 Specification requires the port number {{port}} to be used for communication when security is not enabled.\nWould you like to switch to port {{port}}?", "TR_SEC_CA_CERT_MISSING": "Cannot specify the certificate authority file and the certificate authority directory. Only one can be specified.", "TR_ODBC_QUERY_NO_PERIOD": "The ODBC Query name can not have a '.' in it.", "TR_DEVICE": "<PERSON><PERSON>", "TR_MESSAGE_SEVERITY": "Messages severity level", "TR_GLOBAL_MESSAGE_SEVERITY": "Global message severity", "TR_SCL_MESSAGE_SEVERITY": "SCL message severity", "TR_MMS_MESSAGE_SEVERITY": "MMS message severity", "TR_MAINTENACE_EXPIRED": "Your maintenance has expired this version of SDG is too recent for your current license. Please contact support. All devices are disabled.", "TR_61850_CLIENT_NO_CONTROLLABLE_ELEMENTS_FOUND": "No controllable elements found", "TR_TASE2_COMMAND_POINT_SET_DUPLICATE": "Cannot add ICCP Command Point Set: '{{arg1}}'. Duplicate name.", "TR_TASE2_POLLED_POINT_SET_DUPLICATE": "Cannot add ICCP Polled Point Set: '{{arg1}}'. Duplicate name.", "TR_61850_POLLED_POINT_SET_DUPLICATE": "Cannot add 61850 Polled Point Set: '{{arg1}}'. Duplicate name.", "TR_61850_COMMAND_POINT_SET_DUPLICATE": "Cannot add 61850 Command Point Set: '{{arg1}}'. Duplicate name.", "TR_61850_WRITABLE_POINT_SET_DUPLICATE": "Cannot add 61850 Writable Point Set: '{{arg1}}'. Duplicate name.", "TR_61400_ALARM_DUPLICATE": "Cannot add 61400 Alarm: '{{arg1}}'. Duplicate name.", "TR_EQUATION_ARGUMENT": "Equation Argument", "TR_ERROR_MDO_LIST_UNAVAILABLE": "Equation Argument Unavailable", "TR_GATEWAY_MAX_LOG_BUFFER_ENTRIES": "Maximum Log Buffer Entries", "TR_IS_REQUIRED_NUMERICAL_0_TO_100000": " is required (numerical between 0 and 100000).", "TR_WEBSOCKET_REOPENING": "WebSocket {{websocketName}} reopening, reconnect attempt: {{reconnectAttempt}}.", "TR_WEBSOCKET_RE_SEND": "WebSocket {{websocketName}} re-send info attempt: {{sendToWebsocketAttempts}}.", "TR_ERROR_IN_INI_FILE_PARSE_NUMBER_OF_ERROR": "Error parsing INI file: {{arg1}} error(s) found. The individual error(s) will be in the log.", "TR_FOLDER_ALREADY_HAS": "Error: a member or subfolder already has the name '{{arg1}}' in the folder '{{arg2}}'. Please use a different name. User Folders cannot have duplicates."}