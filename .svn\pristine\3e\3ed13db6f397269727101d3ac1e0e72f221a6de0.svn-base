/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTW61850SlaveDataObject.cpp                                                  */
/* DESCRIPTION:  Definitions of an 61850-specific SDO class                    */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* MPP_COMMAND_AUTO_TLIB_FLAG " %v %l "                                      */
/* " 37 ***_NOBODY_*** "                                                      */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#include "stdafx.h"
#include "GTW61850Server.h"

#include "../thirdPartyCode/OPCToolkit/development/C++/src/Enums.h"
#include "GTW61850SlaveDataObject.h"
#include "GTWM14DoublePointMdo.h"
#include "GTWM14SinglePointMdo.h"
#include "GTWDnpDoubleBinaryInputMdo.h"
#include "GTW61850DataAttributeMDO.h"
#include "GTW61850SdoEditor.h"
#include "GTWM14StepPointCommandMdo.h"

#if defined(_DEBUG) && defined(_WIN32)
#define new DEBUG_CLIENTBLOCK
#endif


ImplementClassBaseInfo(GTW61850SlaveDataObject, GTWBaseDataObject, pClassInfo1, new GTW61850SlaveDataObject())

template <class T>
class GTW61850WriteConverterT : public GTW61850WriteConverter
{
protected:
  GTW61850SlaveDataObject* m_pSdo;
  GTWWriteConverterTemplate<T, GTWSlaveDataObject>* m_pWriteCnvtr;

public:
  GTW61850WriteConverterT(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<T, GTWSlaveDataObject>* pWriteCnvtr)
    :
    m_pSdo(pSdo),
    m_pWriteCnvtr(pWriteCnvtr)
  {}

  virtual ~GTW61850WriteConverterT() { Delete(); }

protected:
  void Delete() override
  {
    if (m_pWriteCnvtr)
    {
      delete m_pWriteCnvtr;
      m_pWriteCnvtr = nullptr;
    }
  }

public:
  bool select(tmw61850::ServerControlPoint* controlPoint) override;

  virtual bool write(tmw61850::ServerControlPoint* controlPoint, GTWDEFS_CTRL_MODE ctrlMode)
  {
    tmw61850::DataAttribute* pDA = controlPoint->GetControlValueDA(tmw61850::ServerControlPoint::ControlPointType::Oper);

    //TRACE("GTW61850WriteConverterT writing value : %s\n", pDA->GetBooleanValue() ? "true" : "false");
    m_pSdo->m_ctrlStat = writeValue(pDA, ctrlMode);

    if (IS_PENDING(m_pSdo->m_ctrlStat))
    {
      int nOperTimeout = controlPoint->GetOperTimeOut();
      if (nOperTimeout == 0 || nOperTimeout == -1) // probably requesting infinite, so we set to 1 minute, which should be plenty long enough
      {
        nOperTimeout = 60000; // one minute
      }
      DWORD iPause = 200;
      int nTimeCount = 0;
      while (IS_PENDING(m_pSdo->m_ctrlStat))
      {
        tmw::Thread::SleepMS(iPause);
        nTimeCount += iPause;
        if (nTimeCount > nOperTimeout)
        {
          m_pSdo->m_ctrlStat = GTWDEFS_CTRL_STAT_FAILURE;

          tmw::String sTemp;
          const char* sFullPath = controlPoint->GetFullName(sTemp);

          CStdString sErr;
          sErr.Format("Operate timed out for '%s' waiting on remote operate to finish. The operate timeout may need to be increased for control point '%s'", sFullPath, sFullPath);
          //          TMWDIAG_ERROR(sErr.c_str());
          LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "%s", sErr.c_str());
          break;
        }
      }
    }

    return m_pSdo->m_ctrlStat == GTWDEFS_CTRL_STAT_SUCCESS;
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override = 0;
};

/*
class c61850EndOperateWorkItem : public WorkItemBase
{
public:
  c61850EndOperateWorkItem(GTW61850SlaveDataObject* pSdo, bool bSucceeded)
    :
    m_pSdo(pSdo),
    m_bOperateSucceeded(bSucceeded)
  {

  }

  virtual void DoWork(void* param)
  {
    std::unique_ptr<c61850EndOperateWorkItem> deleteThisOnReturn(this);
    m_pSdo->DoEndOperate(m_bOperateSucceeded);
  }

  virtual void Abort() {}

  bool m_bOperateSucceeded;
  GTW61850SlaveDataObject* m_pSdo;
};
*/

class GTW61850ReadConverterBool : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<bool>* m_pOpcBoolReadCnvtr;

public:
  GTW61850ReadConverterBool(GTWReadConverterTemplate<bool>* pBoolReadCnvtr) :
    m_pOpcBoolReadCnvtr(pBoolReadCnvtr)
  {
  }

  ~GTW61850ReadConverterBool() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);

protected:
  virtual void Delete(void)
  {
    if (m_pOpcBoolReadCnvtr)
    {
      delete m_pOpcBoolReadCnvtr;
      m_pOpcBoolReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterChar : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_CHAR>* m_pOpcCharReadCnvtr;

public:
  GTW61850ReadConverterChar(GTWReadConverterTemplate<TMWTYPES_CHAR>* pCharReadCnvtr) :
    m_pOpcCharReadCnvtr(pCharReadCnvtr)
  {
  }

  ~GTW61850ReadConverterChar() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcCharReadCnvtr)
    {
      delete m_pOpcCharReadCnvtr;
      m_pOpcCharReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterShort : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_SHORT>* m_pOpcShortReadCnvtr;

public:
  GTW61850ReadConverterShort(GTWReadConverterTemplate<TMWTYPES_SHORT>* pShortReadCnvtr) :
    m_pOpcShortReadCnvtr(pShortReadCnvtr)
  {
  }

  ~GTW61850ReadConverterShort() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcShortReadCnvtr)
    {
      delete m_pOpcShortReadCnvtr;
      m_pOpcShortReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterDouble : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_DOUBLE>* m_pOpcDoubleReadCnvtr;

public:
  GTW61850ReadConverterDouble(GTWReadConverterTemplate<TMWTYPES_DOUBLE>* pDoubleReadCnvtr) :
    m_pOpcDoubleReadCnvtr(pDoubleReadCnvtr)
  {
  }

  ~GTW61850ReadConverterDouble() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcDoubleReadCnvtr)
    {
      delete m_pOpcDoubleReadCnvtr;
      m_pOpcDoubleReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterINT64 : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_INT64>* m_pReadCnvtr;

public:
  GTW61850ReadConverterINT64(GTWReadConverterTemplate<TMWTYPES_INT64>* pInt64ReadCnvtr) :
    m_pReadCnvtr(pInt64ReadCnvtr)
  {
  }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
  virtual void Delete(void)
  {
    if (m_pReadCnvtr)
    {
      delete m_pReadCnvtr;
      m_pReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterSFloat : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_SFLOAT>* m_pOpcSfloatReadCnvtr;

public:
  GTW61850ReadConverterSFloat(GTWReadConverterTemplate<TMWTYPES_SFLOAT>* pSfloatReadCnvtr) :
    m_pOpcSfloatReadCnvtr(pSfloatReadCnvtr)
  {
  }

  ~GTW61850ReadConverterSFloat() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcSfloatReadCnvtr)
    {
      delete m_pOpcSfloatReadCnvtr;
      m_pOpcSfloatReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterLong : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_INT>* m_pOpcLongReadCnvtr;

public:
  GTW61850ReadConverterLong(GTWReadConverterTemplate<TMWTYPES_INT>* pLongReadCnvtr) :
    m_pOpcLongReadCnvtr(pLongReadCnvtr)
  {
  }

  ~GTW61850ReadConverterLong() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcLongReadCnvtr)
    {
      delete m_pOpcLongReadCnvtr;
      m_pOpcLongReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterULong : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_UINT>* m_pOpcULongReadCnvtr;

public:
  GTW61850ReadConverterULong(GTWReadConverterTemplate<TMWTYPES_UINT>* pULongReadCnvtr) :
    m_pOpcULongReadCnvtr(pULongReadCnvtr)
  {
  }

  ~GTW61850ReadConverterULong() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcULongReadCnvtr)
    {
      delete m_pOpcULongReadCnvtr;
      m_pOpcULongReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterUShort : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_USHORT>* m_pOpcUShortReadCnvtr;

public:
  GTW61850ReadConverterUShort(GTWReadConverterTemplate<TMWTYPES_USHORT>* pUShortReadCnvtr) :
    m_pOpcUShortReadCnvtr(pUShortReadCnvtr)
  {
  }

  ~GTW61850ReadConverterUShort() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcUShortReadCnvtr)
    {
      delete m_pOpcUShortReadCnvtr;
      m_pOpcUShortReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterUChar : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWTYPES_UCHAR>* m_pOpcUCharReadCnvtr;

public:
  GTW61850ReadConverterUChar(GTWReadConverterTemplate<TMWTYPES_UCHAR>* pUCharReadCnvtr) :
    m_pOpcUCharReadCnvtr(pUCharReadCnvtr)
  {
  }

  ~GTW61850ReadConverterUChar() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcUCharReadCnvtr)
    {
      delete m_pOpcUCharReadCnvtr;
      m_pOpcUCharReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterTMWDTIME : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<TMWDTIME>* m_pOpcTmwdtimeReadCnvtr;

public:
  GTW61850ReadConverterTMWDTIME(GTWReadConverterTemplate<TMWDTIME>* pTmwdtimeReadCnvtr) :
    m_pOpcTmwdtimeReadCnvtr(pTmwdtimeReadCnvtr)
  {
  }

  ~GTW61850ReadConverterTMWDTIME() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcTmwdtimeReadCnvtr)
    {
      delete m_pOpcTmwdtimeReadCnvtr;
      m_pOpcTmwdtimeReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850ReadConverterString : public GTW61850ReadConverter
{
private:
  GTWReadConverterTemplate<CStdString>* m_pOpcStringReadCnvtr;

public:
  GTW61850ReadConverterString(GTWReadConverterTemplate<CStdString>* pStringReadCnvtr) :
    m_pOpcStringReadCnvtr(pStringReadCnvtr)
  {
  }

  ~GTW61850ReadConverterString() { Delete(); }

  virtual void GTW61850_getValue(GTW61850SlaveDataObject* pOpcSdo, GTWDEFS_STD_QLTY* stdQuality);
protected:
  virtual void Delete(void)
  {
    if (m_pOpcStringReadCnvtr)
    {
      delete m_pOpcStringReadCnvtr;
      m_pOpcStringReadCnvtr = TMWDEFS_NULL;
    }
  }
};

/*****************************************************************************/

class GTW61850WriteConverterString : public GTW61850WriteConverterT<CStdString>
{
public:
  GTW61850WriteConverterString(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject>* pStringWriteCnvtr) :
    GTW61850WriteConverterT<CStdString>(pSdo, pStringWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterBin : public GTW61850WriteConverterT<bool>
{
public:
  GTW61850WriteConverterBin(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<bool, GTWSlaveDataObject>* pBinWriteCnvtr)
    :
    GTW61850WriteConverterT<bool>(pSdo, pBinWriteCnvtr)
  {
  }



  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};


/*****************************************************************************/

class GTW61850WriteConverterChar : public GTW61850WriteConverterT<TMWTYPES_CHAR>
{
public:
  GTW61850WriteConverterChar(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject>* pCharWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_CHAR>(pSdo, pCharWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};


/*****************************************************************************/

class GTW61850WriteConverterUChar : public GTW61850WriteConverterT<TMWTYPES_UCHAR>
{
public:
  GTW61850WriteConverterUChar(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject>* pUCharWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_UCHAR>(pSdo, pUCharWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterShort : public GTW61850WriteConverterT<TMWTYPES_SHORT>
{
public:
  GTW61850WriteConverterShort(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject>* pShortWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_SHORT>(pSdo, pShortWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterUShort : public GTW61850WriteConverterT<TMWTYPES_USHORT>
{
public:
  GTW61850WriteConverterUShort(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject>* pWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_USHORT>(pSdo, pWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterLong : public GTW61850WriteConverterT<TMWTYPES_LONG>
{
public:
  GTW61850WriteConverterLong(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject>* pLongWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_INT>(pSdo, pLongWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterULong : public GTW61850WriteConverterT<TMWTYPES_ULONG>
{
public:
  GTW61850WriteConverterULong(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject>* pWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_UINT>(pSdo, pWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterSFloat : public GTW61850WriteConverterT<TMWTYPES_SFLOAT>
{
public:
  GTW61850WriteConverterSFloat(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject>* pSfloatWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_SFLOAT>(pSdo, pSfloatWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterDouble : public GTW61850WriteConverterT<TMWTYPES_DOUBLE>
{
public:
  GTW61850WriteConverterDouble(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject>* pDoubleWriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_DOUBLE>(pSdo, pDoubleWriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};

/*****************************************************************************/

class GTW61850WriteConverterINT64 : public GTW61850WriteConverterT<TMWTYPES_INT64>
{
public:
  GTW61850WriteConverterINT64(GTW61850SlaveDataObject* pSdo, GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject>* pInt64WriteCnvtr) :
    GTW61850WriteConverterT<TMWTYPES_INT64>(pSdo, pInt64WriteCnvtr)
  {
  }

  GTWDEFS_CTRL_STAT writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode) override;
};


/**********************************************************************************\
Function :			GTW61850SlaveDataObject::GTW61850SlaveDataObject
Description : [none]
Return :			constructor	-
Parameters :
void	-
Note : [none]
\**********************************************************************************/
GTW61850SlaveDataObject::GTW61850SlaveDataObject() :
  m_pReadCnvtr(TMWDEFS_NULL),
  m_pWriteCnvtr(TMWDEFS_NULL),
  m_updateReason(GTWDEFS_UPDTRSN_UNKNOWN)
{
  m_p61850ValueDa = NULL;
  m_p61850TimeDa = NULL;
  m_p61850QualityDa = NULL;
  m_sValueDaName = "";
  m_sTimeDaName = "";
  m_sQualityDaName = "";

  //m_value.ChangeType(GTWDEFS_TYPE_UNKNOWN);
  m_value = 0;
  m_pMyMdo = TMWDEFS_NULL;
  m_quality = GTWDEFS_STD_QLTY_INVALID;
  m_native61850Quality = I61850_QUALITY_VALIDITY_QUESTIONABLE;
  m_timestamp.UTCNow();
  //  m_accessRights = 0;
  m_61850Type = GTW61850_TYPE_UNKNOWN;
  m_bSynchronizeDownstream = false;

  m_bOperating = false;
  m_bTimedOut = false;
  m_bIsCommandSDO = false;
  m_bUseNativeQuality = false;
}

GTW61850SlaveDataObject::~GTW61850SlaveDataObject()
{
  LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "61850 Server removing item: %s", m_sMemberName.c_str());
  if (m_pReadCnvtr)
  {
    delete m_pReadCnvtr;
    m_pReadCnvtr = NULL;
  }
  if (m_pWriteCnvtr)
  {
    delete m_pWriteCnvtr;
    m_pWriteCnvtr = NULL;
  }
}

bool GTW61850SlaveDataObject::FormatQualityString(char* buffer, int bufferSize, GTWEXPND_EXPANSION* pExpansion)
{
  CStdString sQualityBS;
  GTW61850DataAttributeMDO::Get61850QualityBitString(m_native61850Quality, sQualityBS);

  snprintf(buffer, bufferSize, "gtw:%04x,6:%s",
    this->getStdQuality(), sQualityBS.c_str());

  return true;
}

GTWBaseEditor *GTW61850SlaveDataObject::GetBaseEditor(const EditorCommandDTO &dto)
{
  if (!m_pEditor)
  {
    GTW61850Server *pServer = GetServer();
    m_pEditor = new GTW61850SdoEditor(dto, pServer, this, false);
  }
  else
    m_pEditor->SetDTO(dto);

  return m_pEditor;
}


void GTW61850SlaveDataObject::CleanUserData()
{
  if (m_p61850ValueDa)
    m_p61850ValueDa->SetUserData(NULL);
  m_p61850ValueDa = NULL;

  if (m_p61850TimeDa)
    m_p61850TimeDa->SetUserData(NULL);
  m_p61850TimeDa = NULL;

  if (m_p61850QualityDa)
    m_p61850QualityDa->SetUserData(NULL);
  m_p61850QualityDa = NULL;

}

bool GTW61850SlaveDataObject::IsCommandSDO(void)
{
  if (m_bIsCommandSDO)
  {
    return true;
  }

  if (m_p61850ValueDa)
  {
    const char* sFunctionalConstraint = m_p61850ValueDa->GetFC();
    return tmw::util::compareNoCase(sFunctionalConstraint, "co");
  }

  return false;
}

bool GTW61850SlaveDataObject::isWritable()
{
  if (m_p61850ValueDa)
  {
    const char* sFunctionalConstraint = m_p61850ValueDa->GetFC();
    if (sFunctionalConstraint && GTW61850DataAttributeMDO::isWriteableFC(sFunctionalConstraint))
    {
      return true;
    }
  }
  return this->isCommand();
}

bool GTW61850SlaveDataObject::bindSdoCommandWithMdo(GTWMasterDataObject* pMdo)
{
  assert(IsCommandSDO());
  void* writeCnvtr;

  m_pWriteCnvtr = nullptr;

// Special handling for a "TapChg" type command --> tmw61850::EnumDefs::TCMD type enum
  if (m_p61850ValueDa && m_p61850ValueDa->IsEnum())
  {
    //tmw61850::EnumDefs::TCMD v = tmw61850::EnumDefs::TCMD::higher;

    tmw::String sTemp;
    const char* sn = m_p61850ValueDa->GetFullName(sTemp);
    // We are assuming here the name TapChg for the higher/lower behavior in the Tcmd type. If needed, we could get the ServerControlPoint and see if the status name is 'posVal'
    if (CStdString(sn).find("TapChg") > 0)
    {
      if (dynamic_cast<GTWM14StepPointCommandMdo*>(pMdo) == nullptr)
      {
        LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "Can only map a TapChg command to a 101/104 StepPointCommand");
        return false;
      }

      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_6TAP_CHANGE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterUChar(this, (GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
  }

  if (m_pWriteCnvtr == nullptr)
  {
    switch (m_value.GetType())
    {
    case GTWDEFS_TYPE_BOOL:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterBin(this, (GTWWriteConverterTemplate<bool, GTWSlaveDataObject> *) writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_CHAR:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterChar(this, (GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_SHORT:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterShort(this, (GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_USHORT:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterUShort(this, (GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_LONG:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterLong(this, (GTWWriteConverterTemplate<TMWTYPES_LONG, GTWSlaveDataObject> *) writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_ULONG:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterULong(this, (GTWWriteConverterTemplate<TMWTYPES_ULONG, GTWSlaveDataObject> *) writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_SFLOAT:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterSFloat(this, (GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *)writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_DOUBLE:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
      }
      break;
    case GTWDEFS_TYPE_STRING:
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterString(this, (GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject> *)writeCnvtr);
      }
      break;
    } // switch
  }

  if (m_pWriteCnvtr == nullptr && m_value.GetType() != GTWDEFS_TYPE_DOUBLE) // then try double as a fallback
  {
    if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
    }
  }

  if (!m_pWriteCnvtr)
  {
    return false;
  }

  updateSDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);
  m_pMyMdo = pMdo;

  return true;
}

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::bindSdoWithMdoNative
Description : [none]
Return :			bool	-
Parameters :
GTWMasterDataObject *pMdo	-
Note : [none]
\**********************************************************************************/
/*
bool GTW61850SlaveDataObject::bindSdoWithMdoNative(GTWMasterDataObject *pMdo)
{
  bool isWriteAllowed = !IsCommandSDO();
  void *readCnvtr;
  void *writeCnvtr;

  // Initialize converters
  m_pReadCnvtr  = TMWDEFS_NULL; // only readable if set non null below
  m_pWriteCnvtr = TMWDEFS_NULL; // only writable if set non null below

  switch(m_value.GetType())
  {
  case GTWDEFS_TYPE_BOOL:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterBool((GTWReadConverterTemplate<bool> *) readCnvtr);
      if (isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterBin(this, (GTWWriteConverterTemplate<bool, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_CHAR:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterChar((GTWReadConverterTemplate<TMWTYPES_CHAR> *) readCnvtr);
      if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterChar(this, (GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_SHORT:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterShort((GTWReadConverterTemplate<TMWTYPES_SHORT> *) readCnvtr);
      if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterShort(this, (GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_USHORT:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterUShort((GTWReadConverterTemplate<TMWTYPES_USHORT> *) readCnvtr);
      if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterUShort(this, (GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_LONG:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterLong((GTWReadConverterTemplate<TMWTYPES_INT> *) readCnvtr);
      if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterLong(this, (GTWWriteConverterTemplate<TMWTYPES_INT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_ULONG:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterULong((GTWReadConverterTemplate<TMWTYPES_UINT> *) readCnvtr);
      if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterULong(this, (GTWWriteConverterTemplate<TMWTYPES_UINT, GTWSlaveDataObject> *) writeCnvtr);
      }
      else if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_SFLOAT:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterSFloat((GTWReadConverterTemplate<TMWTYPES_SFLOAT> *) readCnvtr);
      if (isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterSFloat(this, (GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_DOUBLE:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterDouble((GTWReadConverterTemplate<TMWTYPES_DOUBLE> *)readCnvtr);
      if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_INT64:
    if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterINT64((GTWReadConverterTemplate<TMWTYPES_INT64> *)readCnvtr);
      if (isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterINT64(this, (GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_STRING:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterString((GTWReadConverterTemplate<CStdString> *)readCnvtr);
      if(isWriteAllowed && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterString(this, (GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;
  case GTWDEFS_TYPE_TIME:
    if((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_TMWDTIME)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterTMWDTIME((GTWReadConverterTemplate<TMWDTIME> *) readCnvtr);
    }
    break;
  } // switch

  if (m_pReadCnvtr == TMWDEFS_NULL && m_pWriteCnvtr == TMWDEFS_NULL)
  {
    return TMWDEFS_FALSE;
  }

  updateSDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);

  m_pMyMdo = pMdo;
  return(TMWDEFS_TRUE);
}
*/

void GTW61850SlaveDataObject::bindSdoWithMdoWithUnknownType(GTWMasterDataObject* pMdo)
{
  bool bSetupWrite = this->isWritable(); // if this SDO is writable then we setup a write converter so this SDO can write to the MDO
  bool bSetupRead = !pMdo->isControl(); // if the mdo can be updated and write back to this SDO, then setup a read converter to update this SDO

  if (bSetupWrite)
  {
    assert(pMdo->isCommandMDO()); // if this SDO is writable, the mdo it is mapped to must be writable
  }

  void* readCnvtr;
  void* writeCnvtr;

  if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterBool((GTWReadConverterTemplate<bool> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterBin(this, (GTWWriteConverterTemplate<bool, GTWSlaveDataObject> *) writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterChar((GTWReadConverterTemplate<TMWTYPES_CHAR> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterChar(this, (GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject> *) writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterUChar((GTWReadConverterTemplate<TMWTYPES_UCHAR> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterUChar(this, (GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject> *) writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterUShort((GTWReadConverterTemplate<TMWTYPES_USHORT> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterUShort(this, (GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject> *) writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterShort((GTWReadConverterTemplate<TMWTYPES_SHORT> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterShort(this, (GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *) writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterULong((GTWReadConverterTemplate<TMWTYPES_ULONG> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterULong(this, (GTWWriteConverterTemplate<TMWTYPES_ULONG, GTWSlaveDataObject> *) writeCnvtr);
    }
    else if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *) writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterLong((GTWReadConverterTemplate<TMWTYPES_LONG> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterLong(this, (GTWWriteConverterTemplate<TMWTYPES_LONG, GTWSlaveDataObject> *) writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterSFloat((GTWReadConverterTemplate<TMWTYPES_SFLOAT> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterSFloat(this, (GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *)writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterDouble((GTWReadConverterTemplate<TMWTYPES_DOUBLE> *)readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterINT64((GTWReadConverterTemplate<TMWTYPES_INT64> *)readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterINT64(this, (GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject> *)writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterString((GTWReadConverterTemplate<CStdString> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterString(this, (GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject> *)writeCnvtr);
    }
  }
  else if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_TMWDTIME)) != TMWDEFS_NULL)
  {
    if (bSetupRead)
    {
      m_pReadCnvtr = new GTW61850ReadConverterTMWDTIME((GTWReadConverterTemplate<TMWDTIME> *) readCnvtr);
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_TMWDTIME)) != TMWDEFS_NULL)
    {
      assert(false); // we dont have a GTW61850WriteConverterTMWDTIME
    }
  }
}

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::bindSdoWithMdo
Description : Binds the SDO with an MDO	This function is called after
the SDO has been created. This is not done by the constructor
because the MDO is not usually known in the scope in which
the SDO is created.
Return :			bool	-
Parameters :
GTWMasterDataObject *pMdo	-
Note : [none]
\**********************************************************************************/
bool GTW61850SlaveDataObject::bindSdoWithMdo(GTWMasterDataObject *pMdo)
{
  m_pMyMdo = pMdo;

  // do native mapping of quality if source protocol is same as destination protocol
  GTW61850DataAttributeMDO* p61850Mdo = dynamic_cast<GTW61850DataAttributeMDO*>(pMdo);
  if (p61850Mdo == nullptr)
  {
    this->m_bUseNativeQuality = false;
  }
  else
  {
    this->m_bUseNativeQuality = true;
  }

  if (this->IsCommandSDO())
  {
    return bindSdoCommandWithMdo(pMdo);
  }

  bool bSetupWrite = this->isWritable(); // if this SDO is writable then we setup a write converter so this SDO can write to the MDO
  bool bSetupRead = !pMdo->isControl(); // we assume that real client controls are not expected to be updated/read and write back to this SDO

  if (!bSetupWrite && !bSetupRead)
  {
    return false;
  }

  if (bSetupWrite)
  {
    assert(pMdo->isWriteable()); // if this SDO is writable, the mdo it is mapped to must be writable
  }

  // first try native type
  //if (bindSdoWithMdoNative(pMdo))
  //{
  //  return TMWDEFS_TRUE;
  //}

  m_pReadCnvtr = TMWDEFS_NULL;
  m_pWriteCnvtr = TMWDEFS_NULL;
  void* readCnvtr;
  void* writeCnvtr;

  switch (m_value.GetType())
  {
  case GTWDEFS_TYPE_BOOL:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterBool((GTWReadConverterTemplate<bool> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_BOOL)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterBin(this, (GTWWriteConverterTemplate<bool, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_CHAR:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterChar((GTWReadConverterTemplate<TMWTYPES_CHAR> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_CHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterChar(this, (GTWWriteConverterTemplate<TMWTYPES_CHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_UCHAR:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterUChar((GTWReadConverterTemplate<TMWTYPES_UCHAR> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_UCHAR)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterUChar(this, (GTWWriteConverterTemplate<TMWTYPES_UCHAR, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_SHORT:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterShort((GTWReadConverterTemplate<TMWTYPES_SHORT> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterShort(this, (GTWWriteConverterTemplate<TMWTYPES_SHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_USHORT:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterUShort((GTWReadConverterTemplate<TMWTYPES_USHORT> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_USHORT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterUShort(this, (GTWWriteConverterTemplate<TMWTYPES_USHORT, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_LONG:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterLong((GTWReadConverterTemplate<TMWTYPES_LONG> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_LONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterLong(this, (GTWWriteConverterTemplate<TMWTYPES_LONG, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_ULONG:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterULong((GTWReadConverterTemplate<TMWTYPES_ULONG> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_ULONG)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterULong(this, (GTWWriteConverterTemplate<TMWTYPES_ULONG, GTWSlaveDataObject> *) writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_SFLOAT:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterSFloat((GTWReadConverterTemplate<TMWTYPES_SFLOAT> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_SFLOAT)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterSFloat(this, (GTWWriteConverterTemplate<TMWTYPES_SFLOAT, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_DOUBLE:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterDouble((GTWReadConverterTemplate<TMWTYPES_DOUBLE> *)readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_INT64:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterINT64((GTWReadConverterTemplate<TMWTYPES_INT64> *)readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_INT64)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterINT64(this, (GTWWriteConverterTemplate<TMWTYPES_INT64, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_STRING:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterString((GTWReadConverterTemplate<CStdString> *) readCnvtr);
      }
    }
    if (bSetupWrite)
    {
      if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_STRING)) != TMWDEFS_NULL)
      {
        m_pWriteCnvtr = new GTW61850WriteConverterString(this, (GTWWriteConverterTemplate<CStdString, GTWSlaveDataObject> *)writeCnvtr);
      }
    }
    break;

  case GTWDEFS_TYPE_TIME:
    if (bSetupRead)
    {
      if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_TMWDTIME)) != TMWDEFS_NULL)
      {
        m_pReadCnvtr = new GTW61850ReadConverterTMWDTIME((GTWReadConverterTemplate<TMWDTIME> *) readCnvtr);
      }
    }
    if (bSetupWrite && (writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_TMWDTIME)) != TMWDEFS_NULL)
    {
      assert(false); // do have write converter for TMWDTIME
      //m_pWriteCnvtr = new GTW61850WriteConverterTMWDTIME(this, (GTWWriteConverterTemplate<TMWDTIME, GTWSlaveDataObject> *)writeCnvtr);
    }

  default:
    {
    assert(false);
    LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "61850 SDO '%s' trying to map to unsupported type : %s", GetFullName().c_str(), m_p61850ValueDa->GetTypeAsXMLString());
    }
  }

  if (!m_pWriteCnvtr && !m_pReadCnvtr) // try other types that may be converted to m_value type
  {
    bindSdoWithMdoWithUnknownType(pMdo);
  }

  if (bSetupWrite && m_pWriteCnvtr == TMWDEFS_NULL && m_value.GetType() != GTWDEFS_TYPE_DOUBLE) // then try double as a fall back
  {
    if ((writeCnvtr = pMdo->bindMdoToSdoForWriting(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pWriteCnvtr = new GTW61850WriteConverterDouble(this, (GTWWriteConverterTemplate<TMWTYPES_DOUBLE, GTWSlaveDataObject> *)writeCnvtr);
    }
  }
  if (bSetupRead && m_pReadCnvtr == TMWDEFS_NULL && m_value.GetType() != GTWDEFS_TYPE_DOUBLE) // then try double as a fall back
  {
    if ((readCnvtr = pMdo->bindMdoToSdoForReading(this, GTWCNVTR_TYPE_DOUBLE)) != TMWDEFS_NULL)
    {
      m_pReadCnvtr = new GTW61850ReadConverterDouble((GTWReadConverterTemplate<TMWTYPES_DOUBLE> *)readCnvtr);
    }
  }

  if (m_pReadCnvtr != nullptr)
  {
    m_pReadCnvtr->pSrcMdo = p61850Mdo; // tell the read converter who our source is (for native quality mapping)
    updateServerValue();
  }

  updateSDO(GTWDEFS_UPDTRSN_REFRESH, pMdo);
  return TMWDEFS_TRUE;
}

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::getFullName
Description : [none]
Return :			CStdString	-
Parameters :
void	-
Note : [none]
\**********************************************************************************/
CStdString GTW61850SlaveDataObject::gtw_getFullName(void)
{
  GTWBaseDataObject *pBdo = this->getBdo();
  if (pBdo != TMWDEFS_NULL)
  {
    return pBdo->GetFullName();
  }
  return "";
}

/*
void GTW61850SlaveDataObject::UpdateInternalControlValues(tmw61850::ServerControlPoint* pControlPoint)
{
  try
  {
    tmw61850::DataAttribute* pTime = nullptr;
    tmw61850::DataAttribute* pQuality = nullptr;

    tmw61850::DataAttribute* pDA = pControlPoint->GetControlValueDA();
    if (pDA)
    {
      m_value.AssignFrom61850Value(*(pDA->GetValue()));

      //tmw::String sn;
      //pDA->GetFullName(sn);

      tmw::String cpName;
      pControlPoint->GetFullName(cpName);

      CStdString sTime = (const char*)cpName;
      sTime += ".t";
      pTime = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(sTime.c_str(), this->GetServer()->GetServerConnection()->Model());

      CStdString sQual = (const char*)cpName;
      sQual += ".q";
      pQuality = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(sQual.c_str(), this->GetServer()->GetServerConnection()->Model());
    }

    //tmw::String sv;
    //pDA->GetValueAsString(sv);

    if (pQuality)
    {
      const tmw::BitString* pBS = pQuality->GetBitStringValue();
      m_quality = GTW61850DataAttributeMDO::getStdQuality(pBS);
    }

    // Set Internal Time
    if (pTime && pTime->GetDateTime())
    {
      TMWDTIME dTime;
      GTW61850Client::SixTSDateTimeToTMWDTime(*pTime->GetDateTime(), dTime);
      m_timestamp.SetFromTMWDTime(&dTime);

      SetReportedTime(&dTime);
      SetUpdatedTime(&dTime);
    }

    tmw61850::DataAttribute* pStatusDA = pControlPoint->GetStatusPointValueDA();
    if (pStatusDA)
    {
      GTW61850SlaveDataObject* pStatusControl = GetServer()->GetSdo(pStatusDA);
      if (pStatusControl)
      {
        bool r = pStatusControl->GTW61850_write(pStatusDA);
        if (!r)
        {
          LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "Update control status mapping failed for %s", pStatusControl->GetFullName().c_str());
        }
      }
    }
  }
  catch (...)
  {
    LOG(GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, nullptr, "%s", "GTW61850SlaveDataObject::Set61850VTQValue caught exception");
  }
}
*/

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::updateSDO
Description :   Called whenever an MDO update an SDO. Used to update
the OPC SDO's time stamp. The actual data is updated by the
retrieve() function.
Return :			void	-
Parameters :
GTWDEFS_UPDTRSN updateReason	-
GTWMasterDataObject *pMdo	-
Note : [none]
\**********************************************************************************/
void GTW61850SlaveDataObject::updateSDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo)
{
  if (m_bOperating)
  {
    return;
  }

  if (!pMdo)
  {
    return;
  }

  GTWDEFS_TIME_QLTY timeQuality;
  TMWDTIME          timeStamp;
  GTWBaseDataObject *pBdo = pMdo->getBdo();
  if (pBdo == NULL)
  {
    return;
  }

  m_updateReason = updateReason;

  // Dont use current timezone, use UTC - in keeping 3.06 implementation
  pBdo->getMdoReportedTime(&timeStamp, &timeQuality, GetGTWApp()->gtwUtcTimeZone);
  if (timeQuality & GTWDEFS_TIME_QLTY_INVALID)
  {
    // Dont use current timezone, use UTC - in keeping 3.06 implementation
    pBdo->getMdoUpdatedTime(&timeStamp, &timeQuality, GetGTWApp()->gtwUtcTimeZone);
  }

  if (timeStamp.invalid == true)
  {
    // ? use last reported timestamp instead of updated time
    m_timestamp.UTCNow();
  }
  else
  {
    m_timestamp.SetFromTMWDTime(&timeStamp);
  }

  m_pMyMdo = pMdo;

  //TRACE("GTW61850SlaveDataObject::updateSDO called for %s\n", this->GetFullName().c_str());
  assert(m_pMyMdo == NULL || m_pMyMdo == pMdo);
  if (m_pMyMdo == NULL)
  {
    m_pMyMdo = pMdo;
  }

  this->updateServerValue();
  }

// This should only be called on a StatusControl SDO to update after the ctlVal has been updated because
// this status point gets updated internally and we need to propogate the change if it is mapped
/*
void GTW61850SlaveDataObject::updateSDO(GTWDEFS_UPDTRSN updateReason, GTW61850SlaveDataObject *pSdo)
{
  m_updateReason = updateReason;
  if (pSdo != this)
  {
    if (this->m_p61850ValueDa && pSdo->m_p61850ValueDa)
    {
      *this->m_p61850ValueDa = *pSdo->m_p61850ValueDa;
      if (m_pWriteCnvtr)
      {
        this->m_pWriteCnvtr->writeValue(this->m_p61850ValueDa);
      }
      //this->m_p61850ValueDa->GetValueAsVariant(m_value);
      GTW61850DataAttributeMDO::AssignFrom61850DataAttribute(m_value, m_p61850ValueDa);

      this->SetCurrentTime();
    }
    if (this->m_p61850QualityDa && pSdo->m_p61850QualityDa)
    {
      *this->m_p61850QualityDa = *pSdo->m_p61850QualityDa;
    }
    if (this->m_p61850TimeDa && pSdo->m_p61850TimeDa)
    {
      *this->m_p61850TimeDa = *pSdo->m_p61850TimeDa;
    }
  }

  int sz = this->m_BoundMdosForWriteList.getSize();
  for (int i = 0; i < sz; i++)
  {
    GTWMasterDataObject *pMdo = m_BoundMdosForWriteList[i];
    pMdo->updateMDO(updateReason);
  }
}
*/

void GTW61850SlaveDataObject::UpdateExtRefChanges()
{
  if (m_p61850ValueDa)
  {
    if (!m_value.AssignFrom61850Value(*m_p61850ValueDa->GetValue()))
    {
      LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s has an unsuported type : %s\n", GetFullName().c_str(), m_p61850ValueDa->GetTypeAsXMLString());
    }
  }

  if (m_p61850TimeDa)
  {
    tmw61850::DateTime curDATime;
    m_p61850TimeDa->GetDateTime(curDATime);

    TMWDTIME curTmwDTime;
    GTW61850Client::SixTSDateTimeToTMWDTime(curDATime, curTmwDTime);
    m_timestamp.SetFromUTCTime((const GtwOsDateTime::UTCBTime*)curDATime.GetAsUTCTime());
  }

  if (m_p61850QualityDa)
  {
    tmw::BitString *bitString = const_cast<tmw::BitString*> (m_p61850QualityDa->GetBitStringValue());

    unsigned int lQuality = bitString->GetIntValue();
    setQuality((GTWDEFS_STD_QLTY)lQuality);
  }

  if (!m_p61850TimeDa)
  {
    TMWDTIME curTmwDTime;
    GtwTimeZone::SetCurrentUtcTime(curTmwDTime);
    m_timestamp.SetFromTMWDTime(&curTmwDTime);
  }
}

bool GTW61850SlaveDataObject::updateServerValue()
{
  //LOG(GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, nullptr, "GTW61850SlaveDataObject::updateServerValue: name=%s\n", this->GetFullName().c_str());

  if (GetGTWApp()->IsShuttingDown() == true)
  {
    return false;
  }

  //
  // Here updatesdo is being called when the ext ref updates its MDO, so we ignore this because the MDO is the destination, NOT this when this is an ExtRef
  //
  if (IsExtRef())
  {
    return true;
  }

  try
  {
    GTWDEFS_STD_QLTY stdQuality;
    if (m_pReadCnvtr)
    {
      m_pReadCnvtr->GTW61850_getValue(this,&stdQuality);

      tmw61850::DataAttribute *p61850ValueDA = Get61850ValueDa();
      tmw61850::DataAttribute *p61850TimeDA = Get61850TimeDa();
      tmw61850::DataAttribute *p61850QualityDA = Get61850QualityDa();
      Set61850VTQValue(p61850ValueDA, p61850TimeDA, p61850QualityDA);
    }
  }
  catch(...)
  {
    LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s", "GTW61850SlaveDataObject::retrieve failed, exception caught");

    // need to get this SDOs collection and delete this item from it
    GTWCollectionBase *pCollection = (GTWCollectionBase *)GetParentCollection();
    if (pCollection)
    {
      pCollection->RemoveCollectionMember(this);
    }
    // since the MDO no longer exists set quality
    setQuality(GTWDEFS_STD_QLTY_NOT_TOPICAL);
    return false; // EnumResultCode_E_OPC_UNKNOWNITEMID;
  }
  return(true);
}


//void GTW61850SlaveDataObject::Set61850Flags(tmw::61850Flags *pFlags, GTWDEFS_STD_QLTY stdQuality)
//{
//  pFlags->ClearFlags();
//
//  pFlags->SetValidityNotValid();
//
//  if(stdQuality == GTWDEFS_STD_QLTY_GOOD)
//    pFlags->SetValidityValid();
//
//  if(stdQuality & GTWDEFS_STD_QLTY_NOT_TOPICAL)
//    pFlags->SetValidityHeld();
//
//  if(stdQuality & GTWDEFS_STD_QLTY_UNINITIALIZED)
//    pFlags->SetValiditySuspect();
//
//  //if(updateReason == GTWDEFS_UPDTRSN_CTRL_AT_DEVICE)
//  //  pFlags->SetValidityValid();
//
//  //if(updateReason == GTWDEFS_UPDTRSN_CTRL_BY_COMM)
//  //  pFlags->SetValidityValid();
//
//  //if(stdQuality & GTWDEFS_STD_QLTY_IN_TRANSIT)
//  //  pFlags->SetValidityValid();
//
//  //if(stdQuality & GTWDEFS_STD_QLTY_REF_ERROR)
//  //  pFlags->SetValidityValid();
//
//  //if(stdQuality & GTWDEFS_STD_QLTY_OVERFLOW)
//  //  pFlags->SetValidityValid();
//}

//void GTW61850SlaveDataObject::Set61850StateFlags(tmw::61850StateQ *pStateFlags, GTWDEFS_STD_QLTY stdQuality)
//{
//  pStateFlags->ClearFlags();
//
//  pStateFlags->SetValidityNotValid();
//
//  if(stdQuality == GTWDEFS_STD_QLTY_GOOD)
//    pStateFlags->SetValidityValid();
//
//  if(stdQuality & GTWDEFS_STD_QLTY_NOT_TOPICAL)
//    pStateFlags->SetValidityHeld();
//
//  if(stdQuality & GTWDEFS_STD_QLTY_UNINITIALIZED)
//    pStateFlags->SetValiditySuspect();
//
//}


GTW61850Server *GTW61850SlaveDataObject::GetServer()
{
  if (GetParentMember() && GetParentMember()->IsA("GTW61850Server"))
  {
    return (GTW61850Server *)GetParentMember();
  }
  return NULL;
}

void GTW61850SlaveDataObject::GetInternalTime(tmw61850::DateTime *p61850Time)
{
  p61850Time->SetFromUTCTime((const tmw61850::DateTime::UTCBTime*)m_timestamp.GetAsUTCTime());
}

/**********************************************************************************/
void GTW61850SlaveDataObject::Set61850VTQValue(
  tmw61850::DataAttribute *p61850valueDA,
  tmw61850::DataAttribute *p61850timeDA,
  tmw61850::DataAttribute *p61850qualityDA
  )
{
  if (p61850valueDA == NULL)
    return;

  //assert(GetServer());
  if (!GetServer())
  {
    return;
  }

  tmw61850::Server *p61850Server = GetServer()->GetServerConnection();
  if (p61850Server == NULL)
    return;

  GetServer()->SetDAValues(this, p61850valueDA, p61850timeDA, p61850qualityDA);
}

// This can only be called the server data change queue thread
void GTW61850SlaveDataObject::SetModelValues(tmw61850::DataAttribute* pValue, tmw61850::DataAttribute* pTime, tmw61850::DataAttribute* pQuality)
{
  class CallEndTransaction
  {
  public:
    CallEndTransaction(tmw61850::Server *pServer) : m_pServer(pServer) {}
    ~CallEndTransaction()
    {
      if (m_pServer)
      {
        m_pServer->EndTransaction();
      }
    }

  private:
    tmw61850::Server *m_pServer;
  };

  //if (p61850valueDA == NULL)
  //  return;

  tmw61850::Server *p61850Server = GetServer()->GetServerConnection();
  if (p61850Server == NULL)
  {
    return;
  }

  try
  {
    if (p61850Server->StartTransaction() == false)
    {
      LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850DataAttributeMDO::SetValue:assert(false)");
      return;
    }
    CallEndTransaction ensureEndTransactionIsCalled(p61850Server);

    if (pValue != NULL)
    {
      if (pValue->GetType() == tmw61850::Value::Type::BITSTRING && m_pMyMdo->getBdo()->IsA("GTW61850DataAttributeMDO") == false)
      {
        if (pValue->GetSize() == 2)
        {
          // NOTE 0 index is most significant digit
          tmw::BitString bitString(2);
          if (m_pMyMdo->getBdo()->IsA("GTWM14SinglePointMdo"))
          {
            GTWM14SinglePointMdo* pDPMdo = (GTWM14SinglePointMdo*)m_pMyMdo;
            if (m_value.GetBooleanValue()) // true = 2 for Dbpos
            {
              bitString.SetFromInt(2, 2);
            }
            else // off = 1 for DbPos
            {
              bitString.SetFromInt(1, 2);
            }
          }
          else if (m_pMyMdo->getBdo()->IsA("GTWM14DoublePointMdo"))
          {
            GTWM14DoublePointMdo *pDPMdo = (GTWM14DoublePointMdo *)m_pMyMdo;

            if (m_value.GetType() == GTWDEFS_TYPE_BOOL || pDPMdo->getProvideAsRaw() == false)
            {
              if (m_value.GetBooleanValue() != 0)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, false);
              }
              else
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, true);
              }
            }
            else
            {
              GtwVariant tempV(m_value);
              tempV.ChangeType(GTWDEFS_TYPE_ULONG);
              unsigned int ui4 = tempV.GetUIntValue();
              if (ui4 == 0)
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, false);
              }
              else if (ui4 == 1)
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, true);
              }
              else if (ui4 == 2)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, false);
              }
              else if (ui4 == 3)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, true);
              }
            }
          }
          else if (m_pMyMdo->getBdo()->IsA("GTWDnpDoubleBinaryInputMdo"))
          {
            GTWDnpDoubleBinaryInputMdo *pDPMdo = (GTWDnpDoubleBinaryInputMdo *)m_pMyMdo;

            if (m_value.GetType() == GTWDEFS_TYPE_BOOL || pDPMdo->getProvideAsRaw() == false)
            {
              if (m_value.GetBooleanValue() != 0)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, false);
              }
              else
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, true);
              }
            }
            else
            {
              GtwVariant tempV(m_value);
              tempV.ChangeType(GTWDEFS_TYPE_ULONG);
              unsigned int ui4 = tempV.GetUIntValue();
              if (ui4 == 0)
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, false);
              }
              else if (ui4 == 1)
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, true);
              }
              else if (ui4 == 2)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, false);
              }
              else if (ui4 == 3)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, true);
              }
            }
          }
          else if (m_pMyMdo->getBdo()->IsA("GTWDnpBinaryInputMdo"))
          {
            GTWDnpDoubleBinaryInputMdo *pDPMdo = (GTWDnpDoubleBinaryInputMdo*)m_pMyMdo;

            if (m_value.GetType() == GTWDEFS_TYPE_BOOL || pDPMdo->getProvideAsRaw() == false)
            {
              if (m_value.GetBooleanValue() != 0)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, false);
              }
              else
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, true);
              }
            }
            else
            {
              GtwVariant tempV(m_value);
              tempV.ChangeType(GTWDEFS_TYPE_ULONG);
              unsigned int ui4 = tempV.GetUIntValue();
              if (ui4 == 0)
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, false);
              }
              else if (ui4 == 1)
              {
                bitString.SetBit(0, false);
                bitString.SetBit(1, true);
              }
              else if (ui4 == 2)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, false);
              }
              else if (ui4 == 3)
              {
                bitString.SetBit(0, true);
                bitString.SetBit(1, true);
              }
            }
          }
          else
          {
            GtwVariant tempV(m_value);
            tempV.ChangeType(GTWDEFS_TYPE_ULONG);
            unsigned int ui4 = tempV.GetUIntValue();
            if (ui4 == 0)
            {
              bitString.SetBit(0, false);
              bitString.SetBit(1, false);
            }
            else if (ui4 == 1)
            {
              bitString.SetBit(0, false);
              bitString.SetBit(1, true);
            }
            else if (ui4 == 2)
            {
              bitString.SetBit(0, true);
              bitString.SetBit(1, false);
            }
            else if (ui4 == 3)
            {
              bitString.SetBit(0, true);
              bitString.SetBit(1, true);
            }
          }

          pValue->SetBitStringValue(bitString);
        }
        else
        {
          //  Data_State bit-string:
          //{
          //    State_hi[0],
          //    State_lo[1],
          //    Validity_hi[2],
          //    Validity_lo[3],
          //    CurrentSource_hi[4],
          //    CurrentSource_lo[5],
          //    NormalValue[6],
          //    TimeStampQuality[7]
          //}
          GtwVariant tempV(m_value);
          tmw::BitString bitString(32);
          tempV.ChangeType(GTWDEFS_TYPE_ULONG);
          if (tempV.GetUIntValue() != 0) // TRUE (on)
          {
            bitString.SetBit(7, true);
            bitString.SetBit(6, false);
          }
          else
          { // FALSE (off)
            bitString.SetBit(7, false);
            bitString.SetBit(6, true);
          }

          pValue->SetBitStringValue(bitString);
        }
      }
      else
      {
        if (!m_value.AssignTo61850DataAttribute(*pValue))
        {
          LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "%s has an unsuported type : %s\n", GetFullName().c_str(), pValue->GetTypeAsXMLString());
        }
      }
    }

    if (pTime != NULL)
    {
      tmw61850::DateTime* pDT = (tmw61850::DateTime*)pTime->GetDateTime();
      GetInternalTime(pDT);
    }

    if (pQuality != NULL)
    {
      if (!m_bUseNativeQuality)
      {
        tmw::BitString bs(13);
        bs.ClearBits();
        if (m_quality == GTWDEFS_STD_QLTY_GOOD)
        {
          bs.SetBit(0, 0);
          bs.SetBit(1, 0);
        }
        else if ((m_quality & GTWDEFS_STD_QLTY_INVALID) == GTWDEFS_STD_QLTY_INVALID)
        {
          bs.SetBit(0, 0);
          bs.SetBit(1, 1);
        }
        else if (
          (m_quality & GTWDEFS_STD_QLTY_UNINITIALIZED) == GTWDEFS_STD_QLTY_UNINITIALIZED
          || (m_quality & GTWDEFS_STD_QLTY_NOT_TOPICAL) == GTWDEFS_STD_QLTY_NOT_TOPICAL
          )
        {
          bs.SetBit(0, 1);
          bs.SetBit(1, 1);
        }

        if ((m_quality & GTWDEFS_STD_QLTY_NOT_TOPICAL) == GTWDEFS_STD_QLTY_NOT_TOPICAL)
        {
          bs.SetBit(7, 1);
        }
        if ((m_quality & GTWDEFS_STD_QLTY_OVERFLOW) == GTWDEFS_STD_QLTY_OVERFLOW)
        {
          bs.SetBit(2, 1);
        }
        if ((m_quality & GTWDEFS_STD_QLTY_SUBSTITUTED) == GTWDEFS_STD_QLTY_SUBSTITUTED)
        {
          bs.SetBit(10, 1);
        }
        if ((m_quality & GTWDEFS_STD_QLTY_REF_ERROR) == GTWDEFS_STD_QLTY_REF_ERROR)
        {
          bs.SetBit(4, 1);
        }
        if ((m_quality & GTWDEFS_STD_QLTY_TEST) == GTWDEFS_STD_QLTY_TEST)
        {
          bs.SetBit(11, 1);
        }
        if ((m_quality & GTWDEFS_STD_QLTY_BLOCKED) == GTWDEFS_STD_QLTY_BLOCKED)
        {
          bs.SetBit(12, 1);
        }

        pQuality->SetBitStringValue(bs);
      }
      else
      {
        pQuality->SetUIntValue(Get61850Quality());
      }
    }
  }
  catch (...)
  {
    LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850SlaveDataObject::Set61850VTQValue caught exception");
  }
}

void GTW61850SlaveDataObject::UpdateInternalControlValues()
{
  try
  {
    tmw61850::DataAttribute* pTime = nullptr;
    tmw61850::DataAttribute* pQuality = nullptr;

    tmw61850::DataAttribute* pDA = m_pControlPoint->GetControlValueDA(tmw61850::ServerControlPoint::ControlPointType::Oper);
    tmw::String cpName, s;
    if (pDA)
    {
      m_pControlPoint->GetFullName(cpName);

      m_value.AssignFrom61850Value(*(pDA->GetValue()));
      CStdString sTime = (const char*)cpName;
      sTime += ".t";
      pTime = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(sTime.c_str(), this->GetServer()->GetServerConnection()->Model());

      CStdString sQual = (const char*)cpName;
      sQual += ".q";
      pQuality = tmw61850::i61850RootNode::FindNode<tmw61850::DataAttribute>(sQual.c_str(), this->GetServer()->GetServerConnection()->Model());
    }

    //tmw::String sv;
    //pDA->GetValueAsString(sv);

    if (pQuality)
    {
      const tmw::BitString* pBS = pQuality->GetBitStringValue();
      m_quality = GTW61850DataAttributeMDO::getStdQuality(pBS);
    }

    // Set Internal Time
    if (pTime && pTime->GetDateTime())
    {
      TMWDTIME dTime;
      GTW61850Client::SixTSDateTimeToTMWDTime(*(pTime->GetDateTime()), dTime);
      m_timestamp.SetFromTMWDTime(&dTime);

      SetReportedTime(&dTime);
      SetUpdatedTime(&dTime);
    }

    /*
    tmw61850::DataAttribute* pStatusDA = m_pControlPoint->GetStatusPointValueDA();
    if (pStatusDA)
    {
      GTW61850SlaveDataObject* pStatusControl = GetServer()->GetSdo(pStatusDA);
      if (!pStatusControl)  // if null then not mapped, so we update the status point internally - otherwise it is mapped to something, so we then do nothing and let the mapped source point update this status
      {
        m_value.AssignFrom61850Value(*pStatusDA->GetValue());
        LOG(GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, nullptr, "61850 Control '%s' succeeded. Updated status point '%s' to %s", (const char*)cpName, pStatusDA->GetFullName(s), m_value.toString().c_str());
      }
      else
      {
        LOG(GtwLogger::Severity_Warning, GtwLogger::SDG_Category_61850, nullptr, "61850 Control '%s' succeeded, but the SDG did not update its status point '%s' because it is mapped", (const char*)cpName, pStatusDA->GetFullName(s));
      }
    }
    */
  }
  catch (...)
  {
    LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "%s", "GTW61850SlaveDataObject::Set61850VTQValue caught unknown exception");
  }
}

void GTW61850SlaveDataObject::SetDaName(const CStdString& valueDaName, const CStdString& timeDaName, const CStdString& qualityDaName)
{
  m_sValueDaName = valueDaName;
  m_sTimeDaName = timeDaName;
  m_sQualityDaName = qualityDaName;
}

void GTW61850SlaveDataObject::SetInternalModelPointers(tmw61850::DataAttribute* p61850valueDA,
  tmw61850::DataAttribute* p61850timeDA,
  tmw61850::DataAttribute* p61850qualityDA)
{
  m_p61850ValueDa = p61850valueDA;
  if (m_p61850ValueDa)
  {
    m_p61850ValueDa->SetUserData(this);
  }

  m_p61850TimeDa = p61850timeDA;
  if (m_p61850TimeDa)
  {
    m_p61850TimeDa->SetUserData(this);

    tmw61850::DateTime* pDT = (tmw61850::DateTime*)m_p61850TimeDa->GetDateTime();
    pDT->SetBitsofAccuracy(9); // millisecond accuracy is only supported at this time in the SDG
    pDT->SetLeapSecondKnown(true);
    pDT->SetClockNotSynchronized(true);
    pDT->SetClockFailure(false);
  }

  m_p61850QualityDa = p61850qualityDA;
  if (m_p61850QualityDa)
  {
    m_p61850QualityDa->SetUserData(this);
  }
}

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::GTW61850_write
Description : called by a gateway OPC server (i.e. FactorySoft) to write value to a point
Return :			HRESULT	-
Parameters :
VARIANT & value	-
Note : [none]
\**********************************************************************************/
bool GTW61850SlaveDataObject::GTW61850_write(tmw61850::DataAttribute* pDA)
{
  GTWDEFS_CTRL_STAT ctrlStat = GTWDEFS_CTRL_STAT_SUCCESS;
  if (m_pWriteCnvtr)
  {
    ctrlStat = m_pWriteCnvtr->writeValue(pDA, GTWDEFS_CTRL_MODE_EXECUTE);
  }
  if (ctrlStat == GTWDEFS_CTRL_STAT_SUCCESS)
  {
    m_value.AssignFrom61850Value(*pDA->GetValue());
  }
  return ctrlStat == GTWDEFS_CTRL_STAT_SUCCESS;
}

bool GTW61850SlaveDataObject::GTW61850_write(tmw61850::ServerControlPoint* controlPoint)
{
  tmw61850::EnumDefs::CtlModel ctlModel = controlPoint->GetCtlModel();
  tmw61850::DataAttribute* pDA = controlPoint->GetControlValueDA(tmw61850::ServerControlPoint::ControlPointType::Oper);
  if (m_pWriteCnvtr)
  {
    GTWDEFS_CTRL_MODE ctrlMode =
      ctlModel == tmw61850::EnumDefs::CtlModel::sbowithnormalsecurity || ctlModel == tmw61850::EnumDefs::CtlModel::sbowithenhancedsecurity
      ? GTWDEFS_CTRL_MODE_EXECUTE : GTWDEFS_CTRL_MODE_AUTO;

    if (m_pWriteCnvtr->write(controlPoint, ctrlMode))
    {
      //m_value.AssignFrom61850Value(*(pDA->GetValue()));
      return true;
    }
  }
  return false;
}

bool GTW61850SlaveDataObject::selectControl(tmw61850::ServerControlPoint* controlPoint)
{
  if (m_pWriteCnvtr)
  {
    return m_pWriteCnvtr->select(controlPoint);
  }
  return false;
}

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::setQuality
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_STD_QLTY stdQuality	-
Note : [none]
\**********************************************************************************/
void GTW61850SlaveDataObject::setQuality(GTWDEFS_STD_QLTY stdQuality)
{
  m_quality = stdQuality;
  //if (stdQuality & GTWDEFS_STD_QLTY_UNINITIALIZED)
  //{
  //  m_quality = EnumQuality_BAD_NOT_CONNECTED;
  //}
  //else if (stdQuality & GTWDEFS_STD_QLTY_BLOCKED)
  //{
  //  m_quality = OPC_QUALITY_OUT_OF_SERVICE;
  //}
  //else if (stdQuality & GTWDEFS_STD_QLTY_INVALID)
  //{
  //  m_quality = OPC_QUALITY_BAD;
  //}
  //else if (stdQuality & GTWDEFS_STD_QLTY_REF_ERROR)
  //{
  //  m_quality = OPC_QUALITY_SENSOR_CAL;
  //}
  //else if (stdQuality & GTWDEFS_STD_QLTY_OVERFLOW)
  //{
  //  m_quality = OPC_QUALITY_EGU_EXCEEDED;
  //}
  //else if (stdQuality & GTWDEFS_STD_QLTY_NOT_TOPICAL)
  //{
  //  m_quality = OPC_QUALITY_LAST_USABLE;
  //}
  //else if (stdQuality & GTWDEFS_STD_QLTY_IN_TRANSIT)
  //{
  //  m_quality = OPC_QUALITY_UNCERTAIN;
  //}
  //else if (stdQuality & GTWDEFS_STD_QLTY_SUBSTITUTED)
  //{
  //  m_quality = OPC_QUALITY_LOCAL_OVERRIDE;
  //}
  //else
  //{
  //  m_quality = OPC_QUALITY_GOOD;
  //  if (m_updateReason & GTWDEFS_UPDTRSN_CTRL_BY_COMM || m_updateReason & GTWDEFS_UPDTRSN_CTRL_AT_DEVICE)
  //  {
  //    m_quality = OPC_QUALITY_LOCAL_OVERRIDE;
  //  }
  //}
}


/**********************************************************************************\
Function :			GTW61850MasterDataObject::getMdoDbasDataId
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_DBAS_DATA_ID *pDbasDataId	-
Note : [none]
\**********************************************************************************/
void GTW61850SlaveDataObject::getDbasDataId(GTWDEFS_DBAS_DATA_ID* pDbasDataId)
{
  pDbasDataId->channelIndex = 0;
  pDbasDataId->dataType = this->m_value.GetType();
  pDbasDataId->elementIndex = 0;
  pDbasDataId->functionType = 0;
  pDbasDataId->infoNumObjAddr = 0;
  pDbasDataId->internalDataType = true;
  pDbasDataId->originator = 0;
  pDbasDataId->recordAddress = 0;
  pDbasDataId->sectorIndex = 0;
  pDbasDataId->sessionIndex = 0;

}

void GTW61850SlaveDataObject::writeSdoCB(GTWDEFS_CTRL_STAT newCtrlStat, void* pExtraCBData)
{
  m_ctrlStat = newCtrlStat;
  //if (newCtrlStat == GTWDEFS_CTRL_STAT_FAILURE)
  //{
  //  int lll = 3;
  //}
}

struct Gtw61850OperThreadContext
{
  GTW61850SlaveDataObject* m_pSdo;
  tmw61850::ServerControlPoint* m_pControlPoint;
};

unsigned int __stdcall GTW61850SlaveDataObject::OperThreadFunc(void* pParam)
{
  Gtw61850OperThreadContext* pContext = (Gtw61850OperThreadContext*)pParam;
  std::unique_ptr<Gtw61850OperThreadContext> deleteThis(pContext);

  unsigned int nTotal = 0;
  unsigned int nTimeout = pContext->m_pControlPoint->GetOperTimeOut();
  while (nTotal < nTimeout)
  {
    if (!pContext->m_pSdo->IsOperating())
    {
      return 0;
    }
    GtwOsSleep(250); // will miss the timeout by at most 250 ms
    nTotal += 250;
  }

  pContext->m_pSdo->EndOperateTimeout();
  return 0;
}

void GTW61850SlaveDataObject::SetCurrentTime()
{
  GtwOsDateTime now;
  now.UTCNow();
  TMWDTIME tempTime;
  now.GetAsTMWDTime(tempTime);

  this->storeUpdatedTime(&tempTime);
  this->storeReportedTime(&tempTime);

  if (this->m_p61850TimeDa)
  {
    tmw61850::Value* pTime = dynamic_cast<tmw61850::Value*>(m_p61850TimeDa);
    if (pTime != NULL)
    {
      tmw61850::DateTime* pDT = (tmw61850::DateTime*)pTime->GetDateTime();
      pDT->SetFromUTCTime((const tmw61850::DateTime::UTCBTime*)m_timestamp.GetAsUTCTime());
    }
  }
}

void GTW61850SlaveDataObject::BeginOperate(tmw61850::ServerControlPoint* pControlPoint)
{
  SetCurrentTime();

  m_bOperating = true;
  m_bTimedOut = false;
  m_pControlPoint = pControlPoint;

  UpdateInternalControlValues();

  tmw::String sVal;
  pControlPoint->GetControlValueDA(tmw61850::ServerControlPoint::ControlPointType::Oper)->GetValueAsString(sVal);

  tmw::String s;
  LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Operate called on %s, control value = %s", this->m_p61850ValueDa->GetFullName(s), (const char*)sVal);
  if (pControlPoint->GetOperTimeOut() > 0)
  {
    LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Debug, GtwLogger::SDG_Category_61850, "Oper timeout ignored on %s.", (const char*)s);
    /*
    ::trace61850InfoFmt("Operate timer started on %s. Will timeout in %d milliseconds.", (const char*)s, pControlPoint->GetOperTimeOut());
    Gtw61850OperThreadContext *pContext = new Gtw61850OperThreadContext;
    pContext->m_pSdo = this;
    pContext->m_pControlPoint = pControlPoint;

    //create thread
    DWORD threadId;
    HANDLE hThread = CreateThread(NULL,
      0,
      GTW61850SlaveDataObject::OperThreadFunc,
      pContext,
      0,
      &threadId);
      */
  }
}

bool GTW61850SlaveDataObject::IsOperating()
{
  return m_bOperating;
}

/*
void GTW61850SlaveDataObject::EndOperate(bool bWriteSucceeded)
{
  // Need to perform the end on another thread because calling m_pControlPoint->UpdateControl calls StartTransaction which cannot be called from within a callback (e.g. OnOperateControl callback in the server)
  GetServer()->GetWorkQueue()->AddWorkItemToQueue(new c61850EndOperateWorkItem(this, bWriteSucceeded));
}
*/

void GTW61850SlaveDataObject::DoEndOperate(bool bWriteSucceeded)
{
  try
  {
    assert(m_pControlPoint);
    m_bOperating = false;

    if (!m_pControlPoint)
    {
      return;
    }

    //tmw61850::DataAttribute* pCtlVal = m_pControlPoint->GetControlValueDA();
    //m_value.AssignFrom61850Value(*pCtlVal->GetValue()); // update the ctlValue SDG value from the model that was jsut written to

    tmw::String sTemp;
    if (bWriteSucceeded) // then update status point mapping if one exists
    {
      tmw61850::DataAttribute* pStatusDA = m_pControlPoint->GetStatusPointValueDA();
      if (pStatusDA)
      {
        tmw::String sn;
        GTW61850SlaveDataObject* pStatusControl = GetServer()->GetSdo(pStatusDA);
        if (!pStatusControl)  // if null then not mapped, so we update the status point internally - otherwise it is mapped to something, so we then do nothing and let the mapped source point update this status
        {
          if (!m_pControlPoint->UpdateControl()) // will update the corresponding status point
          {
            LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "61850 Control '%s' failed to update status value. See logs for details.", m_pControlPoint->GetFullName(sn), pStatusDA->GetFullName(sn));
          }
          else
          {
            LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "61850 Control '%s' succeeded. Updated status point '%s' to %s", m_pControlPoint->GetFullName(sn), pStatusDA->GetFullName(sn), m_value.toString().c_str());
          }
        }
        else
        {
          LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Warning, GtwLogger::SDG_Category_61850, "61850 Control '%s' succeeded. The control status point '%s' will be updated based on its mapping", m_pControlPoint->GetFullName(sn), pStatusDA->GetFullName(sn));

        }
        LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Information, GtwLogger::SDG_Category_61850, "Operate succeeded for %s", m_pControlPoint->GetFullName(sTemp));
      }
    }
    else
    {
      LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Operate failed for %s. See logs.", m_pControlPoint->GetFullName(sTemp));
    }
    m_pControlPoint->OperateFinished();
  }
  catch (std::exception& ex)
  {
    tmw::String sTemp;
    const char* sWhat = ex.what();
    LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "End Operate threw exception for control %s : %s", m_pControlPoint->GetFullName(sTemp), sWhat);
    assert(false);
  }
  catch (...)
  {
    tmw::String sTemp;
    LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Exception, GtwLogger::SDG_Category_61850, "End Operate threw unknown exception for control %s", m_pControlPoint->GetFullName(sTemp));
    assert(false);
  }
}

void GTW61850SlaveDataObject::EndOperateTimeout()
{
  tmw::String s;
  LOG6(GetServer()->GetServerConnection(), GtwLogger::Severity_Error, GtwLogger::SDG_Category_61850, "Operate failed - Timed out on %s", this->m_p61850ValueDa->GetFullName(s));

  m_bTimedOut = true;
  m_bOperating = false;

  if (m_pControlPoint->GetCtlModel() == tmw61850::EnumDefs::CtlModel::sbowithenhancedsecurity ||
    m_pControlPoint->GetCtlModel() == tmw61850::EnumDefs::CtlModel::directwithenhancedsecurity)
  {
    m_pControlPoint->SendCommandTermination(tmw61850::EnumDefs::LastApplError::TimeoutTestFailed, tmw61850::EnumDefs::AddCause::TimeLimitOver);
  }
}

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::StartProcessWriteResult
Description : [none]
Return :			void	-
Parameters :
GTWDEFS_CTRL_STAT startingCtrlStat	-
Note : [none]
\**********************************************************************************/
/*
void GTW61850SlaveDataObject::StartProcessWriteResult(GTWDEFS_CTRL_STAT startingCtrlStat, tmw61850::DataAttribute *pDA)
{
  m_ctrlStat = startingCtrlStat;
  /*
  if (startingCtrlStat == GTWDEFS_CTRL_STAT_SUCCESS)
  {
    writeSdoCB(startingCtrlStat, pDA);
  }
  if (startingCtrlStat == GTWDEFS_CTRL_STAT_FAILURE)
  {
    writeSdoCB(startingCtrlStat, pDA);
  }
  /
}
*/

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::getValueString
Description : [none]
Return :			void	-
Parameters :
const GTWEXPND_FORMAT &pFormat	-
TMWTYPES_CHAR *msg	-
TMWTYPES_USHORT msgLen	-
Note : [none]
\**********************************************************************************/
void GTW61850SlaveDataObject::getValueString(const GTWEXPND_FORMAT& pFormat, TMWTYPES_CHAR* msg, TMWTYPES_USHORT msgLen)
{
  m_value.GetValueAsString(pFormat, msg, msgLen);
}

bool GTW61850SlaveDataObject::IsExtRef()
{
  return m_p61850ValueDa && (m_p61850ValueDa->GetExtRefType() != tmw61850::Node::ExtRefInfo_None);
}

/**********************************************************************************\
Function :			GTW61850SlaveDataObject::getStdQuality
Description : [none]
Return :			GTWDEFS_STD_QLTY	-
Parameters :
Note : [none]
\**********************************************************************************/
GTWDEFS_STD_QLTY GTW61850SlaveDataObject::getStdQuality()
{
  if (IsExtRef())
  {
    return m_quality;
    /* NOTE: logic below is for converting 61850 quality to GTW quality - we dont need for now since anything mapped to the extref cannot be updated with quality and time

    GTWDEFS_STD_QLTY retQuality = 0;
    I61850_QUALITY_TYPE qValidity = m_quality & I61850_QUALITY_VALIDITY_MASK;

    if (qValidity == I61850_QUALITY_VALIDITY_GOOD)
    {
      retQuality = GTWDEFS_STD_QLTY_GOOD;
    }
    else if (qValidity & I61850_QUALITY_VALIDITY_INVALID)
    {
      retQuality = GTWDEFS_STD_QLTY_INVALID;
    }
    else if (qValidity & I61850_QUALITY_VALIDITY_QUESTIONABLE)
    {
      retQuality = GTWDEFS_STD_QLTY_UNINITIALIZED;
    }

    if ((m_quality & I61850_QUALITY_OVERFLOW) == I61850_QUALITY_OVERFLOW)
    {
      retQuality |= GTWDEFS_STD_QLTY_OVERFLOW;
    }
    if ((m_quality & I61850_QUALITY_OUTOFRANGE) == I61850_QUALITY_OUTOFRANGE)
    {
      retQuality |= GTWDEFS_STD_QLTY_OVERFLOW;
    }
    if ((m_quality & I61850_QUALITY_BADREFERENCE) == I61850_QUALITY_BADREFERENCE)
    {
      retQuality |= GTWDEFS_STD_QLTY_REF_ERROR;
    }
    if ((m_quality & I61850_QUALITY_OSCILLIATORY) == I61850_QUALITY_OSCILLIATORY)
    {
      retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
    }
    if ((m_quality & I61850_QUALITY_FAILURE) == I61850_QUALITY_FAILURE)
    {
      retQuality |= GTWDEFS_STD_QLTY_INVALID;
    }
    if ((m_quality & I61850_QUALITY_OLDDATA) == I61850_QUALITY_OLDDATA)
    {
      retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
    }
    if ((m_quality & I61850_QUALITY_INCONSISTENT) == I61850_QUALITY_INCONSISTENT)
    {
      retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
    }
    if ((m_quality & I61850_QUALITY_INACCURATE) == I61850_QUALITY_INACCURATE)
    {
      retQuality |= GTWDEFS_STD_QLTY_NOT_TOPICAL;
    }

    if ((m_quality & I61850_QUALITY_SOURCE_SUBSTITUTED) == I61850_QUALITY_SOURCE_SUBSTITUTED)
    {
      retQuality |= GTWDEFS_STD_QLTY_SUBSTITUTED;
    }
    if ((m_quality & I61850_QUALITY_TEST) == I61850_QUALITY_TEST)
    {
      retQuality |= GTWDEFS_STD_QLTY_TEST;
    }
    if ((m_quality & I61850_QUALITY_OPERATORBLOCKED) == I61850_QUALITY_OPERATORBLOCKED)
    {
      retQuality |= GTWDEFS_STD_QLTY_BLOCKED;
    }
    return (retQuality);
    */
  }

  // do we need to chagne this for ext ref SDOs?

  GTWDEFS_STD_QLTY stdQuality;
  if (m_pReadCnvtr)
  {
    m_pReadCnvtr->GTW61850_getValue(this, &stdQuality);
    return stdQuality;
  }
  return GTWDEFS_STD_QLTY_NOT_USABLE;
}

bool GTW61850SlaveDataObject::getDisplayTimeAndQuality(TMWDTIME* pDateTime, GTWDEFS_TIME_QLTY* pTimeQuality)
{
  if (!IsExtRef())
  {
    return false;
  }
  //::ConvertFileTimeToTMWDTIME(&m_timestamp, pDateTime, false);
  m_timestamp.GetAsTMWDTime(*pDateTime);

  if (m_p61850TimeDa)
  {
    *pTimeQuality = GTWDEFS_TIME_QLTY_REMOTE;
  }
  else
  {
    *pTimeQuality = GTWDEFS_TIME_QLTY_ASUM;
  }
  return true;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterBool::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  bool boolValue;
  TMWTYPES_BOOL v;

  m_pOpcBoolReadCnvtr->getValue(NULL, &boolValue, &stdQuality);
  v = boolValue;
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterChar::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_CHAR v;

  m_pOpcCharReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterShort::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_SHORT v;

  m_pOpcShortReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterSFloat::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_SFLOAT v;

  m_pOpcSfloatReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterDouble::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_DOUBLE v;

  m_pOpcDoubleReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/
void GTW61850ReadConverterINT64::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_INT64 v;

  m_pReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterLong::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_INT v;

  m_pOpcLongReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterUChar::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_UCHAR v;

  m_pOpcUCharReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}


/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterUShort::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_USHORT v;

  m_pOpcUShortReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterULong::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWTYPES_UINT v;

  m_pOpcULongReadCnvtr->getValue(NULL, &v, &stdQuality);
  p61850Sdo->setValue(v);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterTMWDTIME::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  TMWDTIME         tmwdtime;

  m_pOpcTmwdtimeReadCnvtr->getValue(NULL, &tmwdtime, &stdQuality);
  GtwOsDateTime dateTime;
  dateTime.SetFromTMWDTime(&tmwdtime);
  p61850Sdo->setValue(dateTime);// m_value = v;
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}


/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

void GTW61850ReadConverterString::GTW61850_getValue(GTW61850SlaveDataObject* p61850Sdo, GTWDEFS_STD_QLTY* sQ)
{
  GTWDEFS_STD_QLTY stdQuality;
  CStdString       string;

  m_pOpcStringReadCnvtr->getValue(NULL, &string, &stdQuality);
  p61850Sdo->setValue(string);
  if (p61850Sdo->m_bUseNativeQuality)
  {
    p61850Sdo->Set61850Quality(pSrcMdo->get61850Quality());
  }
  p61850Sdo->setQuality(stdQuality);
  *sQ = stdQuality;
}

/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterBin::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  bool bValue = pDA->GetBooleanValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, bValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterChar::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_CHAR charValue = (TMWTYPES_CHAR)pDA->GetIntValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, charValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterUChar::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_UCHAR ucharValue = (TMWTYPES_UCHAR)pDA->GetUIntValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, ucharValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterShort::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_SHORT shortValue = (TMWTYPES_SHORT)pDA->GetIntValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, shortValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterUShort::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_USHORT ushortValue = (TMWTYPES_USHORT)pDA->GetUIntValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, ushortValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterString::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  CStdString writeValueStr = pDA->GetStringValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, writeValueStr, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterLong::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_INT longValue = (TMWTYPES_INT)pDA->GetIntValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, longValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterULong::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_UINT ulongValue = (TMWTYPES_UINT)pDA->GetUIntValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, ulongValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterSFloat::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_SFLOAT floatValue = (TMWTYPES_SFLOAT)pDA->GetDoubleValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, floatValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterDouble::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_DOUBLE doubleValue = pDA->GetDoubleValue();
  return m_pWriteCnvtr->writeValue(m_pSdo, doubleValue, nullptr, ctrlMode);
}

/*****************************************************************************/
/*                                                                           */
/*****************************************************************************/

GTWDEFS_CTRL_STAT GTW61850WriteConverterINT64::writeValue(tmw61850::DataAttribute* pDA, GTWDEFS_CTRL_MODE ctrlMode)
{
  TMWTYPES_INT64 iValue = pDA->GetInt64Value();
  return m_pWriteCnvtr->writeValue(m_pSdo, iValue, nullptr, ctrlMode);
}

template <class T>
bool GTW61850WriteConverterT<T>::select(tmw61850::ServerControlPoint* controlPoint)
{
  tmw61850::DataAttribute* pDA = controlPoint->GetControlValueDA(tmw61850::ServerControlPoint::ControlPointType::SBOw);

  m_pSdo->m_ctrlStat = writeValue(pDA, GTWDEFS_CTRL_MODE_SELECT);

  int nSelectTimeout = controlPoint->GetSBOTimeOut();
  if (nSelectTimeout == 0) // probably requesting infinite, so we set to 1 minute, which should be plenty long enough
  {
    nSelectTimeout = 60000; // one minute
  }
  DWORD iPause = 200;
  int nTimeCount = 0;
  while (IS_PENDING(m_pSdo->m_ctrlStat))
  {
    GtwOsSleep(iPause);
    nTimeCount += iPause;
    if (nTimeCount > nSelectTimeout)
    {
      m_pSdo->m_ctrlStat = GTWDEFS_CTRL_STAT_FAILURE;

      tmw::String sTemp;
      const char* sFullPath = controlPoint->GetFullName(sTemp);

      CStdString sErr;
      sErr.Format("Select timed out for '%s' waiting on remote select to finish. The select timeout may need to be increased for control point '%s'", sFullPath, sFullPath);
      TMWDIAG_ERROR(sErr.c_str());
      break;
    }
  }

  return m_pSdo->m_ctrlStat == GTWDEFS_CTRL_STAT_SUCCESS;
}
