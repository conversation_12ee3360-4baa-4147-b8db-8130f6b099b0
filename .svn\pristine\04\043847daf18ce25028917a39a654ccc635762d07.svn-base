/*****************************************************************************/
/* Triangle MicroWorks, Inc.                         Copyright (c) 1997-2000 */
/*****************************************************************************/
/*                            MPP_COMMAND_PRODUCT                            */
/*                                                                           */
/* FILE NAME:    GTWModbusInputRegisterSdo.h                                 */
/* DESCRIPTION:  Interface definition for DNP3 SDOs                          */
/* PROPERTY OF:  Triangle MicroWorks, Inc. Raleigh, North Carolina USA       */
/*               www.TriangleMicroWorks.com  (919) 870-6615                  */
/*                                                                           */
/* This Source Code and the associated Documentation contain proprietary     */
/* information of Triangle MicroWorks, Inc. and may not be copied or         */
/* distributed in any form without the written permission of Triangle        */
/* MicroWorks, Inc.  Copies of the source code may be made only for backup   */
/* purposes.                                                                 */
/*                                                                           */
/* Your License agreement may limit the installation of this source code to  */
/* specific products.  Before installing this source code on a new           */
/* application, check your license agreement to ensure it allows use on the  */
/* product in question.  Contact Triangle MicroWorks for information about   */
/* extending the number of products that may use this source code library or */
/* obtaining the newest revision.                                            */
/*****************************************************************************/
#ifndef GTWModbusInputRegisterSdo_DEFINITIONS
#define GTWModbusInputRegisterSdo_DEFINITIONS

#include "tmwscl/modbus/mbdefs.h"
#include "GTWModbusMonitorSdo.h"


static GTWDEFS_PARSED_OPTION_TYPE s_GTWModbusInputRegisterSdo_allowedOptions[] = {
  "SIGNED",PARSED_OPTION_BOOL_VALUE,NULL,NULL, "TR_OPT_HELP_MB_SDO_T03_SIGNED", "Allows the specification of a holding register or input register as a signed 16-bit value.  The default is that these types are unsigned 16-bit values.  This only changes the way that the SDG interprets the bits in the register.  If this option is specified for a point and the point is mapped to a different component the 'mapped to component' will see the value of the point as a signed value.",
  "FLOAT",PARSED_OPTION_CHOICE_VALUE,(void*)&GTWConfig::ModbusFloatMasks[0],(void*)(size_t)GTWConfig::ModbusFloatMasksLen, "TR_OPT_HELP_MB_SDO_T03_FLOAT", 
  "Allows the specification of 2 consecutive holding registers or input registers as a 32-bit floating point value.  The value can be both written and read.  \n"
  "\n"
  "The xxx has the following options :\n"
  "  0x01 : 1st register (word)is least significant\n"
  "  0x02 : 1st register (word)is most  significant\n"
  "\n"
  "When this option is specified IEEE - 754 Floating - Point format is used for the values.\n"
  "\n"
  "When writing a floating point value(i.e.as 2 words) the writing of the 2nd word will cause any up - down stream data to be affected.\n"

};

/* class: GTWModbusInputRegisterSdo
 * purpose: DNP Analog Input SDO
 */
class GTWModbusInputRegisterSdo : public GTWModbusMonitorSdo
{
public:
  DeclareClassInfo();

  static TMWTYPES_USHORT numInputRegisterSdos;
  static void init();
  static GTWDEFS_STAT    createModbusInputRegister(CStdString &tagName, GTWBaseDataObject **ppBdo, GTWCollectionMember *pOwner);
  static TMWTYPES_USHORT  ModbusInputRegisterQty(void *pPoint)
  {
    return(numInputRegisterSdos);
  }
  static bool   ModbusInputRegisterValidateRange(void *pHandle, TMWTYPES_USHORT startAddr, TMWTYPES_USHORT length);
  static bool    ModbusInputRegisterRead(void *pPoint, TMWTYPES_USHORT startAddr, TMWTYPES_USHORT length, TMWTYPES_USHORT *pValueArray);

  bool isWritable() override { return true; }

  GTWModbusInputRegisterSdo *Get2ndReg()
  {
    return m_pSecondFloatSdo;
  }

  bool getIsFloat(void)
  {
    if (m_floatWordOrder != MB_FLOAT_UNDEFINED)
    {
      return true;
    }
    return false;
  }

  GTWDEFS_TYPE getSdoType(void)
  {
    if (m_bIsSigned == true)
    {
      return GTWDEFS_TYPE_SHORT;
    }
    return GTWDEFS_TYPE_USHORT;
  }

	virtual GTWDEFS_STD_QLTY getStdQuality(void);
	virtual void getValueString(const GTWEXPND_FORMAT &pFormat, TMWTYPES_CHAR *msg, TMWTYPES_USHORT msgLen);
  /* Constructor
   * arguments:
   *   nPointNum - the point number for this data point
   */
  GTWModbusInputRegisterSdo(TMWTYPES_USHORT nPointNum, TMWTYPES_UCHAR nDataType);
  ~GTWModbusInputRegisterSdo(void)
  {
    if (m_pUShortReadCnvtr)
    {
      delete m_pUShortReadCnvtr;
    }
    if (m_pDblReadCnvtr)
    {
      delete m_pDblReadCnvtr;
    }
    if (m_pShortReadCnvtr)
    {
      delete m_pShortReadCnvtr;
    }
  }

protected:
  GTWAnalogValue &getValue(TMWTYPES_USHORT *pStdQuality)
  { 
    if(m_pDblReadCnvtr)
    {
      TMWTYPES_DOUBLE value;
      m_pDblReadCnvtr->getValue(NULL,&value, pStdQuality);
      m_dsValue = value;
    }
    if(m_pShortReadCnvtr)
    {
      TMWTYPES_SHORT value;
      m_pShortReadCnvtr->getValue(NULL,&value, pStdQuality);
      m_dsValue = value;
    }
    if(m_pUShortReadCnvtr)
    {
      TMWTYPES_USHORT value;
      m_pUShortReadCnvtr->getValue(NULL,&value, pStdQuality);
      m_dsValue = value;
    }
    if(m_pFirstFloatSdo && m_pFirstFloatSdo->m_pDblReadCnvtr)
    {
      TMWTYPES_DOUBLE value;
      GTWReadConverterTemplate<TMWTYPES_DOUBLE> *pDblReadCnvtr = NULL;
      if (this->getIsFloat() && this->m_pSecondFloatSdo == NULL)
      {
        pDblReadCnvtr = m_pFirstFloatSdo->m_pDblReadCnvtr;
      }
      else
      {
        pDblReadCnvtr = m_pDblReadCnvtr;
      }
      pDblReadCnvtr->getValue(NULL,&value, pStdQuality);
      m_dsValue = value;
    }
    return(m_dsValue);
  }

  void setValue(GTWAnalogValue &v)
  { 
    m_dsValue = v;
  }

  bool getIsSigned()
  {
    return m_bIsSigned;
  }

protected:
  virtual void SetDefaultOptions(void)
  {
    m_bIsSigned = false;
    m_floatWordOrder = MB_FLOAT_UNDEFINED;
      return(GTWModbusMonitorSdo::SetDefaultOptions());
  }

  virtual GTWDEFS_STAT ParseOptionsField(const char *connectionToken, const char **ppOptionString)
  {
    TMWTYPES_ULONG floatWordOrder = 0;

    if (ParseOptionsString(ppOptionString, s_GTWModbusInputRegisterSdo_allowedOptions[0].name))
    {
      m_bIsSigned = true;
    }
    if (ParseOptionsHex(ppOptionString, s_GTWModbusInputRegisterSdo_allowedOptions[1].name, &floatWordOrder))
    {
      if (floatWordOrder == MB_FLOAT_LSW || floatWordOrder == MB_FLOAT_MSW)
      {
        m_floatWordOrder = floatWordOrder;
        if (this->m_pSecondFloatSdo != NULL)
        {
          m_pSecondFloatSdo->m_floatWordOrder = floatWordOrder;
        }
      }
      else
      {
        return GTWDEFS_STAT_NOT_VALID;
      }
    }
    else
    {
      return(GTWModbusMonitorSdo::ParseOptionsField(connectionToken, ppOptionString));
    }

    return(GTWDEFS_STAT_SUCCESS);
  }

  void GetAllowedOptions( GTWDEFS_PARSED_OPTION_ARRAY &optionsArray  ) override
  {
    int i;
    for (i=0;i<TMWDEFS_ARRAY_LENGTH(s_GTWModbusInputRegisterSdo_allowedOptions);i++)
    {
      optionsArray.push_back(s_GTWModbusInputRegisterSdo_allowedOptions[i]);
    }
    GTWModbusMonitorSdo::GetAllowedOptions(optionsArray);
    return; 
  }

private:
  bool m_bIsSigned;
  TMWTYPES_UINT m_floatWordOrder;
  GTWModbusInputRegisterSdo *m_pSecondFloatSdo;
  GTWModbusInputRegisterSdo *m_pFirstFloatSdo;


  GTWAnalogValue m_dsValue;                      // the current value
  GTWReadConverterTemplate<TMWTYPES_USHORT> *m_pUShortReadCnvtr;   // converter to read value as u short
  GTWReadConverterTemplate<TMWTYPES_SHORT>  *m_pShortReadCnvtr;   // converter to read value as short
  GTWReadConverterTemplate<TMWTYPES_DOUBLE> *m_pDblReadCnvtr;   // converter to read value as double
  GTWReadConverterTemplate<TMWTYPES_SFLOAT> *m_pFltReadCnvtr;   // converter to read value as float

  virtual bool bindSdoWithMdo(GTWMasterDataObject *pMdo);
  virtual void updateSDO(GTWDEFS_UPDTRSN updateReason, GTWMasterDataObject *pMdo = TMWDEFS_NULL);
};

#endif // GTWModbusInputRegisterSdo_DEFINITIONS