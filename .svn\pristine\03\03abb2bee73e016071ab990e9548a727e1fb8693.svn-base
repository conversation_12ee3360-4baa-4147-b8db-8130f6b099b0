<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.17"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>MQTT C Client Libraries Internals: heap_info Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="pahologo.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">MQTT C Client Libraries Internals
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.17 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Data Fields</a>  </div>
  <div class="headertitle">
<div class="title">heap_info Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Information about the state of the heap.  
 <a href="structheap__info.html#details">More...</a></p>

<p><code>#include &lt;Heap.h&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Data Fields</h2></td></tr>
<tr class="memitem:a26c9bfacf416872c953138adc64e1a3d"><td class="memItemLeft" align="right" valign="top"><a id="a26c9bfacf416872c953138adc64e1a3d"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structheap__info.html#a26c9bfacf416872c953138adc64e1a3d">current_size</a></td></tr>
<tr class="memdesc:a26c9bfacf416872c953138adc64e1a3d"><td class="mdescLeft">&#160;</td><td class="mdescRight">current size of the heap in bytes <br /></td></tr>
<tr class="separator:a26c9bfacf416872c953138adc64e1a3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acca9bc1ad656bc11b35406f8588d2c43"><td class="memItemLeft" align="right" valign="top"><a id="acca9bc1ad656bc11b35406f8588d2c43"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structheap__info.html#acca9bc1ad656bc11b35406f8588d2c43">max_size</a></td></tr>
<tr class="memdesc:acca9bc1ad656bc11b35406f8588d2c43"><td class="mdescLeft">&#160;</td><td class="mdescRight">max size the heap has reached in bytes <br /></td></tr>
<tr class="separator:acca9bc1ad656bc11b35406f8588d2c43"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Information about the state of the heap. </p>
</div><hr/>The documentation for this struct was generated from the following file:<ul>
<li>Heap.h</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated on Thu Sep 29 2022 11:34:46 for MQTT C Client Libraries Internals by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.17
</small></address>
</body>
</html>
