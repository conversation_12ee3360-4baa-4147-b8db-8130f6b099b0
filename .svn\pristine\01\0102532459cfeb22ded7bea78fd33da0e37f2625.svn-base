#pragma once

#include "GTWBaseEditor.h"

class GTWLIB_API GTWInternalUserMDOEditor : public GTWBaseEditor
{
public:
	GTWInternalUserMDOEditor(const EditorCommandDTO &dto, GTWCollectionMember *pParent, GTWCollectionMember *pEditableObject, bool bAddMode);
	virtual ~GTWInternalUserMDOEditor();
	
  TMWTYPES_BOOL BuildSelPopupMenu(int *id,CMenuEntryArray *pMenuEntries) override;
  TMWTYPES_BOOL ValidateObject() override;
  TMWTYPES_BOOL UpdateObject() override;
  TMWTYPES_BOOL WebReadObject(nlohmann::json &pt, bool bEditAtRuntime) override;

  TMWTYPES_BOOL SaveObject() override;
  TMWTYPES_BOOL LoadObject() override;
  TMWTYPES_BOOL DeleteObject(GTWCollectionMember **pRefreshMember, bool bAskIfOk = true) override;
  TMWTYPES_BOOL ChangeValueOfObject(MENUENTRY_EDIT_CMD cmd) override;

protected:
  CStdString IsEditableAtRuntime(TMWTYPES_UINT ui_id) override;
  TMWTYPES_BOOL CreateMDO(CStdString mdoName,GTWMasterDataObject **ppMdo);
  TMWTYPES_UINT GetLastPoint();
  GTWCollectionMember *m_pParentMember;
  CStdString m_sMDOoptions;
  CStdString m_sMDOdescription;
  CStdString m_sMDOuserTagName;
  GTWDEFS_TYPE     m_iMDOtype;

};

